from datetime import timedelta
from django.conf import settings
from rest_framework import serializers
from common.minio_client import get_minio_client
from common.serializers import FileUrlMixin
from . import models


class MedicalCollectionTaskSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.MedicalCollectionTask
        fields = '__all__'
        # exclude = ['patient']  # , 'subject'


class MedicalCollectionTaskCreatRequestSerializer(serializers.ModelSerializer):
    project_id = serializers.CharField(source='project.project_id', label="项目ID")
    project_site_id = serializers.CharField(source='project_site.project_site_id', label="项目中心ID")
    subject_id = serializers.CharField(source='subject.subject_id', label="受试者ID")
    subject_visit_id = serializers.CharField(source='subject_visit.subject_visit_id', label="受试者访视ID")

    class Meta:
        model = models.MedicalCollectionTask
        fields = ['subject_visit_id', 'project_id', 'project_site_id', 'subject_id', 'category']
        # exclude = ['patient']  # , 'subject'


class MedicalCollectionFileSerializer(serializers.ModelSerializer):
    from apps.project.serializers import ProjectMaterialFileSerializer
    # file = serializers.FileField(required=True)  # 文件上传字段
    class Meta:
        model = models.MedicalCollectionFile
        fields = '__all__'
        # exclude = ['patient']  # , 'subject'


class MedicalCollectionGetFileRequestSerializer(serializers.ModelSerializer):

    subject_id = serializers.CharField(source='subject.subject_id', label="受试者ID")
    subject_visit_id = serializers.CharField(source='subject_visit.subject_visit_id', label="受试者访视ID")

    class Meta:
        model = models.MedicalCollectionTask
        fields = ['subject_visit_id','subject_id', 'category']
        # exclude = ['patient']  # , 's
        # ubject'


# class ProjectMaterialFileSerializer(FileUrlMixin, serializers.ModelSerializer):
#     url = serializers.SerializerMethodField(label='文件下载url')
#
#     class Meta:
#         model = models.ProjectMaterialFile
#         exclude = ['create_user', 'create_name', 'update_user', 'update_name']
class MedicalCollectionTasksLatestResponseSerializer(FileUrlMixin, serializers.ModelSerializer):
    url = serializers.SerializerMethodField(label='文件下载url')

    class Meta:
        model = models.MedicalCollectionFile
        exclude = ['create_user', 'create_name', 'update_user', 'update_name']


class MedicalRecordChangeJsonRequestMiniSerializer(serializers.ModelSerializer):
    subject_id = serializers.CharField(source='subject.subject_id', label="受试者ID")
    subject_visit_id = serializers.CharField(source='subject_visit.subject_visit_id', label="受试者访视ID")
    json_data = serializers.Serializer(label="JSON 数据")

    class Meta:
        model = models.MedicalCollectionTask

        fields = ['subject_visit_id','subject_id', 'category', 'json_data']
        # exclude = ['patient']  # , 's
        # ubject'


class MedicalRecordChangeJsonRequestSerializer(serializers.ModelSerializer):
    subject_id = serializers.CharField(source='subject.subject_id', label="受试者ID")
    subject_visit_id = serializers.CharField(source='subject_visit.subject_visit_id', label="受试者访视ID")
    json_data = serializers.JSONField(label="JSON 数据")

    class Meta:
        model = models.MedicalCollectionTask
        fields = ['subject_visit_id','subject_id', 'category', 'json_data']
        # exclude = ['patient']  # , 's
        # ubject'


# class MedicalCollectionFileHistoryRequestSerializer(serializers.ModelSerializer):
#
#     subject_id = serializers.CharField(source='subject.subject_id', label="受试者ID")
#
#     class Meta:
#         model = models.MedicalCollectionTask
#         fields = ['subject_id']
#         # exclude = ['patient']  # , 's
#         # ubject'


class MedicalCollectionFileHistoryResponseSerializer(FileUrlMixin, serializers.ModelSerializer):
    subject_visit_id = serializers.CharField(source='task.subject_visit_id', label="受试者访视ID")
    subject_id = serializers.CharField(source='task.subject_id', label="受试者ID")
    category = serializers.CharField(source='task.category', label="素材分类")
    create_user = serializers.CharField(source='task.create_user', label="创建人")
    create_name = serializers.CharField(source='task.create_name', label="创建人")
    url = serializers.SerializerMethodField(label='文件下载url')

    class Meta:
        model = models.MedicalCollectionFile
        # exclude = ['update_user', 'update_name']
        fields = '__all__'

class MedicalCrfResultTextSerializer(serializers.ModelSerializer):
    subject_id = serializers.CharField(source='task.subject_id', label="受试者ID")
    subject_visit_id = serializers.CharField(source='task.subject_visit_id', label="受试者访视ID")
    # medical_collection_file_id = serializers.CharField(source='medical_collection_file_id', label="文件ID")
    project_id = serializers.CharField(source='task.project_id', label="项目ID")
    project_site_id = serializers.CharField(source='task.project_site_id', label="项目中心ID")
    class Meta:
        model = models.MedicalCollectionCrfResult
        fields = ['subject_id', 'subject_visit_id', 'project_id', 'project_site_id']

class MedicalCollectionCrfLogCreateSerializer(serializers.ModelSerializer):
    subject_id = serializers.CharField(source='task.subject_id', label="受试者ID")
    subject_visit_id = serializers.CharField(source='task.subject_visit_id', label="受试者访视ID")
    # medical_collection_file_id = serializers.CharField(source='medical_collection_file_id', label="文件ID")
    project_id = serializers.CharField(source='task.project_id', label="项目ID")
    project_site_id = serializers.CharField(source='task.project_site_id', label="项目中心ID")
    modified_history = serializers.CharField(required=True)
    create_user = serializers.CharField(source='create_name', label="创建人")


    class Meta:
        model = models.MedicalCollectionCrfLog
        fields = [
            'subject_id',
            'subject_visit_id',
            'project_id',
            'project_site_id',
            'modified_history',
            'create_user'
        ]

class MedicalCollectionCrfLogQuerySerializer(serializers.Serializer):
    project_id = serializers.CharField(required=True, label="项目ID")
    project_site_id = serializers.CharField(required=True, label="项目中心ID")
    subject_id = serializers.CharField(required=True, label="受试者ID")
    subject_visit_id = serializers.CharField(required=True, label="受试者访视ID")
    title = serializers.CharField(required=True, label="一级标题")
    key = serializers.CharField(required=True, label="二级键名")

    class Meta:
        model = models.MedicalCollectionCrfLog
        fields = [
            'subject_id',
            'subject_visit_id',
            'project_id',
            'project_site_id',
            'title',
            'key'
        ]

class MedicalCollectionCrfLogResponseSerializer(serializers.ModelSerializer):
    content_text = serializers.SerializerMethodField()
    class Meta:
        model = models.MedicalCollectionCrfLog
        fields = [
            'id',
            'title',
            'item_key',
            'content_text',
            'create_time',
            'create_user',
            'create_name',
        ]

    def get_content_text(self, obj):
        # create_name = obj.create_name or ""
        # create_user = obj.create_user or ""
        # title = obj.title or ""
        # item_key = obj.item_key or ""
        content_text = obj.content_text or ""
        return content_text  # 或者替换为真实姓名字段，如果有