import os
import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smo.settings.test')
django.setup()

import psycopg2
from django.db import transaction
from apps.project.models import ProjectSite

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smo.settings.test')
django.setup()

# 'postgresql://postgres:Qr%4077.88@**************:5432/oceanus_trial_clinplus'
pg_conn_params = {
    'host': '**************',
    'database': 'oceanus_trial_clinplus',
    'user': 'postgres',
    'password': 'Qr@77.88',
    'port': 5432
}

PAGE_SIZE = 100  # 每次查询的记录数，可以根据实际情况调整

with open('./script/test_project_no.txt') as f:
    PROJECT_NO_LIST = f.readlines()

PROJECT_NO_LIST = list(set([i.strip() for i in PROJECT_NO_LIST if i.strip()]))
print(PROJECT_NO_LIST)


def migrate_project_site():
    # Connect to PostgreSQL
    pg_conn = psycopg2.connect(**pg_conn_params)
    pg_cursor = pg_conn.cursor()

    try:
        # 先获取总记录数
        count_query = f"SELECT COUNT(*) FROM project_site where project_id in (SELECT project_id FROM project where project_no in {tuple(PROJECT_NO_LIST)})"
        pg_cursor.execute(count_query)
        total_count = pg_cursor.fetchone()[0]

        # 计算总页数
        total_pages = (total_count + PAGE_SIZE - 1) // PAGE_SIZE

        for page in range(total_pages):
            offset = page * PAGE_SIZE

            # Query to fetch project_site data from PostgreSQL
            # Adjust the table name and column names to match your PostgreSQL schema
            pg_query = f"""
            SELECT
                t1.id as project_site_id,
                t1.project_id,
                t1.hosp_id,
                t1.hosp_name,
                t1.hosp_department_no,
                t1.status,
                dictconstantname('e888888', t1.status) as status_text,
                projectsiteroleusersandno(t1.id, '20107') as accredit_crc_text,
                projectsiteroleusersandno(t1.id, '212886296')  as backup_crc_text
            FROM
                project_site AS t1
            WHERE 
                project_id in (SELECT project_id FROM project where project_no in {tuple(PROJECT_NO_LIST)})
            LIMIT {PAGE_SIZE} OFFSET {offset}
            """
            pg_cursor.execute(pg_query)
            projects_site_data = pg_cursor.fetchall()

            # Get column names
            column_names = [desc[0] for desc in pg_cursor.description]

            # Process each row and insert/update to MySQL
            for row in projects_site_data:
                with transaction.atomic():
                    # Create a dictionary of project_site data
                    project_site_dict = dict(zip(column_names, row))
                    print(f"Updated backup_crc_text: {project_site_dict['backup_crc_text']}")
                    # Method 1: Using Django ORM
                    try:
                        # Try to get the existing project
                        project_site = ProjectSite.objects.get(project_site_id=project_site_dict['project_site_id'])

                        # Update existing project
                        for key, value in project_site_dict.items():
                            setattr(project_site, key, value)
                        project_site.save()
                        print(f"Updated project_site: {project_site.project_site_id}")

                    except ProjectSite.DoesNotExist:
                        # Create new project
                        ProjectSite.objects.create(**project_site_dict)
                        print(f"Created new project_site: {project_site_dict['project_site_id']}")

        print(f"Migration completed successfully. Processed {total_count} projects.")

    except Exception as e:
        print(f"Error during migration: {str(e)}")
    finally:
        pg_cursor.close()
        pg_conn.close()


if __name__ == "__main__":
    # python -m script.sync_ot_project_site
    # Setup Django environment if running as standalone script
    # django.setup()  # Uncomment if not running within Django

    # Run migration
    migrate_project_site()
