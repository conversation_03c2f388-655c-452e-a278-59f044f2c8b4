import os
import django
from django.utils import timezone
import psycopg2
from django.db import transaction

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smo.settings.test')
django.setup()

from apps.subject.models import Subject
from apps.patient.models import Patient

pg_conn_params = {
    'host': '**************',
    'database': 'oceanus_trial_clinplus',
    'user': 'postgres',
    'password': 'Qr@77.88',
    'port': 5432
}

PAGE_SIZE = 1000


def migrate_subject():
    pg_conn = psycopg2.connect(**pg_conn_params)
    pg_cursor = pg_conn.cursor(name='subject_cursor')  # 使用服务端游标避免一次性加载全部数据

    try:
        pg_query = """
        SELECT
            t1.id AS subject_id,
            t1.project_id,
            t1.project_site_id,
            t1.real_name,
            t1.short_name,
            t1.code,
            t1.status,
            dictconstantname('e888888', t1.status) AS status_text
        FROM subject AS t1
        """
        pg_cursor.execute(pg_query)
        processed_subject_ids = set()
        page = 0
        while True:
            rows = pg_cursor.fetchmany(PAGE_SIZE)
            if not rows:
                break

            page += 1
            column_names = [desc[0] for desc in pg_cursor.description]
            subject_dicts = [dict(zip(column_names, row)) for row in rows]

            # 收集本次处理的所有 subject_id
            subject_ids = [item['subject_id'] for item in subject_dicts]
            processed_subject_ids.update(subject_ids)

            existing_subjects = Subject.objects.in_bulk(subject_ids, field_name='subject_id')

            subjects_to_create = []
            subjects_to_update = []
            patients_to_create = []
            patients_to_update = []

            for item in subject_dicts:
                patient_id = f"{item['project_id']}_{item['project_site_id']}_{item['subject_id']}"

                if item['subject_id'] in existing_subjects:
                    subject = existing_subjects[item['subject_id']]
                    changed = False
                    for k, v in item.items():
                        if getattr(subject, k) != v:
                            setattr(subject, k, v)
                            changed = True
                    if changed:
                        subjects_to_update.append(subject)
                else:
                    subjects_to_create.append(Subject(**item))
                    patients_to_create.append(Patient(
                        subject_id=item['subject_id'],
                        patient_id=patient_id
                    ))

            with transaction.atomic():
                if subjects_to_create:
                    Subject.objects.bulk_create(subjects_to_create, batch_size=PAGE_SIZE)
                if subjects_to_update:
                    update_fields = list(subject_dicts[0].keys())
                    Subject.objects.bulk_update(subjects_to_update, update_fields, batch_size=PAGE_SIZE)

                if patients_to_create:
                    Patient.objects.bulk_create(patients_to_create, ignore_conflicts=True, batch_size=PAGE_SIZE)

            print(f"✅ Page {page}: Created {len(subjects_to_create)} subjects, Updated {len(subjects_to_update)}")

        # 软删除源系统中已删除的数据
        print("🔍 Checking for records to delete...")
        # 获取当前时间用于更新 update_time 字段
        current_time = timezone.now()

        # 分批处理删除操作，避免一次性加载大量数据到内存
        batch_size = 10000
        deleted_count = 0
        offset = 0

        while True:
            # 获取一批本地未删除的记录ID
            local_subject_ids_batch = list(Subject.objects.filter(
                delete_flag=0
            ).values_list('subject_id', flat=True)[offset:offset + batch_size])

            if not local_subject_ids_batch:
                break

            # 找出这批记录中需要删除的（即在源系统中不存在的）
            to_delete_batch = [sid for sid in local_subject_ids_batch if sid not in processed_subject_ids]

            if to_delete_batch:
                # 将这些记录标记为已删除 (软删除) 并更新 update_time
                count = Subject.objects.filter(subject_id__in=to_delete_batch).update(
                    delete_flag=1,
                    update_time=current_time
                )
                deleted_count += count
                print(f"🗑️  Soft deleted {count} records in current batch")

            offset += batch_size

        print(f"🗑️  Total soft deleted {deleted_count} records that no longer exist in source system")


        print("\n🎉 Subject migration completed.")

    except Exception as e:
        print(f"\n❌ Error during migration: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        pg_cursor.close()
        pg_conn.close()
        print("🔚 PostgreSQL connection closed.")


if __name__ == "__main__":
    # python -m script.sync_ot_subject_all
    migrate_subject()
