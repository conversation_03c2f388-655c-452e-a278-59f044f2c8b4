"""
OCR服务相关工具函数

该模块提供了统一的OCR处理入口，集成了OCR服务调用、图片矫正和文本提取功能。

使用方法:
    from common.ocr_mask.utils.ocr_service import process_ocr
    
    # 基本OCR处理（无角度矫正）
    result = process_ocr(image_bytes)
    
    # 启用角度矫正的OCR处理
    result = process_ocr(image_bytes, use_correction=True)

调用参数:
    input_img (bytes): 图片的字节数据
    use_correction (bool): 是否启用角度检测和矫正，默认False

返回数据结构:
    {
        "ocr_result": dict,  # OCR识别结果的完整字典，包含以下结构：
            {
                "result": {
                    "words_block_list": [  # 文本块列表（直接来自OCR服务）
                        {
                            "words": "文本内容", 
                            "location": [[x1,y1], [x2,y2], [x3,y3], [x4,y4]],  # 四角坐标
                            "confidence": 0.95  # 置信度
                        }
                    ],
                    "markdown_result": "markdown格式的文本",  # 可选
                    "has_correction": bool,  # 是否进行了角度矫正
                    "corrected_image": "base64编码的矫正图片",  # 可选
                    "direction": float  # 检测到的角度
                }
            },
        "corrected_image": str or None,  # 矫正后的base64图片，无矫正时为None
        "corrected_image_bytes": bytes or None,  # 矫正后的图片字节数据，用于图片处理
        "has_correction": bool,  # 是否进行了角度矫正
        "direction": float,  # 检测到的角度（度数）
        "markdown_text": "markdown格式的文本",  # 从OCR结果中提取的markdown文本（用于显示和AI分析）
        "text_blocks": list  # 直接来自OCR服务的文本块列表
    }

功能说明:
    - OCR服务调用: 调用外部OCR API进行图片文本识别
    - 角度矫正: 可选择启用图片角度检测和自动矫正
    - 图片处理: 提供base64字符串和字节数组两种格式的矫正图片
    - 文本提取: 自动从OCR结果中提取结构化文本信息
    - 错误处理: 统一的异常处理和错误日志

依赖服务:
    - OCR API: http://192.168.230.3:8011/ocr/single
    - 依赖库: requests, base64, json, common.ocr_tools.getBase64
"""
import base64
import json
import requests


def process_ocr(input_img, use_correction=False):
    """
    统一的OCR处理入口函数
    
    Args:
        input_img: 图片的字节数据
        use_correction: 是否启用角度检测和矫正，默认False
    
    Returns:
        dict: 包含OCR结果和矫正图片的字典
        {
            "ocr_result": dict,  # 解析后的OCR结果
            "corrected_image": str or None,  # 矫正后的base64图片，无矫正时为None
            "corrected_image_bytes": bytes or None,  # 矫正后的图片字节数据，用于脱敏处理
            "has_correction": bool,  # 是否进行了矫正
            "direction": float,  # 检测到的角度
            "markdown_text": str,  # 从OCR结果中提取的markdown文本（用于显示和AI分析）
            "text_blocks": list  # 直接来自OCR服务的文本块列表
        }
    """
    try:
        encoded_str = getBase64(input_img)
        
        url = "http://192.168.230.3:8011/ocr/single"
        payload = {
            "image": encoded_str,
        }
        
        # 添加角度检测参数
        if use_correction:
            payload["use_correction"] = True

        response = requests.request("POST", url, json=payload, verify=False)
        
        if not response.text:
            print("❌ OCR服务返回空结果")
            return {
                "ocr_result": {"result": {"words_block_list": [], "markdown_result": ""}},
                "corrected_image": None,
                "corrected_image_bytes": None,
                "has_correction": False,
                "direction": 0.0,
                "markdown_text": "",
                "text_blocks": []
            }
        
        # 解析JSON响应
        try:
            response_data = json.loads(response.text)
        except json.JSONDecodeError as e:
            print(f"❌ OCR服务返回无效JSON: {e}")
            return {
                "ocr_result": {"result": {"words_block_list": [], "markdown_result": ""}},
                "corrected_image": None,
                "corrected_image_bytes": None,
                "has_correction": False,
                "direction": 0.0,
                "markdown_text": "",
                "text_blocks": []
            }
        result_data = response_data.get("result", {})
        
        # 提取矫正相关字段
        has_correction = result_data.get("has_correction", False)
        corrected_image = result_data.get("corrected_image")
        direction = result_data.get("direction", 0.0)
        
        # 如果corrected_image是data URL格式，提取base64部分
        if corrected_image and corrected_image.startswith("data:image/"):
            corrected_image = corrected_image.split(",", 1)[1]
        
        # 处理矫正图片 - 转换为字节数据供后续脱敏使用
        corrected_image_bytes = None
        if has_correction and corrected_image:
            corrected_image_bytes = base64.b64decode(corrected_image)
            print(f"✅ 矫正图片已转换为字节数据，大小: {len(corrected_image_bytes)} bytes")
        
        if has_correction:
            print(f"✅ OCR服务返回了矫正图片，角度: {direction}°")
        elif use_correction:
            print(f"ℹ️ OCR服务检测角度: {direction}°，无需矫正")
        
        # 直接从OCR结果提取text_blocks
        markdown_text = ""
        text_blocks = []
        
        try:
            result_data = response_data.get("result", {})
            if "result" in response_data and "words_block_list" in result_data:
                markdown_text = result_data.get("markdown_result", "")
                # 直接使用原始的words_block_list数据
                words_block_list = result_data.get("words_block_list", [])
                text_blocks = words_block_list
        except Exception as e:
            print(f"⚠️ 提取text_blocks时出错: {e}")
            text_blocks = []
        
        return {
            "ocr_result": response_data,
            "corrected_image": corrected_image,
            "corrected_image_bytes": corrected_image_bytes,
            "has_correction": has_correction,
            "direction": direction,
            "markdown_text": markdown_text,
            "text_blocks": text_blocks
        }
            
    except Exception as e:
        print(f"❌ OCR服务调用失败: {e}")
        # 抛出异常让PocketFlow的重试机制生效
        raise Exception(f"OCR服务调用失败: {e}")


def getBase64(data):
    if isinstance(data, str):
        # 如果传入的是文件路径
        with open(data, "rb") as image_file:
            encoded_string = base64.b64encode(image_file.read()).decode()
    elif isinstance(data, bytes):
        # 如果传入的是字节流
        encoded_string = base64.b64encode(data).decode()
    else:
        raise ValueError("传入的数据类型必须是字符串（文件路径）或字节类型。")
    return encoded_string