import os
import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smo.settings.test')
django.setup()

import psycopg2
from django.db import transaction
from apps.subject.models import Subject
from apps.patient.models import Patient

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smo.settings.test')
django.setup()

# 'postgresql://postgres:Qr%4077.88@**************:5432/oceanus_trial_clinplus'
pg_conn_params = {
    'host': '**************',
    'database': 'oceanus_trial_clinplus',
    'user': 'postgres',
    'password': 'Qr@77.88',
    'port': 5432
}

PAGE_SIZE = 100  # 每次查询的记录数，可以根据实际情况调整

with open('./script/test_project_no.txt') as f:
    PROJECT_NO_LIST = f.readlines()

PROJECT_NO_LIST = list(set([i.strip() for i in PROJECT_NO_LIST if i.strip()]))
print(PROJECT_NO_LIST)


def migrate_subject():
    # Connect to PostgreSQL
    pg_conn = psycopg2.connect(**pg_conn_params)
    pg_cursor = pg_conn.cursor()

    try:
        # Get total record count
        count_query = f"SELECT COUNT(*) FROM subject where project_id in (SELECT project_id FROM project where project_no in {tuple(PROJECT_NO_LIST)})"
        pg_cursor.execute(count_query)
        total_count = pg_cursor.fetchone()[0]

        # Calculate total pages
        total_pages = (total_count + PAGE_SIZE - 1) // PAGE_SIZE

        for page in range(total_pages):
            offset = page * PAGE_SIZE

            # Query to fetch subject data from PostgreSQL
            pg_query = f"""
            SELECT
                t1.id as subject_id,
                t1.project_id,
                t1.project_site_id,
                t1.real_name,
                t1.short_name,
                t1.code,
                t1.status,
                dictconstantname('e888888', t1.status) as status_text
            FROM
                subject AS t1
            WHERE 
                project_id in (SELECT project_id FROM project where project_no in {tuple(PROJECT_NO_LIST)})
            LIMIT {PAGE_SIZE} OFFSET {offset}
            """
            pg_cursor.execute(pg_query)
            subject_data = pg_cursor.fetchall()

            # Get column names
            column_names = [desc[0] for desc in pg_cursor.description]

            # Process each row and insert/update to MySQL
            for row in subject_data:
                with transaction.atomic():
                    # Create a dictionary of subject data
                    subject_dict = dict(zip(column_names, row))
                    
                    # Generate patient_id from available data
                    # Format: project_id + project_site_id + subject code
                    patient_id = f"{subject_dict['project_id']}_{subject_dict['project_site_id']}_{subject_dict['subject_id']}"

                    try:
                        # Try to get the existing subject
                        subject = Subject.objects.get(subject_id=subject_dict['subject_id'])

                        # Update existing subject
                        for key, value in subject_dict.items():
                            setattr(subject, key, value)
                        subject.save()
                        
                        # Update or create related patient record
                        patient, created = Patient.objects.update_or_create(
                            subject_id=subject_dict['subject_id'],
                            defaults={'patient_id': patient_id}
                        )
                        
                        if created:
                            print(f"Created new patient for subject: {subject.subject_id}")
                        else:
                            print(f"Updated patient for subject: {subject.subject_id}")
                            
                        print(f"Updated subject: {subject.subject_id}")

                    except Subject.DoesNotExist:
                        # Create new subject
                        subject = Subject.objects.create(**subject_dict)
                        
                        # Create corresponding patient record
                        Patient.objects.create(
                            subject_id=subject_dict['subject_id'],
                            patient_id=patient_id
                        )
                        
                        print(f"Created new subject: {subject_dict['subject_id']} with patient record")

        print(f"Migration completed successfully. Processed {total_count} subjects with their patient records.")

    except Exception as e:
        print(f"Error during migration: {str(e)}")
    finally:
        pg_cursor.close()
        pg_conn.close()


if __name__ == "__main__":
    # python -m script.sync_ot_subject
    migrate_subject()
