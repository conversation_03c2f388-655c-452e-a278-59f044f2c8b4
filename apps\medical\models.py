from django.db import models
from common.models import BaseModel, BaseFileModel
from apps.subject_medical.models import SubjectMedicalInfo as MedicalInfoLite
from apps.subject_medical.models import SubjectMedicalFile as MedicalFile
from apps.subject_medical.models import SubjectMedicalFileMasked as MedicalFileMasked


# class MedicalFile(BaseModel, BaseFileModel):
#     """医疗文件"""

#     class Meta:
#         verbose_name = "医疗文件"
#         verbose_name_plural = "医疗文件"
#         db_table = 'medical_file'


# class MedicalFileMasked(BaseModel, BaseFileModel):
#     """医疗脱敏文件"""

#     class Meta:
#         verbose_name = "医疗脱敏文件"
#         verbose_name_plural = "医疗脱敏文件"
#         db_table = 'medical_file_masked'


# class MedicalInfoLite(BaseModel):
#     """医疗信息Lite版"""
#     # 一（医疗信息）对（医疗文件）一
#     file = models.OneToOneField(
#         MedicalFile,
#         on_delete=models.CASCADE,
#         db_constraint=False,
#         related_name='medical_info',
#         to_field='id',
#         db_column='file_id',
#         verbose_name="患者医疗文件ID",
#         db_index=True
#     )

#     # 一（医疗信息）对（医疗脱敏文件）一
#     file_masked = models.OneToOneField(
#         MedicalFileMasked,
#         on_delete=models.CASCADE,
#         db_constraint=False,
#         related_name='medical_info',
#         to_field='id',
#         db_column='file_masked_id',
#         verbose_name="患者医疗脱敏文件ID",
#         db_index=True,
#         null=True
#     )

#     TODO = 'TODO'
#     IN_PROGRESS = 'IN_PROGRESS'
#     COMPLETED = 'COMPLETED'
#     CANCELLED = 'CANCELLED'
#     ERROR = 'ERROR'
#     STATUS_CHOICES = [(TODO, '待办'), (IN_PROGRESS, '进行中'), (COMPLETED, '已完成'), (CANCELLED, '已取消'), (ERROR, '执行错误')]
#     mask_status = models.CharField(max_length=20, choices=STATUS_CHOICES, default=TODO, verbose_name="文件脱敏状态")

#     # 一（患者信息）对（医疗信息）多
#     patient = models.ForeignKey(
#         'patient.Patient',
#         on_delete=models.CASCADE,
#         db_constraint=False,
#         related_name='medical_infos',
#         to_field='patient_id',
#         db_column='patient_id',
#         verbose_name="患者ID",
#         db_index=True
#     )

#     # 一（受试者信息）对（医疗信息）多
#     subject = models.ForeignKey(
#         'subject.Subject',
#         on_delete=models.CASCADE,
#         db_constraint=False,
#         related_name='medical_infos',
#         to_field='subject_id',
#         db_column='subject_id',
#         verbose_name="受试者ID",
#         db_index=True
#     )

#     project = models.ForeignKey(
#         'project.Project',
#         on_delete=models.CASCADE,
#         db_constraint=False,
#         related_name='medical_infos',
#         to_field='project_id',
#         db_column='project_id',
#         verbose_name="项目ID",
#         db_index=True
#     )

#     project_site = models.ForeignKey(
#         'project.ProjectSite',
#         on_delete=models.CASCADE,
#         db_constraint=False,
#         related_name='medical_infos',
#         to_field='project_site_id',
#         db_column='project_site_id',
#         verbose_name="项目中心ID",
#         db_index=True
#     )
#     subject_visit = models.ForeignKey(
#         'subject.SubjectVisit',
#         on_delete=models.CASCADE,
#         null=True,
#         blank=True,
#         db_constraint=False,
#         related_name='medical_info_lite',
#         to_field='subject_visit_id',
#         db_column='subject_visit_id',
#         verbose_name="受试者访视ID",
#         db_index=True
# )

#     categorys = {
#         "1": "知情同意书",
#         "2": "访视病历",
#         "3": "生命体征表",
#         "4": "检验单",
#         "5": "检查单",
#         "6": "病理报告及肿瘤评估表",
#         "7": "量表/问卷",
#         "8": "样本记录表",
#         "9": "药物记录表",
#         "10": "医嘱单",
#         "11": "病史资料",
#         "12": "其他文件"
#     }

#     # CATEGORY_CHOICES = [
#     #     ('1', '病史（末次出入院记录、病理检查、手术单（可选）、知情同意书）'),
#     #     ('2', '医嘱单（可选，如：乳腺癌化疗记录）'),
#     #     ('3', '影筛选期病例（可选，如出入院记录、主治医师查房记录）'),
#     #     ('4', '筛选期报告单、筛选期其他记录（可选，如生物样本采集表、筛选期评估记录表等）、生命体征表'),
#     # ]
    
#     CATEGORY_CHOICES = [(k, v) for k, v in categorys.items()]
    
#     category = models.CharField(
#         max_length=50, choices=CATEGORY_CHOICES, db_index=True, verbose_name="医疗文件分类"
#     )
#     row_ocr = models.JSONField(null=True, verbose_name="原始OCR识别结果")
#     ocr_text = models.TextField(null=True, blank=True, verbose_name="OCR文本")
#     ocr_time = models.DateTimeField(null=True, verbose_name="OCR时间")
#     test_time = models.DateTimeField(null=True, verbose_name='检查时间')
#     report_time = models.DateTimeField(null=True, verbose_name='报告时间')

#     class Meta:
#         verbose_name = "医疗信息Lite版"
#         verbose_name_plural = "医疗信息Lite版"
#         db_table = 'medical_info_lite'
