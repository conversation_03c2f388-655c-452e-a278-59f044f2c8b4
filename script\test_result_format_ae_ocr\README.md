# 医疗检验单识别系统

## 文件结构

### 核心功能模块
- **`main.py`** - 核心功能模块，包含：
  - `process_medical_ocr()` - 处理医疗检验单OCR文本
  - `process_single_image()` - 处理单张图片
  - `process_image_directory()` - 批量处理文件夹
  - `PerformanceTracker` - 性能追踪器类

### 测试模块  
- **`test_main.py`** - 测试和评估模块，包含：
  - 命令行参数解析
  - 测试模式（test）- 不进行答案对比
  - 评估模式（eval）- 与正确答案对比
  - 演示模式（demo）- 使用内置测试用例
  - 性能报告和统计分析

## 使用方法

### 1. 直接测试（推荐）
```bash
# 使用默认配置运行
python test_main.py

# 测试模式 - 处理单个图片
python test_main.py test image.jpg

# 测试模式 - 批量处理文件夹
python test_main.py test /path/to/images/ --max-workers 10

# 评估模式 - 与答案对比
python test_main.py eval /path/to/images/ --ground-truth answers.xlsx --max-workers 8
```

### 2. 作为模块导入
```python
from script.test_result_format_ae_ocr.main import (
    process_medical_ocr,
    process_single_image, 
    process_image_directory,
    PerformanceTracker
)

# 创建性能追踪器
tracker = PerformanceTracker()

# 处理单张图片
ocr_text, result = process_single_image("image.jpg", performance_tracker=tracker)

# 批量处理
results = process_image_directory("/path/to/images/", performance_tracker=tracker)
```

## 设计优势

1. **功能分离** - 核心功能和测试代码分离，便于维护
2. **模块化** - 可以单独导入核心功能，无需测试依赖
3. **灵活性** - 测试模块可以独立运行，支持多种模式
4. **可扩展** - 易于添加新的测试模式或功能

## 注意事项

- `main.py` 专注于核心功能，不包含测试逻辑
- `test_main.py` 提供完整的测试和评估功能
- 性能追踪器现在需要显式传入，提高了代码的清晰度
- 两个文件都可以独立运行，互不影响
