# -*- coding: utf-8 -*-
"""
坐标提取功能使用示例

该脚本展示如何在实际应用中使用坐标提取功能，包括：
1. 基本的坐标提取流程
2. 与现有OCR处理流程的集成
3. 坐标信息的实际应用场景

使用方法:
    python example_usage.py
"""

import os
import sys
import json
from pathlib import Path

# 添加项目根目录到路径
current_script_path = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_script_path)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)


def example_basic_coordinate_extraction():
    """示例1：基本的坐标提取功能（重构后）"""
    print("📍 示例1：基本坐标提取功能（重构后）")
    print("=" * 50)

    # 这里展示如何使用重构后的功能
    print("""
# 导入必要的模块
from script.test_result_format_ae_ocr.main import process_medical_ocr
from script.test_result_format_ae_ocr.utils.ocr_client import OCRClient

# 创建OCR客户端
ocr_client = OCRClient()

# 处理图片并获取OCR数据（包含坐标）
ocr_data = ocr_client.ocr_main_with_coordinates("test_image.jpg")

# 使用重构后的流程处理OCR文本（自动启用坐标提取）
test_items = process_medical_ocr(
    ocr_data["text"],
    ocr_blocks=ocr_data["ocr_blocks"]  # 传入OCR块启用坐标提取
)

# 使用坐标信息（现在直接包含在测试项目中）
for i, item in enumerate(test_items):
    if item.row_coordinates:
        print(f"项目 {i}: {item.test_name}")
        print(f"  坐标点: {item.row_coordinates}")
    """)


def example_enhanced_processing():
    """示例2：向后兼容性演示"""
    print("\n📍 示例2：向后兼容性演示")
    print("=" * 50)

    print("""
# 重构后的函数保持向后兼容性
from script.test_result_format_ae_ocr.main import process_medical_ocr

# 不传入ocr_blocks参数时，功能与之前完全相同
test_items_without_coords = process_medical_ocr("OCR文本内容")

# 传入ocr_blocks参数时，自动启用坐标提取
ocr_data = [
    {
        "page": 1,
        "words_block_list": [
            {
                "words": "葡萄糖",
                "location": [[100, 200], [150, 200], [150, 220], [100, 220]],
                "confidence": 0.95,
                "line_break": True
            }
        ]
    }
]

test_items_with_coords = process_medical_ocr("OCR文本内容", ocr_blocks=ocr_data)

# 检查坐标信息
for item in test_items_with_coords:
    if item.row_coordinates:
        print(f"{item.test_name}: 坐标点 {item.row_coordinates}")
    """)


def example_coordinate_applications():
    """示例3：坐标信息的实际应用"""
    print("\n📍 示例3：坐标信息的实际应用")
    print("=" * 50)
    
    print("""
# 应用场景1：生成标注图片
def create_annotated_image(image_path, row_coordinates):
    from PIL import Image, ImageDraw
    
    image = Image.open(image_path)
    draw = ImageDraw.Draw(image)
    
    # 为每行绘制边界框
    for i, row in enumerate(row_coordinates):
        # 绘制矩形框
        draw.rectangle([row.x_min, row.y_min, row.x_max, row.y_max], 
                      outline="red", width=2)
        
        # 添加行号标签
        draw.text((row.x_min, row.y_min - 15), f"行{i}", fill="red")
    
    return image

# 应用场景2：按坐标区域裁剪图片
def crop_rows(image_path, row_coordinates):
    from PIL import Image
    
    image = Image.open(image_path)
    cropped_rows = []
    
    for i, row in enumerate(row_coordinates):
        # 裁剪每行的图片
        cropped = image.crop((row.x_min, row.y_min, row.x_max, row.y_max))
        cropped_rows.append(cropped)
    
    return cropped_rows

# 应用场景3：根据Y坐标排序测试项目
def sort_items_by_position(test_items, row_coordinates):
    # 创建项目到坐标的映射
    item_to_y = {}
    for row in row_coordinates:
        for item_idx in row.test_item_indices:
            item_to_y[item_idx] = row.center_y
    
    # 按Y坐标排序
    sorted_items = sorted(enumerate(test_items), 
                         key=lambda x: item_to_y.get(x[0], float('inf')))
    
    return [item for idx, item in sorted_items]

# 应用场景4：检测异常位置的项目
def detect_misplaced_items(row_coordinates, expected_y_spacing=50):
    misplaced = []
    
    for i in range(1, len(row_coordinates)):
        current_y = row_coordinates[i].center_y
        previous_y = row_coordinates[i-1].center_y
        spacing = current_y - previous_y
        
        if abs(spacing - expected_y_spacing) > 20:  # 容差20像素
            misplaced.append(i)
    
    return misplaced
    """)


def example_json_export():
    """示例4：坐标信息的JSON导出和使用"""
    print("\n📍 示例4：坐标信息的JSON导出")
    print("=" * 50)
    
    print("""
# 导出坐标信息为JSON格式
from script.test_result_format_ae_ocr.utils.coordinate_extraction import export_coordinates_to_json

# 导出完整的坐标信息
coordinate_data = export_coordinates_to_json(row_coordinates, test_items)

# JSON结构示例：
{
  "total_rows": 3,
  "extraction_timestamp": 1692960000.0,
  "rows": [
    {
      "row_index": 0,
      "coordinates": {
        "x_min": 50.0,
        "y_min": 200.0,
        "x_max": 470.0,
        "y_max": 225.0,
        "center_y": 212.5,
        "height": 25.0,
        "width": 420.0
      },
      "confidence": 0.94,
      "matched_blocks_count": 5,
      "test_item_indices": [0],
      "associated_test_items": [
        {
          "index": 0,
          "test_code": "GLU",
          "test_name": "葡萄糖",
          "test_value": "4.90"
        }
      ]
    }
    // ... 更多行数据
  ]
}

# 保存到文件
with open("coordinates.json", "w", encoding="utf-8") as f:
    json.dump(coordinate_data, f, ensure_ascii=False, indent=2)
    """)


def example_integration_workflow():
    """示例5：完整的集成工作流程"""
    print("\n📍 示例5：完整的集成工作流程")
    print("=" * 50)
    
    print("""
# 完整的处理流程示例
def process_medical_report_with_coordinates(image_path):
    '''处理医疗报告并提取坐标信息'''
    
    # 步骤1：OCR识别（保留坐标信息）
    ocr_client = OCRClient()
    ocr_data = ocr_client.ocr_main_with_coordinates(image_path)
    
    # 步骤2：结构化处理
    from script.test_result_format_ae_ocr.main import process_medical_ocr_with_coordinates
    result = process_medical_ocr_with_coordinates(ocr_data)
    
    # 步骤3：获取结果
    test_items = result["test_items"]
    row_coordinates = result["row_coordinates"]
    
    # 步骤4：应用坐标信息
    # 4.1 生成标注图片
    annotated_image = create_annotated_image(image_path, row_coordinates)
    annotated_image.save("annotated_" + Path(image_path).name)
    
    # 4.2 按位置排序项目
    sorted_items = sort_items_by_position(test_items, row_coordinates)
    
    # 4.3 导出坐标数据
    coordinate_data = export_coordinates_to_json(row_coordinates, test_items)
    
    return {
        "test_items": sorted_items,
        "row_coordinates": row_coordinates,
        "coordinate_data": coordinate_data,
        "annotated_image": annotated_image
    }

# 使用示例
result = process_medical_report_with_coordinates("medical_report.jpg")
print(f"识别到 {len(result['test_items'])} 个检验项目")
print(f"提取到 {len(result['row_coordinates'])} 行坐标")
    """)


def main():
    """主函数"""
    print("🎯 坐标提取功能使用示例")
    print("=" * 60)
    
    example_basic_coordinate_extraction()
    example_enhanced_processing()
    example_coordinate_applications()
    example_json_export()
    example_integration_workflow()
    
    print("\n📚 更多信息:")
    print("  - 查看 COORDINATE_EXTRACTION.md 了解详细文档")
    print("  - 运行 test_coordinate_simple.py 进行功能测试")
    print("  - 运行 demo_coordinate_extraction.py 进行实际演示")


if __name__ == "__main__":
    main()
