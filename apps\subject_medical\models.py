from django.db import models
from common.models import BaseModel, BaseFileModel


class SubjectMedicalFile(BaseModel, BaseFileModel):
    """受试者医疗文件"""

    class Meta:
        verbose_name = "受试者医疗文件"
        verbose_name_plural = "受试者医疗文件"
        db_table = 'subject_medical_file'


class SubjectMedicalFileMasked(BaseModel, BaseFileModel):
    """受试者医疗脱敏文件"""

    class Meta:
        verbose_name = "受试者医疗脱敏文件"
        verbose_name_plural = "受试者医疗脱敏文件"
        db_table = 'subject_medical_file_masked'


class SubjectMedicalInfo(BaseModel):
    """受试者医疗信息"""
    # TODO = 'TODO'
    # IN_PROGRESS = 'IN_PROGRESS'
    # COMPLETED = 'COMPLETED'
    # CANCELLED = 'CANCELLED'
    # ERROR = 'ERROR'
    # STATUS_CHOICES = [(TODO, '待办'), (IN_PROGRESS, '进行中'), (COMPLETED, '已完成'), (CANCELLED, '已取消'), (ERROR, '执行错误')]
    # mask_status = models.CharField(max_length=20, choices=STATUS_CHOICES, default=TODO, verbose_name="文件脱敏状态")
    row_ocr = models.JSONField(null=True, verbose_name="原始OCR识别结果")
    ocr_time = models.DateTimeField(null=True, verbose_name="OCR时间")
    test_time = models.DateTimeField(null=True, verbose_name='检查时间')
    report_time = models.DateTimeField(null=True, verbose_name='报告时间')
    data_source = models.CharField(max_length=20, null=True, default='ERP', verbose_name="数据来源")
    page_count = models.IntegerField(null=True, blank=True, verbose_name="页数")
        # 新增字段
    ocr_text = models.TextField(null=True, blank=True, verbose_name="OCR文本")
    ocr_text_mask = models.TextField(null=True, blank=True, verbose_name="OCR打码文本")
    ocr_box = models.JSONField(null=True, blank=True, verbose_name="OCR带坐标识别结果")
    
    # 任务时间记录字段
    preprocess_start_time = models.DateTimeField(null=True, blank=True, verbose_name="任务开始时间")
    preprocess_end_time = models.DateTimeField(null=True, blank=True, verbose_name="任务结束时间")
    preprocess_duration = models.FloatField(null=True, blank=True, verbose_name="任务总耗时(秒)")
    
    TODO = 'TODO'
    IN_PROGRESS = 'IN_PROGRESS'
    COMPLETED = 'COMPLETED'
    CANCELLED = 'CANCELLED'
    ERROR = 'ERROR'
    STATUS_CHOICES = [
        (TODO, '待办'),
        (IN_PROGRESS, '进行中'),
        (COMPLETED, '已完成'),
        (CANCELLED, '已取消'),
        (ERROR, '执行错误')
    ]
    mask_status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default=TODO,
        verbose_name="文件脱敏状态"
    )
    ocr_status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default=TODO,
        verbose_name="ocr文本提取状态"
    )

    # 一（医疗信息）对（医疗文件）一
    file = models.OneToOneField(
        'subject_medical.SubjectMedicalFile',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='subject_medical_info',
        to_field='id',
        db_column='file_id',
        verbose_name="患者医疗文件ID",
        db_index=True
    )

    # 一（医疗信息）对（医疗脱敏文件）一
    file_masked = models.OneToOneField(
        'subject_medical.SubjectMedicalFileMasked',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='subject_medical_info',
        to_field='id',
        db_column='file_masked_id',
        verbose_name="患者医疗脱敏文件ID",
        db_index=True,
        null=True
    )

    project = models.ForeignKey(
        'project.Project',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='subject_medical_infos',
        to_field='project_id',
        db_column='project_id',
        verbose_name="项目ID",
        db_index=True
    )

    project_site = models.ForeignKey(
        'project.ProjectSite',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='subject_medical_infos',
        to_field='project_site_id',
        db_column='project_site_id',
        verbose_name="项目中心ID",
        db_index=True
    )

    # 一（受试者信息）对（医疗信息）多
    subject = models.ForeignKey(
        'subject.Subject',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='subject_medical_infos',
        to_field='subject_id',
        db_column='subject_id',
        verbose_name="受试者ID",
        db_index=True
    )

    # 一（患者信息）对（医疗信息）多
    patient = models.ForeignKey(
        'patient.Patient',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='subject_medical_infos',
        to_field='patient_id',
        db_column='patient_id',
        verbose_name="患者ID",
        db_index=True
    )

    subject_epoch = models.ForeignKey(
        'subject.SubjectEpoch',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='subject_medical_infos',
        to_field='subject_epoch_id',
        db_column='subject_epoch_id',
        verbose_name="受试者阶段ID）",
        db_index=True
    )

    subject_visit = models.ForeignKey(
        'subject.SubjectVisit',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='subject_medical_infos',
        to_field='subject_visit_id',
        db_column='subject_visit_id',
        verbose_name="受试者访视ID）",
        db_index=True
    )

    subject_item = models.ForeignKey(
        'subject.SubjectItem',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='subject_medical_infos',
        to_field='subject_item_id',
        db_column='subject_item_id',
        verbose_name="受试者操作项ID）",
        db_index=True
    )

    class Meta:
        verbose_name = "受试者医疗信息"
        verbose_name_plural = "受试者医疗信息"
        db_table = 'subject_medical_info'
