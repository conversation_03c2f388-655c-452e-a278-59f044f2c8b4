from django.db import models
from django.contrib.auth.models import AbstractUser

from common.models import BaseModel


class User(AbstractUser):
    """
    用户表
    """
    username = models.CharField(max_length=150, unique=True)
    password = models.CharField(max_length=150, blank=True, null=True)
    project_whitelist_flag = models.SmallIntegerField(default=0, verbose_name="项目白名单标识")

    class Meta:
        db_table = 'auth_user'
        verbose_name = "用户"
        verbose_name_plural = "用户列表"


class SysUser(models.Model):
    """
    ERP用户表
    """
    username = models.Char<PERSON>ield(max_length=150, unique=True)
    password = models.CharField(max_length=150, blank=True, null=True)
    dd_union_id = models.CharField(max_length=100, blank=True, null=True)
    realname = models.Char<PERSON>ield(max_length=150, blank=True, null=True)
    fg_depart = models.Char<PERSON>ield(max_length=150, blank=True, null=True)
    hr_post = models.CharField(max_length=150, blank=True, null=True)
    status = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'sys_user'


class AuthProjectRoleACL(BaseModel):
    """
    项目角色访问权限控制表
    """
    SCOPE_ALL = 1
    SCOPE_PARTIAL = 2

    SCOPE_CHOICES = [
        (SCOPE_ALL, "访问项目所有功能"),
        (SCOPE_PARTIAL, "访问项目部分功能")
    ]

    role_code = models.CharField(max_length=100, verbose_name="角色编码", unique=True)
    role_name = models.CharField(max_length=100, verbose_name="角色名称（中文）")
    role_name_en = models.CharField(max_length=100, verbose_name="角色名称（英文）")
    is_allowed = models.BooleanField(default=False, verbose_name="是否允许访问")
    access_scope = models.SmallIntegerField(choices=SCOPE_CHOICES, default=SCOPE_ALL, verbose_name="访问范围")

    class Meta:
        db_table = "auth_project_role_acl"
        verbose_name = "项目角色访问权限控制表"
        verbose_name_plural = "项目角色访问权限控制表列表"

    def __str__(self):
        return f"{self.role_name}（{self.role_code}）"
