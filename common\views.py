from functools import wraps
from django.conf import settings
from django.core.exceptions import PermissionDenied
from django.http import Http404

from rest_framework.views import set_rollback
from rest_framework import exceptions
from rest_framework.response import Response

from common.exceptions import DeprecatedException


def custom_exception_handler(exc, context):
    """
    Returns the response that should be used for any given exception.

    By default we handle the REST framework `APIException`, and also
    Django's built-in `Http404` and `PermissionDenied` exceptions.

    Any unhandled exceptions may return `None`, which will cause a 500 error
    to be raised.
    """
    if isinstance(exc, Http404):
        exc = exceptions.NotFound()
    elif isinstance(exc, PermissionDenied):
        exc = exceptions.PermissionDenied()

    if isinstance(exc, exceptions.APIException):
        headers = {}
        if getattr(exc, 'auth_header', None):
            headers['WWW-Authenticate'] = exc.auth_header
        if getattr(exc, 'wait', None):
            headers['Retry-After'] = '%d' % exc.wait

        data = {
            'code': str.upper(exc.default_code),
            'message': None,
            'detail': None,
        }

        if isinstance(exc.detail, (list, dict)):
            data['message'] = exc.default_detail
            data['detail'] = exc.detail
        else:
            data['message'] = exc.detail

        set_rollback()
        return Response(data, status=exc.status_code, headers=headers)

    return None


def deprecated_view(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        raise DeprecatedException()
    return wrapper
