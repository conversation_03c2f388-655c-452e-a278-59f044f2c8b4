from django.db import models


class BaseModel(models.Model):
    """
    基础模型类
    """
    class DeleteStatus(models.IntegerChoices):
        ACTIVE = 0, '未删除'
        DELETED = 1, '已删除'

    delete_flag = models.SmallIntegerField(choices=DeleteStatus.choices, default=DeleteStatus.ACTIVE, verbose_name="删除标志（0：未删除；1：已删除）", db_index=True)
    data_version = models.PositiveIntegerField(default=0, verbose_name="数据版本")
    create_time = models.DateTimeField(auto_now_add=True, verbose_name="创建时间", db_index=True)
    update_time = models.DateTimeField(auto_now=True, verbose_name="更新时间", db_index=True)
    create_user = models.CharField(max_length=255, null=True, verbose_name="创建人工号")
    create_name = models.CharField(max_length=255, null=True, verbose_name="创建人姓名")
    update_user = models.CharField(max_length=255, null=True, verbose_name="更新人工号")
    update_name = models.CharField(max_length=255, null=True, verbose_name="更新人姓名")

    class Meta:
        abstract = True


class BaseFileModel(models.Model):
    """BaseFileModel"""
    original_filename = models.CharField(max_length=255, verbose_name="原始文件名")
    bucket_name = models.CharField(max_length=100, verbose_name="存储桶名称")
    object_name = models.CharField(max_length=500, verbose_name="对象名称")
    content_type = models.CharField(max_length=100, verbose_name="文件类型")
    size = models.BigIntegerField(verbose_name="文件大小（字节）")
    hash = models.CharField(max_length=255, verbose_name="文件哈希（sha256）", db_index=True)

    class Meta:
        abstract = True
