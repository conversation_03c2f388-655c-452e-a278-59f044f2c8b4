"""
OCR脱敏处理的PocketFlow节点定义 - 
"""
import time
from pocketflow import Node
from common.clients.ocr_service import process_ocr
from .utils.text_processor import build_char_to_block_mapping, map_entity_to_blocks
from .utils.hippa_deidentify_client import DeidentifyClient


class OCRProcessingNode(Node):
    """OCR识别和基本文本提取节点（合并版）- 支持角度矫正"""
    
    def _generate_ocr_box(self, ocr_result):
        """
        生成ocr_box结构，包含带坐标的OCR信息（单页格式）
        
        Args:
            ocr_result: OCR识别结果字典
            
        Returns:
            dict: ocr_box结构（单页）
            {
                "page": 1,
                "words_block_list": [
                    {
                        "words": "文本内容",
                        "location": [[x1,y1], [x2,y2], [x3,y3], [x4,y4]],
                        "confidence": 0.95,
                        "line_break": false  # 新增：是否需要换行
                    }
                ]
            }
        """
        try:
            if not ocr_result or not isinstance(ocr_result, dict):
                return {
                    "page": 1,
                    "words_block_list": []
                }
            
            # 单页文档处理
            result_data = ocr_result.get("result", {})
            if "result" in ocr_result and "words_block_list" in result_data:
                words_block_list = []
                words_blocks = result_data.get("words_block_list", [])
                
                for block in words_blocks:
                    words_block = {
                        "words": block.get("words", ""),
                        "location": block.get("location", []),
                        "confidence": block.get("confidence", 1.0)
                    }
                    # 只有line_break为true时才添加该字段
                    if block.get("line_break", False):
                        words_block["line_break"] = True
                    words_block_list.append(words_block)
                
                return {
                    "page": 1,
                    "words_block_list": words_block_list
                }
            
            # 其他格式，返回空列表
            else:
                print(f"⚠️ OCR结果格式无法识别，无法生成ocr_box")
                return {
                    "page": 1,
                    "words_block_list": []
                }
                
        except Exception as e:
            print(f"❌ 生成ocr_box时出错: {e}")
            return {
                "page": 1,
                "words_block_list": []
            }
    
    def __init__(self, max_retries=3, wait=10.0, use_correction=True):
        super().__init__(max_retries=max_retries, wait=wait)
        self.use_correction = use_correction

    def prep(self, shared):
        """准备OCR处理所需的数据"""
        return {
            "input_img": shared.get("input_img")
        }

    def exec(self, data):
        """执行OCR识别和旋转处理，支持角度矫正"""
        start_time = time.time()
        input_img = data["input_img"]
        # 使用统一的OCR处理函数
        result = process_ocr(input_img, use_correction=self.use_correction)
        
        ocr_result = result["ocr_result"]
        corrected_image = result["corrected_image"]
        corrected_image_bytes = result["corrected_image_bytes"]
        has_correction = result["has_correction"]
        direction = result["direction"]
        markdown_text = result["markdown_text"]
        text_blocks = result["text_blocks"]
        
        print(f"🔄 OCR处理结果:")
        print(f"  - has_correction: {has_correction}")
        print(f"  - direction: {direction}°")

        # 记录OCR处理时间
        ocr_time = time.time() - start_time
        
        # 获取markdown文本（用于显示和AI分析）
        # markdown_text 已经从 OCR 服务直接获取

        if has_correction:
            print(f"✅ OCR处理完成，已生成矫正图片，角度: {direction}°")
        elif self.use_correction:
            print(f"✅ OCR处理完成，检测角度: {direction}°，无需矫正")
        else:
            print("✅ OCR处理完成")

        # 生成ocr_box结构
        ocr_box = self._generate_ocr_box(ocr_result)
        
        return {
            "ocr_result": ocr_result,
            "ocr_box": ocr_box,  # 新增：带坐标的OCR信息
            "input_text": markdown_text,  # 用于最终返回的文本
            "markdown_text": markdown_text,  # 注释：主要用于后续AI分析
            "ocr_time": ocr_time,
            "corrected_image": corrected_image,
            "corrected_image_bytes": corrected_image_bytes,
            "has_correction": has_correction,
            "direction": direction
        }

    def post(self, shared, prep_res, exec_res):
        """保存OCR结果到共享存储"""
        shared["ocr_result"] = exec_res["ocr_result"]
        shared["ocr_box"] = exec_res["ocr_box"]  # 新增：带坐标的OCR信息
        shared["input_text"] = exec_res["input_text"]  # 用于最终返回的文本
        shared["markdown_text"] = exec_res["markdown_text"]  # 注释：用于AI分析的文本
        shared["ocr_time"] = exec_res.get("ocr_time", 0.0)  # 保存OCR处理时间

        # 保存图片矫正信息
        shared["corrected_image"] = exec_res.get("corrected_image")
        shared["corrected_image_bytes"] = exec_res.get("corrected_image_bytes")
        shared["has_correction"] = exec_res.get("has_correction", False)
        shared["direction"] = exec_res.get("direction", 0.0)

        # 保存用于脱敏的图片数据（优先使用矫正图片）
        if exec_res.get("has_correction") and exec_res.get("corrected_image_bytes"):
            shared["image_for_masking"] = exec_res["corrected_image_bytes"]
            print(f"📸 矫正图片已保存，将用于脱敏处理")
            print(f"📸 矫正角度: {exec_res.get('direction', 0.0)}°")
        else:
            shared["image_for_masking"] = shared.get("input_img")
            print(f"📸 使用原始图片进行脱敏处理")
        return "default"

    def exec_fallback(self, prep_res, exc):
        """OCR处理失败时的降级处理"""
        print(f"❌ OCR处理失败，已重试{self.max_retries}次，最终异常: {exc}")
        print(f"🔄 执行降级处理，返回空结果")
        
        # 返回一个空的OCR结果，让后续流程能够继续
        return {
            "ocr_result": {"result": {"words_block_list": [], "markdown_result": ""}},
            "ocr_box": [],  # 新增：带坐标的OCR信息
            "input_text": "",
            "markdown_text": "",
            "ocr_time": 0.0,
            "corrected_image": None,
            "corrected_image_bytes": None,
            "has_correction": False,
            "direction": 0.0
        }

class HippaDeidentifyNode(Node):
    """HIPAA脱敏服务节点 - 新的敏感词识别和脱敏方案"""

    def __init__(self, max_retries=3, wait=5.0, service_url=None, timeout=30):
        super().__init__(max_retries=max_retries, wait=wait)
        self.timeout = timeout
        # 直接使用DeidentifyClient，让它自己处理配置获取
        self.client = DeidentifyClient(base_url=service_url, timeout=timeout)
        self.service_url = self.client.base_url

    def prep(self, shared):
        """准备HIPAA脱敏所需的数据"""
        return {
            "markdown_text": shared.get("markdown_text", ""),
            "ocr_result": shared.get("ocr_result", ""),
            "project_no": shared.get("project_no")
        }

    def exec(self, data):
        """执行HIPAA脱敏处理"""
        markdown_text = data["markdown_text"]
        ocr_result = data["ocr_result"]

        print("🏥 开始HIPAA脱敏处理...")

        if not markdown_text or not markdown_text.strip():
            print("⚠️ 输入文本为空，跳过HIPAA脱敏")
            return {
                "hippa_entities": [],
                "hippa_deidentified_text": "",
                "char_to_block_mapping": [],
                "success": False,
                "error_message": "输入文本为空"
            }

        try:
            # 获取项目编号
            project_no = data.get("project_no")
            project_code = project_no if project_no else None

            # 调用HIPAA脱敏服务
            result = self.client.deidentify(markdown_text, project_code=project_code)

            if not result.success:
                print(f"❌ HIPAA脱敏失败: {result.error_message}")
                # 抛出异常让PocketFlow的重试机制生效
                raise Exception(f"HIPAA脱敏服务调用失败: {result.error_message}")

            # 构建字符位置映射表 - 直接传递字典
            char_to_block_mapping = build_char_to_block_mapping(ocr_result)

            # 获取适用于OCR遮挡的实体列表
            hippa_entities = result.entities_for_ocr_masking

            print(f"✅ HIPAA脱敏成功:")
            print(f"  识别敏感实体: {len(hippa_entities)} 个")
            print(f"  构建映射表: {len(char_to_block_mapping)} 个文本块")
            print(f"  处理耗时: {result.processing_time:.2f}s")

            # 输出识别到的敏感实体详情
            if hippa_entities:
                print("🔍 识别到的敏感实体:")
                for i, entity in enumerate(hippa_entities):  # 只显示前5个
                    print(f"  {i+1}. {entity['text']} ({entity['entity_type']}) "
                          f"位置:{entity['start']}-{entity['end']} "
                          f"置信度:{entity['confidence']:.2f}")

            return {
                "hippa_entities": hippa_entities,
                "hippa_deidentified_text": result.deidentified_text,
                "char_to_block_mapping": char_to_block_mapping,
                "success": True,
                "processing_time": result.processing_time,
                "deidentify_time": result.processing_time  # 保存脱敏时间
            }

        except Exception as e:
            error_msg = f"HIPAA脱敏处理异常: {str(e)}"
            print(f"💥 {error_msg}")
            import traceback
            traceback.print_exc()

            # 抛出异常让PocketFlow的重试机制生效
            raise Exception(f"HIPAA脱敏处理异常: {error_msg}")

    def _generate_masked_ocr_box(self, original_ocr_box, hippa_entities):
        """
        根据HIPAA脱敏结果生成脱敏后的ocr_box
        
        Args:
            original_ocr_box: 原始的ocr_box数据
            hippa_entities: HIPAA识别的敏感实体列表
        
        Returns:
            dict: 脱敏后的ocr_box数据
        """
        if not original_ocr_box or not hippa_entities:
            return original_ocr_box
        
        # 创建脱敏后的words_block_list
        masked_words_block_list = []
        
        for block in original_ocr_box.get("words_block_list", []):
            original_text = block.get("words", "")
            masked_text = original_text
            
            # 对每个敏感实体进行替换
            for entity in hippa_entities:
                entity_text = entity.get("text", "")
                if entity_text and entity_text in original_text:
                    # 用*替换敏感文本，*的数量与原字符长度相同
                    masked_text = masked_text.replace(entity_text, "*" * len(entity_text))
            
            # 创建脱敏后的文本块
            masked_block = {
                "words": masked_text,
                "location": block.get("location", []),
                "confidence": block.get("confidence", 1.0)
            }
            # 只有line_break为true时才添加该字段
            if block.get("line_break", False):
                masked_block["line_break"] = True
            masked_words_block_list.append(masked_block)
        
        return {
            "page": original_ocr_box.get("page", 1),
            "words_block_list": masked_words_block_list
        }

    def post(self, shared, prep_res, exec_res):
        """保存HIPAA脱敏结果到共享存储"""
        shared["hippa_entities"] = exec_res.get("hippa_entities", [])
        shared["hippa_deidentified_text"] = exec_res.get("hippa_deidentified_text", "")
        shared["char_to_block_mapping"] = exec_res.get("char_to_block_mapping", [])
        shared["hippa_success"] = exec_res.get("success", False)
        shared["hippa_error"] = exec_res.get("error_message", "")

        # 为了保持兼容性，将HIPAA结果转换为原有格式
        # 提取敏感词文本列表用于图像遮挡
        final_keywords = [entity["text"] for entity in exec_res.get("hippa_entities", [])]
        shared["final_keywords"] = final_keywords

        # 设置脱敏后的文本
        shared["masked_text"] = exec_res.get("hippa_deidentified_text", "")
        
        # 保存脱敏时间
        shared["deidentify_time"] = exec_res.get("deidentify_time", 0.0)

        # 生成脱敏后的ocr_box数据
        masked_ocr_box = self._generate_masked_ocr_box(
            shared.get("ocr_box", {}), 
            exec_res.get("hippa_entities", [])
        )
        shared["masked_ocr_box"] = masked_ocr_box

        print(f"💾 HIPAA脱敏结果已保存:")
        print(f"  最终敏感词: {len(final_keywords)} 个")
        print(f"  脱敏文本长度: {len(shared['masked_text'])} 字符")
        print(f"  生成脱敏后ocr_box: {len(masked_ocr_box.get('words_block_list', []))} 个文本块")

        return "default"

    def exec_fallback(self, prep_res, exc):
        """HIPAA脱敏处理失败时的降级处理"""
        print(f"❌ HIPAA脱敏处理失败，已重试{self.max_retries}次，最终异常: {exc}")
        print(f"🔄 执行降级处理，返回空结果")
        
        # 获取原始文本
        markdown_text = prep_res.get("markdown_text", "")
        
        # 返回一个空的脱敏结果，让后续流程能够继续
        return {
            "hippa_entities": [],
            "hippa_deidentified_text": markdown_text,  # 返回原文本
            "char_to_block_mapping": [],
            "success": False,
            "error_message": f"HIPAA脱敏服务调用失败: {str(exc)}",
            "processing_time": 0.0,
            "deidentify_time": 0.0,
            "masked_ocr_box": None  # 脱敏失败时返回None
        }

class MaskImageNode(Node):
    """图片遮挡处理节点 - """

    def prep(self, shared):
        """准备图片遮挡所需的数据"""

        return {
            "input_img":  shared.get("image_for_masking"),  # 使用矫正图片或原始图片
            "ocr_result": shared.get("ocr_result"),
            "hippa_entities": shared.get("hippa_entities", []),
            "char_to_block_mapping": shared.get("char_to_block_mapping", [])
        }

    def exec(self, data):
        """执行HIPAA模式图片遮挡"""
        print("🖼️ 开始HIPAA模式图片遮挡处理...")

        # 添加调试信息
        input_img = data["input_img"]
        # 图片处理和遮挡
        import io
        from PIL import Image, ImageDraw

        A = io.BytesIO(input_img)
        image = Image.open(A)
        draw = ImageDraw.Draw(image)

        # 使用HIPAA模式：基于字符位置进行精确遮挡
        print("🏥 使用HIPAA模式进行精确遮挡")
        return self._mask_with_hippa_entities(
            draw, image, data["hippa_entities"],
            data["char_to_block_mapping"], data["ocr_result"]
        )

    def _mask_with_hippa_entities(self, draw, image, hippa_entities, char_to_block_mapping, ocr_result):
        """使用HIPAA实体进行精确遮挡"""
        total_masks = 0
        precise_masks = 0
        fallback_masks = 0
        fill_color = (0, 0, 0)  # 黑色遮挡

        print(f"🎯 开始处理 {len(hippa_entities)} 个HIPAA敏感实体")

        for entity in hippa_entities:
            try:
                # 将实体映射到OCR文本块
                matched_blocks = map_entity_to_blocks(entity, char_to_block_mapping)

                if not matched_blocks:
                    print(f"⚠️ 实体 '{entity['text']}' 未找到匹配的文本块")
                    continue

                # 对每个匹配的文本块进行遮挡
                for match in matched_blocks:
                    block_info = match["block_info"]
                    relative_start = match["relative_start"]
                    relative_end = match["relative_end"]

                    # 计算精确的遮挡坐标
                    coords = self._calculate_precise_mask_coordinates(
                        block_info["block_text"],
                        entity["text"],
                        block_info["block_location"],
                        relative_start,
                        relative_end
                    )

                    if coords:
                        # 执行遮挡
                        draw.rectangle(coords, fill=fill_color)
                        total_masks += 1
                        precise_masks += 1
                        print(f"✅ 精确遮挡: '{entity['text']}' at {coords}")
                    else:
                        # 降级到整个文本块遮挡
                        location = block_info["block_location"]
                        if len(location) >= 4:
                            x_coords = [point[0] for point in location]
                            y_coords = [point[1] for point in location]
                            coords = [min(x_coords), min(y_coords), max(x_coords), max(y_coords)]
                            draw.rectangle(coords, fill=fill_color)
                            total_masks += 1
                            fallback_masks += 1
                            print(f"🔄 降级遮挡: '{entity['text']}' 整个文本块")

            except Exception as e:
                print(f"❌ 处理实体 '{entity.get('text', 'unknown')}' 时出错: {e}")
                continue

        print(f"📊 HIPAA遮挡统计: 总计{total_masks}处，精确{precise_masks}处，降级{fallback_masks}处")

        return {
            "masked_image": image,
            "mask_stats": {
                "total": total_masks,
                "precise": precise_masks,
                "fallback": fallback_masks,
                "mode": "hippa"
            }
        }



    def _calculate_precise_mask_coordinates(self, block_text, entity_text, block_location, relative_start, relative_end):
        """计算精确的遮挡坐标"""
        try:
            if len(block_location) < 4:
                return None

            # 获取文本块的边界坐标
            x1, y1 = block_location[0]  # 左上角
            x2, y2 = block_location[1]  # 右上角
            x3, y3 = block_location[2]  # 右下角
            x4, y4 = block_location[3]  # 左下角

            # 计算文本块的实际宽度和高度
            block_width = max(x2, x3) - min(x1, x4)
            block_height = max(y3, y4) - min(y1, y2)

            # 计算每个字符的平均宽度
            char_count = len(block_text)
            if char_count == 0:
                return None

            # 中文字符一般比英文字符宽，进行加权计算
            chinese_chars = sum(1 for char in block_text if '\u4e00' <= char <= '\u9fff')
            english_chars = char_count - chinese_chars
            effective_char_count = chinese_chars * 1.8 + english_chars
            avg_char_width = block_width / effective_char_count if effective_char_count > 0 else block_width / char_count

            # 计算实体前面字符的有效宽度
            prefix_text = block_text[:relative_start]
            prefix_chinese = sum(1 for char in prefix_text if '\u4e00' <= char <= '\u9fff')
            prefix_english = len(prefix_text) - prefix_chinese
            prefix_width = (prefix_chinese * 1.8 + prefix_english) * avg_char_width

            # 计算实体本身的宽度
            entity_chinese = sum(1 for char in entity_text if '\u4e00' <= char <= '\u9fff')
            entity_english = len(entity_text) - entity_chinese
            entity_width = (entity_chinese * 1.8 + entity_english) * avg_char_width

            # 计算实体的实际坐标
            entity_x1 = min(x1, x4) + prefix_width
            entity_y1 = min(y1, y2)
            entity_x2 = entity_x1 + entity_width
            entity_y2 = max(y3, y4)

            # 添加一些边距确保完全覆盖
            margin_x = avg_char_width * 0.1  # 10%的边距
            margin_y = block_height * 0.05  # 5%的边距

            return [
                max(0, int(entity_x1 - margin_x)),
                max(0, int(entity_y1 - margin_y)),
                int(entity_x2 + margin_x),
                int(entity_y2 + margin_y)
            ]

        except Exception as e:
            print(f"⚠️ 计算精确坐标时出错: {e}")
            return None

    def post(self, shared, prep_res, exec_res):
        """保存遮挡后的图片和统计信息"""
        shared["final_image"] = exec_res["masked_image"]
        shared["mask_stats"] = exec_res["mask_stats"]
        return "default"