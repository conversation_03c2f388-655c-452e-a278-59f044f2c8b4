import os
import sys
import re
import pytz
import uuid
import time
import json
import yaml
import datetime
import logging
import subprocess
import tempfile
import os
from docx import Document
from docx.shared import Inches
from io import BytesIO
import base64
from pathlib import Path
import fitz  # PyMuPDF
from typing import List


from typing import Union, Type
from requests.auth import HTTPBasicAuth
from urllib.parse import quote_plus

import nacos
import django
import requests
import pandas as pd
from django.conf import settings
from django.db import connections, connection, transaction
from django.db.models.base import Model
from rest_framework.response import Response
from nacos.exception import NacosException, NacosRequestException

from common.pynacos import NacosClientPlus
from common.pydruid import ConfigTools


def custom_exception_handler(exc, context):
    """
    自定义异常处理函数
    """
    res = {'code': -1, 'msg': str(exc), 'data': None}
    return Response(res)


def parse_jdbc_url(url):
    """
    提取jdbc字符串中的数据库信息
    """
    if url.startswith('jdbc:mysql'):
        match = re.match(r'jdbc:mysql:\/\/([\w\.\-]+):(\d+)\/([\w-]+)\?(.*)', url)
        if not match:
            raise Exception(f'Error parsing MySQL JDBC URL {url}')
        host, port, database, options_string = match.groups()
        return 'django.db.backends.mysql', host, port, database, options_string
    elif url.startswith('jdbc:sqlserver'):
        match = re.match(r'jdbc:sqlserver:\/\/([\w\.\-]+):(\d+);DatabaseName=([\w-]+);(.*)', url)
        if not match:
            raise Exception(f'Error parsing SQL Server JDBC URL {url}')
        host, port, database, options_string = match.groups()
        return 'mssql', host, port, database, options_string
    elif url.startswith('jdbc:postgresql'):
        match = re.match(r'jdbc:postgresql:\/\/([\w\.\-]+):(\d+)\/([\w-]+)\?(.*)', url)
        if not match:
            raise Exception(f'Error parsing PostgreSQL JDBC URL {url}')
        host, port, database, options_string = match.groups()
        return 'django.db.backends.postgresql', host, port, database, options_string
    else:
        raise Exception(f'Error parsing Unknown JDBC URL {url}')


def fetch_dbs_conf_by_nacos(nacos):
    """
    获取nacos数据库配置
    """
    client = NacosClientPlus(
        server_addresses=nacos['SERVER_ADDR'],
        namespace=nacos['NAMESPACE'],
        username=nacos['USERNAME'],
        password=nacos['PASSWORD'],
    )
    logging.info(client)

    conf = client.get_config(nacos['APP_DATA_ID'], nacos['APP_SERVER_GROUP'])
    conf_obj = yaml.safe_load(conf)

    datasource = conf_obj["spring"]["datasource"]["dynamic"]["datasource"]

    config_tools = ConfigTools()
    databases = {}
    for label, db in datasource.items():
        engin, host, port, database, options_string = parse_jdbc_url(db['url'])
        username = db['username']
        password = db['password']

        if 'druid' in db and 'public-key' in db['druid']:
            password = config_tools.decrypt(db['druid']['public-key'], password)
        databases[label] = {
            'ENGINE': engin,
            'NAME': database,
            'HOST': host,
            'PORT': int(port),
            'USER': username,
            'PASSWORD': str(password),
        }
    return databases


def load_nacos_dbs(nacos):
    file_path = os.path.abspath(os.path.join(__file__, '../../etc/nacos_databases.json'))
    if os.path.exists(file_path):
        with open(file_path, 'r') as f:
            return json.load(f)
    data = fetch_dbs_conf_by_nacos(nacos)
    with open(file_path, 'w') as f:
        json.dump(data, f)
    return data


def get_db_engin_url(alias):
    """
    通过数据库别名获取Sqlalchemy create_engin函数url参数
    """
    db = settings.DATABASES[alias]
    if db['ENGINE'] == 'django.db.backends.mysql':
        ENGINE = 'mysql+pymysql'
        PARAMS = '?charset=utf8mb4'
    elif db['ENGINE'] == 'mssql':
        ENGINE = 'mssql+pyodbc'
        PARAMS = '?driver=ODBC+Driver+17+for+SQL+Server'
    elif db['ENGINE'] == 'django.db.backends.postgresql':
        ENGINE = 'postgresql'
        PARAMS = ''
    else:
        raise Exception('Unsupported database.')
    url = f'{ENGINE}://{db["USER"]}:{quote_plus(db["PASSWORD"])}@{db["HOST"]}:{db["PORT"]}/{db["NAME"]}{PARAMS}'
    return url


def sql_to_list(db_alias, sql, params=None) -> list:
    with connections[db_alias].cursor() as cursor:
        cursor.execute(sql, params)
        columns = [column[0] for column in cursor.description]
        results = cursor.fetchall()
    data = [dict(zip(columns, i)) for i in results]
    return data


def sql_to_df(db_alias, sql, params=None) -> pd.DataFrame:
    with connections[db_alias].cursor() as cursor:
        cursor.execute(sql, params)
        columns = [column[0] for column in cursor.description]
        results = cursor.fetchall()
    df = pd.DataFrame(results, columns=columns)
    return df


def get_sys_dict(dict_code) -> dict:
    # 获取系统数据字典
    sql = f"""
    select 
        item_text, item_value 
    from 
        sys_dict as t1 join sys_dict_item as t2 on t1.id=t2.dict_id 
    where 
        dict_code=%s and del_flag=0
    """
    item_list = sql_to_list('master', sql, [dict_code])
    data = {i['item_text']: i['item_value'] for i in item_list}
    return data


def get_sys_dict_value(dict_code, dict_text) -> str:
    sys_dict = get_sys_dict(dict_code)
    return sys_dict.get(dict_text)


def get_sys_dict_text(dict_code, dict_value) -> str:
    sys_dict = get_sys_dict(dict_code)
    return {v: k for k, v in sys_dict.items()}.get(dict_value)


def reset_table_id(table: Union[str, Type[Model]]) -> None:
    """重置表中的 ID 列，并将自增计数器归零"""
    if not isinstance(table, str):
        table = table._meta.db_table

    # 开始事务
    with transaction.atomic():
        # 锁定表
        with connection.cursor() as cursor:
            # cursor.execute(f"LOCK TABLES {table} WRITE;")

            try:
                # 重置ID列和自增计数器
                cursor.execute(f"SET @new_id := 0;")
                cursor.execute(f"UPDATE {table} SET id = (@new_id := @new_id + 1) ORDER BY id;")
                cursor.execute(f"ALTER TABLE {table} AUTO_INCREMENT = 0;")
            finally:
                # 释放锁
                # cursor.execute(f"UNLOCK TABLES;")
                pass

    return None


def fetch_mail_conf_by_nacos(nacos):
    """
    获取nacos邮箱配置
    """
    client = NacosClientPlus(
        server_addresses=nacos['SERVER_ADDR'],
        namespace=nacos['NAMESPACE'],
        username=nacos['USERNAME'],
        password=nacos['PASSWORD'],
    )
    logging.info(client)

    conf = client.get_config(nacos['APP_DATA_ID'], nacos['APP_SERVER_GROUP'])
    conf_obj = yaml.safe_load(conf)

    mail = conf_obj["spring"]["mail"]
    return mail


def load_nacos_mail(nacos):
    file_path = os.path.abspath(os.path.join(__file__, '../../etc/nacos_mail.json'))
    if os.path.exists(file_path):
        with open(file_path, 'r') as f:
            return json.load(f)
    data = fetch_mail_conf_by_nacos(nacos)
    with open(file_path, 'w') as f:
        json.dump(data, f)
    return data


def trigger_dag(dag_id, conf=None):
    """
    通过REST API触发Airflow DAG执行

    参数:
    dag_id (str): 要触发的DAG ID
    conf (dict, optional): 传递给DAG的配置参数，默认为None

    返回:
    dict: API响应结果
    """
    # 构建URL
    url = f"http://{settings.AIRFLOW_ENDPOINT}/api/v1/dags/{dag_id}/dagRuns"

    # 构建请求头，包含Basic认证
    auth_string = f"{settings.AIRFLOW_USERNAME}:{settings.AIRFLOW_PASSWORD}"
    encoded_auth = base64.b64encode(auth_string.encode()).decode()
    headers = {
        "Authorization": f"Basic {encoded_auth}"
    }

    # 获取上海时区
    shanghai_tz = pytz.timezone("Asia/Shanghai")
    # 获取当前时间并转换为上海时区
    timestamp = datetime.datetime.now(shanghai_tz).isoformat(timespec='seconds')
    uid = uuid.uuid4().hex[:8]
    dag_run_id = f"manual__{timestamp}__{uid}"  # 自定义Run ID

    # 构建请求体
    payload = {
        "conf": conf if conf else {},
        "dag_run_id": dag_run_id
    }
    response = requests.post(url, headers=headers, json=payload)
    response.raise_for_status()  # 如果响应状态码不是2xx，抛出异常
    return response.json()


# def activate_conn(connection):
#     if not connection.is_usable():
#         connection.close()
#         connection.connect()
#     return connection
def activate_conn(connection):
    try:
        connection.connect()
        if not connection.is_usable():
            connection.close()
            connection.connect()
    except:
        pass
    return connection


def get_ctcae_results_json_pass_huawei(test_results_list):
    ctcae_results_list = []
    try:
        for item in test_results_list:
            url = 'https://192.168.230.5:24462//v1/1ed40ceefc8d40f8b884edb6a84e7768/applications/fb9731ab-7085-474fb6c7-64473586f0f3/uni-search/experience/searchtext'
            user = 'mauser'
            pwd = 'Prs@123456'
            headers = {
                'content-type': 'application/json'
            }
            body = {
                "repo_id": "db2ec974-d021-494d-9bda-e5f06f224df4",
                "content": '{}'.format(item['检查项目']),
                "page_num": 1,
                "page_size": 1,
                "scope": "faq"
            }
            response = requests.post(url, json=body, headers=headers, auth=HTTPBasicAuth(user, pwd), verify=False)
            res = response.json()
            res['doc_list'][0]['content'] = json.loads(res['doc_list'][0]['content'])
            res['doc_list'][0]['content']['定义'] = res['doc_list'][0]['content']['定义'][3:]
            ctcae_results_list.append(res['doc_list'][0]['content'])
    except:
        pass
    return ctcae_results_list


def get_ctcae_results_json(test_results_list):
    ctcae_results_list = []
    try:
        url = settings.USE_MEDS_URL
        api_key = settings.AE_API_KEY
        for item in test_results_list:
            headers = {
                'content-type': 'application/json',
                'Authorization': api_key
            }

            body = {
                "inputs": {},
                "query": '{}'.format(item['检查项目']),
                "response_mode": "blocking",
                "conversation_id": "",
                "user": "abc-123"
            }

            response = requests.post(url, json=body, headers=headers, verify=False)
            res = response.json()
            res['answer'] = json.loads(res['answer'])
            res['answer']['定义'] = res['answer']['定义'][3:]
            ctcae_results_list.append(res['answer'])
    except:
        pass
    return ctcae_results_list


def get_use_meds_results_json_pass(test_results_list, project_no):
    """
    用药rag
    """
    ctcae_results_list = []
    try:
        url = settings.USE_MEDS_URL
        api_key = settings.MEDS_API_KEY
        for item in test_results_list:
            headers = {
                'content-type': 'application/json',
                'Authorization': api_key
            }

            body = {
                "inputs": {"project_no": project_no},
                "query": '{}'.format(item['test_name']),
                "response_mode": "blocking",
                "conversation_id": "",
                "user": "abc-123"
            }
            answer = None
            try:
                response = requests.post(url, json=body, headers=headers, verify=False)
                res = response.json()
                match = re.search(r'(?<=</think>\n\n).*', res['answer'], re.DOTALL)
                if match:
                    answer = match.group()
            except:
                pass
            item['ae_meds'] = answer
    except:
        pass
    return test_results_list


def get_use_meds_results_json_pass1(test_results_list, suggested_medication_list):
    """
    用药工程化
    """
    try:
        for test_result in test_results_list:
            ae_name = str(test_result['ae_name'])
            ae_grade = str(test_result['ae_grade'])
            matched_measures = []

            # 条件1: 两个字段都相等
            for med in suggested_medication_list:
                if (str(med['adverse_reactions']) == ae_name and
                        str(med['ae_grade']) == ae_grade):
                    test_result['ae_meds'] = med['handling_measures']
                    break
            else:
                # 条件2: 只匹配ae_name
                for med in suggested_medication_list:
                    if str(med['adverse_reactions']) == ae_name:
                        matched_measures.append(med['handling_measures'])

                if matched_measures:
                    test_result['ae_meds'] = '; '.join(matched_measures)
                else:
                    # 条件3: 未匹配到任何结果
                    test_result['ae_meds'] = None
    except:
        pass
    return test_results_list


def get_use_meds_results_json(data_list, project_no):
    """
    用药5.0
    """
    for data in data_list:
        ae_name = data.get('ae_name')
        ae_grade = data.get('ae_grade')
        try:
            # 查询project_plan_ae_drug表
            drug_query = """
                        SELECT id FROM project_plan_ae_drug 
                        WHERE project_no = %s AND ae_name = %s AND ae_grade = %s AND delete_flag = 0
                        """
            with connections['default'].cursor() as cursor:
                cursor.execute(drug_query, (project_no, ae_name, ae_grade))
                drug_result = cursor.fetchone()
                if drug_result:
                    # 查询project_plan_ae_drug_detail表
                    detail_query = """
                                SELECT drug_name, measure_name, measure_detail 
                                FROM project_plan_ae_drug_detail 
                                WHERE project_plan_ae_drug_id = %s AND delete_flag = 0
                                """
                    cursor.execute(detail_query, drug_result[0])
                    detail_results = cursor.fetchall()
                    # 拼接结果
                    if detail_results:
                        ae_meds_list = []
                        for detail in detail_results:
                            drug_name = detail[0]
                            measure_name = detail[1]
                            measure_detail = detail[2]
                            ae_meds_list.append(f"{drug_name}：{measure_name}，{measure_detail}")
                        data['ae_meds'] = "\n".join(ae_meds_list)
                    else:
                        data['ae_meds'] = '未匹配到用药，请研究者确定！'
                else:
                    data['ae_meds'] = '未匹配到用药，请研究者确定！'
        except:
            data['ae_meds'] = '未匹配到用药，请研究者确定！'
    return data_list


def get_use_meds_results_json_v7(data_list, project_no):
    """
    用药7.0
    """
    for data in data_list:
        ae_name = data.get('ae_name')
        ae_grade = data.get('ae_grade')
        try:
            # ae_medication_measures_list = []
            ae_medication_measures = {}
            # 查询project_plan_ae_drug表
            drug_query = """
                        SELECT ae_name, ae_grade, drug_name, measure_name FROM project_plan_ae_drug 
                        WHERE project_no = %s AND delete_flag = 0
                        """  # AND ae_name = %s AND ae_grade = %s
            with connections['default'].cursor() as cursor:
                cursor.execute(drug_query, (project_no,))
                detail_results = cursor.fetchall()
                print(data)
                print(detail_results)
                if not detail_results:  # 没有配置：场景1
                    print('############没有配置：场景1')
                    ae_medication_measures['ae_name'] = ae_name
                    ae_medication_measures['ae_grade'] = ae_grade
                    ae_medication_measures['drug_type'] = 1
                    ae_medication_measures['drugs'] = [{
                        "drug_name": None,
                        "measure_name_list": [
                            {"id": 1, "measure_name": "方案未提及"}
                        ],
                        "no_adjustment_needed": 0,
                        "reason": None,
                        "id": 1
                    }]
                    data['ae_meds'] = '方案未提及'
                    data['ae_medication_measures_list'] = ae_medication_measures
                    # ae_meds_list = []
                    # for detail in detail_results:
                    #     drug_name = detail[0]
                    #     measure_name = detail[1]
                    #     ae_meds_list.append(f"{drug_name}：{measure_name}，{measure_detail}")
                    # data['ae_meds'] = "\n".join(ae_meds_list)
                elif not any(record[0] == ae_name for record in detail_results):  # 存在记录但没有匹配的ae_name
                    print('########################### 场景2：存在记录但没有匹配的ae_name')
                    ae_medication_measures['ae_name'] = ae_name
                    ae_medication_measures['ae_grade'] = ae_grade
                    ae_medication_measures['drug_type'] = 2
                    ae_medication_measures['drugs'] = [{
                        "drug_name": None,
                        "measure_name_list": [
                            {"id": 1, "measure_name": "方案未提及"}
                        ],
                        "no_adjustment_needed": 0,
                        "reason": None,
                        "id": 1
                    }]
                    data['ae_meds'] = '方案未提及'
                    data['ae_medication_measures_list'] = ae_medication_measures
                # 场景3：存在匹配的ae_name和ae_grade，但所有drug_name都为空
                elif [record for record in detail_results if record[0] == ae_name and int(record[1]) == int(ae_grade)] \
                        and all(record[2] is None for record in [record for record in detail_results
                                                                 if record[0] == ae_name and int(record[1]) == int(ae_grade)]):
                    print('###################################场景3：存在匹配的ae_name和ae_grade，但所有drug_name都为空')
                    ae_medication_measures['ae_name'] = ae_name
                    ae_medication_measures['ae_grade'] = ae_grade
                    ae_medication_measures['drug_type'] = 3  # 假设这是第三种情况的drug_type
                    measure_names = [[record[1], record[3]] for record in
                                     detail_results
                                     if record[0] == ae_name and int(record[1]) == int(ae_grade)]
                    ae_medication_measures['drugs'] = [{
                        "drug_name": None,
                        "measure_name_list": [
                            {"id": 1, "measure_name": f"{measure_names[0][0]}级：{measure_names[0][1]}"}  # 自定义消息
                        ],
                        "no_adjustment_needed": 0,
                        "reason": None,
                        "id": 1
                    }]
                    data['ae_meds'] = f"{measure_names[0][0]}级：{measure_names[0][1]}"
                    data['ae_medication_measures_list'] = ae_medication_measures
                # 场景4：ae_name存在，ae_grade存在
                elif any(record[0] == ae_name and int(record[1]) == int(ae_grade) for record in detail_results):
                    print('#########################################场景4：ae_name存在，ae_grade存在')
                    ae_medication_measures['ae_name'] = ae_name
                    ae_medication_measures['ae_grade'] = ae_grade
                    ae_medication_measures['drug_type'] = 4  # 第4种情况的drug_type

                    measure_names = [[record[1], record[2], record[3]] for record in detail_results
                                     if
                                     record[0] == ae_name and int(record[1]) == int(ae_grade) and record[2] is not None]

                    # 按药物名称分组
                    drug_groups = {}
                    for grade, drug_name, measure in measure_names:
                        # 如果药物不在分组中，初始化该组并设置计数器为 1
                        if drug_name not in drug_groups:
                            drug_groups[drug_name] = []
                            current_id = 1
                        else:
                            # 获取当前药物组的最后一个 id 并加 1
                            current_id = drug_groups[drug_name][-1]["id"] + 1
                        # 添加当前药物的剂量信息
                        drug_groups[drug_name].append({"id": current_id, "measure_name": f"{grade}级：{measure}"})
                    # 构建药物列表
                    drugs = []
                    for drug_name, measures in drug_groups.items():
                        drugs.append({
                            "drug_name": drug_name,
                            "measure_name_list": measures,
                            "no_adjustment_needed": 0,
                            "reason": None,
                            "id": 1
                        })
                    ae_medication_measures['drugs'] = drugs
                    data['ae_meds'] = "\n".join(
                        [f"{drug_name}：{ae_grade}级：{measure}" for ae_grade, drug_name, measure in measure_names])
                    data['ae_medication_measures_list'] = ae_medication_measures
                # 场景5：ae_name存在，但ae_grade不存在且所有drug_name都为空
                elif (any(record[0] == ae_name for record in detail_results) and
                      not any(record[0] == ae_name and int(record[1]) == int(ae_grade) for record in detail_results) and
                      all(record[2] is None for record in detail_results if record[0] == ae_name)):
                    print('################################### 场景5：ae_name存在，但ae_grade不存在且所有drug_name都为空')
                    ae_medication_measures['ae_name'] = ae_name
                    ae_medication_measures['ae_grade'] = ae_grade
                    ae_medication_measures['drug_type'] = 5  # 第5种情况的drug_type
                    measure_names = [[record[1], record[2], record[3]] for record in detail_results if
                                     record[0] == ae_name]  # and int(record[1]) == int(ae_grade)
                    # 构建措施列表，包含等级信息
                    measure_list = [
                        {"id": i, "measure_name": f"{grade}级：{measure}"}
                        for i, (grade, drug_name, measure) in enumerate(measure_names, start=1)
                    ]
                    ae_medication_measures['drugs'] = [{
                        "drug_name": None,
                        "measure_name_list": measure_list,
                        "no_adjustment_needed": 0,
                        "reason": None,  # 自定义原因
                        "id": None
                    }]
                    data['ae_meds'] = "\n".join([f"{grade}级：{measure}" for grade, drug_name, measure in measure_names])
                    data['ae_medication_measures_list'] = ae_medication_measures
                # 场景6：ae_name存在，但ae_grade不存在
                elif (any(record[0] == ae_name for record in detail_results) and
                      not any(record[0] == ae_name and int(record[1]) == int(ae_grade) for record in detail_results)):
                    print('#################################### 场景6：ae_name存在，但ae_grade不存在')
                    measure_names = [[record[1], record[2], record[3]] for record in detail_results if
                                     record[0] == ae_name and record[2] is not None]
                    ae_medication_measures['ae_name'] = ae_name
                    ae_medication_measures['ae_grade'] = ae_grade
                    ae_medication_measures['drug_type'] = 6  # 第6种情况的drug_type
                    # 按药物名称分组
                    drug_groups = {}
                    for grade, drug_name, measure in measure_names:
                        # 如果药物不在分组中，初始化该组并设置计数器为 1
                        if drug_name not in drug_groups:
                            drug_groups[drug_name] = []
                            current_id = 1
                        else:
                            # 获取当前药物组的最后一个 id 并加 1
                            current_id = drug_groups[drug_name][-1]["id"] + 1
                        # 添加当前药物的剂量信息
                        drug_groups[drug_name].append({"id": current_id, "measure_name": f"{grade}级：{measure}"})
                    # 构建药物列表
                    drugs = []
                    for drug_name, measures in drug_groups.items():
                        drugs.append({
                            "drug_name": drug_name,
                            "measure_name_list": measures,
                            "no_adjustment_needed": 0,
                            "reason": None,
                            "id": None
                        })
                    ae_medication_measures['drugs'] = drugs
                    data['ae_meds'] = "\n".join(
                        [f"{drug_name}：{ae_grade}级：{measure}" for ae_grade, drug_name, measure in measure_names])
                    data['ae_medication_measures_list'] = ae_medication_measures
                else:
                    # ae_medication_measures['ae_name'] = ae_name
                    # ae_medication_measures['ae_grade'] = ae_grade
                    # ae_medication_measures['drug_type'] = 7
                    # ae_medication_measures['drugs'] = [{
                    #     "drug_name": None,
                    #     "measure_name_list": [
                    #         {"is_select": 1, "measure_name": "方案未提及"}
                    #     ],
                    #     "no_adjustment_needed": 1,
                    #     "reason": None
                    # }]
                    # data['ae_meds'] = '方案未提及'
                    # data['ae_medication_measures_list'] = [ae_medication_measures]
                    data['ae_meds'] = '/error!'
                    data['ae_medication_measures_list'] = None
        except Exception as e:
            print(e)
            data['ae_meds'] = '/error!'
            data['ae_medication_measures_list'] = None
    return data_list


def get_ae_name_change_json(test_results_list):
    """
    为工程化用药:肚子疼 变 腹痛
    """
    try:
        url = settings.USE_MEDS_URL
        api_key = settings.AE_API_KEY
        for item in test_results_list:

            headers = {
                'content-type': 'application/json',
                'Authorization': api_key
            }

            body = {
                "inputs": {},
                "query": '{}'.format(item['ae_name']),
                "response_mode": "blocking",
                "conversation_id": "",
                "user": "abc-123"
            }
            try:
                response = requests.post(url, json=body, headers=headers, verify=False)
                res = response.json()
                res['answer'] = json.loads(res['answer'])
                temp = res['answer']['不良事件']
                item['ae_name'] = temp
            except:
                pass
    except:
        pass
    return test_results_list



# def word_binary_to_image_binaries(word_binary):
#     images_binary_list = []
#
#     # 创建临时文件
#     with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as temp_docx:
#         temp_docx.write(word_binary)
#         temp_docx_path = temp_docx.name
#
#     try:
#         # 转换为 PDF
#         temp_pdf_path = temp_docx_path.replace('.docx', '.pdf')
#         convert(temp_docx_path, temp_pdf_path)
#
#         # 读取 PDF 并转换为图片
#         doc = fitz.open(temp_pdf_path)
#
#         for page_num in range(len(doc)):
#             page = doc.load_page(page_num)
#             pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))  # 2x 缩放提高质量
#
#             # 转换为二进制
#             img_binary = pix.tobytes("png")
#             images_binary_list.append(img_binary)
#
#         doc.close()
#
#     finally:
#         # 清理临时文件
#         if os.path.exists(temp_docx_path):
#             os.remove(temp_docx_path)
#         if os.path.exists(temp_pdf_path):
#             os.remove(temp_pdf_path)
#
#     return images_binary_list


# def word_binary_to_image_binaries(bytes, file_extension):
#     images_binary_list = []  # 存储每页的二进制数据
#
#     unique_dir_name = str(uuid.uuid4())
#     temp_dir = os.path.join(os.getcwd(), unique_dir_name)
#     os.makedirs(temp_dir)
#
#     try:
#         # 创建临时Word文件
#         temp_word_path = os.path.join(temp_dir, f'temp{file_extension}')
#         with open(temp_word_path, 'wb') as f:
#             f.write(bytes)
#
#         # 生成输出PDF文件路径
#         output_pdf_path = os.path.splitext(temp_word_path)[0] + '.pdf'
#
#         # 调用libreoffice进行转换
#         subprocess.run(
#             [
#                 'libreoffice',
#                 '--headless',
#                 '--convert-to',
#                 'pdf',
#                 '--outdir',
#                 os.path.abspath(os.path.dirname(temp_word_path)),
#                 temp_word_path
#             ],
#             check=True
#         )
#
#         # 读取PDF文件内容
#         with open(output_pdf_path, 'rb') as f:
#             pdf_bytes = f.read()
#
#         # 将PDF的每一页转换为图像对象
#         images = convert_from_bytes(pdf_bytes)
#
#         # 将每个图像对象转换为二进制流
#         for image in images:
#             img_byte_arr = io.BytesIO()
#             image.save(img_byte_arr, format='JPEG')  # 可改为PNG等其他格式
#             img_byte_arr.seek(0)  # 重置指针到开头
#             images_binary_list.append(img_byte_arr)
#
#     finally:
#         # 删除临时目录
#         shutil.rmtree(temp_dir)
#
#     return images_binary_list  # 返回二进制流列表


def word_to_image_binary_list(word_file, original_filename):
    """
    将Word文档转换为图片二进制列表

    Args:
        word_file: Word文件路径或二进制数据

    Returns:
        List[bytes]: 图片二进制数据列表
    """
    image_binaries = []

    with tempfile.TemporaryDirectory() as temp_dir:
        temp_dir_path = Path(temp_dir)

        # 生成唯一标识符
        unique_id = str(uuid.uuid4())
        user_config_dir = temp_dir_path / f"libreoffice_config_{unique_id}"
        # user_config_dir.mkdir()
        user_config_dir.mkdir(parents=True, exist_ok=True)
        # 处理输入文件
        if isinstance(word_file, bytes):
            # 如果是二进制数据，保存为临时文件
            input_file = temp_dir_path / f"input_{unique_id}{original_filename}"
            input_file.write_bytes(word_file)
        else:
            # 如果是文件路径
            input_file = Path(word_file)
            if not input_file.exists():
                raise FileNotFoundError(f"Word文件不存在: {word_file}")

        # 转换为PDF
        pdf_file = temp_dir_path / f"converted_{unique_id}.pdf"
        env = os.environ.copy()  # 继承当前环境变量
        env["UserInstallation"] = f"file://{user_config_dir}"
        # user_config_dir = temp_dir_path / f"libreoffice_config_{uuid.uuid4()}"
        cmd = [
            'libreoffice',
            # f'--env:UserInstallation=file://{user_config_dir}',  # 使用独立配置目录
            '--headless',
            '--convert-to', 'pdf',
            '--outdir', str(temp_dir_path),
            str(input_file)
        ]

        try:
            result = subprocess.run(cmd, env=env, capture_output=True, text=True, timeout=300)
            if result.returncode != 0:
                raise Exception(f"LibreOffice转换失败: {result.stderr}")
        except subprocess.TimeoutExpired:
            raise Exception("LibreOffice转换超时")

        # 重命名PDF文件
        generated_pdf = temp_dir_path / f"{input_file.stem}.pdf"
        if generated_pdf.exists():
            generated_pdf.rename(pdf_file)
        elif not pdf_file.exists():
            raise Exception("PDF文件生成失败")

        # 使用PyMuPDF将PDF转换为图片
        pdf_doc = fitz.open(str(pdf_file))

        for page_num in range(pdf_doc.page_count):
            page = pdf_doc.load_page(page_num)

            # 设置缩放比例以控制分辨率
            zoom = 144.0 / 72.0
            mat = fitz.Matrix(zoom, zoom)

            # 渲染页面为图片
            pix = page.get_pixmap(matrix=mat)

            # 转换为PIL Image
            img_data = pix.tobytes("png")

            image_binaries.append(img_data)

        pdf_doc.close()

    return image_binaries


def images_to_word_stream(image_objects):
    # 创建新的Word文档
    doc = Document()

    # 遍历图片对象列表
    for img_obj in image_objects:
        # 假设image_objects是PIL Image对象或图片路径
        if isinstance(img_obj, str):  # 如果是文件路径
            doc.add_picture(img_obj, width=Inches(6))
        else:  # 如果是PIL Image对象
            img_buffer = BytesIO()
            img_obj.save(img_buffer, format='PNG')
            img_buffer.seek(0)
            doc.add_picture(img_buffer, width=Inches(6))

        # 添加分页符（可选）
        doc.add_page_break()

    # 保存到内存流
    doc_stream = BytesIO()
    doc.save(doc_stream)
    doc_stream.seek(0)

    return doc_stream


def collect_time_choice(date_value, data):
    """
    检验单 检查单 结构化提取时间
    """
    if date_value is None or date_value == '':
        if data[0]['subject_visit__visit_date']:
            date_value = data[0]['subject_visit__visit_date'].get('cmpl') or \
                         data[0]['subject_visit__visit_date'].get('st') or \
                         data[0]['subject_visit__visit_date'].get('winB')
        else:
            date_value = None

    if data[0]['item_id'] in ['3', '4'] and date_value:
        # 精确到分钟
        if isinstance(date_value, str):
            if ' ' in date_value:
                # 包含时间部分，可能为 YYYY-MM-DD HH:MM 或 YYYY-MM-DD HH:MM:SS
                date_part, time_part = date_value.split(' ', 1)
                if time_part.count(':') >= 2:
                    # 格式为 YYYY-MM-DD HH:MM:SS，只取到分钟
                    hour_minute = ':'.join(time_part.split(':')[:2])
                    collect_time = f"{date_part} {hour_minute}"
                else:
                    # 格式已经是 YYYY-MM-DD HH:MM
                    collect_time = date_value
            else:
                # 只有日期部分 YYYY-MM-DD
                collect_time = date_value
        else:
            # 如果是日期对象，格式化到分钟
            collect_time = date_value.strftime('%Y-%m-%d %H:%M')
    else:
        # 精确到天
        if isinstance(date_value, str):
            # 如果是字符串，只取日期部分
            collect_time = date_value.split(' ')[0] if ' ' in date_value else date_value
        else:
            # 如果是日期对象，格式化到天
            collect_time = date_value.strftime('%Y-%m-%d') if date_value else None
    return collect_time


if __name__ == "__main__":
    # python -m common.tools
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smo.settings.test')
    django.setup()
    # from apps.board.models import SmoManageDataEmployeeInfo
    # reset_table_id(SmoManageDataEmployeeInfo)
    # conf = {
    #     "filename": "123.txt",
    #     "command": "echo",
    #     "message": "这是通过API传递的自定义消息",
    #     "output_dir": "/tmp/airflow_api_output",
    #     "timeout": "120",
    #     "environment": "production",
    #     "notify": "true"
    # }
    # print(trigger_dag('crf_generation', {'task_id': 12345}))
