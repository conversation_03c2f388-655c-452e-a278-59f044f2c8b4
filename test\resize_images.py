"""
递归处理嵌套文件夹中的图片：
1. 如果图片宽度<=2000像素且大小<=16MB，直接复制
2. 如果图片宽度>2000像素，调整大小
3. 如果图片大小>16MB，压缩
4. 所有处理后的图片保存为PNG格式
5. 支持HEIC格式图片
"""

import os
import sys
import shutil
from PIL import Image
import glob
from pathlib import Path
import io

# 检查是否安装了HEIC支持
try:
    from pillow_heif import register_heif_opener
    register_heif_opener()
    HEIC_SUPPORT = True
except ImportError:
    HEIC_SUPPORT = False


def process_images_recursive(input_folder, output_folder, max_width=2000, max_file_size=16 * 1024 * 1024):
    """
    递归处理嵌套文件夹中的图片：
    1. 如果图片宽度<=2000像素且大小<=16MB，直接复制
    2. 如果图片宽度>2000像素，调整大小
    3. 如果图片大小>16MB，压缩
    4. 所有处理后的图片保存为PNG格式
    5. 支持HEIC格式图片

    参数:
        input_folder: 输入文件夹路径
        output_folder: 输出文件夹路径
        max_width: 最大宽度(像素)
        max_file_size: 最大文件大小(字节)
    """
    # 检查HEIC支持
    if not HEIC_SUPPORT:
        print("警告: 未安装HEIC支持库。要处理HEIC文件，请安装pillow-heif：")
        print("pip install pillow-heif")
        print("继续处理其他格式图片...")

    # 确保输出文件夹存在
    os.makedirs(output_folder, exist_ok=True)

    # 支持的图片格式
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff', '*.gif', '*.webp']
    if HEIC_SUPPORT:
        image_extensions.extend(['*.heic', '*.heif'])

    # 处理计数器
    processed_count = 0
    copied_count = 0
    resized_count = 0
    compressed_count = 0
    errors_count = 0

    # 递归处理所有子文件夹
    for root, dirs, files in os.walk(input_folder):
        # 计算相对路径，用于创建对应的输出目录
        rel_path = os.path.relpath(root, input_folder)
        current_output_dir = os.path.join(output_folder, rel_path) if rel_path != '.' else output_folder

        # 确保当前输出目录存在
        os.makedirs(current_output_dir, exist_ok=True)

        # 处理当前目录中的所有图片文件
        image_files = []
        for ext in image_extensions:
            image_files.extend(glob.glob(os.path.join(root, ext)))
            image_files.extend(glob.glob(os.path.join(root, ext.upper())))

        for img_path in image_files:
            try:
                # 设置相对路径的输出文件路径
                rel_img_path = os.path.relpath(img_path, root)
                # 更改扩展名为.png
                output_filename = os.path.splitext(rel_img_path)[0] + '.png'
                output_path = os.path.join(current_output_dir, output_filename)

                # 获取原始文件大小
                original_file_size = os.path.getsize(img_path)

                # 检查是否需要处理
                needs_resize = False
                needs_compress = False

                # 检查文件是否为HEIC格式
                is_heic = img_path.lower().endswith(('.heic', '.heif'))

                # 处理HEIC格式的特殊情况
                if is_heic and not HEIC_SUPPORT:
                    print(f"跳过HEIC文件 {os.path.basename(img_path)}，因为未安装HEIC支持库")
                    errors_count += 1
                    continue

                # 打开图片并检查尺寸
                with Image.open(img_path) as img:
                    original_width, original_height = img.size

                    if original_width > max_width:
                        needs_resize = True

                    if original_file_size > max_file_size:
                        needs_compress = True

                    # 如果不需要处理，直接转换为PNG并复制
                    if not needs_resize and not needs_compress:
                        # 转换为PNG格式
                        if img.mode == 'RGBA':
                            img.save(output_path, format='PNG')
                        else:
                            img.convert('RGBA').save(output_path, format='PNG')

                        copied_count += 1
                        print(
                            f"直接转换到PNG: {os.path.basename(img_path)} -> {os.path.basename(output_path)} (尺寸: {original_width}x{original_height}, 大小: {original_file_size/1024/1024:.2f}MB)")
                        processed_count += 1
                        continue

                    # 需要调整尺寸
                    if needs_resize:
                        # 保持宽高比
                        ratio = max_width / original_width
                        new_width = max_width
                        new_height = int(original_height * ratio)
                        # 调整大小
                        img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                        resized_count += 1
                        print(f"调整 {os.path.basename(img_path)} 从 {original_width}x{original_height} 到 {new_width}x{new_height}")

                    # 需要压缩
                    if needs_compress:
                        # 临时保存路径
                        temp_path = output_path + ".temp"
                        compression_level = 9  # PNG压缩级别，9为最高压缩率
                        quality = 100  # 开始尝试的质量

                        while quality > 30:  # PNG格式最低质量限制
                            try:
                                # 尝试保存为PNG
                                if img.mode == 'RGBA':
                                    img.save(temp_path, format='PNG', compress_level=compression_level, quality=quality)
                                else:
                                    img.convert('RGBA').save(temp_path, format='PNG',
                                                             compress_level=compression_level, quality=quality)

                                # 检查文件大小
                                file_size = os.path.getsize(temp_path)

                                if file_size <= max_file_size:
                                    # 大小满足要求，重命名为最终文件
                                    os.replace(temp_path, output_path)
                                    compressed_count += 1
                                    print(f"成功压缩 {os.path.basename(img_path)} 到 {file_size/1024/1024:.2f}MB (质量: {quality})")
                                    break
                                else:
                                    # 大小不满足要求，删除临时文件并降低质量
                                    os.remove(temp_path)
                                    quality -= 10
                                    print(f"降低PNG质量到 {quality} 继续尝试压缩")
                            except Exception as e:
                                if os.path.exists(temp_path):
                                    os.remove(temp_path)
                                print(f"在质量 {quality} 保存PNG时出错: {e}")
                                quality -= 10

                        # 如果使用PNG无法达到目标大小，尝试其他方案
                        if quality <= 30 and not os.path.exists(output_path):
                            print(f"PNG压缩无法达到目标大小，尝试其他方案")

                            # 最低PNG尝试
                            try:
                                if img.mode == 'RGBA':
                                    img.save(output_path, format='PNG', compress_level=9, quality=30)
                                else:
                                    img.convert('RGBA').save(output_path, format='PNG', compress_level=9, quality=30)

                                compressed_size = os.path.getsize(output_path) / 1024 / 1024
                                print(f"最终大小(PNG): {compressed_size:.2f}MB (使用最低质量设置)")
                            except Exception as e:
                                print(f"最终PNG保存失败: {e}")

                                # 如果PNG仍然失败，尝试更多降低分辨率来达到目标
                                try:
                                    # 更进一步降低分辨率
                                    scale_factor = 0.8
                                    new_width = int(img.width * scale_factor)
                                    new_height = int(img.height * scale_factor)
                                    img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)

                                    # 再次尝试保存为PNG
                                    if img.mode == 'RGBA':
                                        img.save(output_path, format='PNG', compress_level=9)
                                    else:
                                        img.convert('RGBA').save(output_path, format='PNG', compress_level=9)

                                    compressed_size = os.path.getsize(output_path) / 1024 / 1024
                                    print(f"通过进一步降低分辨率到 {new_width}x{new_height}，最终大小: {compressed_size:.2f}MB")
                                except Exception as e:
                                    print(f"尝试降低分辨率也失败: {e}")
                                    # 最后尝试
                                    img.convert('RGB').save(output_path, format='PNG')
                    else:
                        # 只需调整尺寸但不需压缩，保存为PNG
                        if img.mode == 'RGBA':
                            img.save(output_path, format='PNG')
                        else:
                            img.convert('RGBA').save(output_path, format='PNG')
                        print(f"调整尺寸后保存为PNG: {os.path.basename(output_path)}")

                processed_count += 1

            except Exception as e:
                print(f"处理 {img_path} 时出错: {e}")
                errors_count += 1

    return {
        'processed': processed_count,
        'copied': copied_count,
        'resized': resized_count,
        'compressed': compressed_count,
        'errors': errors_count
    }


if __name__ == "__main__":
    # 检查命令行参数
    if len(sys.argv) != 3:
        print("用法: python process_images_recursive.py <输入文件夹路径> <输出文件夹路径>")
        print("例如: python process_images_recursive.py ./input_images ./output_images")
    else:
        input_folder = sys.argv[1]
        output_folder = sys.argv[2]

        if not os.path.isdir(input_folder):
            print(f"错误: 输入文件夹 '{input_folder}' 不存在")
        else:
            print(f"开始递归处理文件夹: {input_folder}")
            print(f"HEIC支持状态: {'已启用' if HEIC_SUPPORT else '未启用，请安装pillow-heif库'}")
            stats = process_images_recursive(input_folder, output_folder)
            print("\n========= 处理完成 =========")
            print(f"总共处理: {stats['processed']} 张图片")
            print(f"直接复制/转换: {stats['copied']} 张图片")
            print(f"调整尺寸: {stats['resized']} 张图片")
            print(f"压缩大小: {stats['compressed']} 张图片")
            print(f"处理失败: {stats['errors']} 张图片")
            print("===========================")
