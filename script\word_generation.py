""" 类  方法"""
import os
import django
import argparse

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smo.settings.test')
django.setup()

import os, sys

llm_tools_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '../PDF_llm_Excel/')
# print(llm_tools_path)
sys.path.insert(0, llm_tools_path)

import shutil
import datetime
import random
import time
import logging
import mimetypes
import uuid
import hashlib
import pymysql
from django.db import connection
from django.conf import settings
from apps.medical_collection.models import MedicalCollectionTask, MedicalCollectionFile
from apps.medical.models import MedicalInfoLite, MedicalFile
from apps.subject.models import Subject
from apps.project.models import ProjectMaterialInfo, ProjectMaterialFile
from common.minio_client import get_minio_client, download_file_from_minio, upload_file_to_minio
# from common.compare2llms_retrospective_study.main import main as llm_process_main  # 调用大模型
from common.word_llm_processor import generate_word_file  # 大模型处理 CRF 任务
from django.db import transaction, DatabaseError


# 初始化 MinIO 客户端
client = get_minio_client()

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 获取项目根目录路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

def get_epoch_id_by_subject_visit(subject_visit_id):
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT t2.label
            FROM subject_visit_info AS t1
            JOIN subject_epoch_info AS t2 ON t1.subject_epoch_id = t2.subject_epoch_id
            WHERE t1.subject_visit_id = %s AND t2.delete_flag = 0;
        """, [subject_visit_id])
        
        result = cursor.fetchone()

    return result[0] if result else None
def activate_conn(connection):
    try:
        connection.connect()
        if not connection.is_usable():
            connection.close()
            connection.connect()
    except:
        pass
    return connection
def create_file():
    temp_name = f"{uuid.uuid4().hex}"
    temp_folder = os.path.join(project_root, temp_name)  # 生成唯一临时目录名称
    # medical_file_folder = os.path.join(temp_folder, 'medical_files')
    template_file_folder = os.path.join(temp_folder, 'template_files')
    retrospective_study_path = os.path.join(temp_folder, 'retrospective_study_results')
    retrospective_study_file = os.path.join(temp_folder, 'retrospective_study_results',
                                            'llm_output.docx')

    # os.makedirs(medical_file_folder, exist_ok=True)
    os.makedirs(template_file_folder, exist_ok=True)
    os.makedirs(retrospective_study_path, exist_ok=True)

    return {
        # 'medical_file_folder': medical_file_folder,
        'template_file_folder': template_file_folder,
        # 'retrospective_study_path': retrospective_study_path,
        'retrospective_study_file': retrospective_study_file,
        'temp_folder': temp_folder,
    }


# def connect_db():
#     if not connection.is_usable():
#         connection.close()
#         connection.connect()


class RetrospectiveStudyTaskProcessor:
    client = client
    logger = logger

    def __init__(self, template_file_folder, retrospective_study_file, temp_folder):
        # self.client = client
        # self.__class__.logger = logger
        # self.medical_file_folder = medical_file_folder
        self.template_file_folder = template_file_folder
        self.retrospective_study_file = retrospective_study_file
        self.temp_folder = temp_folder

    def get_total_page_count(self, task):
        """获取任务中所有文件的总页数"""
        medical_info_lites = MedicalInfoLite.objects.filter(
            subject_id=task.subject_id,
            subject_visit_id=task.subject_visit_id,
            delete_flag=0,
            ocr_status='COMPLETED'
        ).exclude(subject_item__item_id='2')
        
        if not medical_info_lites:
            return 0
        
        total_page_count = 0
        seen_hashes = set()
        
        for medical_info_lite in medical_info_lites:
            medical_file = MedicalFile.objects.filter(id=medical_info_lite.file_id, delete_flag=0).first()
            if not medical_file:
                self.__class__.logger.warning(f"未找到 file_id={medical_info_lite.file_id} 的 MedicalFile")
                continue
            if medical_file.hash in seen_hashes:
                continue

            # 添加 hash 到已处理集合中
            seen_hashes.add(medical_file.hash)
            
            # 累加页数
            page_count = getattr(medical_info_lite, 'page_count', 0) or 0
            total_page_count += page_count
        
        return total_page_count


    def retrospective_study_task_consumer(self, task_id, model_name):
        """扫描任务并处理"""

        try:
            # 每次只取一个待处理的任务1
            task = MedicalCollectionTask.objects.filter(category='MEDICAL_RECORD', id=task_id).first()
            if model_name == 'qwen3':
                import time
                time.sleep(5)
                task = MedicalCollectionTask.objects.filter(category='MEDICAL_RECORD', id=task_id).first()

            if not task:
                print(f"没有发现任务")
                return

            # with transaction.atomic():
                # 尝试更新任务状态，使用乐观锁
            rows_updated = MedicalCollectionTask.objects.filter(
                id=task.id,
                data_version=task.data_version
            ).update(
                status='IN_PROGRESS',
                data_version=task.data_version + 1  # 增加 data_version
            )

            if rows_updated == 1:
                activate_conn(connection)
                self.__class__.logger.info(f"当前任务为：{task.category}，当前状态为：{task.status}，准备下载文件...")
                ocr_text, template_local_file = self.get_medical_template_file(task)
                self.template_local_file = template_local_file
                if task.status == 'ERROR':
                    return
                self.__class__.logger.info(f"当前任务为：{task.category}，当前状态为：{task.status}，正在处理文件...")
                self.llm_deal(task, ocr_text, template_local_file, task_id, model_name)
                if task.status == 'ERROR':
                    return
                self.__class__.logger.info(f"✅ 【回顾性研究-{task.category}生成任务已经完成】！")
                self.__class__.logger.info(f"✅ 临时文件夹中的所有文件已清理完成！")
        except Exception as e:
            print(f"任务执行失败: {e}")

        finally:
            try:
                # 删除临时文件中的所有文件
                self.clear_dir()
            except:
                pass

    def clear_dir(self):
        try:
            # 检查是否为文件夹
            if os.path.isdir(self.temp_folder):
                print(f"正在清理文件夹: {self.temp_folder}")
                shutil.rmtree(self.temp_folder)
                print(f"文件夹 {self.temp_folder} 已全部删除。")
        except Exception as e:
            self.__class__.logger.error(f"❌ Error occurred during directory deletion: {e}")
            raise

    def get_medical_template_file(self, task):
        """下载 CRF 文件和病历文件"""
        try:
            ocr_text, template_local_file = self.download_medical_template_files(task)
            self.__class__.logger.info(f"✅ 获取病历和模板文件成功")
            return ocr_text, template_local_file
        except Exception as e:
            activate_conn(connection)
            task.status = 'ERROR'
            task.save()
            self.__class__.logger.error(f"Error processing task {task.id}: {e}")
            raise

    def get_medical_files(self, task):
        try:
            medical_file_list = self.download_medical_files_for_task(task)
            self.__class__.logger.info(f"✅ 病历文件获取成功.")
            return medical_file_list
        except Exception as e:
            self.__class__.logger.error(f"Error processing task {task.id}: {e}")
            raise

    def download_medical_template_files(self, task):
        """下载与 task 相关的ocr结果和 CRF 表头"""
        ocr_text = self.get_ocr_text(task)
        crf_local_file = self.download_template_file_for_task(task)
        return ocr_text, crf_local_file

    def get_ocr_text(self, task):
        """下载病历文件"""
        # 添加subject_visit_id,关联查询
        medical_info_lites = MedicalInfoLite.objects.filter(
            subject_id=task.subject_id,
            subject_visit_id=task.subject_visit_id,
            # category__in=[1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
            delete_flag=0,
            ocr_status = "COMPLETED"
        ).exclude(
                    subject_item__item_id='2'
                    )
        if not medical_info_lites:
            self.__class__.logger.warning(f'No medical_info_lite found for subject_id: {task.subject_id}')
            raise Exception("No medical files found.")

        # medical_file_list = []  # 文件路径
        ocr_text = ''
        seen_hashes = set()
        for medical_info_lite in medical_info_lites:
            medical_file = MedicalFile.objects.filter(id=medical_info_lite.file_id,delete_flag=0).first()
            if not medical_file:
                self.__class__.logger.warning(f"未找到 file_id={medical_info_lite.file_id} 的 MedicalFile")
                continue
            if medical_file.hash in seen_hashes:
                continue

            # 添加 hash 到已处理集合中
            seen_hashes.add(medical_file.hash)   
            ocr_text =ocr_text+'\n'+medical_info_lite.ocr_text_mask
            # medical_file = MedicalFile.objects.filter(id=medical_info_lite.file_id,delete_flag=0).first()
            # if medical_file:
            #     file_path = self.download_file(medical_file, self.medical_file_folder)
            #     # medical_file_list.append(file_path)  # 添加文件路径
        # medical_file_list = self.medical_file_folder  # 获取 template 文件路径

        return ocr_text

    def download_template_file_for_task(self, task):
        """下载 CRF 文件"""
        subject_info = Subject.objects.filter(subject_id=task.subject_id).first()
        if not subject_info:
            raise Exception(f"{task.id}的受试者ID信息{task.subject_id}没有在 subject_info 表中找到...")

        if task.category == 'CRF':
            project_material = ProjectMaterialInfo.objects.filter(
                project_id=subject_info.project_id, category='CRF_TEMPLATE').first()
            if not project_material:
                raise Exception(
                    f"{task.id}的受试者ID信息{task.subject_id}的 项目ID{subject_info.project_id} CRF 没有在 project_material_info 表中找到...")
        if task.category == 'MEDICAL_RECORD':
            epoch_name = get_epoch_id_by_subject_visit(task.subject_visit_id)
            project_material = ProjectMaterialInfo.objects.filter(
                project_id=subject_info.project_id, category='MEDICAL_RECORD_TEMPLATE', epoch_name = epoch_name).first()
            # project_material = ProjectMaterialInfo.objects.filter(
            #     project_id='prs8859297', category='MEDICAL_RECORD_TEMPLATE', epoch__subject_epoch_id = 'prs26087978').first()
            if not project_material:
                raise Exception(
                    f"{task.id}的受试者ID信息{task.subject_id}的 项目ID{subject_info.project_id} 病历模版 没有在 project_material_info 表中找到...")

        # proj_material_file = project_material.files.order_by('-create_time').first()
        proj_material_file = project_material.files.filter(
            delete_flag=0).order_by('-create_time').first()  # 通过外键 files 连接

        if not proj_material_file:
            raise Exception(f"受试者{task.subject_id}参与的项目没有找到对应的 CRF表头 文件...")

        return self.download_file(proj_material_file, self.template_file_folder)

    def download_file(self, file_obj, download_file_path):
        """下载文件"""
        download_file_path = os.path.join(download_file_path, file_obj.original_filename)
        try:
            download_file_from_minio(self.__class__.client, file_obj.bucket_name,
                                     file_obj.object_name, download_file_path)
            self.__class__.logger.info(f"✅ 文件下载成功：{file_obj.original_filename}")
            return download_file_path
        except Exception as e:
            self.__class__.logger.error(f"❌ 文件下载失败: {e}")
            raise

    def get_file_info(self, file_path):
        file_name = os.path.basename(file_path)  # 获取文件名（包括扩展名）
        file_size = os.path.getsize(file_path)  # 获取文件大小（单位：字节）
        content_type, _ = mimetypes.guess_type(file_path)  # 获取文件的 MIME 类型

        return {
            'file_name': file_name,
            'file_size': file_size,
            'content_type': content_type
        }

    def llm_deal(self, task, ocr_text, template_local_file, task_id, model_name):
        """调用大模型进行处理并上传结果"""
        try:
            # 获取总页数
            total_page_count = self.get_total_page_count(task)
            print(f"总页数: {total_page_count}")
            
            print(f"{template_local_file}")
            print("大模型处理模板 ....")
            # 传递页数参数
            generate_word_file(ocr_text, template_local_file, self.retrospective_study_file, task_id, model_name, total_page_count)
            print(f"返回结果：{self.retrospective_study_file}")
        except Exception as e:
            activate_conn(connection)
            task.status = 'ERROR'
            task.save()
            self.__class__.logger.error(f"Error processing task with LLM: {e}")

        try:
            activate_conn(connection)
            file_info = self.get_file_info(self.retrospective_study_file)
            _, ext = os.path.splitext(file_info.get('file_name'))
            object_name = f"{uuid.uuid4().hex}{ext}"
            bucket_name = settings.MINIO_BUCKET_NAME
            if not os.path.exists(self.retrospective_study_file):  # 生成失败，使用模版文件替代结果
                self.retrospective_study_file = self.template_local_file
            upload_file_to_minio(self.__class__.client, self.retrospective_study_file, bucket_name, object_name)
            original_filename = self.get_original_filename(task, model_name)
            self.save_task_data(task, file_info, object_name, bucket_name, original_filename, model_name)
            activate_conn(connection)
            task.status = 'COMPLETED'
            task.data_version = 2
            task.save()
            self.__class__.logger.info(f"✅ 任务 {model_name}{task.id} 处理完成，状态更新为 'COMPLETED'.")
        except Exception as e:
            activate_conn(connection)
            task.status = 'ERROR'
            task.save()
            self.__class__.logger.error(f"Error processing task with LLM: {model_name}{e}")

    def get_original_filename(self, task, model_name):
        subject_info = Subject.objects.filter(subject_id=task.subject_id).first()
        # return subject_info.real_name, subject_info.code
        original_filename = task.project.project_no + '-' + subject_info.code + '-' + task.category + '{}'.format(model_name) + '.docx'
        return original_filename

    def save_task_data(self, task, file_info, object_name, bucket_name, original_filename="llm_output.docx", model_name=1):
        """保存任务数据到数据库"""
        try:
            now = datetime.datetime.now()
            formatted_time = now.strftime("%Y%m%d%H%M%S")
            task_data = {
                'original_filename': original_filename,
                'bucket_name': bucket_name,
                'object_name': object_name,
                'content_type': file_info.get('content_type'),
                'size': file_info.get('file_size'),
                'hash': self.clc_file_hash(self.retrospective_study_file),
                'task_id': task.id,
                'delete_flag': 0,
                'data_version': 0,
                'create_time': now,
                'update_time': now,
                'create_user': task.create_user,
                'create_name': task.create_name,
                'version': 'v' + str(formatted_time) + '{}'.format(model_name),
                'update_user': task.create_user,
                'update_name': task.create_name,
                'model_name': model_name
            }
            self.save_task_data_with_pymysql(task_data)
        except Exception as e:
            activate_conn(connection)
            task.status = 'ERROR'
            task.save()
            self.__class__.logger.error(f"Error saving task data: {e}")
            raise

    def clc_file_hash(self, file_path, hash_algorithm='sha256', chunk_size=8192):
        """计算文件哈希值"""
        hash_func = hashlib.new(hash_algorithm)
        with open(file_path, 'rb') as file_obj:
            while chunk := file_obj.read(chunk_size):
                hash_func.update(chunk)
        return hash_func.hexdigest()

    def save_task_data_with_pymysql(self, task_data: dict):
        """使用 PyMySQL 保存任务数据"""
        try:
            activate_conn(connection)
            # conn = pymysql.connect(
            #     host='**************',
            #     port=3309,
            #     user='aigc_test',
            #     password='KZx6KmA6JNCQPypdpHtbUvphSiXn6AUM',
            #     database='aigc_test',
            #     charset='utf8mb4')
            conn = connection
            with conn.cursor() as cursor:
                sql = """
                INSERT INTO medical_collection_file (
                    original_filename, 
                    bucket_name, 
                    object_name, 
                    content_type, 
                    size, 
                    hash, 
                    task_id,
                    delete_flag, 
                    data_version, 
                    create_time, 
                    update_time,
                    create_user,
                    create_name,
                    version,
                    update_user,
                    update_name,
                    model_name
                ) VALUES (
                    %(original_filename)s, 
                    %(bucket_name)s, 
                    %(object_name)s, 
                    %(content_type)s, 
                    %(size)s, 
                    %(hash)s, 
                    %(task_id)s, 
                    %(delete_flag)s, 
                    %(data_version)s, 
                    %(create_time)s, 
                    %(update_time)s, 
                    %(create_user)s, 
                    %(create_name)s, 
                    %(version)s, 
                    %(update_user)s, 
                    %(update_name)s,
                    %(model_name)s
                )
                """
                cursor.execute(sql, task_data)
                conn.commit()
            self.__class__.logger.info("✅ 数据库记录创建成功！")
        except pymysql.MySQLError as e:
            self.__class__.logger.error(f"❌ 数据库插入失败: {e}")
            raise


def main():
    parser = argparse.ArgumentParser(description="WROD病例生成任务")
    parser.add_argument('--task_id', type=int, required=True, help="task_id")
    args = parser.parse_args()
    # task_processor = RetrospectiveStudyTaskProcessor(**create_file())
    # task_processor.retrospective_study_task_consumer(args.task_id)

    import asyncio
    from concurrent.futures import ThreadPoolExecutor
    async def run_task_async(model_name):
        loop = asyncio.get_running_loop()
        await loop.run_in_executor(
            None,  # 使用默认线程池
            lambda: RetrospectiveStudyTaskProcessor(**create_file()).retrospective_study_task_consumer(args.task_id, model_name)
        )
    # 创建并运行协程
    async def main():
        await asyncio.gather(
            run_task_async('DeepSeek'),
            run_task_async('qwen3')
        )
    # 执行异步任务
    asyncio.run(main())
    import script.medical_record_compare as  mc
    try:
        mc.process_and_update(args.task_id)
    except Exception as e:
        print(f"做病历小结对比时处理任务 {args.task_id} 时发生错误: {e}")

if __name__ == "__main__":
    main()
