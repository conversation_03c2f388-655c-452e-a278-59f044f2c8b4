#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
优化版完整数据处理流程测试
适配新的结果格式：llm1_results和llm2_results直接返回所有结果

更新说明：
- 优化了双模型结果处理逻辑，适配新的数据结构
- 增强了结果分类和对比分析功能
- 改进了结果展示格式，更加清晰直观
- 添加了更详细的统计信息
"""
# 获取当前脚本的绝对路径
import sys
import os
current_script_path = os.path.abspath(__file__)
# 获取项目根目录
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_script_path)))

# 将项目根目录添加到 sys.path
if project_root not in sys.path:
    sys.path.insert(0, project_root)

import time
import logging
from typing import List, Dict, Any
from tabulate import tabulate

# 配置日志输出
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),  # 输出到控制台
    ]
)

# 设置特定模块的日志级别
logger = logging.getLogger(__name__)
logging.getLogger('script.ae_grade.nodes').setLevel(logging.INFO)
logging.getLogger('script.ae_grade.utils').setLevel(logging.INFO)
logging.getLogger('common.clients.llm_client').setLevel(logging.INFO)

# 添加测试开始的日志
print("=" * 80)
print("🚀 AE Grade Recognition System 优化版测试")
print("=" * 80)


def print_dual_model_results(llm1_results, llm2_results, input_data_map):
    """
    使用清晰的分组展示双模型结果对比 - 优化版
    适配新的结果格式：llm1_results和llm2_results直接返回所有结果

    Args:
        llm1_results: 主模型结果列表（包含所有项目）
        llm2_results: 副模型结果列表（包含所有项目）
        input_data_map: 输入数据映射
    """
    print(f"\n{'='*80}")
    print(f"🤖 双模型结果对比分析（优化版）")
    print(f"{'='*80}")

    # 创建结果映射，确保key为整数
    llm1_map = {}
    if llm1_results:
        for item in llm1_results:
            try:
                key = int(item.get('id', 0))
                llm1_map[key] = item
            except (ValueError, TypeError):
                continue

    llm2_map = {}
    if llm2_results:
        for item in llm2_results:
            try:
                key = int(item.get('id', 0))
                llm2_map[key] = item
            except (ValueError, TypeError):
                continue

    # 获取所有唯一ID
    all_ids = set(llm1_map.keys()) | set(llm2_map.keys())

    # 分析结果
    normal_items = []  # 两个模型都判定为正常的项目
    abnormal_items = []  # 两个模型都判定为异常的项目
    disagreement_items = []  # 两个模型判定结果不一致的项目

    # 统计信息
    total_items = len(all_ids)
    llm1_abnormal_count = 0
    llm2_abnormal_count = 0
    agreement_count = 0

    # 大模型实际处理的项目统计
    llm_processed_items = 0  # 大模型实际处理的项目数
    llm_agreement_count = 0  # 大模型结果一致的项目数
    
    for item_id in sorted(all_ids):
        # 获取原始输入数据
        original_item = input_data_map.get(item_id, input_data_map.get(str(item_id), {}))
        test_name = original_item.get('test_name', '')
        test_value = original_item.get('test_value', '')
        test_flag = original_item.get('test_flag', 0)
        
        # 获取模型结果
        llm1_item = llm1_map.get(item_id, {})
        llm2_item = llm2_map.get(item_id, {})
        
        llm1_ae_name = llm1_item.get('ae_name', '')
        llm1_ae_grade = llm1_item.get('ae_grade', 0)
        llm2_ae_name = llm2_item.get('ae_name', '')
        llm2_ae_grade = llm2_item.get('ae_grade', 0)
        
        # 判断是否为异常项
        is_llm1_abnormal = llm1_ae_name and llm1_ae_name != '' and llm1_ae_name != 'CTCAE未涉及，请研究者判定' and llm1_ae_grade and llm1_ae_grade != 0
        is_llm2_abnormal = llm2_ae_name and llm2_ae_name != '' and llm2_ae_name != 'CTCAE未涉及，请研究者判定' and llm2_ae_grade and llm2_ae_grade != 0

        # 判断是否为大模型实际处理的项目（排除正常项和CTCAE未匹配项）
        is_llm1_processed = (llm1_ae_name and llm1_ae_name != '' and llm1_ae_name != 'CTCAE未涉及，请研究者判定') or (llm1_ae_grade and llm1_ae_grade != 0)
        is_llm2_processed = (llm2_ae_name and llm2_ae_name != '' and llm2_ae_name != 'CTCAE未涉及，请研究者判定') or (llm2_ae_grade and llm2_ae_grade != 0)
        is_llm_processed = is_llm1_processed or is_llm2_processed

        # 更新统计信息
        if is_llm1_abnormal:
            llm1_abnormal_count += 1
        if is_llm2_abnormal:
            llm2_abnormal_count += 1

        # 统计大模型实际处理的项目
        if is_llm_processed:
            llm_processed_items += 1
            
        # 分类
        if is_llm1_abnormal and is_llm2_abnormal:
            # 两个模型都判定为异常
            if llm1_ae_name == llm2_ae_name and str(llm1_ae_grade) == str(llm2_ae_grade):
                # 结果一致
                abnormal_items.append({
                    'id': item_id,
                    'test_name': test_name,
                    'test_value': test_value,
                    'test_flag': test_flag,
                    'ae_name': llm1_ae_name,
                    'ae_grade': llm1_ae_grade
                })
                agreement_count += 1
                if is_llm_processed:
                    llm_agreement_count += 1
            else:
                # 结果不一致
                disagreement_items.append({
                    'id': item_id,
                    'test_name': test_name,
                    'test_value': test_value,
                    'test_flag': test_flag,
                    'llm1_ae_name': llm1_ae_name,
                    'llm1_ae_grade': llm1_ae_grade,
                    'llm2_ae_name': llm2_ae_name,
                    'llm2_ae_grade': llm2_ae_grade
                })
        elif is_llm1_abnormal:
            # 只有LLM1判定为异常
            disagreement_items.append({
                'id': item_id,
                'test_name': test_name,
                'test_value': test_value,
                'test_flag': test_flag,
                'llm1_ae_name': llm1_ae_name,
                'llm1_ae_grade': llm1_ae_grade,
                'llm2_ae_name': llm2_ae_name,
                'llm2_ae_grade': llm2_ae_grade
            })
        elif is_llm2_abnormal:
            # 只有LLM2判定为异常
            disagreement_items.append({
                'id': item_id,
                'test_name': test_name,
                'test_value': test_value,
                'test_flag': test_flag,
                'llm1_ae_name': llm1_ae_name,
                'llm1_ae_grade': llm1_ae_grade,
                'llm2_ae_name': llm2_ae_name,
                'llm2_ae_grade': llm2_ae_grade
            })
        else:
            # 两个模型都判定为正常
            normal_items.append({
                'id': item_id,
                'test_name': test_name,
                'test_value': test_value,
                'test_flag': test_flag
            })
            agreement_count += 1
            # 如果是大模型处理的项目且都判定为正常，也算一致
            if is_llm_processed:
                llm_agreement_count += 1
    
    # 计算一致率
    agreement_rate = (agreement_count / total_items * 100) if total_items > 0 else 0
    llm_agreement_rate = (llm_agreement_count / llm_processed_items * 100) if llm_processed_items > 0 else 0

    # 输出统计信息
    print(f"📊 双模型分析统计:")
    stats_table = [
        ["总检验项目", total_items],
        ["大模型实际处理项目", llm_processed_items],
        ["DeepSeek-R1检出异常", llm1_abnormal_count],
        ["Qwen3-32B检出异常", llm2_abnormal_count],
        ["整体结果一致项目", agreement_count],
        ["大模型结果一致项目", llm_agreement_count],
        ["整体结果分歧项目", len(disagreement_items)],
        ["整体一致率", f"{agreement_rate:.1f}%"],
        ["真实一致率（仅大模型结果）", f"{llm_agreement_rate:.1f}%"]
    ]
    print(tabulate(stats_table, headers=["指标", "数值"], tablefmt="grid"))
    
    # 展示结果分歧的项目
    if disagreement_items:
        print(f"\n⚠️ 结果分歧项目 ({len(disagreement_items)}项):")
        disagreement_table = []
        for item in disagreement_items:
            disagreement_table.append([
                item['id'],
                item['test_name'][:20],
                item['test_value'],
                item['llm1_ae_name'][:20],
                item['llm1_ae_grade'],
                item['llm2_ae_name'][:20],
                item['llm2_ae_grade']
            ])
        print(tabulate(disagreement_table, 
                      headers=["ID", "检验项目", "检验值", "DeepSeek-AE", "等级", "Qwen3-AE", "等级"], 
                      tablefmt="grid"))
    
    # 展示异常项目
    if abnormal_items:
        print(f"\n🔴 检出异常项目 ({len(abnormal_items)}项):")
        abnormal_table = []
        for item in abnormal_items:
            abnormal_table.append([
                item['id'],
                item['test_name'][:25],
                item['test_value'],
                item['ae_name'][:25],
                item['ae_grade']
            ])
        print(tabulate(abnormal_table, 
                      headers=["ID", "检验项目", "检验值", "AE名称", "等级"], 
                      tablefmt="grid"))
    
    print("=" * 80)


def get_standard_answers() -> List[Dict[str, Any]]:
    """获取标准答案数据"""
    return [
        {"id": 25611, "ae_grade": "1", "ae_name": "低钠血症", "ae_desc": "实验室检查结果显示，血液中钠浓度低。"},
        {"id": 25516, "ae_grade": "2", "ae_name": "贫血", "ae_desc": "100mL 血液中的血红蛋白总量降低为特征的疾病。"},
        {"id": 25521, "ae_grade": "1", "ae_name": "丙氨酸氨基转移酶增高", "ae_desc": "实验室检查结果显示，血液样本中丙氨酸转移酶（ALT或SGPT）水平增高。"},
        {"id": 25525, "ae_grade": "1", "ae_name": "γ-谷氨酰转移酶增高", "ae_desc": "血液样本实验室检查结果显示，γ-谷氨酰转移酶（GGT）水平增高。"},
        {"id": 25529, "ae_grade": "1", "ae_name": "血乳酸脱氢酶升高", "ae_desc": "实验室检查提示血乳酸脱氢酶升高。"},
        {"id": 25537, "ae_grade": "1", "ae_name": "低白蛋白血症", "ae_desc": "实验室检查结果显示，血中白蛋白浓度低。"},
        {"id": 25549, "ae_grade": "1", "ae_name": "丙氨酸氨基转移酶增高", "ae_desc": "实验室检查结果显示，血液样本中丙氨酸转移酶（ALT或SGPT）水平增高。"},
        {"id": 25550, "ae_grade": "1", "ae_name": "低钙血症", "ae_desc": "实验室检查结果显示，血液中钙浓度（针对白蛋白，校正）低。"},
        {"id": 25553, "ae_grade": "1", "ae_name": "γ-谷氨酰转移酶增高", "ae_desc": "血液样本实验室检查结果显示，γ-谷氨酰转移酶（GGT）水平增高。"},
        {"id": 25557, "ae_grade": "1", "ae_name": "血乳酸脱氢酶升高", "ae_desc": "实验室检查提示血乳酸脱氢酶升高。"},
        {"id": 25563, "ae_grade": "1", "ae_name": "低白蛋白血症", "ae_desc": "实验室检查结果显示，血中白蛋白浓度低。"},
        {"id": 25587, "ae_grade": "2", "ae_name": "血小板计数降低", "ae_desc": "血液样本实验室检查结果显示，血小板计数降低。"},
        {"id":25621, "ae_grade": "1", "ae_name": "蛋白尿", "ae_desc": "实验室检查结果显示尿液中出现过多的蛋白质，主要是白蛋白，也有球蛋白。"},
    ]


def compare_with_standard_answers(llm1_results, llm2_results, input_data_map):
    """
    将大模型结果与标准答案进行对比

    Args:
        llm1_results: 主模型结果列表
        llm2_results: 副模型结果列表
        input_data_map: 输入数据映射
    """
    print(f"\n{'='*80}")
    print(f"📋 标准答案对比分析")
    print(f"{'='*80}")

    # 获取标准答案
    standard_answers = get_standard_answers()

    # 创建模型结果映射
    llm1_map = {}
    if llm1_results:
        for item in llm1_results:
            try:
                key = int(item.get('id', 0))
                llm1_map[key] = item
            except (ValueError, TypeError):
                continue

    llm2_map = {}
    if llm2_results:
        for item in llm2_results:
            try:
                key = int(item.get('id', 0))
                llm2_map[key] = item
            except (ValueError, TypeError):
                continue

    # 统计信息
    total_standard = len(standard_answers)
    llm1_correct_count = 0
    llm2_correct_count = 0
    llm1_partial_count = 0
    llm2_partial_count = 0
    llm1_missed_count = 0
    llm2_missed_count = 0

    # 详细对比结果
    comparison_results = []

    print(f"📊 标准答案统计:")
    print(f"   标准答案异常项目数: {total_standard}")

    # 逐项对比
    for standard_item in standard_answers:
        item_id = standard_item['id']
        standard_ae_name = standard_item['ae_name']
        standard_ae_grade = str(standard_item['ae_grade'])

        # 获取原始输入数据
        original_item = input_data_map.get(item_id, input_data_map.get(str(item_id), {}))
        test_name = original_item.get('test_name', '')
        test_value = original_item.get('test_value', '')

        # 获取模型结果
        llm1_item = llm1_map.get(item_id, {})
        llm2_item = llm2_map.get(item_id, {})

        llm1_ae_name = llm1_item.get('ae_name', '')
        llm1_ae_grade = str(llm1_item.get('ae_grade', ''))
        llm2_ae_name = llm2_item.get('ae_name', '')
        llm2_ae_grade = str(llm2_item.get('ae_grade', ''))

        # 判断是否为有效的异常结果
        def is_valid_abnormal(ae_name, ae_grade):
            return (ae_name and ae_name != '' and
                   ae_name != 'CTCAE未涉及，请研究者判定' and
                   ae_grade and ae_grade != '0' and ae_grade != '')

        llm1_is_abnormal = is_valid_abnormal(llm1_ae_name, llm1_ae_grade)
        llm2_is_abnormal = is_valid_abnormal(llm2_ae_name, llm2_ae_grade)

        # 评估LLM1结果
        llm1_status = "未检出"
        if llm1_is_abnormal:
            if llm1_ae_name == standard_ae_name and llm1_ae_grade == standard_ae_grade:
                llm1_status = "完全正确"
                llm1_correct_count += 1
            elif llm1_ae_name == standard_ae_name or llm1_ae_grade == standard_ae_grade:
                llm1_status = "部分正确"
                llm1_partial_count += 1
            else:
                llm1_status = "错误检出"
        else:
            llm1_missed_count += 1

        # 评估LLM2结果
        llm2_status = "未检出"
        if llm2_is_abnormal:
            if llm2_ae_name == standard_ae_name and llm2_ae_grade == standard_ae_grade:
                llm2_status = "完全正确"
                llm2_correct_count += 1
            elif llm2_ae_name == standard_ae_name or llm2_ae_grade == standard_ae_grade:
                llm2_status = "部分正确"
                llm2_partial_count += 1
            else:
                llm2_status = "错误检出"
        else:
            llm2_missed_count += 1

        # 记录对比结果
        comparison_results.append({
            'id': item_id,
            'test_name': test_name,
            'test_value': test_value,
            'standard_ae_name': standard_ae_name,
            'standard_ae_grade': standard_ae_grade,
            'llm1_ae_name': llm1_ae_name,
            'llm1_ae_grade': llm1_ae_grade,
            'llm1_status': llm1_status,
            'llm2_ae_name': llm2_ae_name,
            'llm2_ae_grade': llm2_ae_grade,
            'llm2_status': llm2_status
        })

    # 计算准确率
    llm1_accuracy = (llm1_correct_count / total_standard * 100) if total_standard > 0 else 0
    llm2_accuracy = (llm2_correct_count / total_standard * 100) if total_standard > 0 else 0
    llm1_detection_rate = ((llm1_correct_count + llm1_partial_count) / total_standard * 100) if total_standard > 0 else 0
    llm2_detection_rate = ((llm2_correct_count + llm2_partial_count) / total_standard * 100) if total_standard > 0 else 0

    # 输出统计结果
    print(f"\n📈 对比统计结果:")
    stats_table = [
        ["指标", "DeepSeek-R1", "Qwen3-32B"],
        ["完全正确", f"{llm1_correct_count}/{total_standard}", f"{llm2_correct_count}/{total_standard}"],
        ["部分正确", f"{llm1_partial_count}/{total_standard}", f"{llm2_partial_count}/{total_standard}"],
        ["未检出", f"{llm1_missed_count}/{total_standard}", f"{llm2_missed_count}/{total_standard}"],
        ["完全准确率", f"{llm1_accuracy:.1f}%", f"{llm2_accuracy:.1f}%"],
        ["检出率（含部分正确）", f"{llm1_detection_rate:.1f}%", f"{llm2_detection_rate:.1f}%"]
    ]
    print(tabulate(stats_table, headers="firstrow", tablefmt="grid"))

    # 输出详细对比结果
    print(f"\n📋 详细对比结果:")
    detail_table = []
    for result in comparison_results:
        detail_table.append([
            result['id'],
            result['test_name'][:15],
            result['test_value'][:10],
            result['standard_ae_name'][:20],
            result['standard_ae_grade'],
            result['llm1_ae_name'][:20] if result['llm1_ae_name'] else '-',
            result['llm1_ae_grade'] if result['llm1_ae_grade'] else '-',
            result['llm1_status'],
            result['llm2_ae_name'][:20] if result['llm2_ae_name'] else '-',
            result['llm2_ae_grade'] if result['llm2_ae_grade'] else '-',
            result['llm2_status']
        ])

    headers = ["ID", "检验项目", "检验值", "标准AE名称", "标准等级",
              "DeepSeek-AE", "等级", "状态", "Qwen3-AE", "等级", "状态"]
    print(tabulate(detail_table, headers=headers, tablefmt="grid"))

    print("=" * 80)


def create_test_data() -> List[Dict[str, Any]]:
    """创建测试数据"""
    return [{'id': 25486, 'test_type': '数值', 'test_code': '', 'test_name': '*巨细胞病毒脱氧核糖核酸(CMV-DNA)', 'test_unit': '拷贝/mL', 'test_value': '1.23E+03', 'test_flag': 1, 'reference_value': '<1E+03', 'reference_range_min': None, 'reference_range_max': '1000.0', 'abnormal_symbol': ''}, {'id': 25487, 'test_type': '数值', 'test_code': '', 'test_name': '*EB病毒脱氧核糖核酸(EBV-DNA)', 'test_unit': '拷贝/mL', 'test_value': '<4E+02', 'test_flag': 1, 'reference_value': '<4E+02', 'reference_range_min': None, 'reference_range_max': '400.0', 'abnormal_symbol': ''}, {'id': 25488, 'test_type': '数值', 'test_code': 'PT', 'test_name': '★凝血酶原时间', 'test_unit': '', 'test_value': '10.4', 'test_flag': 0, 'reference_value': '9.4-12.5', 'reference_range_min': '9.4', 'reference_range_max': '12.5', 'abnormal_symbol': '5'}, {'id': 25489, 'test_type': '数值', 'test_code': 'PT-%', 'test_name': '凝血酶原活动度', 'test_unit': '%', 'test_value': '112', 'test_flag': 0, 'reference_value': '70-120', 'reference_range_min': '70.0', 'reference_range_max': '120.0', 'abnormal_symbol': ''}, {'id': 25490, 'test_type': '数值', 'test_code': 'PT-INR', 'test_name': '凝血酶原国际标准化比率', 'test_unit': '', 'test_value': '0.93', 'test_flag': 0, 'reference_value': '0.90-1.20', 'reference_range_min': '0.9', 'reference_range_max': '1.2', 'abnormal_symbol': ''}, {'id': 25491, 'test_type': '数值', 'test_code': 'FIB-C', 'test_name': '*纤维蛋白原', 'test_unit': 'mg/dL', 'test_value': '320', 'test_flag': 0, 'reference_value': '200-400', 'reference_range_min': '200.0', 'reference_range_max': '400.0', 'abnormal_symbol': ''}, {'id': 25492, 'test_type': '数值', 'test_code': 'APTT', 'test_name': '*活化部分凝血活酶时间', 'test_unit': 's', 'test_value': '26.8', 'test_flag': 0, 'reference_value': '25.1-36.5', 'reference_range_min': '25.1', 'reference_range_max': '36.5', 'abnormal_symbol': ''}, {'id': 25493, 'test_type': '数值', 'test_code': 'APTT-R', 'test_name': '活化部分凝血活酶时间比率', 'test_unit': '', 'test_value': '0.87', 'test_flag': 2, 'reference_value': '0.91-1.38', 'reference_range_min': '0.91', 'reference_range_max': '1.38', 'abnormal_symbol': '1'}, {'id': 25494, 'test_type': '数值', 'test_code': 'FDP', 'test_name': '*纤维蛋白降解产物', 'test_unit': 'ug/ml', 'test_value': '5.5', 'test_flag': 1, 'reference_value': '0.0-5.0', 'reference_range_min': '0.0', 'reference_range_max': '5.0', 'abnormal_symbol': '↑'}, {'id': 25495, 'test_type': '数值', 'test_code': 'D-dimer', 'test_name': '*D-二聚体', 'test_unit': 'ng/mL', 'test_value': '827', 'test_flag': 1, 'reference_value': '0-243', 'reference_range_min': '0.0', 'reference_range_max': '243.0', 'abnormal_symbol': '1'}, {'id': 25496, 'test_type': '数值', 'test_code': 'WBC', 'test_name': '★白细胞计数', 'test_unit': '/6.01', 'test_value': '11.90', 'test_flag': 1, 'reference_value': '3.5-9.5', 'reference_range_min': '3.5', 'reference_range_max': '9.5', 'abnormal_symbol': '↑'}, {'id': 25497, 'test_type': '数值', 'test_code': 'AD-MCH', 'test_name': '红细胞分布宽度变异系', 'test_unit': '%', 'test_value': '22.7', 'test_flag': 1, 'reference_value': '0.0-15.0', 'reference_range_min': '0.0', 'reference_range_max': '15.0', 'abnormal_symbol': '↑'}, {'id': 25498, 'test_type': '数值', 'test_code': 'NE%', 'test_name': '中性粒细胞百分比', 'test_unit': '%', 'test_value': '54.8', 'test_flag': 0, 'reference_value': '40-75', 'reference_range_min': '40.0', 'reference_range_max': '75.0', 'abnormal_symbol': ''}, {'id': 25499, 'test_type': '数值', 'test_code': 'RDW-SD', 'test_name': '红细胞分布宽度标准差', 'test_unit': 'fL', 'test_value': '68.7', 'test_flag': 1, 'reference_value': '40.0-53.0', 'reference_range_min': '40.0', 'reference_range_max': '53.0', 'abnormal_symbol': 't'}, {'id': 25500, 'test_type': '数值', 'test_code': 'LY%', 'test_name': '淋巴细胞百分比', 'test_unit': '%', 'test_value': '16.5', 'test_flag': 2, 'reference_value': '20-50', 'reference_range_min': '20.0', 'reference_range_max': '50.0', 'abnormal_symbol': '1'}, {'id': 25501, 'test_type': '数值', 'test_code': 'RDW-SD', 'test_name': '红细胞分布宽度标准差', 'test_unit': 'fL', 'test_value': '68.7', 'test_flag': 1, 'reference_value': '40.0-53.0', 'reference_range_min': '40.0', 'reference_range_max': '53.0', 'abnormal_symbol': 't'}, {'id': 25502, 'test_type': '数值', 'test_code': 'MO%', 'test_name': '单核细胞百分比', 'test_unit': '%', 'test_value': '18.8', 'test_flag': 1, 'reference_value': '3-10', 'reference_range_min': '3.0', 'reference_range_max': '10.0', 'abnormal_symbol': '1'}, {'id': 25503, 'test_type': '数值', 'test_code': 'MPY', 'test_name': '平均血小板体积', 'test_unit': 'fL', 'test_value': '8.1', 'test_flag': 0, 'reference_value': '6.8-13.5', 'reference_range_min': '6.8', 'reference_range_max': '13.5', 'abnormal_symbol': ''}, {'id': 25504, 'test_type': '数值', 'test_code': 'EO%', 'test_name': '嗜酸性粒细胞百分比', 'test_unit': '%', 'test_value': '0.0', 'test_flag': 2, 'reference_value': '0.4-8.0', 'reference_range_min': '0.4', 'reference_range_max': '8.0', 'abnormal_symbol': '1'}, {'id': 25505, 'test_type': '数值', 'test_code': 'PCT', 'test_name': '血小板比容', 'test_unit': '%', 'test_value': '0.03', 'test_flag': 2, 'reference_value': '0.11-0.27', 'reference_range_min': '0.11', 'reference_range_max': '0.27', 'abnormal_symbol': '！'}, {'id': 25506, 'test_type': '数值', 'test_code': 'BA%', 'test_name': '嗜碱性粒细胞百分比', 'test_unit': '%', 'test_value': '0.4', 'test_flag': 0, 'reference_value': '0-1', 'reference_range_min': '0.0', 'reference_range_max': '1.0', 'abnormal_symbol': ''}, {'id': 25507, 'test_type': '数值', 'test_code': 'PDIF', 'test_name': '血小板体积分布宽度', 'test_unit': 'fL', 'test_value': '18.7', 'test_flag': 1, 'reference_value': '9.0-17.0', 'reference_range_min': '9.0', 'reference_range_max': '17.0', 'abnormal_symbol': '1'}, {'id': 25508, 'test_type': '数值', 'test_code': 'NE#', 'test_name': '中性粒细胞饱对数', 'test_unit': '10^9/L', 'test_value': '7.60', 'test_flag': 1, 'reference_value': '1.8-6.3', 'reference_range_min': '1.8', 'reference_range_max': '6.3', 'abnormal_symbol': '1'}, {'id': 25509, 'test_type': '数值', 'test_code': 'RET#', 'test_name': '网织红细胞绝对值', 'test_unit': '10^6/uL', 'test_value': '0.0469', 'test_flag': 0, 'reference_value': '0.024-0.084', 'reference_range_min': '0.024', 'reference_range_max': '0.084', 'abnormal_symbol': ''}, {'id': 25510, 'test_type': '数值', 'test_code': 'LY#', 'test_name': '淋巴细胞绝对数', 'test_unit': '10^9/L', 'test_value': '2.00', 'test_flag': 0, 'reference_value': '1.1-3.2', 'reference_range_min': '1.1', 'reference_range_max': '3.2', 'abnormal_symbol': ''}, {'id': 25511, 'test_type': '数值', 'test_code': 'RET%', 'test_name': '网织红细胞百分比', 'test_unit': '%', 'test_value': '1.89', 'test_flag': 1, 'reference_value': '0.5-1.5', 'reference_range_min': '0.5', 'reference_range_max': '1.5', 'abnormal_symbol': '1'}, {'id': 25512, 'test_type': '数值', 'test_code': 'MO#', 'test_name': '单核细胞绝对数', 'test_unit': '$10^9/L', 'test_value': '2.20', 'test_flag': 1, 'reference_value': '0.10-0.60', 'reference_range_min': '0.1', 'reference_range_max': '0.6', 'abnormal_symbol': '1'}, {'id': 25513, 'test_type': '数值', 'test_code': 'EO#', 'test_name': '嗜酸性粒细胞绝对数', 'test_unit': '10^9/L', 'test_value': '0.00', 'test_flag': 2, 'reference_value': '0.02-0.52', 'reference_range_min': '0.02', 'reference_range_max': '0.52', 'abnormal_symbol': '1'}, {'id': 25514, 'test_type': '数值', 'test_code': 'BA', 'test_name': '哈碱性粒细胞绝对数', 'test_unit': '10^9/L', 'test_value': '0.10', 'test_flag': 1, 'reference_value': '0.00-0.06', 'reference_range_min': '0.0', 'reference_range_max': '0.06', 'abnormal_symbol': '1'}, {'id': 25515, 'test_type': '数值', 'test_code': 'RBC', 'test_name': '★红细胞 计数', 'test_unit': '10^12/L', 'test_value': '2.48', 'test_flag': 2, 'reference_value': '4.30-5.80', 'reference_range_min': '4.3', 'reference_range_max': '5.8', 'abnormal_symbol': '1'}, {'id': 25516, 'test_type': '数值', 'test_code': 'HGB', 'test_name': '★血红蛋白含量', 'test_unit': 'R/L', 'test_value': '89', 'test_flag': 2, 'reference_value': '130-175', 'reference_range_min': '130.0', 'reference_range_max': '175.0', 'abnormal_symbol': '1'}, {'id': 25517, 'test_type': '数值', 'test_code': 'HCT', 'test_name': '★红细胞比积', 'test_unit': '%', 'test_value': '21.20', 'test_flag': 0, 'reference_value': '10-50', 'reference_range_min': '10.0', 'reference_range_max': '50.0', 'abnormal_symbol': '1'}, {'id': 25518, 'test_type': '数值', 'test_code': 'MCV', 'test_name': '平均红细胞体积', 'test_unit': 'fL', 'test_value': '87.0', 'test_flag': 0, 'reference_value': '82-100', 'reference_range_min': '82.0', 'reference_range_max': '100.0', 'abnormal_symbol': ''}, {'id': 25519, 'test_type': '数值', 'test_code': 'MCH', 'test_name': '平均红细胞血红蛋白量', 'test_unit': 'Pg', 'test_value': '28.1', 'test_flag': 0, 'reference_value': '27-34', 'reference_range_min': '27.0', 'reference_range_max': '34.0', 'abnormal_symbol': ''}, {'id': 25520, 'test_type': '数值', 'test_code': 'SCHC', 'test_name': '平均红细胞血红蛋白浓', 'test_unit': '', 'test_value': '323', 'test_flag': 0, 'reference_value': '316-354', 'reference_range_min': '316.0', 'reference_range_max': '354.0', 'abnormal_symbol': '/1.'}, {'id': 25521, 'test_type': '数值', 'test_code': 'ALT', 'test_name': '★丙氨酸氨基转移酶', 'test_unit': 'U/L', 'test_value': '75', 'test_flag': 1, 'reference_value': '9-50', 'reference_range_min': '9.0', 'reference_range_max': '50.0', 'abnormal_symbol': '↑'}, {'id': 25522, 'test_type': '数值', 'test_code': 'CHO', 'test_name': '★总胆固醇', 'test_unit': 'mmol/L', 'test_value': '4.25', 'test_flag': 0, 'reference_value': '2.90-6.20', 'reference_range_min': '2.9', 'reference_range_max': '6.2', 'abnormal_symbol': ''}, {'id': 25523, 'test_type': '数值', 'test_code': 'AST', 'test_name': '★天门冬氨酸氨基转', 'test_unit': 'U/L', 'test_value': '37', 'test_flag': 0, 'reference_value': '15-40', 'reference_range_min': '15.0', 'reference_range_max': '40.0', 'abnormal_symbol': ''}, {'id': 25524, 'test_type': '数值', 'test_code': 'TG', 'test_name': '★甘油 三酯', 'test_unit': 'mmol/L', 'test_value': '0.75', 'test_flag': 0, 'reference_value': '0.45-1.70', 'reference_range_min': '0.45', 'reference_range_max': '1.7', 'abnormal_symbol': ''}, {'id': 25525, 'test_type': '数值', 'test_code': 'r-GT', 'test_name': '★γ-谷氨酰转肽酶', 'test_unit': 'U/L', 'test_value': '113', 'test_flag': 1, 'reference_value': '10-60', 'reference_range_min': '10.0', 'reference_range_max': '60.0', 'abnormal_symbol': '↑'}, {'id': 25526, 'test_type': '数值', 'test_code': 'HDL-C', 'test_name': '★高密度脂蛋白胆固醇', 'test_unit': 'mmol/L', 'test_value': '0.98', 'test_flag': 2, 'reference_value': '1.03-1.55', 'reference_range_min': '1.03', 'reference_range_max': '1.55', 'abnormal_symbol': '4'}, {'id': 25527, 'test_type': '数值', 'test_code': 'ALP', 'test_name': '*碱性磷酸酶', 'test_unit': 'U/L', 'test_value': '71', 'test_flag': 0, 'reference_value': '45-125', 'reference_range_min': '45.0', 'reference_range_max': '125.0', 'abnormal_symbol': ''}, {'id': 25528, 'test_type': '数值', 'test_code': 'LDL-C', 'test_name': '★低密度脂蛋白胆固醇', 'test_unit': 'mmol/L', 'test_value': '2.54', 'test_flag': 0, 'reference_value': '1.90-4.10', 'reference_range_min': '1.9', 'reference_range_max': '4.1', 'abnormal_symbol': ''}, {'id': 25529, 'test_type': '数值', 'test_code': 'LDH', 'test_name': '★乳酸脱氢酶', 'test_unit': 'U/L', 'test_value': '318', 'test_flag': 1, 'reference_value': '109-245', 'reference_range_min': '109.0', 'reference_range_max': '245.0', 'abnormal_symbol': 't'}, {'id': 25530, 'test_type': '数值', 'test_code': 'Ca', 'test_name': '★钙', 'test_unit': 'mmol1/L', 'test_value': '2.21', 'test_flag': 0, 'reference_value': '2.20-2.65', 'reference_range_min': '2.2', 'reference_range_max': '2.65', 'abnormal_symbol': ''}, {'id': 25531, 'test_type': '数值', 'test_code': 'CK', 'test_name': '★肌酸激酶', 'test_unit': 'U/L', 'test_value': '14', 'test_flag': 2, 'reference_value': '56-244', 'reference_range_min': '56.0', 'reference_range_max': '244.0', 'abnormal_symbol': '1'}, {'id': 25532, 'test_type': '数值', 'test_code': 'IP', 'test_name': '★无机磷酸盐', 'test_unit': 'mmol/L', 'test_value': '1.22', 'test_flag': 0, 'reference_value': '0.80-1.45', 'reference_range_min': '0.8', 'reference_range_max': '1.45', 'abnormal_symbol': ''}, {'id': 25533, 'test_type': '数值', 'test_code': 'HBD', 'test_name': '★α-羟丁酸脱氢酶', 'test_unit': 'U/L', 'test_value': '214', 'test_flag': 1, 'reference_value': '72-182', 'reference_range_min': '72.0', 'reference_range_max': '182.0', 'abnormal_symbol': '↑'}, {'id': 25534, 'test_type': '数值', 'test_code': 'Na', 'test_name': '★钠', 'test_unit': 'mmol/L', 'test_value': '137.5', 'test_flag': 0, 'reference_value': '137.0-147.0', 'reference_range_min': '137.0', 'reference_range_max': '147.0', 'abnormal_symbol': ''}, {'id': 25535, 'test_type': '数值', 'test_code': 'TP', 'test_name': '★总蛋白', 'test_unit': 'g/L', 'test_value': '55.3', 'test_flag': 2, 'reference_value': '65.0-85.0', 'reference_range_min': '65.0', 'reference_range_max': '85.0', 'abnormal_symbol': 't'}, {'id': 25536, 'test_type': '数值', 'test_code': 'K', 'test_name': '★钾', 'test_unit': 'mmol/L', 'test_value': '4.57', 'test_flag': 0, 'reference_value': '3.50-5.30', 'reference_range_min': '3.5', 'reference_range_max': '5.3', 'abnormal_symbol': ''}, {'id': 25537, 'test_type': '数值', 'test_code': 'Alb', 'test_name': '★白蛋白', 'test_unit': '7/8', 'test_value': '33.1', 'test_flag': 2, 'reference_value': '40.0-55.0', 'reference_range_min': '40.0', 'reference_range_max': '55.0', 'abnormal_symbol': 't'}, {'id': 25538, 'test_type': '数值', 'test_code': 'Cl', 'test_name': '★氯', 'test_unit': 'mmol/L', 'test_value': '106.6', 'test_flag': 0, 'reference_value': '99.0-110.0', 'reference_range_min': '99.0', 'reference_range_max': '110.0', 'abnormal_symbol': ''}, {'id': 25539, 'test_type': '数值', 'test_code': 'A/G', 'test_name': '白蛋白/球蛋白', 'test_unit': '', 'test_value': '1.49', 'test_flag': 0, 'reference_value': '1.20-2.40', 'reference_range_min': '1.2', 'reference_range_max': '2.4', 'abnormal_symbol': ''}, {'id': 25540, 'test_type': '数值', 'test_code': 'T-CO2', 'test_name': '总二氧化 碳', 'test_unit': 'mmol/L', 'test_value': '25.0', 'test_flag': 0, 'reference_value': '22.0-29.0', 'reference_range_min': '22.0', 'reference_range_max': '29.0', 'abnormal_symbol': ''}, {'id': 25541, 'test_type': '数值', 'test_code': 'TBIL', 'test_name': '*总胆红素', 'test_unit': 'μmol/L', 'test_value': '7.8', 'test_flag': 0, 'reference_value': '3.0-21.0', 'reference_range_min': '3.0', 'reference_range_max': '21.0', 'abnormal_symbol': ''}, {'id': 25542, 'test_type': '数值', 'test_code': 'DBIL', 'test_name': '*直接胆红素', 'test_unit': 'μmol/L', 'test_value': '3.5', 'test_flag': 0, 'reference_value': '0.0-7.0', 'reference_range_min': '0.0', 'reference_range_max': '7.0', 'abnormal_symbol': ''}, {'id': 25543, 'test_type': '数值', 'test_code': 'Urea', 'test_name': '★尿素', 'test_unit': 'mmol/L', 'test_value': '8.2', 'test_flag': 1, 'reference_value': '2.8-7.2', 'reference_range_min': '2.8', 'reference_range_max': '7.2', 'abnormal_symbol': 't'}, {'id': 25544, 'test_type': '数值', 'test_code': 'CRE', 'test_name': '★肌', 'test_unit': 'μmol/L', 'test_value': '70', 'test_flag': 0, 'reference_value': '59-104', 'reference_range_min': '59.0', 'reference_range_max': '104.0', 'abnormal_symbol': ''}, {'id': 25545, 'test_type': '数值', 'test_code': 'eGFR', 'test_name': '估算肾小球滤过率', 'test_unit': 'ml/min', 'test_value': '106.83', 'test_flag': 0, 'reference_value': '', 'reference_range_min': None, 'reference_range_max': None, 'abnormal_symbol': ''}, {'id': 25546, 'test_type': '数值', 'test_code': 'UA', 'test_name': '★尿酸', 'test_unit': 'μmol/L', 'test_value': '171', 'test_flag': 2, 'reference_value': '208-428', 'reference_range_min': '208.0', 'reference_range_max': '428.0', 'abnormal_symbol': 't'}, {'id': 25547, 'test_type': '数值', 'test_code': 'Glu', 'test_name': '★葡萄糖', 'test_unit': 'mmol/L', 'test_value': '4.38', 'test_flag': 0, 'reference_value': '3.30-6.10', 'reference_range_min': '3.3', 'reference_range_max': '6.1', 'abnormal_symbol': ''}, {'id': 25548, 'test_type': '数值', 'test_code': '', 'test_name': '曲霉菌细胞壁半乳甘露聚糖试验', 'test_unit': '', 'test_value': 'II0', 'test_flag': 0, 'reference_value': '<0.5', 'reference_range_min': None, 'reference_range_max': '0.5', 'abnormal_symbol': '阴性'}, {'id': 25549, 'test_type': '数值', 'test_code': 'ALT', 'test_name': '★丙氨酸氨基转移酶', 'test_unit': 'U/L', 'test_value': '56', 'test_flag': 1, 'reference_value': '9-50', 'reference_range_min': '9.0', 'reference_range_max': '50.0', 'abnormal_symbol': '+'}, {'id': 25550, 'test_type': '数值', 'test_code': 'Ca', 'test_name': '★钙', 'test_unit': 'mmol/L', 'test_value': '2.17', 'test_flag': 2, 'reference_value': '2.20-2.65', 'reference_range_min': '2.2', 'reference_range_max': '2.65', 'abnormal_symbol': '↑'}, {'id': 25551, 'test_type': '数值', 'test_code': 'LSV', 'test_name': '★天门冬氨酸氨基转', 'test_unit': 'U/L', 'test_value': '30', 'test_flag': 0, 'reference_value': '15-40', 'reference_range_min': '15.0', 'reference_range_max': '40.0', 'abnormal_symbol': ''}, {'id': 25552, 'test_type': '数值', 'test_code': 'IP', 'test_name': '★无机磷酸盐', 'test_unit': 'mmol/L', 'test_value': '1.07', 'test_flag': 0, 'reference_value': '0.80-1.45', 'reference_range_min': '0.8', 'reference_range_max': '1.45', 'abnormal_symbol': ''}, {'id': 25553, 'test_type': '数值', 'test_code': '19-1', 'test_name': '★γ-谷氨酰转肽酶', 'test_unit': 'U/L', 'test_value': '90', 'test_flag': 1, 'reference_value': '10-60', 'reference_range_min': '10.0', 'reference_range_max': '60.0', 'abnormal_symbol': 't'}, {'id': 25554, 'test_type': '数值', 'test_code': 'Na', 'test_name': '★钠', 'test_unit': 'mmol/L', 'test_value': '137.5', 'test_flag': 0, 'reference_value': '137.0-147.0', 'reference_range_min': '137.0', 'reference_range_max': '147.0', 'abnormal_symbol': ''}, {'id': 25555, 'test_type': '数值', 'test_code': 'ALP', 'test_name': '*碱性磷酸酶', 'test_unit': 'U/L', 'test_value': '74', 'test_flag': 0, 'reference_value': '45-125', 'reference_range_min': '45.0', 'reference_range_max': '125.0', 'abnormal_symbol': ''}, {'id': 25556, 'test_type': '数值', 'test_code': 'K', 'test_name': '★钾', 'test_unit': 'mmol/L', 'test_value': '4.95', 'test_flag': 0, 'reference_value': '3.50-5.30', 'reference_range_min': '3.5', 'reference_range_max': '5.3', 'abnormal_symbol': ''}, {'id': 25557, 'test_type': '数值', 'test_code': 'LDH', 'test_name': '★乳酸脱氢酶', 'test_unit': 'U/L', 'test_value': '317', 'test_flag': 1, 'reference_value': '109-245', 'reference_range_min': '109.0', 'reference_range_max': '245.0', 'abnormal_symbol': 't'}, {'id': 25558, 'test_type': '数值', 'test_code': 'Cl', 'test_name': '★氯', 'test_unit': 'mmol/L', 'test_value': '108.3', 'test_flag': 0, 'reference_value': '99.0-110.0', 'reference_range_min': '99.0', 'reference_range_max': '110.0', 'abnormal_symbol': ''}, {'id': 25559, 'test_type': '数值', 'test_code': 'CK', 'test_name': '★肌酸激酶', 'test_unit': 'U/L', 'test_value': '19', 'test_flag': 2, 'reference_value': '56-244', 'reference_range_min': '56.0', 'reference_range_max': '244.0', 'abnormal_symbol': '1'}, {'id': 25560, 'test_type': '数值', 'test_code': 'T-CO2', 'test_name': '总二氧化碳', 'test_unit': 'mmol/L', 'test_value': '22.8', 'test_flag': 0, 'reference_value': '22.0-29.0', 'reference_range_min': '22.0', 'reference_range_max': '29.0', 'abnormal_symbol': ''}, {'id': 25561, 'test_type': '数值', 'test_code': 'HBD', 'test_name': '★α-羟丁酸脱 氢酶', 'test_unit': 'U/L', 'test_value': '210', 'test_flag': 1, 'reference_value': '72-182', 'reference_range_min': '72.0', 'reference_range_max': '182.0', 'abnormal_symbol': '†'}, {'id': 25562, 'test_type': '数值', 'test_code': 'TP', 'test_name': '★总蛋白', 'test_unit': 'g/L', 'test_value': '55.5', 'test_flag': 2, 'reference_value': '65.0-85.0', 'reference_range_min': '65.0', 'reference_range_max': '85.0', 'abnormal_symbol': 't'}, {'id': 25563, 'test_type': '数值', 'test_code': 'AIb', 'test_name': '★白蛋白', 'test_unit': 'g/L', 'test_value': '32.8', 'test_flag': 2, 'reference_value': '40.0-55.0', 'reference_range_min': '40.0', 'reference_range_max': '55.0', 'abnormal_symbol': '1'}, {'id': 25564, 'test_type': '数值', 'test_code': 'A/G', 'test_name': '白蛋白/球蛋白', 'test_unit': '', 'test_value': '1.44', 'test_flag': 0, 'reference_value': '1.20-2.40', 'reference_range_min': '1.2', 'reference_range_max': '2.4', 'abnormal_symbol': '1'}, {'id': 25565, 'test_type': '数值', 'test_code': 'TBIL', 'test_name': '*总胆红素', 'test_unit': 'μmol/L', 'test_value': '8.0', 'test_flag': 0, 'reference_value': '3.0-21.0', 'reference_range_min': '3.0', 'reference_range_max': '21.0', 'abnormal_symbol': ''}, {'id': 25566, 'test_type': '数值', 'test_code': 'DBIL', 'test_name': '*直接胆红素', 'test_unit': 'μmol/L', 'test_value': '3.5', 'test_flag': 0, 'reference_value': '0.0-7.0', 'reference_range_min': '0.0', 'reference_range_max': '7.0', 'abnormal_symbol': ''}, {'id': 25567, 'test_type': '数值', 'test_code': 'Urea', 'test_name': '★尿素', 'test_unit': 'mmol/L', 'test_value': '7.8', 'test_flag': 1, 'reference_value': '2.8-7.2', 'reference_range_min': '2.8', 'reference_range_max': '7.2', 'abnormal_symbol': 't'}, {'id': 25568, 'test_type': '数值', 'test_code': 'CRE', 'test_name': '★肌', 'test_unit': 'μmol/L', 'test_value': '73', 'test_flag': 0, 'reference_value': '59-104', 'reference_range_min': '59.0', 'reference_range_max': '104.0', 'abnormal_symbol': ''}, {'id': 25569, 'test_type': '数值', 'test_code': 'eGFR', 'test_name': '估算肾小球滤过率', 'test_unit': 'ml/min', 'test_value': '105.00', 'test_flag': 0, 'reference_value': '', 'reference_range_min': None, 'reference_range_max': None, 'abnormal_symbol': ''}, {'id': 25570, 'test_type': '数值', 'test_code': 'UA', 'test_name': '★尿酸', 'test_unit': 'μmol/L', 'test_value': '154', 'test_flag': 2, 'reference_value': '208-428', 'reference_range_min': '208.0', 'reference_range_max': '428.0', 'abnormal_symbol': '1'}, {'id': 25571, 'test_type': '数值', 'test_code': 'Glu', 'test_name': '★葡萄糖', 'test_unit': 'mmol/L', 'test_value': '4.65', 'test_flag': 0, 'reference_value': '3.30-6.10', 'reference_range_min': '3.3', 'reference_range_max': '6.1', 'abnormal_symbol': ''}, {'id': 25572, 'test_type': '数值', 'test_code': '', 'test_name': '真菌细胞壁(1,3)-β-D葡聚糖试验', 'test_unit': 'pg/ml', 'test_value': '<10.0', 'test_flag': 0, 'reference_value': '<60', 'reference_range_min': None, 'reference_range_max': '60.0', 'abnormal_symbol': ''}, {'id': 25573, 'test_type': '数值', 'test_code': 'PCT', 'test_name': '降钙素原', 'test_unit': 'ng/ml', 'test_value': '0.193', 'test_flag': 0, 'reference_value': '<0.500', 'reference_range_min': None, 'reference_range_max': '0.5', 'abnormal_symbol': ''}, {'id': 25574, 'test_type': '数值', 'test_code': 'HBsAg', 'test_name': '★乙型肝炎病毒表面抗原', 'test_unit': '/I', 'test_value': '0.00', 'test_flag': 0, 'reference_value': '0.00-0.05', 'reference_range_min': '0.0', 'reference_range_max': '0.05', 'abnormal_symbol': ''}, {'id': 25575, 'test_type': '数值', 'test_code': 'HBsAb', 'test_name': '★ 抗乙型肝炎病毒表而抗体', 'test_unit': 'mIU/ml', 'test_value': '10.67', 'test_flag': 1, 'reference_value': '0.00-10.00', 'reference_range_min': '0.0', 'reference_range_max': '10.0', 'abnormal_symbol': '↑'}, {'id': 25576, 'test_type': '数值', 'test_code': 'HBeAg', 'test_name': '*乙型肝炎病毒e抗原', 'test_unit': 'S/CO', 'test_value': '0.36', 'test_flag': 0, 'reference_value': '0.00-1.00', 'reference_range_min': '0.0', 'reference_range_max': '1.0', 'abnormal_symbol': '阴性(-)'}, {'id': 25577, 'test_type': '数值', 'test_code': 'HBeAb', 'test_name': '*抗乙型肝炎病毒e抗体', 'test_unit': 'S/CO', 'test_value': '1.68', 'test_flag': 0, 'reference_value': '>1.00', 'reference_range_min': '1.0', 'reference_range_max': None, 'abnormal_symbol': '阴性(-)'}, {'id': 25578, 'test_type': '数值', 'test_code': 'HBcAb', 'test_name': '*抗乙型肝炎病毒核心抗体', 'test_unit': 'S/CO', 'test_value': '0.46', 'test_flag': 0, 'reference_value': '0.00-1.00', 'reference_range_min': '0.0', 'reference_range_max': '1.0', 'abnormal_symbol': '阴性(-)'}, {'id': 25579, 'test_type': '数值', 'test_code': 'Anti-HCV', 'test_name': '★抗丙型肝炎病毒抗体', 'test_unit': 'S/CO', 'test_value': '0.14', 'test_flag': 0, 'reference_value': '0.00-1.00', 'reference_range_min': '0.0', 'reference_range_max': '1.0', 'abnormal_symbol': '阴性（-)'}, {'id': 25580, 'test_type': '数值', 'test_code': 'TP', 'test_name': '抗梅垂螺旋体抗体', 'test_unit': 'S/CO', 'test_value': '0.09', 'test_flag': 0, 'reference_value': '0.00-1.00', 'reference_range_min': '0.0', 'reference_range_max': '1.0', 'abnormal_symbol': '阴性(-)'}, {'id': 25581, 'test_type': '数值', 'test_code': 'ATH', 'test_name': '*人免疫缺陷病毒抗原/抗体联合检测', 'test_unit': 'S/CO', 'test_value': '0.09', 'test_flag': 0, 'reference_value': '0.00-1.00', 'reference_range_min': '0.0', 'reference_range_max': '1.0', 'abnormal_symbol': '阴性(-)'}, {'id': 25582, 'test_type': '数值', 'test_code': '', 'test_name': '★白细胞计数', 'test_unit': '10^9/L', 'test_value': '12.70', 'test_flag': 1, 'reference_value': '3.5-9.5', 'reference_range_min': '3.5', 'reference_range_max': '9.5', 'abnormal_symbol': '1'}, {'id': 25583, 'test_type': '数值', 'test_code': 'RDW-CV', 'test_name': '红细胞分布宽度变异系', 'test_unit': '%', 'test_value': '23.1', 'test_flag': 1, 'reference_value': '0.0-15.0', 'reference_range_min': '0.0', 'reference_range_max': '15.0', 'abnormal_symbol': '1'}, {'id': 25584, 'test_type': '数值', 'test_code': 'NE%', 'test_name': '中性粒细胞百分比', 'test_unit': '%', 'test_value': '56.5', 'test_flag': 0, 'reference_value': '40-75', 'reference_range_min': '40.0', 'reference_range_max': '75.0', 'abnormal_symbol': ''}, {'id': 25585, 'test_type': '数值', 'test_code': 'RDW-SD', 'test_name': '红细胞分布宽度标准差', 'test_unit': 'fL', 'test_value': '70.0', 'test_flag': 1, 'reference_value': '40.0-53.0', 'reference_range_min': '40.0', 'reference_range_max': '53.0', 'abnormal_symbol': '↑'}, {'id': 25586, 'test_type': '数值', 'test_code': 'LY%', 'test_name': '淋巴细胞百分比', 'test_unit': '%', 'test_value': '18.7', 'test_flag': 2, 'reference_value': '20-50', 'reference_range_min': '20.0', 'reference_range_max': '50.0', 'abnormal_symbol': '1'}, {'id': 25587, 'test_type': '数值', 'test_code': 'PLT', 'test_name': '★血小板计数', 'test_unit': '10^9/L', 'test_value': '54', 'test_flag': 2, 'reference_value': '125-350', 'reference_range_min': '125.0', 'reference_range_max': '350.0', 'abnormal_symbol': '1'}, {'id': 25588, 'test_type': '数值', 'test_code': 'MO%', 'test_name': '单核细胞百分比', 'test_unit': '%', 'test_value': '29.4', 'test_flag': 1, 'reference_value': '3-10', 'reference_range_min': '3.0', 'reference_range_max': '10.0', 'abnormal_symbol': '↑'}, {'id': 25589, 'test_type': '数值', 'test_code': 'MPV', 'test_name': '平均血小板体积', 'test_unit': 'fL', 'test_value': '8.4', 'test_flag': 0, 'reference_value': '6.8-13.5', 'reference_range_min': '6.8', 'reference_range_max': '13.5', 'abnormal_symbol': ''}, {'id': 25590, 'test_type': '数值', 'test_code': 'EO%', 'test_name': '嗜酸性粒细胞百分比', 'test_unit': '%', 'test_value': '0.1', 'test_flag': 2, 'reference_value': '0.4-8.0', 'reference_range_min': '0.4', 'reference_range_max': '8.0', 'abnormal_symbol': '一'}, {'id': 25591, 'test_type': '数值', 'test_code': 'PCT', 'test_name': '血小板比容', 'test_unit': '%', 'test_value': '0.05', 'test_flag': 2, 'reference_value': '0.11-0.27', 'reference_range_min': '0.11', 'reference_range_max': '0.27', 'abnormal_symbol': '1'}, {'id': 25592, 'test_type': '数值', 'test_code': 'BA%', 'test_name': '嗜碱性粒细胞百分比', 'test_unit': '%', 'test_value': '0.3', 'test_flag': 0, 'reference_value': '0-1', 'reference_range_min': '0.0', 'reference_range_max': '1.0', 'abnormal_symbol': ''}, {'id': 25593, 'test_type': '数值', 'test_code': 'PDIF', 'test_name': '血小板体积分布宽度', 'test_unit': 'T3', 'test_value': '18.0', 'test_flag': 1, 'reference_value': '9.0-17.0', 'reference_range_min': '9.0', 'reference_range_max': '17.0', 'abnormal_symbol': '↑'}, {'id': 25594, 'test_type': '数值', 'test_code': 'NE', 'test_name': '中性粒细胞绝对数', 'test_unit': '10^9/L', 'test_value': '7.20', 'test_flag': 1, 'reference_value': '1.8-6.3', 'reference_range_min': '1.8', 'reference_range_max': '6.3', 'abnormal_symbol': '↑'}, {'id': 25595, 'test_type': '数值', 'test_code': 'CRP', 'test_name': '*快速C-反应蛋白', 'test_unit': 'm/L', 'test_value': '2.7', 'test_flag': 0, 'reference_value': '0-10', 'reference_range_min': '0.0', 'reference_range_max': '10.0', 'abnormal_symbol': ''}, {'id': 25596, 'test_type': '数值', 'test_code': 'LYW', 'test_name': '淋巴 细胞绝对数', 'test_unit': '10^9/L', 'test_value': '1.70', 'test_flag': 0, 'reference_value': '1.1-3.2', 'reference_range_min': '1.1', 'reference_range_max': '3.2', 'abnormal_symbol': ''}, {'id': 25597, 'test_type': '数值', 'test_code': 'MO', 'test_name': '单核细胞绝对数', 'test_unit': '10^9/L', 'test_value': '3.70', 'test_flag': 1, 'reference_value': '0.10-0.60', 'reference_range_min': '0.1', 'reference_range_max': '0.6', 'abnormal_symbol': '1'}, {'id': 25598, 'test_type': '数值', 'test_code': 'EO', 'test_name': '嗜酸性粒细胞绝对数', 'test_unit': '10^9/L', 'test_value': '0.00', 'test_flag': 2, 'reference_value': '0.02-0.52', 'reference_range_min': '0.02', 'reference_range_max': '0.52', 'abnormal_symbol': '↓'}, {'id': 25599, 'test_type': '数值', 'test_code': 'BA', 'test_name': '噬碱性粒细胞绝对数', 'test_unit': '10^9/L', 'test_value': '0.00', 'test_flag': 0, 'reference_value': '0.00-0.06', 'reference_range_min': '0.0', 'reference_range_max': '0.06', 'abnormal_symbol': ''}, {'id': 25600, 'test_type': '数值', 'test_code': 'RBC', 'test_name': '★红细胞计数', 'test_unit': '10^12/L', 'test_value': '2.40', 'test_flag': 2, 'reference_value': '4.30-5.80', 'reference_range_min': '4.3', 'reference_range_max': '5.8', 'abnormal_symbol': '1'}, {'id': 25601, 'test_type': '数值', 'test_code': 'HGB', 'test_name': '鼠红蛋白含量', 'test_unit': 'g/L', 'test_value': '69', 'test_flag': 2, 'reference_value': '130-175', 'reference_range_min': '130.0', 'reference_range_max': '175.0', 'abnormal_symbol': '1'}, {'id': 25602, 'test_type': '数值', 'test_code': 'HCT', 'test_name': '红细胞比积', 'test_unit': '%', 'test_value': '21.10', 'test_flag': 2, 'reference_value': '40-50', 'reference_range_min': '40.0', 'reference_range_max': '50.0', 'abnormal_symbol': '↓'}, {'id': 25603, 'test_type': '数值', 'test_code': 'MCY', 'test_name': '平均红细胞体积', 'test_unit': 'fL', 'test_value': '88.1', 'test_flag': 0, 'reference_value': '82-100', 'reference_range_min': '82.0', 'reference_range_max': '100.0', 'abnormal_symbol': ''}, {'id': 25604, 'test_type': '数值', 'test_code': 'MCH', 'test_name': '平均红细胞血红蛋白量', 'test_unit': 'Pg', 'test_value': '88Z', 'test_flag': 1, 'reference_value': '27-34', 'reference_range_min': '27.0', 'reference_range_max': '34.0', 'abnormal_symbol': ''}, {'id': 25605, 'test_type': '数值', 'test_code': 'MCHC', 'test_name': '平均红细胞血红蛋白浓', 'test_unit': 'π/L', 'test_value': '2', 'test_flag': 2, 'reference_value': '316-354', 'reference_range_min': '316.0', 'reference_range_max': '354.0', 'abnormal_symbol': ''}, {'id': 25606, 'test_type': '数值', 'test_code': 'Urea', 'test_name': '★尿素', 'test_unit': 'mmol/L', 'test_value': '8.0', 'test_flag': 1, 'reference_value': '2.8-7.2', 'reference_range_min': '2.8', 'reference_range_max': '7.2', 'abnormal_symbol': '1'}, {'id': 25607, 'test_type': '数值', 'test_code': 'CRE', 'test_name': '★肌酐', 'test_unit': 'μmol/L', 'test_value': '78', 'test_flag': 0, 'reference_value': '59-104', 'reference_range_min': '59.0', 'reference_range_max': '104.0', 'abnormal_symbol': ''}, {'id': 25608, 'test_type': '数值', 'test_code': 'eGFR', 'test_name': '估算肾小球滤过率', 'test_unit': 'ml/min', 'test_value': '102.18', 'test_flag': 0, 'reference_value': '', 'reference_range_min': None, 'reference_range_max': None, 'abnormal_symbol': ''}, {'id': 25609, 'test_type': '数值', 'test_code': 'Glu', 'test_name': '★葡萄糖', 'test_unit': 'mmol/L', 'test_value': '5.17', 'test_flag': 0, 'reference_value': '3.30-6.10', 'reference_range_min': '3.3', 'reference_range_max': '6.1', 'abnormal_symbol': ''}, {'id': 25610, 'test_type': '数值', 'test_code': 'Ca', 'test_name': '★钙', 'test_unit': 'mmol/L', 'test_value': '2.20', 'test_flag': 0, 'reference_value': '2.20-2.65', 'reference_range_min': '2.2', 'reference_range_max': '2.65', 'abnormal_symbol': ''}, {'id': 25611, 'test_type': '数值', 'test_code': 'Na', 'test_name': '★钠', 'test_unit': 'mmol/L', 'test_value': '136.3', 'test_flag': 2, 'reference_value': '137.0-147.0', 'reference_range_min': '137.0', 'reference_range_max': '147.0', 'abnormal_symbol': '1'}, {'id': 25612, 'test_type': '数值', 'test_code': 'K', 'test_name': '★钾', 'test_unit': 'mmol/L', 'test_value': '4.94', 'test_flag': 0, 'reference_value': '3.50-5.30', 'reference_range_min': '3.5', 'reference_range_max': '5.3', 'abnormal_symbol': ''}, {'id': 25613, 'test_type': '数值', 'test_code': 'Cl', 'test_name': '★氯', 'test_unit': 'mmol/L', 'test_value': '105.8', 'test_flag': 0, 'reference_value': '99.0-110.0', 'reference_range_min': '99.0', 'reference_range_max': '110.0', 'abnormal_symbol': ''}, {'id': 25614, 'test_type': '数值', 'test_code': 'T-002', 'test_name': '总二氧化碳', 'test_unit': 'mmol/L', 'test_value': '25.8', 'test_flag': 0, 'reference_value': '22.0-29.0', 'reference_range_min': '22.0', 'reference_range_max': '29.0', 'abnormal_symbol': ''}, {'id': 25615, 'test_type': '数值', 'test_code': 'LPS', 'test_name': '*脂肪酶', 'test_unit': 'U/L', 'test_value': '53.2', 'test_flag': 0, 'reference_value': '13.0-60.0', 'reference_range_min': '13.0', 'reference_range_max': '60.0', 'abnormal_symbol': ''}, {'id': 25616, 'test_type': '数值', 'test_code': 'AMY', 'test_name': '淀粉酶(血)', 'test_unit': 'U/L', 'test_value': '92', 'test_flag': 0, 'reference_value': '28-100', 'reference_range_min': '28.0', 'reference_range_max': '100.0', 'abnormal_symbol': ''}, {'id': 25617, 'test_type': '数值', 'test_code': 'SG', 'test_name': '*(干化学)比重', 'test_unit': '', 'test_value': '6101', 'test_flag': 1, 'reference_value': '1.003-1.030', 'reference_range_min': '1.003', 'reference_range_max': '1.03', 'abnormal_symbol': ''}, {'id': 25618, 'test_type': '数值', 'test_code': 'SPERM', 'test_name': '(尿流式)精子', 'test_unit': '/uL', 'test_value': '0', 'test_flag': 0, 'reference_value': '0-0', 'reference_range_min': '0.0', 'reference_range_max': '0.0', 'abnormal_symbol': ''}, {'id': 25619, 'test_type': '定性', 'test_code': 'GLU', 'test_name': '*(干化学)葡萄糖', 'test_unit': '', 'test_value': '阴性', 'test_flag': 0, 'reference_value': '阴性', 'reference_range_min': None, 'reference_range_max': None, 'abnormal_symbol': ''}, {'id': 25620, 'test_type': '数值', 'test_code': 'SRC', 'test_name': '(尿流式)小圆上皮细胞', 'test_unit': '/$', 'test_value': '0.4', 'test_flag': 0, 'reference_value': 'ε-0', 'reference_range_min': None, 'reference_range_max': None, 'abnormal_symbol': '↑'}, {'id': 25621, 'test_type': '定性', 'test_code': 'PRO', 'test_name': '*(干化学)蛋白', 'test_unit': '', 'test_value': '+', 'test_flag': 0, 'reference_value': '阴性', 'reference_range_min': None, 'reference_range_max': None, 'abnormal_symbol': '↑'}, {'id': 25622, 'test_type': '数值', 'test_code': 'EC', 'test_name': '(尿流式)上皮细胞', 'test_unit': '/uL', 'test_value': '0', 'test_flag': 0, 'reference_value': '0-5', 'reference_range_min': '0.0', 'reference_range_max': '5.0', 'abnormal_symbol': '↑'}, {'id': 25623, 'test_type': '数值', 'test_code': 'PH', 'test_name': '*(干化学)酸度', 'test_unit': '', 'test_value': '7.5', 'test_flag': 0, 'reference_value': '4.5-8.0', 'reference_range_min': '4.5', 'reference_range_max': '8.0', 'abnormal_symbol': ''}, {'id': 25624, 'test_type': '数值', 'test_code': 'MUS', 'test_name': '(尿 流式)粘液丝', 'test_unit': '/uL', 'test_value': '0.00', 'test_flag': 0, 'reference_value': '', 'reference_range_min': None, 'reference_range_max': None, 'abnormal_symbol': ''}, {'id': 25625, 'test_type': '定性', 'test_code': 'KET', 'test_name': '*(干化学)酮体', 'test_unit': '', 'test_value': '阴性', 'test_flag': 0, 'reference_value': '阴性', 'reference_range_min': None, 'reference_range_max': None, 'abnormal_symbol': ''}, {'id': 25626, 'test_type': '定性', 'test_code': 'CAST1', 'test_name': '(镜检)管型类别1', 'test_unit': '个/LPF', 'test_value': '未见', 'test_flag': 0, 'reference_value': '', 'reference_range_min': None, 'reference_range_max': None, 'abnormal_symbol': ''}, {'id': 25627, 'test_type': '定性', 'test_code': 'UBG', 'test_name': '*(干化学)尿胆原', 'test_unit': '', 'test_value': '阴性', 'test_flag': 0, 'reference_value': '阴性', 'reference_range_min': None, 'reference_range_max': None, 'abnormal_symbol': ''}, {'id': 25628, 'test_type': '定性', 'test_code': 'CAST2', 'test_name': '(镜检)管型类别2', 'test_unit': '个/LPF', 'test_value': '', 'test_flag': 0, 'reference_value': '', 'reference_range_min': None, 'reference_range_max': None, 'abnormal_symbol': ''}, {'id': 25629, 'test_type': '定性', 'test_code': 'BLD', 'test_name': '*(干化学)潜血', 'test_unit': '', 'test_value': '++', 'test_flag': 0, 'reference_value': '阴性', 'reference_range_min': None, 'reference_range_max': None, 'abnormal_symbol': '1'}, {'id': 25630, 'test_type': '定性', 'test_code': 'LEU', 'test_name': '*(干化学)白细胞酯酶', 'test_unit': '', 'test_value': '阴性', 'test_flag': 0, 'reference_value': '阴性', 'reference_range_min': None, 'reference_range_max': None, 'abnormal_symbol': ''}, {'id': 25631, 'test_type': '定性', 'test_code': 'NIT', 'test_name': '*(干化学)亚硝酸盐', 'test_unit': '', 'test_value': '阴性', 'test_flag': 0, 'reference_value': '阴性', 'reference_range_min': None, 'reference_range_max': None, 'abnormal_symbol': ''}, {'id': 25632, 'test_type': '定性', 'test_code': 'BIL', 'test_name': '*(干化学)胆红素', 'test_unit': '', 'test_value': '阴性', 'test_flag': 0, 'reference_value': '阴性', 'reference_range_min': None, 'reference_range_max': None, 'abnormal_symbol': ''}, {'id': 25633, 'test_type': '数值', 'test_code': 'WBC', 'test_name': '(尿流式)白细胞', 'test_unit': '/uL', 'test_value': '27', 'test_flag': 1, 'reference_value': '0-11', 'reference_range_min': '0.0', 'reference_range_max': '11.0', 'abnormal_symbol': '↑'}, {'id': 25634, 'test_type': '数值', 'test_code': 'RBC', 'test_name': '(尿流 式)红细胞', 'test_unit': '', 'test_value': '6139', 'test_flag': 1, 'reference_value': '0-14', 'reference_range_min': '0.0', 'reference_range_max': '14.0', 'abnormal_symbol': '1'}, {'id': 25635, 'test_type': '数值', 'test_code': '', 'test_name': '(尿流式)完整红细胞百分比', 'test_unit': '%', 'test_value': '100.0', 'test_flag': 0, 'reference_value': '', 'reference_range_min': None, 'reference_range_max': None, 'abnormal_symbol': ''}, {'id': 25636, 'test_type': '数值', 'test_code': 'BACT', 'test_name': '(尿流式)细菌', 'test_unit': '/uL', 'test_value': '13', 'test_flag': 0, 'reference_value': '0-51', 'reference_range_min': '0.0', 'reference_range_max': '51.0', 'abnormal_symbol': ''}, {'id': 25637, 'test_type': '数值', 'test_code': 'YLC', 'test_name': '(尿流式)酵母样菌', 'test_unit': '/uL', 'test_value': '0', 'test_flag': 0, 'reference_value': '0', 'reference_range_min': None, 'reference_range_max': None, 'abnormal_symbol': ''}, {'id': 25638, 'test_type': '数值', 'test_code': 'X,TAL', 'test_name': '(尿流式)结品数量', 'test_unit': '/uL', 'test_value': '0', 'test_flag': 0, 'reference_value': '0', 'reference_range_min': None, 'reference_range_max': None, 'abnormal_symbol': ''}, {'id': 25639, 'test_type': '定性', 'test_code': 'X,TAL', 'test_name': '1(镜检)结晶类别', 'test_unit': '', 'test_value': '', 'test_flag': 0, 'reference_value': '', 'reference_range_min': None, 'reference_range_max': None, 'abnormal_symbol': ''}]


def test_optimized_flow():
    """测试优化版完整流程"""
    print("=" * 80)
    print("🧪 测试优化版完整数据处理流程")
    print("=" * 80)

    test_data = create_test_data()
    total_input = len(test_data)

    print(f"📋 输入数据 (共{total_input}项)")

    try:
        from main import process_ae_analysis

        start_time = time.time()

        # 使用process_ae_analysis函数
        result = process_ae_analysis(test_data)
        duration = time.time() - start_time

        # 获取结果
        output_results = result.get('results', [])
        total_output = len(output_results)

        # 获取双模型数据
        dual_model_data = result.get("dual_model_data", {})
        llm1_results = dual_model_data.get("llm1_results", [])
        llm2_results = dual_model_data.get("llm2_results", [])

        # 输出基本信息
        print(f"\n📊 处理结果:")
        print(f"   输入项目数: {total_input}")
        print(f"   输出项目数: {total_output}")
        print(f"   LLM1结果数: {len(llm1_results)}")
        print(f"   LLM2结果数: {len(llm2_results)}")
        print(f"   处理耗时: {duration:.2f}秒")
        
        # 验证数据完整性
        is_complete = (len(llm1_results) == total_input and len(llm2_results) == total_input)
        print(f"   数据完整性: {'✅ 通过' if is_complete else '❌ 失败'}")
        
        if not is_complete:
            print(f"   ⚠️ 警告: 双模型结果数量与输入不一致，可能数据合并不完整")
        
        # 创建输入数据映射
        input_data_map = {item['id']: item for item in test_data}
        
        # 输出双模型对比结果
        if llm1_results and llm2_results:
            print_dual_model_results(llm1_results, llm2_results, input_data_map)

            # 添加标准答案对比
            compare_with_standard_answers(llm1_results, llm2_results, input_data_map)
        else:
            print(f"\n⚠️ 未获取到双模型结果，可能是模型调用失败")

        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_optimized_flow()
    if success:
        print("\n🎉 优化版测试完成!")
    else:
        print("\n❌ 测试失败，请检查错误信息")
