import re
import ast
import io
import fitz
from sqlalchemy import text
import hashlib
import argparse
from datetime import datetime, timedelta

import os
import django
from django.db import connections
from django.conf import settings

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smo.settings.test')
os.environ["DJANGO_ALLOW_ASYNC_UNSAFE"] = "true"
django.setup()
# mails = settings.MAILS
# DATABASES = settings.DATABASES
from sqlalchemy import create_engine
from sqlalchemy.pool import NullPool
from common.tools import get_db_engin_url
from django.db import transaction
from apps.medical.models import MedicalFileMasked, MedicalInfoLite, MedicalFile
from common.ocr_mask.main import ocr_desensitive
from common.ocr_tools import bytes_word_to_pdf
from common.minio_client import get_minio_client
# from apps.system.models import ModelInvocationLog  # Disabled: HIPAA mode doesn't use AI model logging
# 创建数据库引擎对象
from common.tools import word_to_image_binary_list, images_to_word_stream


db_erp = create_engine(get_db_engin_url('default'), poolclass=NullPool)


def close_db_connection():
    # 关闭数据库连接
    db_erp.dispose()
    print("Database connection closed")


def find_all_indices(s, target_char):
    """
    找到所有字符串中指定字符的下标
    s：输入字符串
    target_char：要查找的字符
    return：所有下标的列表
    """
    if len(target_char)!=1:
        raise ValueError("target_char 必须是单个字符")
    return [i for i, char in enumerate(s) if char == target_char]


def mask_image_by_location(draw, image_path, ocrResult, keywords):
    """
    根据给定的位置信息对图像中的特定字符进行打码。

    :param image_path: 原始图像路径
    :param ocr_result: OCR识别结果
    :param keywords: 需要打码的关键字列表
    """

    # 提取位置信息
    for block in ast.literal_eval(ocrResult)["result"]["words_block_list"]:
        words = block["words"]
        locations = block["location"]
        if not locations:
            continue
        num_chars = len(words)

        # 计算每个字符的起始和结束位置
        x_coords = [point[0] for point in locations]
        y_coords = [point[1] for point in locations]

        left = min(x_coords)
        right = max(x_coords)
        top = min(y_coords)
        bottom = max(y_coords)

        # 使用线性插值计算每个字符的边界
        char_width = (right - left) / num_chars

        if keywords in words:
            previous_index = -1
            for keyword in keywords:
                indices = find_all_indices(words, keyword)
                if len(indices) == 1:
                    start_index = words.find(keyword)
                    if start_index != -1:
                        end_index = start_index + len(keyword)

                        for i in range(start_index, end_index):
                            if previous_index == -1 or i > previous_index:
                                char_left = left + i * char_width
                                char_right = left + (i + 1) * char_width

                                top_y, bottom_y = linear_interpolate(char_left, char_right, top, bottom, x_coords, y_coords)

                                draw.rectangle([char_left, top_y, char_right, bottom_y], fill='black')

                                previous_index = start_index
                else:
                    start_index = previous_index + 1
                    if start_index != -1:
                        end_index = start_index + len(keyword)

                        for i in range(start_index, end_index):
                            if previous_index == -1 or i > previous_index:
                                char_left = left + i * char_width
                                char_right = left + (i + 1) * char_width\

                                top_y, bottom_y = linear_interpolate(char_left, char_right, top, bottom, x_coords, y_coords)

                                draw.rectangle([char_left, top_y, char_right, bottom_y], fill='black')

                                previous_index = start_index


def linear_interpolate(x_start, x_end, y_top, y_bottom, x_coords, y_coords):
    """
    使用线性插值计算顶部和底部的y坐标。

    :param x_start: 字符的左边界
    :param x_end: 字符的右边界
    :param y_top: 多边形的顶部y坐标
    :param y_bottom: 多边形的底部y坐标
    :param x_coords: 多边形的x坐标列表
    :param y_coords: 多边形的y坐标列表
    :return: 插值后的y坐标 (top_y, bottom_y)
    """
    n = len(x_coords)
    if n < 2:
        return y_top, y_bottom

    def interpolate(x, x_coords, y_coords):
        for i in range(n - 1):
            if x_coords[i] <= x <= x_coords[i + 1]:
                t = (x - x_coords[i]) / (x_coords[i + 1] - x_coords[i])
                return y_coords[i] + t * (y_coords[(i + 1) % n] - y_coords[i])
        # 如果x不在任何区间内，使用最近的点
        return y_coords[-1]

    # 计算顶部的y坐标
    top_y_start = interpolate(x_start, x_coords, y_coords)
    top_y_end = interpolate(x_end, x_coords, y_coords)
    top_y = (top_y_start + top_y_end) / 2

    # 计算底部的y坐标
    bottom_y_start = interpolate(x_start, x_coords, [y_bottom] * n)
    bottom_y_end = interpolate(x_end, x_coords, [y_bottom] * n)
    bottom_y = (bottom_y_start + bottom_y_end) / 2

    return top_y, bottom_y


def match_chinese_string(text):
    # 匹配姓名后面的名字
    # name_pattern = r"姓名：(\w+)"
    # 匹配年龄后面的数字
    age_pattern = r"(\d+) 岁"

    # 查找所有匹配项
    # name_match = re.search(name_pattern, text)
    age_match = re.search(age_pattern, text)

    # if name_match and age_match:
    if age_match:
        # name = name_match.group(1)
        age = age_match.group(1)
        # return {"name": name, "age": age}
        return age
    else:
        return None


def process_row(file_id, project_no=None):
    print('开始处理下面的row了！！！')
    task = MedicalFile.objects.filter(id=file_id, delete_flag=0).first()
    content_type = task.content_type
    if not task:
        print(f"没有发现任务")
        return
    medical_info_lite = MedicalInfoLite.objects.filter(file_id=file_id, delete_flag=0).first()

    # 记录任务开始时间
    medical_info_lite.preprocess_start_time = datetime.now()
    medical_info_lite.mask_status = 'IN_PROGRESS'
    medical_info_lite.ocr_status = 'IN_PROGRESS'
    medical_info_lite.ocr_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    medical_info_lite.save()

    print(file_id)
    minio_client = get_minio_client()
    # for index, row in df2.iterrows():
    try:
        bucket_name = task.bucket_name
        object_name = task.object_name
        original_filename = task.original_filename
        # 从 MinIO 中获取对象
        response = minio_client.get_object(bucket_name, object_name)
        # 读取对象内容到内存中
        data = response.read()

        both_conditions_false = True
        if original_filename[-3:].lower() == 'pdf':
            print('pdf!!!')
            # 将数据加载到 PDF 文档对象中
            pdf_doc = fitz.open(stream=io.BytesIO(data), filetype="pdf")
            page_count = pdf_doc.page_count
            # 遍历 PDF 的每一页
            image_list = []
            all_ocr_boxes = []  # 收集所有页面的ocr_box信息
            matrix = fitz.Matrix(2, 2)
            # model_invocation_log_instances = []  # Disabled: HIPAA mode doesn't use AI model logging
            title_text = "[{}]".format(original_filename)
            ocr_text_list = []
            ocr_text_list_mask = []
            for page_num in range(pdf_doc.page_count):
                page_text = "[Page {}]".format(page_num + 1)
                page = pdf_doc.load_page(page_num)
                # 将页面转换为图像
                pix = page.get_pixmap(matrix=matrix)
                # 创建图像字节流
                img_bytes = pix.tobytes()
                try:
                    # 调用的ocr_desensitive函数，返回4元组：(image, input_text, masked_text, ocr_box)
                    # 注释掉的原8元组调用：
                    # image, inputText, outputText, think, generated_tokens, ocr_text, ocr_text_mask = ocr_desensitive(img_bytes, 6)
                    image, input_text, masked_text, ocr_box = ocr_desensitive(img_bytes, project_no=project_no)

                    # 更新ocr_box的页码信息并收集
                    ocr_box["page"] = page_num + 1
                    all_ocr_boxes.append(ocr_box)

                    # 为了保持后续代码兼容，将返回值映射到原有变量名
                    ocr_text = input_text      # 原始OCR文本
                    ocr_text_mask = masked_text  # 脱敏后的文本
                    ocr_text_list_temp = os.linesep.join([title_text, page_text, ocr_text])
                    ocr_text_list.append(ocr_text_list_temp)
                    ocr_text_list_mask_temp = os.linesep.join([title_text, page_text, ocr_text_mask])
                    ocr_text_list_mask.append(ocr_text_list_mask_temp)

                    # 注释掉AI相关的数据库保存逻辑，因为HIPAA模式不使用AI模型
                    # Disabled: HIPAA mode doesn't use AI models, so AI-related fields are None/empty
                    # result = {}
                    # result['task_id'] = file_id
                    # result['create_user'] = medical_info_lite.create_user
                    # result['create_name'] = medical_info_lite.create_name
                    # result['category'] = 'SENSITIVE_INFORMATION_RECOGNITION'
                    # result['model_name'] = 'DeepSeek-R1-Distill-Qwen-32B'  # No longer applicable for HIPAA mode
                    # result['start_time'] = start_time
                    # result['end_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    # result['input_text'] = inputText
                    # result['think_text'] = think  # HIPAA mode returns descriptive text, not AI thinking
                    # result['output_text'] = outputText  # HIPAA mode returns None
                    # result['prompt_tokens'] = generated_tokens['prompt_tokens']  # HIPAA mode returns empty dict
                    # result['completion_tokens'] = generated_tokens['completion_tokens']  # HIPAA mode returns empty dict
                    # result['business_id'] = medical_info_lite.patient_id
                    # model_invocation_log_instances.append(ModelInvocationLog(**result))

                    image_list.append(image)
                except Exception as e:
                    # 即使某页处理失败，也要添加空的ocr_box以保持页码一致性
                    all_ocr_boxes.append({
                        "page": page_num + 1,
                        "words_block_list": []
                    })
                    print(f"处理PDF第{page_num + 1}页时出错: {e}")
                    pass
            
            # 创建标准多页ocr_box结构（直接使用数组）
            multi_page_ocr_box = all_ocr_boxes
            print(f"✅ PDF处理完成，共处理{len(all_ocr_boxes)}页，收集到{sum(len(page['words_block_list']) for page in all_ocr_boxes)}个文本块")
            formatted_list = [f"{text1}{os.linesep}{os.linesep}" for text1 in ocr_text_list]
            combined_text = "".join(formatted_list).rstrip(os.linesep)
            formatted_list_mask = [f"{text1}{os.linesep}{os.linesep}" for text1 in ocr_text_list_mask]
            combined_text_mask = "".join(formatted_list_mask).rstrip(os.linesep)
            medical_info_lite.ocr_text = combined_text
            medical_info_lite.ocr_text_mask = combined_text_mask
            medical_info_lite.ocr_box = multi_page_ocr_box  # 保存多页ocr_box信息
            medical_info_lite.ocr_status = 'COMPLETED'
            medical_info_lite.save()
            # ModelInvocationLog.objects.bulk_create(model_invocation_log_instances)  # Disabled: HIPAA mode doesn't use AI models
            file_bytes = io.BytesIO()
            if image_list:
                # 取列表中的第一个 Image 对象作为基础
                first_image = image_list[0]
                # 使用 save 方法将 PDF 内容保存到 BytesIO 对象中
                first_image.save(file_bytes, save_all=True, append_images=image_list[1:], format='PDF')
                # 将文件指针移动到流的开头，以便后续读取
                file_bytes.seek(0)
                print('PDF 文件已成功保存到流中。')
            else:
                print('没有可用的图片，无法创建 PDF 文件。')
            pdf_doc.close()
            both_conditions_false = False
        if original_filename.lower().endswith('.docx') or original_filename.lower().endswith('.doc'):
            print('word!!!')
            image_list = []
            all_ocr_boxes = []  # 收集所有页面的ocr_box信息

            images = word_to_image_binary_list(data, '.{}'.format(original_filename.split('.')[1]))
            page_count = len(images)
            # model_invocation_log_instances = []
            title_text = "[{}]".format(original_filename)
            ocr_text_list = []
            ocr_text_list_mask = []
            for page_num in range(len(images)):
                page_text = "[Page {}]".format(page_num + 1)

                try:
                    # 调用的ocr_desensitive函数，返回4元组，但Word文档只需要image
                    image, input_text, masked_text, ocr_box = ocr_desensitive(images[page_num], project_no=project_no)  # Only need image for Word docs
                    
                    # 更新ocr_box的页码信息并收集
                    ocr_box["page"] = page_num + 1
                    all_ocr_boxes.append(ocr_box)

                    ocr_text = input_text  # 原始OCR文本
                    ocr_text_mask = masked_text  # 脱敏后的文本
                    ocr_text_list_temp = os.linesep.join([title_text, page_text, ocr_text])
                    ocr_text_list.append(ocr_text_list_temp)
                    ocr_text_list_mask_temp = os.linesep.join([title_text, page_text, ocr_text_mask])
                    ocr_text_list_mask.append(ocr_text_list_mask_temp)
                    # 注释：Word文档处理中不保存AI相关数据到数据库，因为HIPAA模式不使用AI模型
                    # Note: No AI-related data saved for Word documents as HIPAA mode doesn't use AI models
                    image_list.append(image)
                except Exception as e:
                    # 即使某页处理失败，也要添加空的ocr_box以保持页码一致性
                    all_ocr_boxes.append({
                        "page": page_num + 1,
                        "words_block_list": []
                    })
                    print(f"处理Word第{page_num + 1}页时出错: {e}")
                    pass
            
            # 创建标准多页ocr_box结构（直接使用数组）
            multi_page_ocr_box = all_ocr_boxes
            print(f"✅ Word处理完成，共处理{len(all_ocr_boxes)}页，收集到{sum(len(page['words_block_list']) for page in all_ocr_boxes)}个文本块")
            formatted_list = [f"{text1}{os.linesep}{os.linesep}" for text1 in ocr_text_list]
            combined_text = "".join(formatted_list).rstrip(os.linesep)
            formatted_list_mask = [f"{text1}{os.linesep}{os.linesep}" for text1 in ocr_text_list_mask]
            combined_text_mask = "".join(formatted_list_mask).rstrip(os.linesep)
            medical_info_lite.ocr_text = combined_text
            medical_info_lite.ocr_text_mask = combined_text_mask
            medical_info_lite.ocr_box = multi_page_ocr_box  # 保存多页ocr_box信息
            medical_info_lite.ocr_status = 'COMPLETED'
            medical_info_lite.save()
            original_filename = original_filename.split('.')[0] + '.pdf'
            content_type = 'application/pdf'
            # file_bytes = images_to_word_stream(image_list)
            # both_conditions_false = False
            file_bytes = io.BytesIO()
            if image_list:
                # 取列表中的第一个 Image 对象作为基础
                first_image = image_list[0]
                # 使用 save 方法将 PDF 内容保存到 BytesIO 对象中
                first_image.save(file_bytes, save_all=True, append_images=image_list[1:], format='PDF')
                # 将文件指针移动到流的开头，以便后续读取
                file_bytes.seek(0)
                print('PDF 文件已成功保存到流中。')
            else:
                print('没有可用的图片，无法创建 PDF 文件。')
            both_conditions_false = False
        if original_filename.lower().endswith('.jpg') or original_filename.lower().endswith(
                '.jpeg') or original_filename.lower().endswith('.png'):
            page_count = 1
            ocr_text_list = []
            ocr_text_list_mask = []
            title_text = "[{}]".format(original_filename)
            print('图片！！！')
            file_bytesX = data
            # 调用的ocr_desensitive函数，返回4元组：(image, input_text, masked_text, ocr_box)
            # 注释掉的原8元组调用：
            # image, inputText, outputText, think, generated_tokens, ocr_text, ocr_text_mask = ocr_desensitive(file_bytesX, 6)
            image, input_text, masked_text, ocr_box = ocr_desensitive(file_bytesX, project_no=project_no)

            # 为单页图片创建标准ocr_box结构（直接使用数组）
            single_page_ocr_box = [ocr_box]

            # 为了保持后续代码兼容，将返回值映射到原有变量名
            ocr_text = input_text      # 原始OCR文本
            ocr_text_mask = masked_text  # 脱敏后的文本
            ocr_text_list_temp = os.linesep.join([title_text, ocr_text])
            ocr_text_list.append(ocr_text_list_temp)
            ocr_text_list_mask_temp = os.linesep.join([title_text, ocr_text_mask])
            ocr_text_list_mask.append(ocr_text_list_mask_temp)
            formatted_list = [f"{text1}{os.linesep}{os.linesep}" for text1 in ocr_text_list]
            combined_text = "".join(formatted_list).rstrip(os.linesep)
            formatted_list_mask = [f"{text1}{os.linesep}{os.linesep}" for text1 in ocr_text_list_mask]
            combined_text_mask = "".join(formatted_list_mask).rstrip(os.linesep)
            medical_info_lite.ocr_text = combined_text
            medical_info_lite.ocr_text_mask = combined_text_mask
            medical_info_lite.ocr_box = single_page_ocr_box  # 保存单页ocr_box信息
            medical_info_lite.ocr_status = 'COMPLETED'
            medical_info_lite.save()

            # 注释掉AI相关的数据库保存逻辑，因为HIPAA模式不使用AI模型
            # Disabled: HIPAA mode doesn't use AI models, so AI-related fields are None/empty
            # result = {}
            # result['task_id'] = file_id
            # result['create_user'] = medical_info_lite.create_user
            # result['create_name'] = medical_info_lite.create_name
            # result['category'] = 'SENSITIVE_INFORMATION_RECOGNITION'
            # result['model_name'] = 'DeepSeek-R1-Distill-Qwen-32B'  # No longer applicable for HIPAA mode
            # result['start_time'] = start_time
            # result['end_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            # result['input_text'] = inputText
            # result['think_text'] = think  # HIPAA mode returns descriptive text, not AI thinking
            # result['output_text'] = outputText  # HIPAA mode returns None
            # result['prompt_tokens'] = generated_tokens['prompt_tokens']  # HIPAA mode returns empty dict
            # result['completion_tokens'] = generated_tokens['completion_tokens']  # HIPAA mode returns empty dict
            # result['business_id'] = medical_info_lite.patient_id
            # ModelInvocationLog.objects.create(**result)
            format_mapping = {
                'jpg': 'JPEG',
                'jpeg': 'JPEG',
                'png': 'PNG',
                # 'bmp': 'BMP',
                # 'gif': 'GIF'
            }
            #
            # # 获取对应的格式
            image_format = format_mapping.get(original_filename.split('.')[1].lower(), 'PNG')
            #
            if image.mode == 'RGBA':
                image = image.convert('RGB')
            file_bytes = io.BytesIO()

            # 将 Image 对象保存到 BytesIO 对象中，这里以 PNG 格式为例，可根据实际需求修改
            image.save(file_bytes, format=image_format)
            file_bytes.seek(0)

            both_conditions_false = False
        if both_conditions_false:
            print(file_id)
            print('文件类型不属于处理范畴！！！')
            medical_info_lite.mask_status = 'CANCELLED'
            medical_info_lite.ocr_status = 'CANCELLED'
            medical_info_lite.save()
            return True

        class FileStreamWithName:
            def __init__(self, stream, filename):
                self.stream = stream
                self.filename = filename
                self.size = len(stream.getvalue())
                self.content_type = content_type

            @property
            def name(self):
                return self.filename

        # 创建包含文件流和文件名的对象
        file = FileStreamWithName(file_bytes, original_filename)
        import uuid

        _, ext = os.path.splitext(file.name)
        object_name = f"{uuid.uuid4().hex}{ext}"
        # 保存文件
        try:
            minio_client = get_minio_client()
            # Ensure bucket exists
            if not minio_client.bucket_exists(bucket_name):
                minio_client.make_bucket(bucket_name)
            # Upload file to MinIO
            minio_client.put_object(
                bucket_name=bucket_name,
                object_name=object_name,
                data=file_bytes,  # 直接使用 output 作为 data 参数
                length=file.size,
                part_size=1024 * 1024 * 5,
                content_type=content_type
            )
        except Exception as e:
            # logger = logging.getLogger(__name__)
            # logger.error(e)
            raise Exception(f"文件上传失败：{e}")

        # 数据入库
        output = file_bytes

        with transaction.atomic():
            # 计算 hash
            hash_object = hashlib.sha256(output.getvalue())
            hash = hash_object.hexdigest()
            # now = datetime.now()  # Disabled: not needed
            # formatted_time = now.strftime("%Y%m%d%H%M%S")  # Disabled: not used
            material_file = {
                'original_filename': file.name,
                'bucket_name': bucket_name,
                # 'version': 'v' + str(formatted_time),  # Disabled: version not used
                'object_name': object_name,
                'content_type': content_type,
                'size': file.size,
                'hash': hash,

            }
        task_info = MedicalFileMasked.objects.create(**material_file)
        id_value = task_info.id

        medical_info_lite.mask_status = 'COMPLETED'
        medical_info_lite.file_masked_id = id_value
        medical_info_lite.page_count = page_count
        
        # 记录任务结束时间和计算耗时
        medical_info_lite.preprocess_end_time = datetime.now()
        if medical_info_lite.preprocess_start_time:
            duration = medical_info_lite.preprocess_end_time - medical_info_lite.preprocess_start_time
            medical_info_lite.preprocess_duration = duration.total_seconds()
        
        medical_info_lite.save()

        file_bytes.close()
    except Exception as e:
        print(e)
        print('文件处理错误！！！')
        print(file_id)

        medical_info_lite.mask_status = 'ERROR'
        medical_info_lite.ocr_status = 'ERROR'
        medical_info_lite.save()

    return True


def main():
    parser = argparse.ArgumentParser(description="病历归集打码任务")
    parser.add_argument('--task_id', type=int, required=True, help="task_id")
    parser.add_argument('--project_no', type=str, required=False, help="project_no")
    # parser.add_argument('--create_user', type=str, required=True, help="create_user")
    # parser.add_argument('--create_name', type=str, required=True, help="create_name")
    args = parser.parse_args()
    process_row(args.task_id, args.project_no)


if __name__ == "__main__":
    main()
    close_db_connection()