
from django.db.models import Q
from django_filters import rest_framework as filters
from . import models


class PrivacyRuleConfigListFilter(filters.FilterSet):
    field_name = filters.CharFilter(field_name='field_name', lookup_expr='icontains', label='隐私字段名称', required=False)
    status_ind = filters.CharFilter(field_name='status_ind', label='字段状态', required=False)

    class Meta:
        model = models.PrivacyRuleConfig
        fields = ['field_name', 'status_ind']
