from drf_spectacular.openapi import AutoSchema


class CustomSchema(AutoSchema):

    def _get_response_bodies(self, *args, **kwargs):
        responses = super()._get_response_bodies(*args, **kwargs)
        self.path: str
        if self.path and self.path.startswith('/api/v1/'):
            responses.update({
                '401': {
                    'content': {
                        'application/json': {
                            'schema': {
                                "type": "object",
                                "properties": {
                                    "code": {
                                        "type": "string",
                                        "title": "错误代码",
                                        "example": str.upper("not_authenticated")
                                    },
                                    "message": {
                                        "type": "string",
                                        "title": "错误描述"
                                    },
                                    "detail": {
                                        "type": "object",
                                        "title": "错误详情"
                                    }
                                }
                            }
                        }
                    },
                    'description': 'Unauthorized'
                },
                '403': {
                    'content': {
                        'application/json': {
                            'schema': {
                                "type": "object",
                                "properties": {
                                    "code": {
                                        "type": "string",
                                        "title": "错误代码",
                                        "example": str.upper("permission_denied")
                                    },
                                    "message": {
                                        "type": "string",
                                        "title": "错误描述"
                                    },
                                    "detail": {
                                        "type": "object",
                                        "title": "错误详情"
                                    }
                                }
                            }
                        }
                    },
                    'description': 'Forbidden'
                }
            })
        return responses
