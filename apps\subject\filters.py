
from django.db.models import Q
from django_filters import rest_framework as filters


class ProjectSiteSubjectFilter(filters.FilterSet):
    project_id = filters.CharFilter(field_name='project_id', label='项目ID')
    project_no = filters.CharFilter(field_name='project__project_no', lookup_expr='exact', label='项目号')
    project_site_id = filters.Char<PERSON>ilter(field_name='project_site_id', label='项目中心ID')
    hosp_department_no = filters.CharFilter(field_name='project_site__hosp_department_no', label='项目中心编号')
    real_name = filters.CharFilter(field_name='real_name', lookup_expr='icontains', label='受试者姓名（模糊查询）')
    short_name = filters.CharFilter(field_name='short_name', lookup_expr='icontains', label='受试者姓名缩写（模糊查询）')
    code = filters.Char<PERSON>ilter(field_name='code', lookup_expr='icontains', label='受试者编号（模糊查询）')
    status = filters.CharFilter(field_name='status', lookup_expr='exact', label='受试者状态')
    query = filters.CharFilter(method='filter_by_query', label='综合查询（模糊匹配）')

    def filter_by_query(self, queryset, name, value):
        """
        综合查询，OR 关系
        """
        return queryset.filter(
            Q(real_name__icontains=value) | Q(short_name__icontains=value) | Q(code__icontains=value)
        )


class SubjectEpochFilter(filters.FilterSet):
    subject_id = filters.CharFilter(field_name='subject_id', label='受试者ID', required=True)


class SubjectVisitFilter(filters.FilterSet):
    subject_id = filters.CharFilter(field_name='subject_id', label='受试者ID', required=True)
    subject_epoch_id = filters.CharFilter(field_name='subject_epoch_id', label='受试者阶段ID')


class SubjectItemFilter(filters.FilterSet):
    subject_id = filters.CharFilter(field_name='subject_id', label='受试者ID', required=True)
    subject_visit_id = filters.CharFilter(field_name='subject_visit_id', label='受试者访视ID')


class AETrackerFilter(filters.FilterSet):
    subject_id = filters.CharFilter(field_name='subject_id', label='受试者ID', required=True)
    subject_item_label = filters.CharFilter(field_name='subject_item__label',
                                            lookup_expr='icontains', label='操作项名称（模糊匹配）')
    abnormal_flag = filters.CharFilter(field_name='abnormal_flag', label='是否为异常值；0：正常， 1：异常')
    test_name = filters.CharFilter(field_name='test_name', lookup_expr='icontains', label='检查名称（模糊匹配）')


class TestResultsFilter(filters.FilterSet):
    subject_id = filters.CharFilter(field_name='subject_id', label='受试者ID', required=True)
    subject_item_id = filters.CharFilter(field_name='subject_item_id', required=False)