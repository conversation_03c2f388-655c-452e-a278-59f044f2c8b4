
from django.db.models import Q
from django_filters import rest_framework as filters
from . import models


class SubjectMedicalInfoFilter(filters.FilterSet):
    # project_id = filters.Char<PERSON>ilter(field_name='project__project_id', label='项目ID', required=True)
    # project_site_id = filters.CharFilter(field_name='project_site__project_site_id', label='项目中心ID', required=True)
    subject_id = filters.CharFilter(field_name='subject__subject_id', label='受试者ID', required=True)
    subject_item_id = filters.Char<PERSON>ilter(field_name='subject_item__subject_item_id', label='受试者操作项ID', required=False)

    class Meta:
        model = models.SubjectMedicalInfo
        # fields = ['project_id', 'project_site_id', 'subject_id', 'subject_item_id']
        fields = ['subject_id', 'subject_item_id']
