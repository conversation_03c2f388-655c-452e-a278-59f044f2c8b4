# -*- coding: utf-8 -*-
"""
简化测试脚本

测试并行处理模块的基础功能，不依赖Django
"""

import os
import sys

def test_config_import():
    """测试配置模块导入"""
    try:
        from script.test_result_format_ae_ocr.simple_config import SimpleParallelConfig, SimpleConfigManager
        print("✅ 配置模块导入成功")
        
        # 测试配置类
        config = SimpleParallelConfig()
        print(f"  - 默认并行模式: {config.enable_parallel}")
        print(f"  - 默认并发数: {config.max_workers}")
        
        # 测试配置管理器
        manager = SimpleConfigManager()
        current_config = manager.get_config()
        print(f"  - 管理器获取配置成功: {type(current_config)}")
        
        return True
    except Exception as e:
        print(f"❌ 配置模块导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_parallel_processor_import():
    """测试并行处理器导入"""
    try:
        from script.test_result_format_ae_ocr.parallel_processor import (
            PageData, PageResult, FileResult, PageProcessor, 
            ResultAggregator, ParallelPageProcessor, ParallelIntegrationAdapter
        )
        print("✅ 并行处理器模块导入成功")
        
        # 测试基础类
        page_data = PageData(
            page_id="test_001",
            page_content="测试内容",
            page_num=1,
            file_id="file_001",
            ocr_blocks=[],
            metadata={},
            task_info={}
        )
        print(f"  - PageData创建成功: {page_data.page_id}")
        
        # 测试适配器
        adapter = ParallelIntegrationAdapter(enable_parallel=False, max_workers=1)
        print(f"  - 适配器创建成功: {type(adapter)}")
        
        return True
    except Exception as e:
        print(f"❌ 并行处理器模块导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_transaction_manager_import():
    """测试事务管理器导入"""
    try:
        from script.test_result_format_ae_ocr.simple_transaction import SimpleTransactionManager
        print("✅ 事务管理器模块导入成功")
        
        # 测试事务管理器
        manager = SimpleTransactionManager()
        print(f"  - 事务管理器创建成功: {type(manager)}")
        
        return True
    except Exception as e:
        print(f"❌ 事务管理器模块导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_integration():
    """测试主脚本集成"""
    try:
        # 检查主脚本是否包含新的函数
        with open('script/test_result_format.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键函数是否存在
        required_functions = [
            '_request_llm_api1_parallel',
            '_request_llm_api1_serial', 
            '_process_with_legacy_transaction'
        ]
        
        missing_functions = []
        for func in required_functions:
            if func not in content:
                missing_functions.append(func)
        
        if missing_functions:
            print(f"❌ 主脚本缺少函数: {missing_functions}")
            return False
        
        print("✅ 主脚本集成检查通过")
        print("  - 包含并行处理函数")
        print("  - 包含串行处理函数")
        print("  - 包含回退事务处理函数")
        
        return True
    except Exception as e:
        print(f"❌ 主脚本集成检查失败: {e}")
        return False

def test_file_structure():
    """测试文件结构"""
    required_files = [
        'script/test_result_format_ae_ocr/simple_config.py',
        'script/test_result_format_ae_ocr/simple_transaction.py',
        'script/test_result_format_ae_ocr/parallel_processor.py',
        'script/test_result_format_ae_ocr/config_switch.py',
        'script/test_result_format_ae_ocr/并行设计方案.md'
    ]
    
    missing_files = []
    existing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            existing_files.append(file_path)
        else:
            missing_files.append(file_path)
    
    print(f"📁 文件结构检查:")
    print(f"  - 存在的文件: {len(existing_files)}")
    for file_path in existing_files:
        print(f"    ✅ {file_path}")
    
    if missing_files:
        print(f"  - 缺失的文件: {len(missing_files)}")
        for file_path in missing_files:
            print(f"    ❌ {file_path}")
        return False
    
    print("✅ 文件结构检查通过")
    return True

def main():
    """主测试函数"""
    print("🧪 开始简化集成测试")
    print("=" * 60)
    
    tests = [
        ("文件结构", test_file_structure),
        ("配置模块导入", test_config_import),
        ("并行处理器导入", test_parallel_processor_import),
        ("事务管理器导入", test_transaction_manager_import),
        ("主脚本集成", test_main_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 测试: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - 通过")
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"💥 {test_name} - 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有基础测试通过！")
        print("📝 下一步:")
        print("  1. 激活conda环境: conda activate smo-ai-backend")
        print("  2. 运行完整测试: python test_integration.py")
        print("  3. 测试配置切换: python script/test_result_format_ae_ocr/config_switch.py")
    else:
        print("⚠️ 部分测试失败，请检查相关模块")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
