
import os
import json


from docx import Document
from docx.oxml.ns import qn
from docx.shared import Pt
from common.constants import *
from common.utils import get_general_prompt_sql
from django.db import transaction, connection
from common.ocr_tools import  extract_text_from_folder, doc_to_docx
from common.llm_tools import deepseek_r1_llama_70b, deepseek_r1_qwen_32b, get_json_from_ai_word_reply, pangu_v35_128k, get_think_text_from_ai_reply,qwen3_32b
from datetime import datetime
from apps.medical_collection.models import MedicalCollectionTask
from apps.system.models import ModelInvocationLog

def activate_conn(connection):
    try:
        connection.connect()
        if not connection.is_usable():
            connection.close()
            connection.connect()
    except:
        pass
    return connection

# PROMPT_TEMPLATE = """
# 【患者信息】
# 第一部分：病历和病理报告
#
# {ocr_text}
#
#
# 【任务】
# 你是一名专业的临床医生，请根据以上【患者信息】生成一篇病例总结报告，【患者信息】包括第一部分：病历和病理报告。请按照【输出格式】的样式输出，输出内容包括患者的“主诉”，“病史”，“就诊情况”，“诊断名称”，“处理措施”等信息。
# 要求：
# 1、输出内容严格来自【患者信息】，并确保抽取的信息能在【患者信息】中找到对应的内容。
# 2、不要编造内容，不要添加【患者信息】中没有提到的信息，对于找不到的内容，输出“无”。
# 3、保持信息的完整性和准确性，不要遗漏。
# 4、输出内容不能来自【输出格式】中内容，可以参考【输出格式】的输出风格。
# 5、请假设在提问时没有相应文本作为参考，因此不要使用“根据参考资料”、“在参考资料中”、“这篇文章”、“这些”、“本文”等指代词。
#
# 【输出格式】
# {word_text}
#
# """


# PROMPT_TEMPLATE = """
# 【患者信息】
# 第一部分：病历和病理报告

# {ocr_text}


# {word_text}

# """

PROMPT_TEMPLATE = get_general_prompt_sql(MEDICAL_SUMMARY_PROMPT)


# 拼接大模型提示词 prompt（传入 OCR 文本与 CRF 表头结构）
def assembly_prompt(ocr_text, word_text):
    prompt = PROMPT_TEMPLATE.format(ocr_text=ocr_text, word_text=word_text)
    return prompt


def word_file_to_json(filename):
    """
    从 Word 文档（支持 .doc 和 .docx）提取段落内容，返回 JSON 列表
    """
    # 如果是 .doc 文件，则先转为 .docx
    ext = os.path.splitext(filename)[1].lower()
    if ext == ".doc":
       filename = doc_to_docx(filename)

    # 使用 python-docx 读取 .docx 文档
    doc = Document(filename)
    result = []

    for para in doc.paragraphs:
        text = para.text.strip()
        if text:
            result.append({"paragraph": text})

    return result


# # 将 word 模版 转换为 JSON 格式
# def word_file_to_json(filename):
#     doc = Document(filename)
#     result = []
#
#     for para in doc.paragraphs:
#         text = para.text.strip()
#         if text:
#             result.append({"paragraph": text})
#
#     return result

# 将 JSON 数据转换成 WORD 文件格式并保存
# def word_json_data_to_file(data, output_filename):
#     # 创建 Word 文档对象
#     doc = Document()
#
#     style = doc.styles['Normal']
#     style.font.name = u'宋体'
#     style._element.rPr.rFonts.set(qn('w:eastAsia'), u'宋体')
#     style.font.size = Pt(11)
#
#     # 遍历 JSON 中的段落
#     for item in data:
#         paragraph = item.get("paragraph", "").strip()
#         if paragraph:
#             doc.add_paragraph(paragraph)
#
#     # 保存 Word 文件
#     doc.save(output_filename)
#
#     return output_filename
def word_json_data_to_file(data, output_filename):
    """
    将 JSON 结构的段落内容写入 Word 文档，支持标题识别、段落分割、美化输出。
    """
    doc = Document()

    # 设置默认字体：宋体 + 11pt
    style = doc.styles['Normal']
    style.font.name = u'宋体'
    style._element.rPr.rFonts.set(qn('w:eastAsia'), u'宋体')
    style.font.size = Pt(11)

    for item in data:
        paragraph = item.get("paragraph", "").strip()
        if not paragraph:
            continue
        lines = paragraph.splitlines()
        for line in lines:
            line = line.strip()
            if not line:
                continue
            # if line.startswith("### "):
            #     doc.add_heading(line[4:], level=1)
            # elif line.startswith("#### "):
            #     doc.add_heading(line[5:], level=2)
            # else:
            doc.add_paragraph(line)
        doc.add_paragraph("")

    doc.save(output_filename)
    return output_filename


# 将 OCR 文本 与 Word JSON 结构输入给大模型，返回 JSON 格式数据
def process_word_header(ocr_content, word_header_json, task_id, model_name, page_count=0):
    word_header_json_text = json.dumps(word_header_json, ensure_ascii=False)

    # 如果数据太长则分段处理
    if len(word_header_json_text) > 3000 and len(word_header_json) >= 2:
        half_length = len(word_header_json) // 2
        first_half = word_header_json[:half_length]
        second_half = word_header_json[half_length:]
        return process_word_header(ocr_content, first_half, task_id, model_name, page_count) + process_word_header(ocr_content, second_half, task_id, model_name, page_count)
    else:
        # 构建 prompt 并调用大模型接口
        prompt = assembly_prompt(ocr_content, json.dumps(word_header_json, ensure_ascii=False))
        print(prompt, "------prompt")
        start_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        if model_name == 'DeepSeek':
            response = deepseek_r1_qwen_32b(prompt)
        if model_name == 'qwen3':
            response = qwen3_32b(prompt)
        print(response, "------{}response".format(model_name))

        think_text = get_think_text_from_ai_reply(response)
        print(think_text)
        result = {}
        outputText = response.json()
        # think = response.json()["choices"][0]["message"]["content"]
        # start_index = think.find("<think>") + len("<think>")
        # end_index = think.find("</think>")
        # think = think[start_index:end_index]
        generated_tokens = response.json().get('usage', {})
        activate_conn(connection)
        task = MedicalCollectionTask.objects.filter(id=task_id, delete_flag=0).first()
        result['task_id'] = task_id
        result['create_user'] = task.create_user
        result['create_name'] = task.create_name
        result['category'] = 'MEDICAL_RECORD_SUMMARY'
        if model_name == 'DeepSeek':
            result['model_name'] = 'DeepSeek-R1-Distill-Qwen-32B'
        if model_name == 'qwen3':
            result['model_name'] = 'qwen3_32b'
        result['start_time'] = start_time
        result['end_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        result['input_text'] = prompt
        result['think_text'] = think_text
        result['output_text'] = outputText
        result['prompt_tokens'] = generated_tokens['prompt_tokens']
        result['completion_tokens'] = generated_tokens['completion_tokens']
        result['business_id'] = task.patient_id
        result['page_count'] = page_count  # 添加页数字段
        activate_conn(connection)
        ModelInvocationLog.objects.create(**result)
        # 提取模型的回复内容

        print(response.json().get("choices", [{}])[0].get("message", {}).get("content", ""))
        return get_json_from_ai_word_reply(response, model_name)



# 主入口：生成 Excel 输出文件（最终结构化结果）
# 修改 generate_word_file 函数
def generate_word_file(ocr_text, word_header_file, output_filename, task_id, model_name, page_count=0):
    # 步骤1：从文件夹提取 OCR 文本（病历内容）
    ocr_content = ocr_text
    print(ocr_content,"------ocr_content")

    # 限制最大字符长度，避免 prompt 超限
    ocr_content = ocr_content[:80000]

    # 步骤2：解析 WORD 表头为 JSON 格式
    word_header_json = word_file_to_json(word_header_file)
    print(word_header_json,"------word_header_json")

    word_header_json_text = json.dumps(word_header_json, ensure_ascii=False)

    # 步骤3：调用大模型处理 OCR 内容和表头结构，获取带值的字段 JSON
    # data = process_word_header(ocr_content, word_header_json, task_id, model_name, page_count)
    # print(data,"------data")

    results = []
    for idx, section in enumerate(split_by_separator(word_header_json, "###")):
        data = process_word_header(ocr_content, section, task_id, model_name, page_count)
        print(data, "------data")
        results.append({"paragraph": data})
    print(results, "------results")

    # 步骤4：写入 Excel，使用 AI 结果 或 fallback 原始表头
    if results:
        word_json_data_to_file(results, output_filename)
    else:
        word_json_data_to_file(word_header_json, output_filename)

    print(output_filename, "------output_filename")


def split_by_separator(text, separator="###"):
    """
    分段函数：按分隔符切割并清理空白段
    """
    full_text = "\n".join(item["paragraph"] for item in text)
    return [part.strip() for part in full_text.split("###") if part.strip()]


# 测试入口（用于开发验证）
if __name__ == "__main__":
    # 示例路径请替换为你的实际路径
    generate_word_file(
        r"C:\files",
        #r'C:\crf.DOCX',
        r'C:\病历小结提示词429.docx',
        r'./crf.docx'
    )
    # data = [{
    #             'paragraph': '医生万建绩在2024年3月21日08:20向患者李仲添介绍了多中心、随机、双盲、平行、安慰剂对照的临床Ⅲ期研究以评价皮下注射JS005注射液治疗成人中重度慢性斑块状银屑病的疗效和安全性，方案编号:JS005-005-III-PsO，版本号:2.0，版本日期:2023年11月06日。试验设计、试验目的、试验流程及试验可能存在的风险和受益，以及详细介绍了探索性研究的目的、流程以及风险和获益等，给予患者充分的时间思考。患者在充分理解所有试验相关信息后，对避孕措施和银屑病数码照片采集表示疑问，医生万建绩详细解答了患者的疑问，最终，患者决定参与本研究并同意所有的试验条款，于2024年3月21日08:46签署知情同意书，一式两份。已签署的知情同意书一份原件保存在研究中心，一份原件已提供给受试者。即日进行入组筛选，受试者筛选号CHN013013。'},
    #         {
    #             'paragraph': '### 受试者病历\n\n#### 基本信息\n- **姓名**：李仲添  \n- **性别**：男  \n- **年龄**：52岁  \n- **婚姻状况**：未提及  \n- **民族**：汉族  \n\n---\n\n#### 现病史\n患者李仲添，52岁，男性，因“银屑病9月余”于2024年3月21日签署知情同意书...'},
    #         {
    #             'paragraph': '### 受试者筛选期体格检查报告\n\n#### 1. 基础检验检查指标\n- **体温**：36.0℃（2024-03-21），36.9℃（2024-03-28）...'},
    #         {
    #             'paragraph': '### 筛选成功模板\n\n**主诉：**  \n银屑病9月余，躯干、头皮出现红斑、鳞屑，伴瘙痒。\n\n**病史：**  \n患者李仲添，男性，52岁，否认高血压史、冠心病史...'}
    #         ]
    #
    # word_json_data_to_file(data, "./crf.docx")

