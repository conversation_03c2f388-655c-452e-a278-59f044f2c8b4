# Generated by Django 4.1.5 on 2025-05-30 09:56

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("system", "0005_remove_modelinvocationlog_patient_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="PrivacyRuleConfig",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "delete_flag",
                    models.SmallIntegerField(
                        choices=[(0, "未删除"), (1, "已删除")],
                        db_index=True,
                        default=0,
                        verbose_name="删除标志（0：未删除；1：已删除）",
                    ),
                ),
                (
                    "filed_name",
                    models.CharField(default="", max_length=255, verbose_name="隐私字段名称"),
                ),
                (
                    "status_ind",
                    models.CharField(
                        choices=[(1, "打码字段开关状态开"), (0, "打码字段开关状态关")],
                        db_index=True,
                        default=0,
                        max_length=50,
                        verbose_name="字段状态",
                    ),
                ),
                (
                    "create_user",
                    models.CharField(max_length=255, null=True, verbose_name="创建人工号"),
                ),
                (
                    "create_name",
                    models.CharField(max_length=255, null=True, verbose_name="创建人姓名"),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True, db_index=True, verbose_name="创建时间"
                    ),
                ),
                (
                    "update_user",
                    models.CharField(max_length=255, null=True, verbose_name="更新人工号"),
                ),
                (
                    "update_name",
                    models.CharField(max_length=255, null=True, verbose_name="更新人姓名"),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name="更新时间"
                    ),
                ),
            ],
            options={
                "verbose_name": "打码字段配置",
                "verbose_name_plural": "打码字段配置",
                "db_table": "privacy_rule_config",
            },
        ),
    ]
