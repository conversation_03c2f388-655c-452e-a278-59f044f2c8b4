import os
import logging
import uuid
from datetime import timed<PERSON>ta

from minio import <PERSON>o

from django.http import FileResponse
from django.conf import settings
from django.shortcuts import render
from django.db import transaction
from django.db.models import Subquery, OuterRef
from django.db.models import Count
from rest_framework.viewsets import GenericViewSet
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import serializers
from rest_framework.fields import <PERSON>r<PERSON><PERSON>
from rest_framework import filters, viewsets
from rest_framework.mixins import ListModelMixin, CreateModelMixin, UpdateModelMixin, DestroyModelMixin
from rest_framework.viewsets import ReadOnlyModelViewSet
from rest_framework.filters import OrderingFilter
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.renderers import BaseRenderer
from rest_framework.exceptions import ValidationError, NotFound, APIException
from rest_framework.parsers import <PERSON>PartParser, FormParser
from rest_framework.permissions import IsAuthenticated
from rest_framework.exceptions import APIException

from django_filters.rest_framework import DjangoFilterBackend

from rest_pandas.views import PandasViewBase
from rest_pandas import PandasExcelRenderer
from openpyxl.styles import Font, Border
from openpyxl.styles import Alignment

from drf_spectacular.utils import extend_schema
from drf_spectacular.utils import extend_schema, OpenApiParameter

from common.auth import ERPSysJWTAuthentication
from common.utils import calculate_file_hash
from common.minio_client import get_minio_client
from common.pagination import StandardResultsSetPagination
from common.ocr_tools import heic_to_jpg_stream, heic_to_jpg_file, doc_to_docx_file, bytes_word_to_pdf

from apps.project.models import Project, ProjectSite
from apps.subject.models import Subject, SubjectItem, SubjectVisit, SubjectEpoch
from . import serializers
from . import models
from . import filters
from apps.ae_tracker.models import AeTrackerTask, TestResult, TestOcrResult
from apps.system.models import OperationLog
import io



class TaskExistsStatusIN_PROGRESS(APIException):
    status_code = status.HTTP_409_CONFLICT
    default_detail = "先前任务流程还未完全结束,正在处理中！！！"
    default_code = "error"


class FileAlreadyExistsError(APIException):
    status_code = status.HTTP_409_CONFLICT
    default_detail = "文件已存在！！！"
    default_code = "error"


logger = logging.getLogger('app')


class BaseAPIView(APIView):
    authentication_classes = [ERPSysJWTAuthentication]
    permission_classes = [IsAuthenticated]


class BaseViewSet(BaseAPIView, GenericViewSet):
    pass


class BaseListViewSet(BaseViewSet, ListModelMixin):
    filter_backends = (DjangoFilterBackend, OrderingFilter)


class SubjectMedicalInfoListViewSet(BaseListViewSet, CreateModelMixin):
    parser_classes = (MultiPartParser, )  # 支持文件上传
    queryset = models.SubjectMedicalInfo.objects.filter(delete_flag=0).select_related(
        'file', 'file_masked', 'project', 'project_site', 'subject')
    serializer_class = serializers.SubjectMedicalInfoSerializer
    filterset_class = filters.SubjectMedicalInfoFilter
    # pagination_class = StandardResultsSetPagination
    ordering_fields = '__all__'
    ordering = ['create_time']

    @extend_schema(
        summary='受试者病历文件列表',
        tags=['访视详情'],
        # request=serializers.ProjectMaterialLatestRequestSerializer,
        responses=serializers.SubjectMedicalInfoSerializer
    )
    def list(self, request, format=None):
        return super().list(request, format)

    @extend_schema(
        summary='上传受试者病历文件',
        tags=['访视详情'],
        request=serializers.SubjectMedicalInfoCreateRequestSerializer,
        responses=serializers.SubjectMedicalInfoFileSerializer
    )
    def create(self, request, format=None):
        file = request.FILES.get('file')
        # project_id = request.POST.get('project_id')
        # project_site_id = request.POST.get('project_site_id')
        subject_id = request.POST.get('subject_id')
        subject_item_id = request.POST.get('subject_item_id')
        item_type = request.POST.get('item_type')

        task = AeTrackerTask.objects.filter(
            subject_id=subject_id,
            subject_item_id=subject_item_id,
            status__in=[AeTrackerTask.IN_PROGRESS, AeTrackerTask.TODO],
            delete_flag=0
        ).order_by('-create_time').first()
        if task:
            raise TaskExistsStatusIN_PROGRESS()

        serializer = serializers.SubjectMedicalInfoCreateRequestSerializer(data=request.data)
        if not serializer.is_valid():
            raise ValidationError(serializer.errors)

        if not file:
            raise ValidationError({'file': ['未提供文件']})
        # if item_type is None:
        #     raise ValidationError({'file': ['请先选择操作项类型']})
        # try:
        #     item_type = int(item_type)
        #     if item_type == 1:
        #         valid_extensions = ['.jpg', '.jpeg', '.png', '.heic']
        #     else:
        #         valid_extensions = ['.jpg', '.jpeg', '.png', '.heic', '.pdf', '.docx', '.doc']
        #
        #     file_extension = '.' + file.name.split('.')[-1].lower()
        #     if file_extension not in valid_extensions:
        #         if item_type == 1:
        #             raise ValidationError({'file': ['检验项目仅支持图片类型文件']})
        #         else:
        #             raise ValidationError({'file': ['检查项目、病史、NA仅支持图片、pdf、word类型文件']})
        # except ValueError:
        #     raise ValidationError({'file': ['操作项类型必须为有效的整数']})

        # 检查数据是否存在
        # try:
        #     project = Project.objects.filter(delete_flag=0).get(project_id=project_id)
        # except Project.DoesNotExist:
        #     raise NotFound({'project_id': ['项目不存在']})

        # try:
        #     project_site = ProjectSite.objects.filter(delete_flag=0).get(project_site_id=project_site_id)
        # except Project.DoesNotExist:
        #     raise NotFound({'project_site': ['项目中心不存在']})

        try:
            subject = Subject.objects.filter(delete_flag=0).get(subject_id=subject_id)
        except Subject.DoesNotExist:
            raise NotFound({'subject_id': ['受试者不存在']})

        project = subject.project
        project_site = subject.project_site

        try:
            subject_item = SubjectItem.objects.filter(delete_flag=0).get(subject_item_id=subject_item_id)
        except Subject.DoesNotExist:
            raise NotFound({'subject_id': ['受试者操作项不存在']})

        # if file.name.lower().endswith('.heic'):
        #     file = heic_to_jpg_file(file)
        #     return file

        # 计算文件的 SHA-256 哈希值
        # hash = calculate_file_hash(file)
        # if models.SubjectMedicalFile.objects.filter(delete_flag=0).filter(
        #     subject_medical_info__delete_flag=0,
        #     subject_medical_info__project_id=project.project_id,
        #     subject_medical_info__project_site_id=project_site.project_site_id,
        #     subject_medical_info__subject_id=subject_id,
        #     subject_medical_info__subject_item_id=subject_item_id,
        #     hash=hash
        # ).exists():
        #     raise FileAlreadyExistsError("文件已存在")
        # original_filename = file.name
        # file_content_type = file.content_type
        # 生成唯一的对象名称
        base_name, ext = os.path.splitext(file.name)
        if ext.lower() == '.heic':
            file = heic_to_jpg_file(file)
            base_name, ext = os.path.splitext(file.name)
            # print(file.content_type)
            # print(file.size)
            # print(file.name)
            # ext = '.jpg'
            # original_filename = base_name + ext
        if ext.lower() == '.doc' or ext.lower() == '.docx':
            file = bytes_word_to_pdf(file, ext.lower())
            base_name, ext = os.path.splitext(file.name)
            print(file.content_type)
            print(file.size)
            print(file.name)
            print(ext)
            # import time
            # time.sleep(1000)
        hash = calculate_file_hash(file)
        if models.SubjectMedicalFile.objects.filter(delete_flag=0).filter(
                subject_medical_info__delete_flag=0,
                subject_medical_info__project_id=project.project_id,
                subject_medical_info__project_site_id=project_site.project_site_id,
                subject_medical_info__subject_id=subject_id,
                subject_medical_info__subject_item_id=subject_item_id,
                hash=hash
        ).exists():
            raise FileAlreadyExistsError("文件已存在")

        object_name = f"{uuid.uuid4().hex}{ext}"
        bucket_name = settings.MINIO_BUCKET_NAME
        # file_size = file.size if hasattr(file, 'size') else len(file.getvalue())

        # 保存文件
        try:
            minio_client = get_minio_client()
            # Ensure bucket exists
            if not minio_client.bucket_exists(bucket_name):
                minio_client.make_bucket(bucket_name)
            # Upload file to MinIO
            minio_client.put_object(
                bucket_name=bucket_name,
                object_name=object_name,
                data=file,
                length=file.size,
                part_size=1024 * 1024 * 5,
                content_type=file.content_type  # 'image/jpeg' if ext.lower() == '.jpg' else file_content_type
            )
        except Exception as e:
            logger.error(e)
            raise APIException(f"文件上传失败：{e}")

        # 数据入库
        with transaction.atomic():
            sunject_medical_file = {
                'original_filename': file.name,
                'bucket_name': bucket_name,
                'object_name': object_name,
                'content_type': file.content_type,
                'size': file.size,
                'hash': hash,
                'create_user': request.sys_user.username,
                'create_name': request.sys_user.realname,
            }
            sunject_medical_file = models.SubjectMedicalFile.objects.create(**sunject_medical_file)
            subject_visit = subject_item.subject_visit

            medical_info = models.SubjectMedicalInfo.objects.create(
                subject_item=subject_item,
                subject_visit=subject_item.subject_visit,
                subject_epoch=subject_visit.subject_epoch,
                project=project,
                project_site=project_site,
                subject=subject,
                patient=subject.patient,
                file=sunject_medical_file,
                create_user=request.sys_user.username,
                create_name=request.sys_user.realname,
            )

            serializer = serializers.SubjectMedicalInfoSerializer(medical_info)
            # if not serializer.is_valid():
            #     raise ValidationError(serializer.errors)

        test_result_ids = list(
            TestResult.objects.filter(
                subject_id=subject_id,
                subject_item_id=subject_item_id,
                delete_flag=0
            ).values_list("id", flat=True)
        )
        TestResult.objects.filter(id__in=test_result_ids).update(delete_flag=1)
        OperationLog.objects.filter(
            target_id__in=test_result_ids
        ).update(delete_flag=1)
        OperationLog.objects.filter(
            target_id=subject_item_id,
            delete_flag=0
        ).update(delete_flag=1)

        TestOcrResult.objects.filter(
            subject_id=subject_id,
            subject_item_id=subject_item_id,
            delete_flag=0
        ).update(delete_flag=1)

        # 软删除 AeTrackerTask 模型的相关对象
        AeTrackerTask.objects.filter(
            subject_id=subject_id,
            subject_item_id=subject_item_id,
            delete_flag=0
        ).update(delete_flag=1)

        # models.SubjectMedicalInfo.objects.filter(
        #     subject_id=subject_id,
        #     subject_item_id=subject_item_id,
        #     delete_flag=0
        # ).update(file_masked_id=None, ocr_time=None)
        # 软删除 OperationLog 模型的相关对象
        # a = SubjectItem.objects.filter(subject_id=subject_id, subject_item_id=subject_item_id).first()
        SubjectItem.objects.filter(subject_id=subject_id, subject_item_id=subject_item_id).update(
            ae_ai_current_step=0, ae_ai_task_id=0)

        return Response(serializer.data)


    # @extend_schema(
    # summary='上传新脱敏文件并替换旧脱敏文件',
    # tags=['访视详情'],
    # request=serializers.SubjectMedicalInfoMaskedUpdateRequestSerializer,
    # responses=serializers.SubjectMedicalInfoSerializer
    # )
    # @action(url_path='update-masked-file', detail=False, methods=['post'])
    # def update_masked_file(self, request, *args, **kwargs):


    #     # 使用序列化器验证数据（包括新增的字段）
    #     serializer = serializers.SubjectMedicalInfoMaskedUpdateRequestSerializer(data=request.data)
    #     if not serializer.is_valid():
    #         raise ValidationError(serializer.errors)

    #     data = serializer.validated_data
    #     file = data.get('file')

    #     # 获取外键字段值
    #     project_id = data.get('project').get('project_id')
    #     project_site_id = data.get('project_site').get('project_site_id')
    #     subject_id = data.get('subject').get('subject_id')
    #     subject_epoch_id = data.get('subject_epoch').get('subject_epoch_id')
    #     subject_item_id = data.get('subject_item').get('subject_item_id')
    #     subject_visit_id = data.get('subject_visit').get('subject_visit_id')

    #     # 查询目标对象
    #     instance = models.SubjectMedicalInfo.objects.filter(
    #         subject_id=subject_id,
    #         # category=category,
    #         project_id=project_id,
    #         project_site_id=project_site_id,
    #         subject_epoch_id = subject_epoch_id,
    #         subject_item_id=subject_item_id,
    #         subject_visit_id=subject_visit_id,
    #         delete_flag=0
    #     ).first()

    #     if not instance:
    #         raise NotFound("未找到匹配的 SubjectMedicalInfo 记录")
    #     base_name, ext = os.path.splitext(file.name)
    #     object_name = f"{uuid.uuid4().hex}{ext}"
    #     bucket_name = settings.MINIO_BUCKET_NAME

    #     try:
    #         minio_client = get_minio_client()
    #         if not minio_client.bucket_exists(bucket_name):
    #             minio_client.make_bucket(bucket_name)

    #         minio_client.put_object(
    #             bucket_name=bucket_name,
    #             object_name=object_name,
    #             data=file,
    #             length=file.size,
    #             part_size=1024 * 1024 * 5,
    #             content_type=file.content_type
    #         )
    #     except Exception as e:
    #         logger.error(e)
    #         raise APIException(f"文件上传失败：{e}")

    #     with transaction.atomic():
    #         # 1. 如果存在旧脱敏文件，则标记为删除
    #         if instance.file_masked:
    #             old_masked_file = instance.file_masked
    #             old_masked_file.delete_flag = 1
    #             old_masked_file.update_user = request.sys_user.username
    #             old_masked_file.save()

    #         # 3. 创建新的脱敏文件记录
    #         new_masked_file = models.SubjectMedicalFileMasked.objects.create(
    #             original_filename=file.name,
    #             bucket_name=bucket_name,
    #             object_name=object_name,
    #             content_type=file.content_type,
    #             size=file.size,
    #             hash=calculate_file_hash(file),
    #             create_user=request.sys_user.username,
    #             create_name=request.sys_user.realname
    #         )

    #         # 4. 更新 SubjectMedicalInfo 的关联字段
    #         instance.file_masked = new_masked_file
    #         instance.project_id = project_id
    #         instance.project_site_id = project_site_id
    #         instance.subject_id = subject_id
    #         instance.subject_epoch_id = subject_epoch_id
    #         instance.subject_item_id = subject_item_id
    #         instance.subject_visit_id = subject_visit_id
    #         instance.update_user = request.sys_user.username
    #         instance.update_name = request.sys_user.realname

    #         instance.save(update_fields=[
    #             'file_masked',
    #             'project_id',
    #             'project_site_id',
    #             'subject_id',
    #             'subject_epoch_id',
    #             'subject_item_id',
    #             'subject_visit_id',
    #             'update_user',
    #             'update_name'
    #         ])

    #     # 5. 返回更新后的数据
    #     medical_info_serializer = self.serializer_class(instance)
    #     return Response(medical_info_serializer.data, status=status.HTTP_200_OK)


class SubjectMedicalInfoListViewSetDeprecated(SubjectMedicalInfoListViewSet):

    @extend_schema(
        summary='受试者病历文件列表',
        tags=['访视详情'],
        # request=serializers.ProjectMaterialLatestRequestSerializer,
        responses=serializers.SubjectMedicalInfoSerializer,
        deprecated=True
    )
    def list(self, request, format=None):
        return super().list(request, format)

    @extend_schema(
        summary='上传受试者病历文件',
        tags=['访视详情'],
        request=serializers.SubjectMedicalInfoCreateRequestSerializer,
        responses=serializers.SubjectMedicalInfoFileSerializer,
        deprecated=True
    )
    def create(self, request, format=None):
        return super().create(request, format)


class SubjectMedicalInfoDetailViewSet(BaseViewSet, UpdateModelMixin, DestroyModelMixin):
    queryset = models.SubjectMedicalInfo.objects.filter(delete_flag=0)
    serializer_class = serializers.SubjectMedicalInfoSerializer
    http_method_names = ['patch', 'delete']

    @extend_schema(
        summary='更新受试者病历文件名',
        tags=['访视详情'],
        request=serializers.MedicalFileUpdateSerializer,
        responses=serializers.SubjectMedicalInfoSerializer
    )
    def partial_update(self, request, *args, **kwargs):
        data = request.data.copy()
        data['update_user'] = request.sys_user.username
        data['update_name'] = request.sys_user.realname

        medical_info = self.get_object()
        partial = kwargs.pop('partial', True)

        serializer = serializers.MedicalFileUpdateSerializer(medical_info.file, data=data, partial=partial)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        with transaction.atomic():
            self.perform_update(serializer)

            file_masked = medical_info.file_masked
            if file_masked:
                serializer = serializers.MedicalFileMaskedUpdateSerializer(file_masked, data=data, partial=partial)
                if serializer.is_valid():
                    self.perform_update(serializer)

        serializer = self.serializer_class(medical_info)
        return Response(serializer.data)

    @extend_schema(
        summary='删除受试者病历文件',
        tags=['访视详情'],
        request=serializers.MedicalFileUpdateSerializer,
        responses=serializers.SubjectMedicalInfoSerializer
    )
    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.delete_flag = 1
        subject_item_id = instance.subject_item_id
        subject_id = instance.subject_id
        task = AeTrackerTask.objects.filter(
            subject_id=subject_id,
            subject_item_id=subject_item_id,
            status__in=[AeTrackerTask.IN_PROGRESS, AeTrackerTask.TODO],
            delete_flag=0
        ).order_by('-create_time').first()
        if task:
            raise TaskExistsStatusIN_PROGRESS()

        instance.update_user = request.sys_user.username
        instance.update_name = request.sys_user.realname
        instance.save()

        instance.file.delete_flag = 1
        instance.file.update_user = request.sys_user.username
        instance.file.update_name = request.sys_user.realname
        instance.file.save()

        # test_result = self.get_object()  删除一张照片  下面联动  根据id  拿到  item_id

        # models.TestResult.objects.filter(
        #     subject_id=subject_id,
        #     subject_item_id=subject_item_id,
        #     delete_flag=0
        # ).update(delete_flag=1)

        # print(test_result.id)
        #
        # try:
        #     if data['medical_history_flag'] is None:
        #         a = models.TestResult.objects.filter(
        #             id=test_result.id,
        #         ).first()
        #         latest_result = models.TestResult.objects.filter(
        #             subject_item_id=a.subject_item_id,
        #             test_name=data['test_name'],
        #             medical_history_flag__isnull=False
        #         ).order_by('-create_time').first()
        #         data['medical_history_flag'] = latest_result.medical_history_flag
        # except:
        #     pass

        test_result_ids = list(
            TestResult.objects.filter(
                subject_id=subject_id,
                subject_item_id=subject_item_id,
                delete_flag=0
            ).values_list("id", flat=True)
        )
        TestResult.objects.filter(id__in=test_result_ids).update(delete_flag=1)
        OperationLog.objects.filter(
            target_id__in=test_result_ids
        ).update(delete_flag=1)
        OperationLog.objects.filter(
            target_id=subject_item_id,
            delete_flag=0
        ).update(delete_flag=1)

        TestOcrResult.objects.filter(
            subject_id=subject_id,
            subject_item_id=subject_item_id,
            delete_flag=0
        ).update(delete_flag=1)

        # 软删除 AeTrackerTask 模型的相关对象
        AeTrackerTask.objects.filter(
            subject_id=subject_id,
            subject_item_id=subject_item_id,
            delete_flag=0
        ).update(delete_flag=1)

        # models.SubjectMedicalInfo.objects.filter(
        #     subject_id=subject_id,
        #     subject_item_id=subject_item_id,
        #     delete_flag=0
        # ).update(file_masked_id=None, ocr_time=None)
        # 软删除 OperationLog 模型的相关对象
        # a = SubjectItem.objects.filter(subject_id=subject_id, subject_item_id=subject_item_id).first()

        SubjectItem.objects.filter(subject_id=subject_id, subject_item_id=subject_item_id).update(
            ae_ai_current_step=0, ae_ai_task_id=0)
        return Response(status=status.HTTP_204_NO_CONTENT)


class SubjectMedicalInfoDetailViewSetDeprecated(SubjectMedicalInfoDetailViewSet):

    @extend_schema(
        summary='更新受试者病历文件名',
        tags=['访视详情'],
        request=serializers.MedicalFileUpdateSerializer,
        responses=serializers.SubjectMedicalInfoSerializer,
        deprecated=True
    )
    def partial_update(self, request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)

    @extend_schema(
        summary='删除受试者病历文件',
        tags=['访视详情'],
        request=serializers.MedicalFileUpdateSerializer,
        responses=serializers.SubjectMedicalInfoSerializer,
        deprecated=True
    )
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)
    

class SubjectMedicalMaskedFileUpdateViewSet(BaseViewSet):
    parser_classes = (MultiPartParser, )  # 支持文件上传
    queryset = models.SubjectMedicalInfo.objects.filter(delete_flag=0).select_related(
        'file', 'file_masked', 'project', 'project_site', 'subject')
    serializer_class = serializers.SubjectMedicalInfoSerializer
    filterset_class = filters.SubjectMedicalInfoFilter
    # pagination_class = StandardResultsSetPagination
    ordering_fields = '__all__'
    ordering = ['create_time']
    http_method_names = ['post']

    @extend_schema(
        summary='上传新脱敏文件并替换旧脱敏文件',
        tags=['访视详情'],
        request=serializers.SubjectMedicalInfoMaskedUpdateRequestSerializer,
        responses=serializers.SubjectMedicalInfoSerializer
    )
    @action(url_path='update-masked-file', detail=False, methods=['post'])
    def update_masked_file(self, request, *args, **kwargs):
        # 原有逻辑保持不变，从这里开始复制粘贴
        serializer = serializers.SubjectMedicalInfoMaskedUpdateRequestSerializer(data=request.data)
        if not serializer.is_valid():
            raise ValidationError(serializer.errors)

        data = serializer.validated_data
        file = data.get('file')
        key_id = data.get('key_id')
        file_masked_id = data.get('file_masked_id')
        # project_id = data.get('project').get('project_id')
        # project_site_id = data.get('project_site').get('project_site_id')
        # subject_id = data.get('subject').get('subject_id')
        # subject_epoch_id = data.get('subject_epoch').get('subject_epoch_id')
        # subject_item_id = data.get('subject_item').get('subject_item_id')
        # subject_visit_id = data.get('subject_visit').get('subject_visit_id')

        instance = models.SubjectMedicalInfo.objects.filter(
            id = key_id,
            file_masked_id = file_masked_id,
            delete_flag=0
        ).first()

        if not instance:
            raise NotFound("未找到匹配的 SubjectMedicalInfo 记录")

        base_name, ext = os.path.splitext(file.name)
        object_name = f"{uuid.uuid4().hex}{ext}"
        bucket_name = settings.MINIO_BUCKET_NAME

        try:
            minio_client = get_minio_client()
            if not minio_client.bucket_exists(bucket_name):
                minio_client.make_bucket(bucket_name)

            minio_client.put_object(
                bucket_name=bucket_name,
                object_name=object_name,
                data=file,
                length=file.size,
                part_size=1024 * 1024 * 5,
                content_type=file.content_type
            )
        except Exception as e:
            logger.error(e)
            raise APIException(f"文件上传失败：{e}")

        with transaction.atomic():
            if instance.file_masked:
                old_masked_file = instance.file_masked
                old_masked_file.delete_flag = 1
                old_masked_file.update_user = request.sys_user.username
                old_masked_file.save()

            new_masked_file = models.SubjectMedicalFileMasked.objects.create(
                original_filename=file.name,
                bucket_name=bucket_name,
                object_name=object_name,
                content_type=file.content_type,
                size=file.size,
                hash=calculate_file_hash(file),
                create_user=request.sys_user.username,
                create_name=request.sys_user.realname
            )

            instance.file_masked = new_masked_file

            instance.update_user = request.sys_user.username
            instance.update_name = request.sys_user.realname

            instance.save(update_fields=[
                'file_masked',
                'update_user',
                'update_name'
            ])

        medical_info_serializer = self.serializer_class(instance)
        return Response(medical_info_serializer.data, status=status.HTTP_200_OK)