# 蕊星AI

#### 服务导航
```
1.接口文档

PROD：http://**************:32091/api/schema/swagger-ui/
TEST：http://**************:30377/api/schema/swagger-ui/
UAT：http://**************:31612/api/schema/swagger-ui/
账户：admin
密码：Woqp1221..

2.定时任务

PROD：http://**************:30436/
TEST：http://**************:30554/
UAT：http://**************:31363/
账户：admin
密码：Woqp1221..

3.ERP首页

PROD：http://**************/
UAT：http://**************/
TEST：http://***************/
DEV：http://***************/
```

### 本地测试

#### 安装依赖包
```
pip install -r requirements.txt
```

#### 收集静态文件
```
python manage.py collectstatic
```

#### 生成数据库迁移版本文件
```
python manage.py makemigrations
```

#### 应用数据库迁移版本到数据库
```
python manage.py migrate
```

#### 添加超级用户
```
python manage.py createsuperuser
```

#### 创建缓存表
```
python manage.py createcachetable
```

#### 启动
```
python manage.py runserver 0.0.0.0:8000
```

#### 启动定时任务
```
bash cronicle.sh
```

#### 导出cron定时脚本配置
```
/opt/cronicle/bin/control.sh export
```

### 钉钉服务端API身份验证（免登）文档
```
https://open.dingtalk.com/document/orgapp/sso-overview
https://open.dingtalk.com/tools/explorer/jsapi?spm=ding_open_doc.document.0.0.648cd394IBQxsq&id=11723
```

### Docker部署

#### 构建镜象
```
docker build -t smo-ai-backend .
```

#### 删除指定的镜象
```
docker image rm smo-ai-backend
```

#### 删除未使用的镜象
```
docker image prune
```

#### 登陆私有仓库
```
docker login -u logic -p 123456 ***************:8084
```

#### 打标镜象
```
docker tag smo-ai-backend ***************:8084/smo-ai-backend:v1.0
docker tag smo-ai-backend ***************:8084/smo-ai-backend-prod:v1.0
```

#### 推送到私有仓库
```
docker push ***************:8084/smo-ai-backend:v1.0
docker push ***************:8084/smo-ai-backend-prod:v1.0
```

#### 拉取私有仓库镜象
```
docker pull ***************:8084/smo-ai-backend:v1.0
```

#### 启动web服务容器
```
docker run --name smo-ai-backend-instance-test --env DJANGO_SETTINGS_MODULE=smo.settings.test --env APP_DEPLOY_HOST=************** --env APP_DEPLOY_PORT=31216 -p 8000:8000 -d smo-ai-backend

docker run --name smo-ai-backend-instance-test --env DJANGO_SETTINGS_MODULE=smo.settings.test --env APP_DEPLOY_HOST=************** --env APP_DEPLOY_PORT=31216 -p 8000:8000 -d ***************:8084/smo-ai-backend:v1.0
```

#### 进入web服务容器
```
docker exec -it smo-ai-backend-instance-test /bin/bash
```

### 启动cron定时脚本容器
```
docker run --name smo-ai-backend-cron-test --env DJANGO_SETTINGS_MODULE=smo.settings.test -p 3012:3012 -d ***************:8084/smo-ai-backend:v1.0 /bin/bash -c "bash cronicle.sh && tail -f /dev/null"

docker run --name smo-ai-backend-cron-test --env DJANGO_SETTINGS_MODULE=smo.settings.test -p 3012:3012 -d smo-ai-backend /bin/bash -c "bash cronicle.sh && tail -f /dev/null"
```

#### 进入cron定时脚本容器
```
docker exec -it smo-ai-backend-cron-test /bin/bash
```

#### 导出cron定时脚本配置
```
/opt/cronicle/bin/control.sh export /etc/cronicle.txt
```

#### 停止容器
```
docker stop smo-ai-backend-cron-test
```

#### 删除容器
```
docker rm smo-ai-backend-cron-test
```

### 环境变量
```
DJANGO_SETTINGS_MODULE     Django项目配置模块（默认：smo.settings.test）
```

### Git分支提交代码指南
```
分支命名解释：

dev     - 开发
test    - 测试（开发）
uat     - 测试（业务）
master  - 生产

test-T  - 测试（开发）中转分支
uat-T   - 测试（业务）中转分支

-T结尾分支是为了避免同时有多个开发需求，需求发布时间不确定，错峰和修复冲突使用

feat/   - feat开头的分支是新增或者优化功能的分支，此分支一般从master分叉

fix/    - fix开头的分支是bug修复分支

feat/ 分支命名规则：feat/ + 功能或者需求描述 + jira编号，例：feat/访视预测-7231

一个新功能合并到master分支的步骤：

举例分支：feat/新功能-001

1. 拉取远程 master 分支到本地
git checkout feat/新功能-001      # 切换到本地的 feat/新功能-001 分支
git pull origin master            # 拉取远程 master 分支的最新代码并合并到当前分支

2. 切换到 test-T 分支
git checkout test-T               # 切换到 test-T 分支
git pull origin test-T            # 确保 test-T 是最新状态
git pull origin master            # 拉取远程 master 分支的最新代码并合并到当前分支
git fetch origin                  # 拉取远程仓库的所有分支更新

3. 合并 feat/新功能-001 到 test-T
git merge feat/新功能-001         # 将 feat/新功能-001 合并到 test-T
如果发生冲突，手动解决冲突后：
git add .                         # 标记冲突解决的文件
git commit                        # 提交解决冲突的修改

5. 在 GitLab 发起合并请求到 test 分支
首先将 test-T 推送到远程：

git push origin test-T           # 推送本地 test-T 分支到远程
然后在 GitLab 上发起 test-T 到 test 分支的合并请求（此步骤需在 GitLab Web 界面完成）。

6. 合并
在 GitLab 上完成代码审查后，直接在 GitLab 上完成 test-T 到 test 分支的合并。

7. test 验证通过后 uat重复上述步骤

8. uat验证通过后 master重复上述步骤

9. git操作建议使用github desktop客户端
```

### 前端对接指南
```
1.接口文档（测试环境）

地址：http://**************:30377/api/schema/swagger-ui/
账户：admin
密码：Woqp1221..

2.接口鉴权

接口鉴权方式和ERP保持一致，请求时需要携带X-Access-Token请求Header

3.钉钉微应用免登录流程

1）微应用客户端调用 requestAuthCode 获取免登录code
2）使用code请求后端接口 /api/v1/auth/dingtalk 获取用户信息和X-Access-Token

DINGTALK_APP_KEY = 'dinggblpwxtne5eey9kz'  # 测试环境

4. 接口返回数据格式说明

1）请求处理成功接口返回200状态码，响应体为业务数据，具体的数据格式请参考Swagger文档。
2）请求处理失败接口回其他状态码，响应体为标准格式：

code：错误代码
message：错误描述
detail：错误详情

例：
    {
        "code": 401,
        "message": "Authentication credentials were not provided.",
        "detail": null
    }

3）分页数据格式:

count：符合条件的总记录数
page：当前页码，起始为1
size：每页返回的记录条数
results：当前页的结果数据

例：
    {
        "count": 100,
        "page": 1,
        "size": 10,
        "results": [
            {
                "id": 0,
                "created_time": "2025-02-17T13:44:14.903Z",
                "updated_time": "2025-02-17T13:44:14.903Z",
                "delete_flag": 32767,
                "data_version": 2147483647
            }
        ]
    }

注：以上数据格式在Swagger文档中均有详细描述和调用示例
```

### 访问配置

| 环境 | 容器内部地址                          | 容器外部地址         | 网关访问地址            |
| ---- | ------------------------------------- | -------------------- | ----------------------- |
| prod |        |  | **************:30325/ai |
| uat  |  |  | **************:30973/ai |
| test |    | http://**************:30377/ | **************:32199/ai |
| dev  |    |  | ***************:9999/ai |


### Airflow
```
python3.9 -m pip install --only-binary=google-re2 google-re2 -i https://pypi.tuna.tsinghua.edu.cn/simple

python3.9 -m pip install "apache-airflow==2.10.5" -i https://pypi.tuna.tsinghua.edu.cn/simple

python3.9 -m pip install pendulum==2.1.2 -i https://pypi.tuna.tsinghua.edu.cn/simple

export AIRFLOW_HOME="/app/etc/airflow/test"

airflow db init

airflow db migrate

airflow dags list

airflow users create \
--username admin \
--firstname xx \
--lastname xx \
--email <EMAIL> \
--role Admin \
--password Woqp1221..

airflow webserver --port 3012

airflow scheduler
```