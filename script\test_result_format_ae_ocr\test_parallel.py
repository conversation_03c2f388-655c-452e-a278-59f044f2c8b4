# -*- coding: utf-8 -*-
"""
并行处理功能测试脚本

本脚本用于测试页面级并行处理功能，包括：
- 基础功能测试
- 性能对比测试
- 错误处理测试
- 资源使用监控
"""

import os
import sys
import time
import json
import logging
from typing import Dict, List, Any

# 添加项目根目录到路径
current_script_path = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_script_path)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smo.settings.test')
os.environ["DJANGO_ALLOW_ASYNC_UNSAFE"] = "true"

import django
django.setup()

from script.test_result_format_ae_ocr.parallel_processor import ParallelIntegrationAdapter
from script.test_result_format_ae_ocr.parallel_config import get_parallel_config
from script.test_result_format_ae_ocr.main import process_medical_ocr

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ParallelProcessingTester:
    """并行处理测试器"""
    
    def __init__(self):
        self.logger = logger
        self.test_data_path = os.path.join(
            os.path.dirname(__file__), 
            'test_data'
        )
        
    def load_test_data(self) -> Dict[str, Any]:
        """加载测试数据"""
        try:
            # 加载OCR文本数据
            text_file = os.path.join(self.test_data_path, 'text_test_data.txt')
            with open(text_file, 'r', encoding='utf-8') as f:
                ocr_text = f.read()
            
            # 加载OCR块数据
            ocr_file = os.path.join(self.test_data_path, 'ocr_test_data.json')
            with open(ocr_file, 'r', encoding='utf-8') as f:
                ocr_blocks = json.load(f)
            
            return {
                'ocr_text': ocr_text,
                'ocr_blocks': ocr_blocks,
                'task_info': {
                    'task_id': 'test_task_001',
                    'create_user': 'test_user',
                    'create_name': 'Test User',
                    'business_id': 'test_business'
                },
                'file_metadata': {
                    'subject_medical_info_id': 'test_file_001',
                    'subject_id': 'test_subject_001',
                    'subject_item_id': 'test_item_001',
                    'visit_data': []
                }
            }
        except Exception as e:
            self.logger.error(f"加载测试数据失败: {e}")
            return self._create_mock_data()
    
    def _create_mock_data(self) -> Dict[str, Any]:
        """创建模拟测试数据"""
        mock_ocr_text = """
        [Page 1]
        血常规检查报告
        
        检查项目    结果    单位    参考范围
        白细胞计数  6.5     10^9/L  3.5-9.5
        红细胞计数  4.2     10^12/L 4.3-5.8
        血红蛋白    120     g/L     130-175
        
        [Page 2]
        生化检查报告
        
        检查项目    结果    单位    参考范围
        总胆固醇    5.2     mmol/L  3.1-5.2
        甘油三酯    1.8     mmol/L  0.4-1.8
        血糖        6.1     mmol/L  3.9-6.1
        """
        
        return {
            'ocr_text': mock_ocr_text,
            'ocr_blocks': [],
            'task_info': {
                'task_id': 'mock_task_001',
                'create_user': 'mock_user',
                'create_name': 'Mock User',
                'business_id': 'mock_business'
            },
            'file_metadata': {
                'subject_medical_info_id': 'mock_file_001',
                'subject_id': 'mock_subject_001',
                'subject_item_id': 'mock_item_001',
                'visit_data': []
            }
        }
    
    def test_basic_functionality(self) -> Dict[str, Any]:
        """测试基础功能"""
        self.logger.info("🧪 开始基础功能测试")
        
        test_data = self.load_test_data()
        
        # 创建并行处理适配器
        adapter = ParallelIntegrationAdapter(enable_parallel=True, max_workers=2)
        
        try:
            start_time = time.time()
            
            result = adapter.process_medical_ocr_enhanced(
                ocr_text=test_data['ocr_text'],
                task_info=test_data['task_info'],
                ocr_blocks=test_data['ocr_blocks'],
                file_metadata=test_data['file_metadata']
            )
            
            processing_time = time.time() - start_time
            
            test_result = {
                'success': True,
                'processing_time': processing_time,
                'result_count': len(result.get('test_results', [])),
                'result': result
            }
            
            self.logger.info(f"✅ 基础功能测试成功，处理时间: {processing_time:.2f}秒")
            self.logger.info(f"📊 识别到 {test_result['result_count']} 个检验项目")
            
            return test_result
            
        except Exception as e:
            self.logger.error(f"❌ 基础功能测试失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'processing_time': 0,
                'result_count': 0
            }
    
    def test_performance_comparison(self) -> Dict[str, Any]:
        """测试性能对比（并行 vs 串行）"""
        self.logger.info("🏃 开始性能对比测试")
        
        test_data = self.load_test_data()
        
        # 测试串行处理
        serial_result = self._test_serial_processing(test_data)
        
        # 测试并行处理
        parallel_result = self._test_parallel_processing(test_data)
        
        # 计算性能提升
        if serial_result['success'] and parallel_result['success']:
            speedup = serial_result['processing_time'] / parallel_result['processing_time']
            self.logger.info(f"🚀 性能提升: {speedup:.2f}x")
        else:
            speedup = 0
        
        return {
            'serial_result': serial_result,
            'parallel_result': parallel_result,
            'speedup': speedup
        }
    
    def _test_serial_processing(self, test_data: Dict) -> Dict[str, Any]:
        """测试串行处理"""
        try:
            start_time = time.time()
            
            result = process_medical_ocr(
                ocr_text=test_data['ocr_text'],
                task_info=test_data['task_info'],
                ocr_blocks=test_data['ocr_blocks']
            )
            
            processing_time = time.time() - start_time
            
            return {
                'success': True,
                'processing_time': processing_time,
                'result_count': len(result)
            }
            
        except Exception as e:
            self.logger.error(f"串行处理测试失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'processing_time': 0,
                'result_count': 0
            }
    
    def _test_parallel_processing(self, test_data: Dict) -> Dict[str, Any]:
        """测试并行处理"""
        try:
            adapter = ParallelIntegrationAdapter(enable_parallel=True, max_workers=3)
            
            start_time = time.time()
            
            result = adapter.process_medical_ocr_enhanced(
                ocr_text=test_data['ocr_text'],
                task_info=test_data['task_info'],
                ocr_blocks=test_data['ocr_blocks'],
                file_metadata=test_data['file_metadata']
            )
            
            processing_time = time.time() - start_time
            
            return {
                'success': True,
                'processing_time': processing_time,
                'result_count': len(result.get('test_results', []))
            }
            
        except Exception as e:
            self.logger.error(f"并行处理测试失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'processing_time': 0,
                'result_count': 0
            }
    
    def test_error_handling(self) -> Dict[str, Any]:
        """测试错误处理"""
        self.logger.info("🛠️ 开始错误处理测试")
        
        # 测试空输入
        empty_result = self._test_empty_input()
        
        # 测试无效输入
        invalid_result = self._test_invalid_input()
        
        return {
            'empty_input_test': empty_result,
            'invalid_input_test': invalid_result
        }
    
    def _test_empty_input(self) -> Dict[str, Any]:
        """测试空输入"""
        try:
            adapter = ParallelIntegrationAdapter(enable_parallel=True, max_workers=2)
            
            result = adapter.process_medical_ocr_enhanced(
                ocr_text="",
                task_info={},
                ocr_blocks=[],
                file_metadata={}
            )
            
            return {
                'success': True,
                'handled_gracefully': len(result.get('test_results', [])) == 0
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _test_invalid_input(self) -> Dict[str, Any]:
        """测试无效输入"""
        try:
            adapter = ParallelIntegrationAdapter(enable_parallel=True, max_workers=2)
            
            result = adapter.process_medical_ocr_enhanced(
                ocr_text="这是一段无效的OCR文本，不包含任何医疗检验信息",
                task_info={'task_id': 'invalid_test'},
                ocr_blocks=None,
                file_metadata={'subject_medical_info_id': 'invalid_file'}
            )
            
            return {
                'success': True,
                'handled_gracefully': True,
                'result_count': len(result.get('test_results', []))
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试"""
        self.logger.info("🚀 开始运行所有测试")
        
        # 显示当前配置
        config = get_parallel_config()
        self.logger.info(f"当前配置: 并行={config.enable_parallel}, 最大并发={config.max_workers}")
        
        results = {}
        
        # 基础功能测试
        results['basic_functionality'] = self.test_basic_functionality()
        
        # 性能对比测试
        results['performance_comparison'] = self.test_performance_comparison()
        
        # 错误处理测试
        results['error_handling'] = self.test_error_handling()
        
        # 生成测试报告
        self._generate_test_report(results)
        
        return results
    
    def _generate_test_report(self, results: Dict[str, Any]):
        """生成测试报告"""
        self.logger.info("📋 测试报告")
        self.logger.info("=" * 60)
        
        # 基础功能测试结果
        basic = results.get('basic_functionality', {})
        if basic.get('success'):
            self.logger.info(f"✅ 基础功能测试: 通过 ({basic.get('processing_time', 0):.2f}秒)")
            self.logger.info(f"   识别项目数: {basic.get('result_count', 0)}")
        else:
            self.logger.info(f"❌ 基础功能测试: 失败 - {basic.get('error', '未知错误')}")
        
        # 性能对比测试结果
        perf = results.get('performance_comparison', {})
        if perf.get('speedup', 0) > 0:
            self.logger.info(f"🚀 性能对比测试: 并行处理提升 {perf['speedup']:.2f}x")
            serial_time = perf.get('serial_result', {}).get('processing_time', 0)
            parallel_time = perf.get('parallel_result', {}).get('processing_time', 0)
            self.logger.info(f"   串行处理时间: {serial_time:.2f}秒")
            self.logger.info(f"   并行处理时间: {parallel_time:.2f}秒")
        else:
            self.logger.info("⚠️ 性能对比测试: 无法计算性能提升")
        
        # 错误处理测试结果
        error = results.get('error_handling', {})
        empty_test = error.get('empty_input_test', {})
        invalid_test = error.get('invalid_input_test', {})
        
        if empty_test.get('success') and invalid_test.get('success'):
            self.logger.info("✅ 错误处理测试: 通过")
        else:
            self.logger.info("❌ 错误处理测试: 部分失败")
        
        self.logger.info("=" * 60)


def main():
    """主函数"""
    tester = ParallelProcessingTester()
    results = tester.run_all_tests()
    
    # 保存测试结果
    output_file = os.path.join(
        os.path.dirname(__file__), 
        'test_results.json'
    )
    
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2, default=str)
        logger.info(f"测试结果已保存到: {output_file}")
    except Exception as e:
        logger.error(f"保存测试结果失败: {e}")


if __name__ == "__main__":
    main()
