"""
HIPAA脱敏服务请求工具

提供简洁易用的脱敏接口调用功能，适配OCR脱敏系统。
"""

import requests
import asyncio
import time
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
try:
    from django.conf import settings
    print(f"🔧 Django配置加载成功，当前环境: {getattr(settings, 'ENVIRONMENT', 'UNKNOWN')}")
    
    # 尝试获取HIPAA服务URL配置
    hipaa_url = getattr(settings, 'HIPAA_DEIDENTIFY_SERVICE_URL', None)
    
    if hipaa_url:
        print(f"✅ 成功读取HIPAA_DEIDENTIFY_SERVICE_URL: {hipaa_url}")
        HIPAA_DEFAULT_URL = hipaa_url
    else:
        print("⚠️ HIPAA_DEIDENTIFY_SERVICE_URL配置项不存在，使用默认URL")
        HIPAA_DEFAULT_URL = 'http://***************:50505'
        
    # 输出所有相关配置用于调试
    print(f"📋 Django设置中的相关配置:")
    for attr in dir(settings):
        if 'HIPAA' in attr.upper() or 'DEIDENTIFY' in attr.upper():
            print(f"   {attr}: {getattr(settings, attr, 'NOT_SET')}")
            
except ImportError as e:
    print(f"❌ Django导入失败: {e}")
    print("🔄 使用默认HIPAA服务URL")
    HIPAA_DEFAULT_URL = 'http://***************:50505'
except Exception as e:
    print(f"💥 读取Django配置时发生异常: {e}")
    print("🔄 使用默认HIPAA服务URL")
    HIPAA_DEFAULT_URL = 'http://***************:50505'


@dataclass
class DeidentifyResult:
    """脱敏结果数据类"""
    sensitive_words: List[Dict[str, Any]]
    deidentified_text: str
    processing_time: float
    total_entities: int
    success: bool = True
    error_message: Optional[str] = None

    @property
    def sensitive_words_only(self) -> List[str]:
        """获取纯敏感词列表"""
        return [entity['text'] for entity in self.sensitive_words]

    @property
    def entities_for_ocr_masking(self) -> List[Dict[str, Any]]:
        """
        获取适用于OCR图像遮挡的实体列表

        Returns:
            List[Dict]: 包含text, entity_type, start, end, confidence的实体列表
        """
        return [
            {
                "text": entity.get("text", ""),
                "entity_type": entity.get("entity_type", "UNKNOWN"),
                "start": entity.get("start", 0),
                "end": entity.get("end", 0),
                "confidence": entity.get("confidence", 0.0)
            }
            for entity in self.sensitive_words
            if entity.get("text")  # 过滤空文本
        ]


class DeidentifyClient:
    """HIPAA脱敏服务客户端"""

    def __init__(self,
                 base_url: str = None,
                 timeout: int = 30):
        if base_url is None:
            base_url = HIPAA_DEFAULT_URL
        """
        初始化脱敏客户端

        Args:
            base_url: 脱敏服务的基础URL
            timeout: 请求超时时间（秒）
        """
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.deidentify_url = f"{self.base_url}/deidentify"

    def deidentify(self, text: str, project_code: str = None) -> DeidentifyResult:
        """
        调用脱敏接口

        Args:
            text: 待脱敏的文本
            project_code: 项目编码

        Returns:
            DeidentifyResult: 脱敏结果对象
        """
        if not text or not text.strip():
            return DeidentifyResult(
                sensitive_words=[],
                deidentified_text=text,
                processing_time=0.0,
                total_entities=0,
                success=False,
                error_message="文本内容为空"
            )

        start_time = time.time()

        try:
            print(f"🔗 调用HIPAA脱敏服务: {self.deidentify_url}")
            print(f"📝 待处理文本长度: {len(text)} 字符")

            print(project_code)

            # 构建请求数据
            request_data = {"text": text}
            # 如果project_code为空，默认使用'master'
            if project_code:
                request_data["project_code"] = project_code
            else:
                request_data["project_code"] = "master"
            
            response = requests.post(
                self.deidentify_url,
                json=request_data,
                timeout=self.timeout,
                headers={"Content-Type": "application/json"}
            )

            processing_time = time.time() - start_time

            if response.status_code == 200:
                data = response.json()

                # 验证返回数据格式
                if not isinstance(data, dict):
                    raise ValueError("服务返回数据格式错误：不是有效的JSON对象")

                sensitive_words = data.get('sensitive_words', [])
                deidentified_text = data.get('deidentified_text', text)
                total_entities = data.get('total_entities', len(sensitive_words))

                print(f"✅ HIPAA脱敏完成，识别到 {total_entities} 个敏感实体")
                print(f"⏱️ 处理耗时: {processing_time:.2f}s")

                return DeidentifyResult(
                    sensitive_words=sensitive_words,
                    deidentified_text=deidentified_text,
                    processing_time=processing_time,
                    total_entities=total_entities,
                    success=True
                )

            elif response.status_code == 400:
                error_data = response.json()
                error_msg = error_data.get('detail', {}).get('message', '请求参数错误')
                print(f"❌ HIPAA服务参数错误: {error_msg}")

                return DeidentifyResult(
                    sensitive_words=[],
                    deidentified_text=text,
                    processing_time=time.time() - start_time,
                    total_entities=0,
                    success=False,
                    error_message=f"请求参数错误: {error_msg}"
                )

            else:
                error_msg = f"服务返回错误，状态码: {response.status_code}"
                print(f"❌ HIPAA服务错误: {error_msg}")

                return DeidentifyResult(
                    sensitive_words=[],
                    deidentified_text=text,
                    processing_time=time.time() - start_time,
                    total_entities=0,
                    success=False,
                    error_message=error_msg
                )

        except requests.exceptions.Timeout:
            error_msg = f"请求超时（{self.timeout}秒）"
            print(f"⏰ HIPAA服务超时: {error_msg}")

            return DeidentifyResult(
                sensitive_words=[],
                deidentified_text=text,
                processing_time=time.time() - start_time,
                total_entities=0,
                success=False,
                error_message=error_msg
            )

        except requests.exceptions.ConnectionError:
            error_msg = f"无法连接到脱敏服务: {self.base_url}"
            print(f"🔌 HIPAA服务连接失败: {error_msg}")

            return DeidentifyResult(
                sensitive_words=[],
                deidentified_text=text,
                processing_time=time.time() - start_time,
                total_entities=0,
                success=False,
                error_message=error_msg
            )

        except requests.exceptions.RequestException as e:
            error_msg = f"请求失败: {str(e)}"
            print(f"❌ HIPAA服务请求异常: {error_msg}")

            return DeidentifyResult(
                sensitive_words=[],
                deidentified_text=text,
                processing_time=time.time() - start_time,
                total_entities=0,
                success=False,
                error_message=error_msg
            )

        except Exception as e:
            error_msg = f"未知错误: {str(e)}"
            print(f"💥 HIPAA服务未知异常: {error_msg}")

            return DeidentifyResult(
                sensitive_words=[],
                deidentified_text=text,
                processing_time=time.time() - start_time,
                total_entities=0,
                success=False,
                error_message=error_msg
            )

    async def deidentify_async(self, text: str) -> DeidentifyResult:
        """
        异步调用脱敏接口

        Args:
            text: 待脱敏的文本

        Returns:
            DeidentifyResult: 脱敏结果对象
        """
        if not text or not text.strip():
            raise ValueError("文本内容不能为空")

        try:
            import aiohttp

            timeout = aiohttp.ClientTimeout(total=self.timeout)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(
                    self.deidentify_url,
                    json={"text": text},
                    headers={"Content-Type": "application/json"}
                ) as response:

                    if response.status == 200:
                        data = await response.json()
                        return DeidentifyResult(
                            sensitive_words=data['sensitive_words'],
                            deidentified_text=data['deidentified_text'],
                            processing_time=data['processing_time'],
                            total_entities=data['total_entities']
                        )

                    elif response.status == 400:
                        error_data = await response.json()
                        raise ValueError(f"请求参数错误: {error_data.get('detail', {}).get('message', '未知错误')}")

                    else:
                        raise RuntimeError(f"服务返回错误，状态码: {response.status}")

        except asyncio.TimeoutError:
            raise TimeoutError(f"异步请求超时（{self.timeout}秒）")

        except ImportError:
            raise RuntimeError("异步功能需要安装aiohttp: pip install aiohttp")

        except Exception as e:
            if "aiohttp" in str(type(e)):
                raise ConnectionError(f"无法连接到脱敏服务: {self.base_url}")
            raise RuntimeError(f"异步请求失败: {str(e)}")


# 便捷函数
def deidentify_text(text: str,
                   base_url: str = None,
                   timeout: int = 30) -> DeidentifyResult:
    if base_url is None:
        base_url = HIPAA_DEFAULT_URL
    """
    便捷的脱敏函数

    Args:
        text: 待脱敏的文本
        base_url: 服务地址
        timeout: 超时时间

    Returns:
        DeidentifyResult: 脱敏结果
    """
    client = DeidentifyClient(base_url=base_url, timeout=timeout)
    return client.deidentify(text)


async def deidentify_text_async(text: str,
                               base_url: str = None,
                               timeout: int = 30) -> DeidentifyResult:
    if base_url is None:
        base_url = HIPAA_DEFAULT_URL
    """
    便捷的异步脱敏函数

    Args:
        text: 待脱敏的文本
        base_url: 服务地址
        timeout: 超时时间

    Returns:
        DeidentifyResult: 脱敏结果
    """
    client = DeidentifyClient(base_url=base_url, timeout=timeout)
    return await client.deidentify_async(text)
