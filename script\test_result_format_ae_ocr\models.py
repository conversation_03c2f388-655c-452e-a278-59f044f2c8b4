"""
数据模型：定义医院检验单处理系统的数据结构
"""
from dataclasses import dataclass
from typing import Dict, Optional, Any, List, Tuple

@dataclass
class TestItem:
    """单个检验项目数据模型"""
    test_code: str                                    # 检查代码
    test_name: str                                    # 检查名称
    test_value: str                                   # 检查结果值
    test_flag: int                                    # 检查结果值标志 (0正常/1偏高/2偏低)
    test_type: str                                    # 检查结果值类型 (数值/定性)
    test_unit: Optional[str] = None                   # 检查单位
    reference_value: Optional[str] = None             # 参考范围
    reference_range_min: Optional[float] = None       # 参考范围最小值
    reference_range_max: Optional[float] = None       # 参考范围最大值
    test_text: Optional[str] = None                   # 检查名称对应的所有文本
    collect_time: Optional[str] = None                # 采集时间
    report_time: Optional[str] = None                 # 报告时间
    abnormal_symbol: Optional[str] = None
    page_num: Optional[int] = None                        # 所属页码
    # 坐标信息（可选）- 与OCR数据格式完全一致
    location: Optional[List[List[float]]] = None          # 四个坐标点：[[左上x,左上y], [右上x,右上y], [右下x,右下y], [左下x,左下y]]
    
    def __post_init__(self):
        """初始化后处理"""
        # 标准化数据
        self.test_code = self.test_code.strip()
        self.test_name = self.test_name.strip()
        self.test_value = str(self.test_value).strip()
        
        if self.test_unit:
            self.test_unit = self.test_unit.strip()
        if self.reference_value:
            self.reference_value = self.reference_value.strip()
        if self.test_text:
            self.test_text = self.test_text.strip()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'test_code': self.test_code,
            'test_name': self.test_name,
            'test_value': self.test_value,
            'test_flag': self.test_flag,
            'test_type': self.test_type,
            'test_unit': self.test_unit,
            'reference_value': self.reference_value,
            'reference_range_min': self.reference_range_min,
            'reference_range_max': self.reference_range_max,
            'test_text': self.test_text,
            'collect_time': self.collect_time,
            'report_time': self.report_time,
            'abnormal_symbol': self.abnormal_symbol,
            'page_num': self.page_num,
            'location': self.location,
        }
    
    @property
    def is_abnormal(self) -> bool:
        """是否异常"""
        return self.test_flag != 0

