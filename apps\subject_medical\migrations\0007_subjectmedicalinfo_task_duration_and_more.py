# Generated by Django 4.1.5 on 2025-08-18 14:59

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("subject_medical", "0006_subjectmedicalinfo_page_count"),
    ]

    operations = [
        migrations.AddField(
            model_name="subjectmedicalinfo",
            name="task_duration",
            field=models.FloatField(
                blank=True, null=True, verbose_name="任务总耗时(秒)"
            ),
        ),
        migrations.AddField(
            model_name="subjectmedicalinfo",
            name="task_end_time",
            field=models.DateTimeField(
                blank=True, null=True, verbose_name="任务结束时间"
            ),
        ),
        migrations.AddField(
            model_name="subjectmedicalinfo",
            name="task_start_time",
            field=models.DateTimeField(
                blank=True, null=True, verbose_name="任务开始时间"
            ),
        ),
    ]
