# -*- coding: utf-8 -*-
"""
医疗检验单识别系统核心模块

核心功能：
- 医疗检验单OCR文本处理和结构化提取

注意：测试、评估和批量处理功能请使用 test_main.py
"""

import sys
import os
import json
import logging
import re
from tabulate import tabulate
from typing import List, Tuple, Dict, Optional

# 获取当前脚本的绝对路径
current_script_path = os.path.abspath(__file__)
# 获取项目根目录 (假设项目根目录是 d:\Code\smo-ai-backend)
# 向上两级目录，从 script/test_result_format_ae_ocr/main.py 到 d:\Code\smo-ai-backend
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_script_path)))

# 将项目根目录添加到 sys.path
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from script.test_result_format_ae_ocr.flow import create_flow
from script.test_result_format_ae_ocr.utils.coordinate_extraction import extract_row_coordinates

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def split_ocr_text_by_pages(ocr_text: str, ocr_blocks: Optional[List[Dict]] = None) -> Tuple[List[Tuple[str, int]], Dict[int, List[Dict]]]:
    """
    将多页OCR文本按页面拆分，同时分组对应的OCR块
    
    Args:
        ocr_text: 可能包含多页的OCR文本
        ocr_blocks: 可选的OCR块列表，包含坐标信息
        
    Returns:
        Tuple[List[Tuple[str, int]], Dict[int, List[Dict]]]: 
            - 每页内容和页码的列表
            - 按页码分组的OCR块字典 {page_num: words_block_list}
    """
    if not ocr_text or not ocr_text.strip():
        return [], {}
    
    try:
        # 使用正则表达式匹配页面标记 [Page N]
        page_pattern = r'\[Page\s+(\d+)\]'
        
        # 查找所有页面标记的位置
        page_matches = list(re.finditer(page_pattern, ocr_text, re.IGNORECASE))
        
        # 预处理OCR块，按页码分组
        page_blocks_map = {}
        if ocr_blocks:
            for block in ocr_blocks:
                page_num = block.get('page')
                if page_num is not None:
                    words_block_list = block.get('words_block_list', [])
                    if page_num not in page_blocks_map:
                        page_blocks_map[page_num] = []
                    page_blocks_map[page_num].extend(words_block_list)
        
        # 防御性检查：如果没有找到页面标记或没有找到[Page 1]，按单页处理
        if not page_matches:
            logger.info("未找到页面标记，按单页处理")
            single_page_blocks = page_blocks_map.get(1, [])
            return [(ocr_text.strip(), 1)], {1: single_page_blocks}
        
        # 检查是否存在[Page 1]，如果不存在则按单页处理
        has_page_1 = any(int(match.group(1)) == 1 for match in page_matches)
        if not has_page_1:
            logger.info("未找到[Page 1]标记，按单页处理")
            single_page_blocks = page_blocks_map.get(1, [])
            return [(ocr_text.strip(), 1)], {1: single_page_blocks}
        
        pages = []
        
        for i, match in enumerate(page_matches):
            page_num = int(match.group(1))
            start_pos = match.end()  # 页面标记之后的位置
            
            # 确定当前页面的结束位置
            if i + 1 < len(page_matches):
                # 不是最后一页，结束位置是下一个页面标记的开始
                end_pos = page_matches[i + 1].start()
            else:
                # 最后一页，结束位置是文本末尾
                end_pos = len(ocr_text)
            
            # 提取页面内容
            page_content = ocr_text[start_pos:end_pos].strip()
            
            if page_content:  # 只添加非空页面
                pages.append((page_content, page_num))
                # 获取当前页面的OCR块数量用于日志
                current_page_blocks = page_blocks_map.get(page_num, [])
                logger.info(f"提取到第{page_num}页，内容长度: {len(page_content)}，OCR块数: {len(current_page_blocks)}")
        
        # 防御性检查：如果拆分后没有有效页面，按单页处理
        if not pages:
            logger.warning("页面拆分后没有有效内容，按单页处理")
            single_page_blocks = page_blocks_map.get(1, [])
            return [(ocr_text.strip(), 1)], {1: single_page_blocks}
        
        logger.info(f"总共拆分出 {len(pages)} 页")
        return pages, page_blocks_map
        
    except Exception as e:
        # 防御性处理：如果拆分过程中出现任何异常，按单页处理
        logger.warning(f"页面拆分过程中发生异常: {e}，按单页处理")
        single_page_blocks = page_blocks_map.get(1, []) if ocr_blocks else []
        return [(ocr_text.strip(), 1)], {1: single_page_blocks}


def process_single_page_ocr(page_content: str, page_num: int = 1, task_info: dict = None,
                           words_block_list: Optional[List[Dict]] = None):
    """
    处理单页医疗检验单OCR文本

    Args:
        page_content: 单页OCR文本内容
        page_num: 页码（用于日志记录）
        task_info: 任务信息字典，用于LLM调用日志记录
        words_block_list: 可选的OCR文本块列表（包含坐标信息），传入时启用坐标提取


    Returns:
        List[TestItem]: 处理后的检验项目列表（包含坐标信息，如果提供了words_block_list）
    """
    # 根据是否提供OCR块决定是否启用坐标提取
    enable_coordinates = words_block_list is not None and len(words_block_list) > 0
    flow = create_flow(enable_coordinate_extraction=enable_coordinates)

    # 🎯 准备共享数据，包含任务信息和OCR块
    shared = {
        "ocr_text": page_content,
        "task_info": task_info or {},  # 传递任务信息到共享存储
        "words_block_list": words_block_list or []  # 传递OCR块信息
    }
    
    try:
        # 运行流程
        flow.run(shared)
        
        # 获取结果
        result = shared.get("processed_items")
        if not result:
            logger.warning(f"第{page_num}页流程执行完成但未生成结果")
            return []
        
        # 获取各种统计信息
        llm_stats = shared.get("llm_stats", {})
        llm_time = llm_stats.get('processing_time', 0)
        llm_chars = llm_stats.get('response_length', 0)

        # 为每个结果设置页码
        for item in result:
            item.page_num = page_num
        
        # 输出页面处理统计信息
        row_coordinates = shared.get("row_coordinates", [])
        coord_info = f"，{len(row_coordinates)} 行坐标" if row_coordinates else ""
        logger.info(f"第{page_num}页处理完成，识别到 {len(result)} 个检验项目{coord_info}")
        if llm_stats:
            logger.info(f"第{page_num}页LLM处理: {llm_time:.2f}秒, 输出{llm_chars}字符")

        return result
        
    except Exception as e:
        logger.error(f"处理第{page_num}页时发生错误: {e}")
        return []


def process_medical_ocr(ocr_text: str, task_info: dict = None,
                       ocr_blocks: Optional[List[Dict]] = None):
    """
    处理医疗检验单OCR文本（支持多页）

    Args:
        ocr_text: OCR识别的文本，可能包含多页
        task_info: 任务信息字典，包含task_id, create_user等日志参数
        ocr_blocks: 可选的OCR文本块列表（包含坐标信息），支持传入字符串或字典格式，传入时启用坐标提取
        ocr_blocks数据结构：[{
            "page": 页码,
            "words_block_list": [
                {
                    "words": "文本内容",
                    "confidence": 置信度,
                    "location": [[x1, y1],[x2, y2],[x3, y3],[x4, y4]]
                },
            ]
        }]
    Returns:
        List[TestItem]: 处理后的检验项目列表（包含坐标信息，如果提供了ocr_blocks）
    """
    logger.info("开始处理医疗检验单OCR文本")
    
    # 🎯 解析ocr_blocks参数，支持字符串和字典格式
    if ocr_blocks is not None:
        if isinstance(ocr_blocks, str):
            try:
                # 如果是字符串，尝试解析为JSON
                import json
                ocr_blocks = json.loads(ocr_blocks)
                logger.info("成功解析字符串类型的ocr_blocks")
            except (json.JSONDecodeError, TypeError) as e:
                logger.warning(f"解析ocr_blocks字符串失败: {e}，将禁用坐标提取功能")
                ocr_blocks = None
        elif not isinstance(ocr_blocks, list):
            logger.warning(f"ocr_blocks格式不支持，期望list或str，实际得到{type(ocr_blocks)}，将禁用坐标提取功能")
            ocr_blocks = None
    
    # 1. 拆分页面和OCR块
    pages, page_blocks_map = split_ocr_text_by_pages(ocr_text, ocr_blocks)
    
    if not pages:
        logger.warning("没有有效的页面内容")
        return []
    
    # 2. 处理每一页并收集结果
    all_results = []
    total_items = 0
    
    for page_content, page_num in pages:
        try:
            # 🎯 获取当前页面的OCR块（已经在split函数中分组好了）
            current_page_blocks = page_blocks_map.get(page_num, [])
            # 传递任务信息和当前页面的OCR块到单页处理函数
            page_results = process_single_page_ocr(page_content, page_num, task_info, current_page_blocks)
            if page_results:
                all_results.extend(page_results)
                total_items += len(page_results)
                logger.info(f"第{page_num}页成功处理，获得 {len(page_results)} 个检验项目")
            else:
                logger.warning(f"第{page_num}页未获得有效结果")
        except Exception as e:
            logger.error(f"处理第{page_num}页时发生异常: {e}")
            continue
    
    # 3. 输出汇总统计信息
    print("\n📊 多页处理统计:")
    print("=" * 50)
    print(f"📄 总页数: {len(pages)}")
    print(f"🔬 总检验项目数: {total_items}")
    print(f"📊 平均每页项目数: {total_items/len(pages):.1f}" if pages else "0")
    print("=" * 50)

    # 4. 输出坐标信息日志（如果启用了坐标提取）
    # if ocr_blocks and all_results:
    #     print("\n📍 坐标信息日志:")
    #     print("=" * 50)
    #     coordinate_log = {
    #         "words_block_list": []
    #     }

    #     items_with_coordinates = [item for item in all_results if hasattr(item, 'location') and item.location]
    #     print(f"包含坐标信息的项目数: {len(items_with_coordinates)}")

    #     for item in items_with_coordinates:
    #         if item.location:
    #             coordinate_entry = {
    #                 "words": item.test_name,
    #                 "location": item.location
    #             }
    #             coordinate_log["words_block_list"].append(coordinate_entry)

    #             # 输出格式化的坐标信息
    #             print(f"  {item.test_name}: {item.location}")

    #     # 输出JSON格式的坐标日志
    #     import json
    #     print("\n📋 JSON格式坐标日志:")
    #     print(json.dumps(coordinate_log, ensure_ascii=False, indent=2))
    #     print("=" * 50)

    # 5. 输出最终结果表格
    if all_results:
        print("\n检验项目识别结果（所有页面汇总）:")
        headers = ["页码", "检查代码", "检查名称", "检查结果值", "异常符号", "检查单位", "参考范围", "坐标"]
        table_data = []
        for item in all_results:
            print(item)
            table_data.append([
                item.page_num or "",
                item.test_code or "",
                item.test_name or "",
                item.test_value or "",
                item.abnormal_symbol or "",
                item.test_unit or "",
                item.reference_value or "",
                item.location or ""
            ])
        print(tabulate(table_data, headers=headers, tablefmt="grid"))
    
    logger.info(f"医疗检验单处理完成，总共获得 {total_items} 个检验项目")
    return all_results





if __name__ == "__main__":
    # 读取D:\Code\smo-ai-backend\script\test_result_format_ae_ocr\test_data\muti_page_ocr.json 为ocr_blocks
    # 读取D:\Code\smo-ai-backend\script\test_result_format_ae_ocr\test_data\muti_page_text.txt 为ocr_text
    with open(r"D:\Code\smo-ai-backend\script\test_result_format_ae_ocr\test_data\ocr_test_data.json", "r", encoding="utf-8") as f:
        ocr_blocks = json.load(f)
    with open(r"D:\Code\smo-ai-backend\script\test_result_format_ae_ocr\test_data\text_test_data.txt", "r", encoding="utf-8") as f:
        ocr_text = f.read()
    process_medical_ocr(ocr_text, ocr_blocks=ocr_blocks)










