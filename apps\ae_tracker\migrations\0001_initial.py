# Generated by Django 4.1.5 on 2025-03-26 22:00

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        (
            "subject",
            "0002_subjectepoch_alter_subject_update_time_subjectvisit_and_more",
        ),
        ("patient", "0002_alter_patient_update_time"),
        ("subject_medical", "0001_initial"),
        ("project", "0002_alter_project_update_time_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="TestResult",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "delete_flag",
                    models.SmallIntegerField(
                        choices=[(0, "未删除"), (1, "已删除")],
                        db_index=True,
                        default=0,
                        verbose_name="删除标志（0：未删除；1：已删除）",
                    ),
                ),
                (
                    "data_version",
                    models.PositiveIntegerField(default=0, verbose_name="数据版本"),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True, db_index=True, verbose_name="创建时间"
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name="更新时间"
                    ),
                ),
                (
                    "create_user",
                    models.CharField(max_length=255, null=True, verbose_name="创建人工号"),
                ),
                (
                    "create_name",
                    models.CharField(max_length=255, null=True, verbose_name="创建人姓名"),
                ),
                (
                    "update_user",
                    models.CharField(max_length=255, null=True, verbose_name="更新人工号"),
                ),
                (
                    "update_name",
                    models.CharField(max_length=255, null=True, verbose_name="更新人姓名"),
                ),
                (
                    "test_type",
                    models.CharField(
                        blank=True, max_length=50, null=True, verbose_name="检查结果值类型"
                    ),
                ),
                (
                    "test_code",
                    models.CharField(
                        blank=True, max_length=100, null=True, verbose_name="检查代码"
                    ),
                ),
                (
                    "test_name",
                    models.CharField(
                        blank=True, max_length=100, null=True, verbose_name="检查名称"
                    ),
                ),
                (
                    "test_unit",
                    models.CharField(
                        blank=True, max_length=20, null=True, verbose_name="检查单位"
                    ),
                ),
                (
                    "test_value",
                    models.CharField(
                        blank=True, max_length=20, null=True, verbose_name="检查结果值"
                    ),
                ),
                (
                    "test_flag",
                    models.SmallIntegerField(
                        blank=True,
                        default=0,
                        null=True,
                        verbose_name="检查结果标志；0：正常，1：偏高，2：偏低",
                    ),
                ),
                (
                    "abnormal_flag",
                    models.SmallIntegerField(
                        default=0, verbose_name="是否为异常值；0：正常， 1：异常"
                    ),
                ),
                (
                    "reference_value",
                    models.CharField(
                        blank=True, max_length=20, null=True, verbose_name="参考值"
                    ),
                ),
                (
                    "reference_range_min",
                    models.CharField(
                        blank=True, max_length=20, null=True, verbose_name="参考范围最小值"
                    ),
                ),
                (
                    "reference_range_max",
                    models.CharField(
                        blank=True, max_length=20, null=True, verbose_name="参考范围最大值"
                    ),
                ),
                (
                    "collect_time",
                    models.DateTimeField(blank=True, null=True, verbose_name="采集时间"),
                ),
                (
                    "report_time",
                    models.DateTimeField(blank=True, null=True, verbose_name="报告时间"),
                ),
                (
                    "ae_name",
                    models.CharField(
                        blank=True, max_length=50, null=True, verbose_name="不良事件名称"
                    ),
                ),
                (
                    "ae_grade",
                    models.CharField(
                        blank=True,
                        max_length=50,
                        null=True,
                        verbose_name="判定CS等级:数据字典 AE_GRADE_CODE",
                    ),
                ),
                (
                    "ae_desc",
                    models.CharField(
                        blank=True, max_length=20, null=True, verbose_name="检查结果/AE事件描述"
                    ),
                ),
                (
                    "ae_edit_flag",
                    models.CharField(
                        blank=True,
                        max_length=20,
                        null=True,
                        verbose_name="是否编辑过AE信息：0-否 1-是",
                    ),
                ),
                (
                    "ae_ai_result_list",
                    models.JSONField(blank=True, null=True, verbose_name="多个AI判断的结果"),
                ),
                (
                    "ae_ai_result_flag",
                    models.SmallIntegerField(
                        blank=True,
                        null=True,
                        verbose_name="表示AI结果是否不一致；0：一致，1：不一致，2：已确认",
                    ),
                ),
                (
                    "medical_history_flag",
                    models.SmallIntegerField(
                        blank=True, null=True, verbose_name="是否病史标识：0-否 1-是"
                    ),
                ),
                (
                    "ae_tracker_flag",
                    models.SmallIntegerField(
                        blank=True, default=0, verbose_name="是否ae_tracker：0-否 1-是"
                    ),
                ),
                (
                    "patient",
                    models.ForeignKey(
                        db_column="patient_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="test_results",
                        to="patient.patient",
                        to_field="patient_id",
                        verbose_name="患者ID",
                    ),
                ),
                (
                    "project",
                    models.ForeignKey(
                        db_column="project_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="test_results",
                        to="project.project",
                        to_field="project_id",
                        verbose_name="项目ID",
                    ),
                ),
                (
                    "project_site",
                    models.ForeignKey(
                        db_column="project_site_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="test_results",
                        to="project.projectsite",
                        to_field="project_site_id",
                        verbose_name="项目中心ID",
                    ),
                ),
                (
                    "subject",
                    models.ForeignKey(
                        db_column="subject_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="test_results",
                        to="subject.subject",
                        to_field="subject_id",
                        verbose_name="受试者ID",
                    ),
                ),
                (
                    "subject_epoch",
                    models.ForeignKey(
                        db_column="subject_epoch_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="test_results",
                        to="subject.subjectepoch",
                        to_field="subject_epoch_id",
                        verbose_name="受试者阶段ID）",
                    ),
                ),
                (
                    "subject_item",
                    models.ForeignKey(
                        db_column="subject_item_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="test_results",
                        to="subject.subjectitem",
                        to_field="subject_item_id",
                        verbose_name="受试者操作项ID）",
                    ),
                ),
                (
                    "subject_medical_info",
                    models.ForeignKey(
                        db_column="subject_medical_info_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="test_results",
                        to="subject_medical.subjectmedicalinfo",
                        verbose_name="医疗信息ID",
                    ),
                ),
                (
                    "subject_visit",
                    models.ForeignKey(
                        db_column="subject_visit_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="test_results",
                        to="subject.subjectvisit",
                        to_field="subject_visit_id",
                        verbose_name="受试者访视ID）",
                    ),
                ),
            ],
            options={
                "verbose_name": "化验结果",
                "verbose_name_plural": "化验结果",
                "db_table": "test_result",
            },
        ),
        migrations.CreateModel(
            name="AeTrackerTask",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "delete_flag",
                    models.SmallIntegerField(
                        choices=[(0, "未删除"), (1, "已删除")],
                        db_index=True,
                        default=0,
                        verbose_name="删除标志（0：未删除；1：已删除）",
                    ),
                ),
                (
                    "data_version",
                    models.PositiveIntegerField(default=0, verbose_name="数据版本"),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True, db_index=True, verbose_name="创建时间"
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name="更新时间"
                    ),
                ),
                (
                    "create_user",
                    models.CharField(max_length=255, null=True, verbose_name="创建人工号"),
                ),
                (
                    "create_name",
                    models.CharField(max_length=255, null=True, verbose_name="创建人姓名"),
                ),
                (
                    "update_user",
                    models.CharField(max_length=255, null=True, verbose_name="更新人工号"),
                ),
                (
                    "update_name",
                    models.CharField(max_length=255, null=True, verbose_name="更新人姓名"),
                ),
                ("name", models.CharField(max_length=255, verbose_name="任务名称")),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("OCR_EXTRACTION", "OCR_EXTRACTION"),
                            ("AE_RECOGNITION", "AE_RECOGNITION"),
                            ("PIC_MASK", "PIC_MASK"),
                        ],
                        default="OCR_EXTRACTION",
                        max_length=20,
                        verbose_name="任务分类",
                    ),
                ),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="任务描述"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("TODO", "待办"),
                            ("IN_PROGRESS", "进行中"),
                            ("COMPLETED", "已完成"),
                            ("CANCELLED", "已取消"),
                            ("ERROR", "执行错误"),
                        ],
                        default="TODO",
                        max_length=20,
                        verbose_name="任务状态",
                    ),
                ),
                (
                    "start_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="开始时间"),
                ),
                (
                    "end_time",
                    models.DateTimeField(blank=True, null=True, verbose_name="结束时间"),
                ),
                (
                    "patient",
                    models.ForeignKey(
                        db_column="patient_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="ae_tracker_tasks",
                        to="patient.patient",
                        to_field="patient_id",
                        verbose_name="患者ID",
                    ),
                ),
                (
                    "project",
                    models.ForeignKey(
                        db_column="project_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="ae_tracker_tasks",
                        to="project.project",
                        to_field="project_id",
                        verbose_name="项目ID",
                    ),
                ),
                (
                    "project_site",
                    models.ForeignKey(
                        db_column="project_site_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="ae_tracker_tasks",
                        to="project.projectsite",
                        to_field="project_site_id",
                        verbose_name="项目中心ID",
                    ),
                ),
                (
                    "subject",
                    models.ForeignKey(
                        db_column="subject_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="ae_tracker_tasks",
                        to="subject.subject",
                        to_field="subject_id",
                        verbose_name="受试者ID",
                    ),
                ),
                (
                    "subject_epoch",
                    models.ForeignKey(
                        db_column="subject_epoch_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="ae_tracker_tasks",
                        to="subject.subjectepoch",
                        to_field="subject_epoch_id",
                        verbose_name="受试者阶段ID）",
                    ),
                ),
                (
                    "subject_item",
                    models.ForeignKey(
                        db_column="subject_item_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="ae_tracker_tasks",
                        to="subject.subjectitem",
                        to_field="subject_item_id",
                        verbose_name="受试者操作项ID）",
                    ),
                ),
                (
                    "subject_visit",
                    models.ForeignKey(
                        db_column="subject_visit_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="ae_tracker_tasks",
                        to="subject.subjectvisit",
                        to_field="subject_visit_id",
                        verbose_name="受试者访视ID）",
                    ),
                ),
            ],
            options={
                "verbose_name": "AE Tracker任务",
                "verbose_name_plural": "AE Tracker任务",
                "db_table": "ae_tracker_task",
            },
        ),
    ]
