"""
只掉smo.setting,不让smo setting调本文件
"""
import time
import random
import requests
from django.conf import settings
from apps.ae_tracker.models import TestResult

JAVA_IP = settings.JAVA_PUSH_TRACKER_IP


def post_tracker(project_id, project_site_id, subject_id, subject_item_id, username, realname):
    """推送tracker给JAVA"""
    url = JAVA_IP + '/ai-platform/ae/trackerInfo/createAeTrackerAuto'
    data = {
        "projectId": project_id,
        "projectSiteId": project_site_id,
        "subjectId": subject_id,
        "subjectItemId": subject_item_id,
        "username": username,
        "realname": realname
    }
    try:
        response = requests.post(url, json=data, timeout=30)  # 添加超时设置
        response.raise_for_status()  # 检查HTTP错误
    except requests.exceptions.RequestException as e:
        # 记录错误日志
        print(f"Failed to create AE tracker for subject {subject_id}: {str(e)}")
    return True


def make_ae_tracker_flag(test_results, subject_item):
    """
    异常值tracker推送规则
    """
    grouped_results = {}
    # 对结果进行分组

    for result in test_results:
        key = (result.subject_id, result.test_name)
        if key not in grouped_results:
            grouped_results[key] = []
        grouped_results[key].append(result)
    # 遍历每个分组
    if int(subject_item.item_type) == 1:
        for group in grouped_results.values():
            # 检查分组中是否存在 ae_grade 大于 0 的记录
            if any((result.abnormal_flag is not None and result.abnormal_flag == 1) or
                   (result.abnormal_symbol is not None and result.abnormal_symbol.strip()) or (result.test_flag is not None and result.test_flag != 0) for result in group):
                # 如果存在，则将该分组所有记录的 ae_tracker_flag 更新为 1
                TestResult.objects.filter(
                    subject_id=group[0].subject_id,
                    subject_item__label=subject_item.label,
                    subject_item__ae_ai_current_step=6,
                    test_name=group[0].test_name,
                    delete_flag=0
                ).update(ae_tracker_flag=1)
            if all((result.abnormal_flag is None or result.abnormal_flag != 1) and
                   (result.abnormal_symbol is None or not result.abnormal_symbol.strip()) and (result.test_flag is not None and result.test_flag == 0) for result in group):
                # 如果分组中所有记录的 ae_grade 都为 0，则将该分组所有记录的 ae_tracker_flag 更新为 0
                TestResult.objects.filter(
                    subject_id=group[0].subject_id,
                    subject_item__label=subject_item.label,
                    subject_item__ae_ai_current_step=6,
                    test_name=group[0].test_name,
                    delete_flag=0
                ).update(ae_tracker_flag=0)
    else:
        for group in grouped_results.values():
            # 检查分组中是否存在 ae_grade 大于 0 的记录
            if any(result.ae_grade is not None and result.ae_grade.strip()  # 去除首尾空格后不为空
                   and result.ae_grade.isdigit() and int(result.ae_grade) > 0 for result in group):
                # 如果存在，则将该分组所有记录的 ae_tracker_flag 更新为 1
                TestResult.objects.filter(
                    subject_id=group[0].subject_id,
                    subject_item__label=subject_item.label,
                    subject_item__ae_ai_current_step=6,
                    test_name=group[0].test_name,
                    delete_flag=0
                ).update(ae_tracker_flag=1)
            if all(result.ae_grade is None or result.ae_grade == "" or (
                    result.ae_grade.isdigit() and int(result.ae_grade) == 0) for result in group):
                # 如果分组中所有记录的 ae_grade 都为 0，则将该分组所有记录的 ae_tracker_flag 更新为 0
                TestResult.objects.filter(
                    subject_id=group[0].subject_id,
                    subject_item__label=subject_item.label,
                    subject_item__ae_ai_current_step=6,
                    test_name=group[0].test_name,
                    delete_flag=0
                ).update(ae_tracker_flag=0)
    return True


def generate_numeric_unique_id():
    # 1. 获取当前时间戳（精确到微秒，转换为整数）
    timestamp = int(time.time() * 1000000)  # 微秒级时间戳，确保唯一性基础
    # 2. 生成随机数字（6位，减少重复概率）
    random_num = random.randint(100000, 999999)
    # 3. 拼接为纯数字ID
    unique_id = int(f"{timestamp}{random_num}")
    return unique_id