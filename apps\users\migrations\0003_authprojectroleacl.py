# Generated by Django 4.1.5 on 2025-04-19 20:05

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0002_user_project_whitelist_flag"),
    ]

    operations = [
        migrations.CreateModel(
            name="AuthProjectRoleACL",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "delete_flag",
                    models.SmallIntegerField(
                        choices=[(0, "未删除"), (1, "已删除")],
                        db_index=True,
                        default=0,
                        verbose_name="删除标志（0：未删除；1：已删除）",
                    ),
                ),
                (
                    "data_version",
                    models.PositiveIntegerField(default=0, verbose_name="数据版本"),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True, db_index=True, verbose_name="创建时间"
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name="更新时间"
                    ),
                ),
                (
                    "create_user",
                    models.CharField(max_length=255, null=True, verbose_name="创建人工号"),
                ),
                (
                    "create_name",
                    models.CharField(max_length=255, null=True, verbose_name="创建人姓名"),
                ),
                (
                    "update_user",
                    models.CharField(max_length=255, null=True, verbose_name="更新人工号"),
                ),
                (
                    "update_name",
                    models.CharField(max_length=255, null=True, verbose_name="更新人姓名"),
                ),
                (
                    "role_code",
                    models.CharField(max_length=100, unique=True, verbose_name="角色编码"),
                ),
                (
                    "role_name",
                    models.CharField(max_length=100, verbose_name="角色名称（中文）"),
                ),
                (
                    "role_name_en",
                    models.CharField(max_length=100, verbose_name="角色名称（英文）"),
                ),
                (
                    "is_allowed",
                    models.BooleanField(default=False, verbose_name="是否允许访问"),
                ),
                (
                    "access_scope",
                    models.SmallIntegerField(
                        choices=[(1, "访问项目所有功能"), (2, "访问项目部分功能")],
                        default=1,
                        verbose_name="访问范围",
                    ),
                ),
            ],
            options={
                "verbose_name": "项目角色访问权限控制表",
                "verbose_name_plural": "项目角色访问权限控制表列表",
                "db_table": "auth_project_role_acl",
            },
        ),
    ]
