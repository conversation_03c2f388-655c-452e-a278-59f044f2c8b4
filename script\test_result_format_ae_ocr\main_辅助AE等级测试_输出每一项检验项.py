# -*- coding: utf-8 -*-
"""
医疗检验单识别系统主程序

支持智能时间提取功能，包括：
- 正则表达式快速提取
- LLM备用提取
- 智能结果合并

新增测试功能：
- 支持单个图片文件或文件夹批量处理
- 集成OCR识别功能
- 可选的答案对比评估
- 命令行参数支持
"""

import sys
import os
import json
import logging
import argparse
import time
import re
from pathlib import Path
from tabulate import tabulate
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# 全局统计信息
global_stats = {}

# 获取当前脚本的绝对路径
current_script_path = os.path.abspath(__file__)
# 获取项目根目录 (假设项目根目录是 d:\Code\smo-ai-backend)
# 向上两级目录，从 script/test_result_format_ae_ocr/main.py 到 d:\Code\smo-ai-backend
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_script_path)))

# 将项目根目录添加到 sys.path
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from script.test_result_format_ae_ocr.flow import create_flow
from script.test_result_format_ae_ocr.evaluation import load_ground_truth, evaluate_ocr_results, print_evaluation_report, evaluate_ocr_results_with_cache
from script.test_result_format_ae_ocr.utils.ocr_client import OCRClient

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PerformanceTracker:
    """性能追踪器，记录处理时间和统计信息（线程安全）"""
    
    def __init__(self):
        self._lock = threading.Lock()  # 添加线程锁
        self.reset()
    
    def reset(self):
        """重置统计信息"""
        with self._lock:
            self.start_time = None
            self.end_time = None
            self.file_times = []  # 存储每个文件的处理时间
            self.file_details = []  # 存储每个文件的详细信息
            self.ocr_times = []  # OCR处理时间
            self.processing_times = []  # 医疗检验单处理时间
            self.total_files = 0
            self.total_items = 0
            self.failed_files = 0
    
    def start_session(self):
        """开始会话计时"""
        self.start_time = time.time()
        logger.info(f"开始处理会话: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    def end_session(self):
        """结束会话计时"""
        self.end_time = time.time()
        logger.info(f"结束处理会话: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    def record_file_processing(self, filename, ocr_time, processing_time, total_time, item_count, success=True):
        """记录单个文件的处理信息（线程安全）"""
        with self._lock:
            self.total_files += 1
            if success:
                self.file_times.append(total_time)
                self.ocr_times.append(ocr_time)
                self.processing_times.append(processing_time)
                self.total_items += item_count
                
                self.file_details.append({
                    'filename': filename,
                    'ocr_time': ocr_time,
                    'processing_time': processing_time,
                    'total_time': total_time,
                    'item_count': item_count,
                    'success': True
                })
                
                logger.info(f"文件 {filename} 处理完成 - OCR: {ocr_time:.2f}s, 处理: {processing_time:.2f}s, 总计: {total_time:.2f}s, 项目数: {item_count}")
            else:
                self.failed_files += 1
                self.file_details.append({
                    'filename': filename,
                    'ocr_time': 0,
                    'processing_time': 0,
                    'total_time': 0,
                    'item_count': 0,
                    'success': False
                })
                logger.warning(f"文件 {filename} 处理失败")
    
    def get_session_time(self):
        """获取会话总时间"""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return 0
    
    def get_statistics(self):
        """获取统计信息"""
        if not self.file_times:
            return None
        
        session_time = self.get_session_time()
        avg_file_time = sum(self.file_times) / len(self.file_times)
        avg_ocr_time = sum(self.ocr_times) / len(self.ocr_times) if self.ocr_times else 0
        avg_processing_time = sum(self.processing_times) / len(self.processing_times) if self.processing_times else 0
        
        return {
            'session_time': session_time,
            'total_files': self.total_files,
            'successful_files': len(self.file_times),
            'failed_files': self.failed_files,
            'total_items': self.total_items,
            'avg_file_time': avg_file_time,
            'avg_ocr_time': avg_ocr_time,
            'avg_processing_time': avg_processing_time,
            'min_file_time': min(self.file_times),
            'max_file_time': max(self.file_times),
            'throughput_files_per_second': len(self.file_times) / session_time if session_time > 0 else 0,
            'throughput_items_per_second': self.total_items / session_time if session_time > 0 else 0
        }
    
    def print_performance_report(self):
        """打印性能报告"""
        stats = self.get_statistics()
        if not stats:
            print("\n📊 性能统计: 无有效数据")
            return
        
        print("\n" + "=" * 60)
        print("📊 性能统计报告")
        print("=" * 60)
        
        # 基本统计
        print(f"🕐 会话总时间: {stats['session_time']:.2f} 秒")
        print(f"📁 处理文件总数: {stats['total_files']}")
        print(f"✅ 成功处理文件: {stats['successful_files']}")
        print(f"❌ 失败文件数: {stats['failed_files']}")
        print(f"📋 识别项目总数: {stats['total_items']}")
        
        # 时间统计
        print(f"\n⏱️  平均处理时间:")
        print(f"   • 单文件平均: {stats['avg_file_time']:.2f} 秒")
        print(f"   • OCR平均: {stats['avg_ocr_time']:.2f} 秒")
        print(f"   • 医疗处理平均: {stats['avg_processing_time']:.2f} 秒")
        print(f"   • 最快文件: {stats['min_file_time']:.2f} 秒")
        print(f"   • 最慢文件: {stats['max_file_time']:.2f} 秒")
        
        # 吞吐量统计
        print(f"\n🚀 处理吞吐量:")
        print(f"   • 文件处理速度: {stats['throughput_files_per_second']:.2f} 文件/秒")
        print(f"   • 项目识别速度: {stats['throughput_items_per_second']:.2f} 项目/秒")
        
        # 详细文件统计表格
        if len(self.file_details) <= 10:  # 如果文件数量不多，显示详细表格
            print(f"\n📋 详细文件处理统计:")
            headers = ["文件名", "OCR时间(s)", "处理时间(s)", "总时间(s)", "项目数", "状态"]
            table_data = []
            for detail in self.file_details:
                status = "✅" if detail['success'] else "❌"
                table_data.append([
                    detail['filename'][:30] + "..." if len(detail['filename']) > 30 else detail['filename'],
                    f"{detail['ocr_time']:.2f}" if detail['success'] else "N/A",
                    f"{detail['processing_time']:.2f}" if detail['success'] else "N/A",
                    f"{detail['total_time']:.2f}" if detail['success'] else "N/A",
                    detail['item_count'] if detail['success'] else "N/A",
                    status
                ])
            print(tabulate(table_data, headers=headers, tablefmt="grid"))
        else:
            print(f"\n📋 文件数量较多({len(self.file_details)}个)，跳过详细表格显示")
        
        print("=" * 60)


# 全局性能追踪器实例
performance_tracker = PerformanceTracker()


# 默认配置
DEFAULT_CONFIG = {
    'mode': 'test',  # 默认运行模式: test, eval, demo
    # 'input': r"D:\data\ocr\13张图片",  # 默认输入文件或文件夹
    'input': r"D:\data\AE-OCR-格式化\50张标答",  # 默认输入文件或文件夹
    'ground_truth': r"D:\data\ocr\13张图片\答案.xlsx",  # 默认正确答案文件（仅在eval和demo模式下使用）
    'max_workers': 13,  # 并发线程数，默认5
}

def get_default_config():
    """获取默认配置"""
    return DEFAULT_CONFIG.copy()

def update_default_config(**kwargs):
    """更新默认配置"""
    DEFAULT_CONFIG.update(kwargs)


def process_medical_ocr(ocr_text: str):
    """
    处理医疗检验单OCR文本
    
    Args:
        ocr_text: OCR识别的文本

    Returns:
        ProcessingResult: 处理结果对象
    """

    flow = create_flow()

    # 准备共享数据
    shared = {
        "ocr_text": ocr_text
    }
    
    try:
        # 运行流程
        flow.run(shared)
        
        # 获取结果
        result = shared.get("processed_items")
        if not result:
            logger.error(f"流程执行完成但未生成结果。Shared数据: {json.dumps(shared, ensure_ascii=False, indent=2)}")
            raise RuntimeError("流程执行完成但未生成结果")
        
        # 获取各种统计信息
        regex_times = shared.get("times", {})
        llm_times = shared.get("llm_times", {})
        final_times = shared.get("final_times", {})
        need_llm = shared.get("need_llm_time_extraction", False)
        llm_stats = shared.get("llm_stats", {})
        parsing_stats = shared.get("parsing_stats", {})
        validation_stats = shared.get("validation_stats", {})

        # 提取LLM统计数据
        llm_time = llm_stats.get('processing_time', 0)
        llm_chars = llm_stats.get('response_length', 0)
        
        # 更新全局统计信息
        if 'total_files' not in global_stats:
            global_stats['total_files'] = 0
            global_stats['total_llm_time'] = 0
            global_stats['total_llm_chars'] = 0
        
        global_stats['total_files'] += 1
        global_stats['total_llm_time'] += llm_time
        global_stats['total_llm_chars'] += llm_chars
        
        # 输出简化的处理统计信息
        print("\n📊 LLM性能统计:")
        print("=" * 50)
        
        # 只显示LLM统计
        if llm_stats:
            print(f"🤖 LLM处理: {llm_time:.2f}秒, 输出{llm_chars}字符")
            if llm_time > 0 and llm_chars > 0:
                print(f"   LLM速度: {llm_chars/llm_time:.0f} 字符/秒")
        
        print("=" * 50)

        # 输出最终结果
        if result:
            print("\n检验项目识别结果:")
            headers = ["检查代码", "检查名称", "检查结果值", "异常符号", "检查单位", "参考范围"]
            table_data = []
            for item in result:
                table_data.append([
                    item.test_code or "",
                    item.test_name or "",
                    item.test_value or "",
                    item.abnormal_symbol or "",
                    item.test_unit or "",
                    item.reference_value or ""
                ])
            print(tabulate(table_data, headers=headers, tablefmt="grid"))
        
        # 保留原有的markdown格式输出（注释掉，备用）
        # print("| test_code | test_name | test_value | abnormal_symbol | test_unit | reference_value |")
        # print("|-----------|-----------|------------|-----------|-----------------|-----------------|")
        # for item in result:
        #     print(
        #         f"| {item.test_code} | {item.test_name} | {item.test_value} | {item.abnormal_symbol} | {item.test_unit} | {item.reference_value} |")

        return result
        
    except Exception as e:
        logger.error(f"处理过程中发生错误: {e}")
        raise


def process_single_image(image_path: str, ocr_client: OCRClient = None, record_performance: bool = True) -> tuple:
    """
    处理单张图片：OCR识别 + 医疗检验单处理
    
    Args:
        image_path: 图片文件路径
        ocr_client: OCR客户端实例
        record_performance: 是否记录性能数据
        
    Returns:
        tuple: (ocr_text, processed_result)
    """
    if ocr_client is None:
        ocr_client = OCRClient()
    
    filename = Path(image_path).name
    total_start_time = time.time()
    
    logger.info(f"开始处理图片: {image_path}")
    
    try:
        # 检查文件是否存在
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"图片文件不存在: {image_path}")
        
        # 检查文件格式
        if not ocr_client.is_supported_image_format(image_path):
            raise ValueError(f"不支持的图片格式: {image_path}")
        
        # OCR识别
        logger.info("正在进行OCR识别...")
        ocr_start_time = time.time()
        ocr_text = ocr_client.ocr_main(image_path)
        ocr_end_time = time.time()
        ocr_time = ocr_end_time - ocr_start_time
        
        if not ocr_text.strip():
            logger.warning(f"OCR识别结果为空: {image_path}")
            if record_performance:
                performance_tracker.record_file_processing(filename, ocr_time, 0, time.time() - total_start_time, 0, False)
            return ocr_text, []
        
        logger.info(f"OCR识别完成，文本长度: {len(ocr_text)}，耗时: {ocr_time:.2f}秒")
        
        # 医疗检验单处理
        logger.info("正在进行医疗检验单处理...")
        processing_start_time = time.time()
        processed_result = process_medical_ocr(ocr_text)
        processing_end_time = time.time()
        processing_time = processing_end_time - processing_start_time
        
        total_time = time.time() - total_start_time
        item_count = len(processed_result) if processed_result else 0
        
        logger.info(f"处理完成，识别到 {item_count} 个检验项目，医疗处理耗时: {processing_time:.2f}秒")
        
        # 记录性能数据
        if record_performance:
            performance_tracker.record_file_processing(filename, ocr_time, processing_time, total_time, item_count, True)
        
        return ocr_text, processed_result
        
    except Exception as e:
        total_time = time.time() - total_start_time
        logger.error(f"处理文件 {filename} 时发生错误: {e}")
        if record_performance:
            performance_tracker.record_file_processing(filename, 0, 0, total_time, 0, False)
        raise


def process_image_directory(directory_path: str, ocr_client: OCRClient = None, max_workers: int = 5) -> dict:
    """
    批量处理文件夹中的图片（支持并发处理）
    
    Args:
        directory_path: 图片文件夹路径
        ocr_client: OCR客户端实例
        max_workers: 最大并发线程数，默认5
        
    Returns:
        dict: {filename: (ocr_text, processed_result)}
    """
    if ocr_client is None:
        ocr_client = OCRClient()
    
    directory = Path(directory_path)
    if not directory.exists():
        raise FileNotFoundError(f"文件夹不存在: {directory_path}")
    
    if not directory.is_dir():
        raise ValueError(f"路径不是文件夹: {directory_path}")
    
    results = {}
    supported_extensions = ('.png', '.jpg', '.jpeg', '.bmp', '.webp')
    
    # 获取所有支持的图片文件（避免重复）
    image_files = set()  # 使用set避免重复
    for ext in supported_extensions:
        image_files.update(directory.glob(f"*{ext}"))
        image_files.update(directory.glob(f"*{ext.upper()}"))
    
    image_files = list(image_files)  # 转换回list
    
    if not image_files:
        logger.warning(f"文件夹中没有找到支持的图片文件: {directory_path}")
        return results
    
    logger.info(f"找到 {len(image_files)} 个图片文件，将使用 {max_workers} 个线程并发处理")
    
    # 自定义排序函数：优先按数字排序，然后按字符排序
    def natural_sort_key(file_path):
        import re
        # 提取文件名（不含扩展名）
        name = file_path.stem
        # 将文件名分解为数字和非数字部分
        parts = re.split(r'(\d+)', name)
        # 将数字部分转换为整数，非数字部分保持字符串
        result = []
        for part in parts:
            if part.isdigit():
                result.append(int(part))
            else:
                result.append(part)
        return result
    
    # 按自然排序对文件进行排序
    sorted_image_files = sorted(image_files, key=natural_sort_key)
    
    # 定义单个文件处理函数（用于线程池）
    def process_single_file_wrapper(args):
        image_file, file_index, total_files = args
        try:
            logger.info(f"[线程] 开始处理第 {file_index}/{total_files} 个文件: {image_file.name}")
            
            # 为每个线程创建独立的OCR客户端实例，避免线程冲突
            thread_ocr_client = OCRClient()
            ocr_text, processed_result = process_single_image(str(image_file), thread_ocr_client)
            
            logger.info(f"[线程] 完成处理第 {file_index}/{total_files} 个文件: {image_file.name}")
            return image_file.stem, (ocr_text, processed_result)
        except Exception as e:
            logger.error(f"[线程] 处理文件 {image_file.name} 时发生错误: {e}")
            return image_file.stem, ("", [])
    
    # 准备任务参数
    tasks = [(image_file, i + 1, len(sorted_image_files)) for i, image_file in enumerate(sorted_image_files)]
    
    # 使用线程池并发处理
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务并记录任务顺序
        future_to_index = {}
        for i, task in enumerate(tasks):
            future = executor.submit(process_single_file_wrapper, task)
            future_to_index[future] = i
        
        # 收集结果（保持并行处理）
        completed_count = 0
        results_by_index = {}  # 用索引来保持顺序
        
        # 并行等待所有任务完成
        for future in as_completed(future_to_index):
            task_index = future_to_index[future]
            image_file = sorted_image_files[task_index]
            try:
                filename, result = future.result()
                results_by_index[task_index] = (filename, result)
                completed_count += 1
                logger.info(f"进度: {completed_count}/{len(sorted_image_files)} 个文件处理完成")
            except Exception as e:
                logger.error(f"处理文件 {image_file.name} 时发生异常: {e}")
                results_by_index[task_index] = (image_file.stem, ("", []))
                completed_count += 1
        
        # 按照原始排序顺序构建结果字典
        for i in range(len(sorted_image_files)):
            if i in results_by_index:
                filename, result = results_by_index[i]
                results[filename] = result
    
    logger.info(f"所有文件处理完成！成功处理 {len([r for r in results.values() if r[0]])} 个文件")
    return results


def match_results_with_ground_truth(results: dict, ground_truth_data: list) -> tuple:
    """
    根据文件名匹配处理结果和正确答案
    
    Args:
        results: 处理结果字典 {filename: (ocr_text, processed_result)}
        ground_truth_data: 正确答案数据列表
        
    Returns:
        tuple: (matched_ocr_texts, matched_ground_truth, file_mapping)
    """
    matched_ocr_texts = []
    matched_ground_truth = []
    file_mapping = []  # 记录文件名和匹配的Sheet的对应关系
    
    # 自定义排序函数：优先按数字排序，然后按字符排序
    def natural_sort_key(filename):
        import re
        # 将文件名分解为数字和非数字部分
        parts = re.split(r'(\d+)', filename)
        # 将数字部分转换为整数，非数字部分保持字符串
        result = []
        for part in parts:
            if part.isdigit():
                result.append(int(part))
            else:
                result.append(part)
        return result
    
    # 按自然排序处理文件
    for filename in sorted(results.keys(), key=natural_sort_key):
        ocr_text, _ = results[filename]
        # 尝试匹配文件名和Sheet名称
        matched = False
        
        # 方式1: 直接匹配文件名和Sheet名称
        for i, sheet_data in enumerate(ground_truth_data):
            sheet_name = f"Sheet{i+1}"  # 假设Sheet命名为Sheet1, Sheet2, ...
            if filename.lower() == sheet_name.lower():
                matched_ocr_texts.append(ocr_text)
                matched_ground_truth.append(sheet_data)
                file_mapping.append((filename, sheet_name, i))
                matched = True
                logger.info(f"文件 {filename} 匹配到 {sheet_name}")
                break
        
        # 方式2: 尝试从文件名中提取数字匹配
        if not matched:
            import re
            numbers = re.findall(r'\d+', filename)
            if numbers:
                try:
                    file_number = int(numbers[0])
                    if 1 <= file_number <= len(ground_truth_data):
                        matched_ocr_texts.append(ocr_text)
                        matched_ground_truth.append(ground_truth_data[file_number - 1])
                        sheet_name = f"Sheet{file_number}"
                        file_mapping.append((filename, sheet_name, file_number - 1))
                        matched = True
                        logger.info(f"文件 {filename} 通过数字 {file_number} 匹配到 {sheet_name}")
                except ValueError:
                    pass
        
        if not matched:
            logger.warning(f"文件 {filename} 无法匹配到正确答案")
    
    return matched_ocr_texts, matched_ground_truth, file_mapping


def print_file_accuracy_report(evaluation_report: dict, file_mapping: list):
    """
    打印每个文件的正确率报告
    
    Args:
        evaluation_report: 评估报告字典
        file_mapping: 文件映射列表 [(filename, sheet_name, sheet_index), ...]
    """
    print("\n" + "=" * 80)
    print("📊 各文件正确率统计")
    print("=" * 80)
    
    if not file_mapping:
        print("❌ 没有匹配的文件")
        return
    
    # 准备表格数据
    headers = ["文件名", "匹配Sheet", "项目准确率", "字段准确率", "异常符号召回率", "异常符号精确率", "匹配项目数", "总项目数"]
    table_data = []
    
    evaluation_logs = evaluation_report.get('evaluation_logs', [])
    
    for i, (filename, sheet_name, sheet_index) in enumerate(file_mapping):
        if i < len(evaluation_logs):
            log = evaluation_logs[i]
            
            # 计算项目准确率
            item_accuracy = log['matched_item_count'] / log['ground_truth_item_count'] if log['ground_truth_item_count'] > 0 else 0.0
            
            # 字段准确率已经在log中
            field_accuracy = log.get('field_accuracy', 0.0)
            
            # 计算异常符号召回率和精确率
            abnormal_stats = log.get('abnormal_symbol_stats', {})
            abnormal_recall = 0.0
            abnormal_precision = 0.0
            
            if abnormal_stats:
                # 计算召回率
                if abnormal_stats.get('ground_truth_count', 0) > 0:
                    abnormal_recall = (abnormal_stats.get('ground_truth_count', 0) - 
                                     abnormal_stats.get('missed_count', 0)) / abnormal_stats.get('ground_truth_count', 0)
                
                # 计算精确率
                if abnormal_stats.get('predicted_count', 0) > 0:
                    abnormal_precision = (abnormal_stats.get('predicted_count', 0) - 
                                        abnormal_stats.get('false_positive_count', 0)) / abnormal_stats.get('predicted_count', 0)
            
            # 计算正确字段数和总字段数
            matched_items = log['matched_item_count']
            total_items = log['ground_truth_item_count']
            
            table_data.append([
                filename[:20] + "..." if len(filename) > 20 else filename,
                sheet_name,
                f"{item_accuracy:.1%}",
                f"{field_accuracy:.1%}",
                f"{abnormal_recall:.1%}" if abnormal_stats else "N/A",
                f"{abnormal_precision:.1%}" if abnormal_stats else "N/A",
                f"{matched_items}",
                f"{total_items}"
            ])
        else:
            # 如果没有对应的评估日志，显示错误
            table_data.append([
                filename[:20] + "..." if len(filename) > 20 else filename,
                sheet_name,
                "N/A",
                "N/A",
                "N/A",
                "N/A",
                "N/A",
                "N/A"
            ])
    
    print(tabulate(table_data, headers=headers, tablefmt="grid"))
    
    # 计算总体统计
    if evaluation_logs:
        total_item_accuracy = sum(log['matched_item_count'] / log['ground_truth_item_count'] 
                                if log['ground_truth_item_count'] > 0 else 0.0 
                                for log in evaluation_logs) / len(evaluation_logs)
        
        total_field_accuracy = sum(log.get('field_accuracy', 0.0) for log in evaluation_logs) / len(evaluation_logs)
        
        # 计算异常符号的总体统计
        total_abnormal_ground_truth = sum(log.get('abnormal_symbol_stats', {}).get('ground_truth_count', 0) for log in evaluation_logs)
        total_abnormal_predicted = sum(log.get('abnormal_symbol_stats', {}).get('predicted_count', 0) for log in evaluation_logs)
        total_abnormal_missed = sum(log.get('abnormal_symbol_stats', {}).get('missed_count', 0) for log in evaluation_logs)
        total_abnormal_false_positive = sum(log.get('abnormal_symbol_stats', {}).get('false_positive_count', 0) for log in evaluation_logs)
        
        # 计算总体异常符号召回率和精确率
        overall_abnormal_recall = 0.0
        if total_abnormal_ground_truth > 0:
            overall_abnormal_recall = (total_abnormal_ground_truth - total_abnormal_missed) / total_abnormal_ground_truth
        
        overall_abnormal_precision = 0.0
        if total_abnormal_predicted > 0:
            overall_abnormal_precision = (total_abnormal_predicted - total_abnormal_false_positive) / total_abnormal_predicted
        
        # 计算F1分数
        overall_abnormal_f1 = 0.0
        if overall_abnormal_recall + overall_abnormal_precision > 0:
            overall_abnormal_f1 = 2 * (overall_abnormal_recall * overall_abnormal_precision) / (overall_abnormal_recall + overall_abnormal_precision)
        
        print(f"\n📈 总体平均正确率:")
        print(f"├─ 平均项目准确率: {total_item_accuracy:.1%}")
        print(f"└─ 平均字段准确率: {total_field_accuracy:.1%}")
        
        print(f"\n⚠️ 异常符号综合统计:")
        print(f"├─ 总异常符号数 (答案): {total_abnormal_ground_truth}")
        print(f"├─ 总异常符号数 (识别): {total_abnormal_predicted}")
        print(f"├─ 漏检数量: {total_abnormal_missed}")
        print(f"├─ 误检数量: {total_abnormal_false_positive}")
        print(f"├─ 总体召回率: {overall_abnormal_recall:.1%}")
        print(f"├─ 总体精确率: {overall_abnormal_precision:.1%}")
        print(f"└─ 总体F1分数: {overall_abnormal_f1:.1%}")
    
    print("=" * 80)


def run_evaluation_mode(args):
    """运行评估模式"""
    logger.info("=" * 60)
    logger.info("运行评估模式")
    logger.info("=" * 60)
    
    # 开始性能追踪
    performance_tracker.reset()
    performance_tracker.start_session()
    
    # 检查正确答案文件
    if not os.path.exists(args.ground_truth):
        raise FileNotFoundError(f"正确答案文件不存在: {args.ground_truth}")
    
    # 加载正确答案
    logger.info(f"加载正确答案: {args.ground_truth}")
    ground_truth_data = load_ground_truth(args.ground_truth)
    logger.info(f"加载了 {len(ground_truth_data)} 个正确答案")
    
    # 创建OCR客户端
    config = get_default_config()
    ocr_client = OCRClient(
    )
    
    # 处理输入
    if os.path.isfile(args.input):
        # 单个文件
        logger.info("处理单个图片文件")
        filename = Path(args.input).stem
        ocr_text, processed_result = process_single_image(args.input, ocr_client)
        results = {filename: (ocr_text, processed_result)}
    else:
        # 文件夹
        logger.info("批量处理文件夹中的图片")
        max_workers = getattr(args, 'max_workers', 5)  # 从args获取并发数，默认5
        results = process_image_directory(args.input, ocr_client, max_workers)
    
    # 匹配结果和正确答案
    matched_ocr_texts, matched_ground_truth, file_mapping = match_results_with_ground_truth(results, ground_truth_data)
    
    if not matched_ocr_texts:
        logger.error("没有找到匹配的测试用例和正确答案")
        return
    
    logger.info(f"匹配到 {len(matched_ocr_texts)} 个测试用例")
    
    # 执行评估
    logger.info("开始执行评估...")
    # 从results中提取已处理的结果，避免重复调用大模型
    matched_processed_results = []
    for filename in sorted(results.keys(), key=lambda x: [int(c) if c.isdigit() else c for c in re.split(r'(\d+)', x)]):
        if filename in [mapping[0] for mapping in file_mapping]:
            _, processed_result = results[filename]
            matched_processed_results.append(processed_result)
    
    evaluation_report = evaluate_ocr_results_with_cache(matched_ocr_texts, matched_ground_truth, matched_processed_results)
    
    # 打印评估报告
    print_evaluation_report(evaluation_report)
    
    # 打印每个文件的正确率
    print_file_accuracy_report(evaluation_report, file_mapping)
    
    # 结束性能追踪并输出报告
    performance_tracker.end_session()
    performance_tracker.print_performance_report()
    
    # 输出简化的全局统计信息总结
    print("\n" + "=" * 80)
    print("📊 LLM性能统计总结")
    print("=" * 80)
    
    total_files = global_stats.get('total_files', 0)
    total_llm_time = global_stats.get('total_llm_time', 0)
    total_llm_chars = global_stats.get('total_llm_chars', 0)
    
    print(f"处理文件总数: {total_files}个")
    
    if total_llm_time > 0:
        avg_llm_time = total_llm_time / total_files
        print(f"LLM总处理时间: {total_llm_time:.2f}秒, 平均每文件: {avg_llm_time:.2f}秒")
        
    if total_llm_chars > 0 and total_llm_time > 0:
        avg_chars = total_llm_chars / total_files
        avg_speed = total_llm_chars / total_llm_time
        print(f"LLM总输出字符: {total_llm_chars}字符, 平均每文件: {avg_chars:.0f}字符")
        print(f"LLM平均速度: {avg_speed:.0f}字符/秒")
    
    print("=" * 80)


def run_test_mode(args):
    """运行测试模式（不进行答案对比）"""
    logger.info("=" * 60)
    logger.info("运行测试模式")
    logger.info("=" * 60)
    
    # 开始性能追踪
    performance_tracker.reset()
    performance_tracker.start_session()
    
    # 创建OCR客户端
    config = get_default_config()
    ocr_client = OCRClient(
    )
    
    results = {}
    
    # 处理输入
    if os.path.isfile(args.input):
        # 单个文件
        logger.info(f"处理单个图片文件: {args.input}")
        filename = Path(args.input).stem
        ocr_text, processed_result = process_single_image(args.input, ocr_client)
        results[filename] = (ocr_text, processed_result)
        
        print("\n" + "=" * 50)
        print(f"文件: {Path(args.input).name}")
        print("=" * 50)
        print("\n� OCR识识别结果:")
        print("-" * 30)
        print(ocr_text)
        
        print("\n🔍 检验项目识别结果:")
        print("-" * 30)
        if processed_result:
            headers = ["检查代码", "检查名称", "检查结果值", "异常符号", "检查单位", "参考范围"]
            table_data = []
            for item in processed_result:
                table_data.append([
                    item.test_code or "",
                    item.test_name or "",
                    item.test_value or "",
                    item.abnormal_symbol or "",
                    item.test_unit or "",
                    item.reference_value or ""
                ])
            print(tabulate(table_data, headers=headers, tablefmt="grid"))
            print(f"\n✅ 共识别到 {len(processed_result)} 个检验项目")
            
            # 保留原有的markdown格式输出（注释掉，备用）
            # print("| test_code | test_name | test_value | abnormal_symbol | test_unit | reference_value |")
            # print("|-----------|-----------|------------|-----------------|-----------|-----------------|")
            # for item in processed_result:
            #     print(f"| {item.test_code} | {item.test_name} | {item.test_value} | {item.abnormal_symbol} | {item.test_unit} | {item.reference_value} |")
        else:
            print("❌ 未识别到任何检验项目")
    
    else:
        # 文件夹
        logger.info(f"批量处理文件夹: {args.input}")
        max_workers = getattr(args, 'max_workers', 5)  # 从args获取并发数，默认5
        results = process_image_directory(args.input, ocr_client, max_workers)
        
        print("\n" + "=" * 60)
        print("批量处理结果汇总")
        print("=" * 60)
        
        total_items = 0
        for filename, (ocr_text, processed_result) in results.items():
            print(f"\n📁 文件: {filename}")
            print("-" * 40)
            
            if processed_result:
                print(f"✅ 识别到 {len(processed_result)} 个检验项目")
                total_items += len(processed_result)
                
                # 显示前几个项目作为示例
                headers = ["检查代码", "检查名称", "检查结果值", "异常符号"]
                table_data = []
                display_count = min(3, len(processed_result))  # 最多显示3个
                
                for item in processed_result[:display_count]:
                    table_data.append([
                        item.test_code or "",
                        item.test_name or "",
                        item.test_value or "",
                        item.abnormal_symbol or ""
                    ])
                
                if table_data:
                    print(tabulate(table_data, headers=headers, tablefmt="grid"))
                    if len(processed_result) > display_count:
                        print(f"... 还有 {len(processed_result) - display_count} 个项目")
                
                # 保留原有的markdown格式输出（注释掉，备用）
                # print("| test_code | test_name | test_value | abnormal_symbol |")
                # print("|-----------|-----------|------------|-----------------|")
                # for item in processed_result[:3]:  # 只显示前3个
                #     print(f"| {item.test_code} | {item.test_name} | {item.test_value} | {item.abnormal_symbol} |")
                # if len(processed_result) > 3:
                #     print(f"| ... | ... | ... | ... |")
                #     print(f"（共 {len(processed_result)} 个项目）")
            else:
                print("❌ 未识别到任何检验项目")
        
        print(f"\n🎉 批量处理完成！")
        print(f"📊 处理文件数: {len(results)}")
        print(f"📋 总检验项目数: {total_items}")
    
    # 结束性能追踪并输出报告
    performance_tracker.end_session()
    performance_tracker.print_performance_report()
    
    return results


def run_demo_mode(args):
    """运行演示模式（原有功能）"""
    logger.info("=" * 60)
    logger.info("运行演示模式")
    logger.info("=" * 60)
    
    # 开始性能追踪
    performance_tracker.reset()
    performance_tracker.start_session()
    
    # 测试用例OCR文本
    ocr_test_cases = [
        """
    第1页/共1页 尿液常规分析（尿10项/有形成分/镜检） 周栋强 采样时间：2024-09-1406:38\n北京大学人民医院检验报告单\nPeking University People's Hospital Laboratory Report 托：\n姓名： 周栋强 卡号/病案号： 4495893 标本编号： 124091401022 科别：血液科病房 床号： 027\n性别：男 年龄： 47岁 流水号： 2178 标本种类： 尿 病房：18A病区 申请医生：陈育红\n检验项目： 尿液常规分析（尿10项/有形 执行科室： 检验科病房临检 临床诊断： 异基因造血干细胞移植术\n检验项目 结果 单位 参考区间 检验项目 结果 单位 参考区间\nSG *（干化学）比重 6101 1.003-1.030 SPERM（尿流式）精子 0 /ul 0-0\nGLU *（干化学）葡萄糖 阴性 阴性 SRC （尿流式）小圆上皮细胞 0.4 /ul 0-3\nPRO *（干化学）蛋白 + 阴性 EC （尿流式)上皮细胞 0 /uL 0-5\nPH *（干化学）酸碱度 7.5 4.5-8.0 M
        """,
    ]

    try:
        # 加载正确答案
        ground_truth_data = load_ground_truth(args.ground_truth)
        
        # 检查测试用例和正确答案数量匹配
        if len(ground_truth_data) != len(ocr_test_cases):
            logger.warning(f"正确答案Sheet数量 ({len(ground_truth_data)}) 与测试用例数量 ({len(ocr_test_cases)}) 不匹配")
            min_cases = min(len(ground_truth_data), len(ocr_test_cases))
            ocr_test_cases = ocr_test_cases[:min_cases]
            ground_truth_data = ground_truth_data[:min_cases]
            logger.info(f"将评测前 {min_cases} 个用例")

        # 执行评测
        print("\n开始执行评测...")
        evaluation_report = evaluate_ocr_results(ocr_test_cases, ground_truth_data, process_medical_ocr)
        print_evaluation_report(evaluation_report)
        # 打印简化的评估报告
        print("\n" + "="*80)
        print("📊 评估结果总结")
        print("="*80)
        overall = evaluation_report['overall_metrics']
        print(f"总测试用例数: {evaluation_report['total_test_cases']}")
        print(f"项目匹配准确率: {overall['item_accuracy']}")
        print(f"字段准确率: {overall['field_accuracy']}")
        print(f"总错误数: {evaluation_report['error_summary']['total_errors']}")
        print("="*80)
        
        # 结束性能追踪并输出报告
        performance_tracker.end_session()
        performance_tracker.print_performance_report()
        
        # 输出简化的全局统计信息总结
        print("\n" + "=" * 80)
        print("📊 LLM性能统计总结")
        print("=" * 80)
        
        total_files = global_stats.get('total_files', 0)
        total_llm_time = global_stats.get('total_llm_time', 0)
        total_llm_chars = global_stats.get('total_llm_chars', 0)
        
        print(f"处理文件总数: {total_files}个")
        
        if total_llm_time > 0:
            avg_llm_time = total_llm_time / total_files
            print(f"LLM总处理时间: {total_llm_time:.2f}秒, 平均每文件: {avg_llm_time:.2f}秒")
            
        if total_llm_chars > 0 and total_llm_time > 0:
            avg_chars = total_llm_chars / total_files
            avg_speed = total_llm_chars / total_llm_time
            print(f"LLM总输出字符: {total_llm_chars}字符, 平均每文件: {avg_chars:.0f}字符")
            print(f"LLM平均速度: {avg_speed:.0f}字符/秒")
        
        print("=" * 80)

    except Exception as e:
        logger.critical(f"评测过程中发生严重错误: {e}")
        raise


def collect_test_names(results):
    """
    收集所有检验项目名称并去重
    
    Args:
        results: 处理结果字典 {filename: (ocr_text, processed_result)}
        
    Returns:
        tuple: (所有test_name列表, 去重后的test_name列表)
    """
    all_test_names = []
    
    # 收集所有test_name
    for filename, (_, processed_result) in results.items():
        if processed_result:
            for item in processed_result:
                if hasattr(item, 'test_name') and item.test_name:
                    all_test_names.append(item.test_name)
    
    # 去重
    unique_test_names = sorted(list(set(all_test_names)))
    
    return all_test_names, unique_test_names

def print_test_names_report(all_test_names, unique_test_names):
    """
    打印检验项目名称报告
    
    Args:
        all_test_names: 所有test_name列表
        unique_test_names: 去重后的test_name列表
    """
    print("\n" + "=" * 80)
    print("📋 检验项目名称统计")
    print("=" * 80)
    
    print(f"总共识别到 {len(all_test_names)} 个检验项目名称")
    print(f"去重后共有 {len(unique_test_names)} 个不同的检验项目名称")
    
    # 统计每个检验项目名称出现的次数
    name_counts = {}
    for name in all_test_names:
        if name in name_counts:
            name_counts[name] += 1
        else:
            name_counts[name] = 1
    
    # 按出现次数排序
    sorted_names = sorted(name_counts.items(), key=lambda x: x[1], reverse=True)
    
    # 打印出现频率最高的前20个检验项目名称
    print("\n📊 出现频率最高的检验项目名称:")
    headers = ["检验项目名称", "出现次数"]
    table_data = []
    
    for name, count in sorted_names[:20]:  # 只显示前20个
        table_data.append([name, count])
    
    print(tabulate(table_data, headers=headers, tablefmt="grid"))
    
    # 打印所有去重后的检验项目名称
    print("\n📋 所有去重后的检验项目名称:")
    for i, name in enumerate(unique_test_names):
        print(f"{i+1}. {name}")
    
    print("=" * 80)

def print_all_items_json(results):
    """
    输出所有检验项目的JSON格式数据
    
    Args:
        results: 处理结果字典 {filename: (ocr_text, processed_result)}
    """
    print("\n" + "=" * 80)
    print("📋 所有检验项目JSON格式输出")
    print("=" * 80)
    
    all_items = []
    item_id = 1
    
    # 收集所有检验项目
    for filename, (_, processed_result) in results.items():
        if processed_result:
            for item in processed_result:
                
                # json_item = {
                #     "id": item_id,
                #     "test_name": getattr(item, 'test_name', '') or '',
                #     "test_value": getattr(item, 'test_value', '') or '',
                #     "test_flag": getattr(item, 'test_flag', '') or '',
                #     "test_unit": getattr(item, 'test_unit', '') or '',
                #     "reference_value": getattr(item, 'reference_value', '') or '',
                #     "test_code": getattr(item, 'test_code', '') or '',
                #     "abnormal_symbol": getattr(item, 'abnormal_symbol', '') or ''
                # }
                all_items.append(item)
                item_id += 1
    
    # 输出JSON格式（一行一个对象）
    print(f"总共 {len(all_items)} 个检验项目:")
    print()
    for item in all_items:
        print(json.dumps(item, ensure_ascii=False))
    
    print("=" * 80)

def run_with_default_config():
    """使用默认配置运行"""
    config = get_default_config()
    
    print("医疗检验单OCR处理系统")
    print("=" * 50)
    print("🔧 使用默认配置运行")
    print(f"📁 模式: {config['mode']}")
    print(f"📄 输入: {config['input']}")
    if config['mode'] in ['eval', 'demo']:
        print(f"📊 答案文件: {config.get('ground_truth', '未配置')}")
    print("=" * 50)
    
    # 创建模拟的args对象
    class Args:
        def __init__(self, config):
            self.mode = config['mode']
            self.input = config['input']
            self.max_workers = config.get('max_workers', 5)
            # 只在需要的模式下设置 ground_truth
            if config['mode'] in ['eval', 'demo']:
                self.ground_truth = config.get('ground_truth', '')
            else:
                self.ground_truth = None
    
    args = Args(config)
    
    try:
        results = {}
        
        if args.mode == 'test':
            results = run_test_mode(args)
        elif args.mode == 'eval':
            run_evaluation_mode(args)
            # For eval mode, we need to collect results separately
            if os.path.isfile(args.input):
                filename = Path(args.input).stem
                ocr_client = OCRClient()
                ocr_text, processed_result = process_single_image(args.input, ocr_client)
                results[filename] = (ocr_text, processed_result)
            else:
                ocr_client = OCRClient()
                results = process_image_directory(args.input, ocr_client, args.max_workers)
        elif args.mode == 'demo':
            run_demo_mode(args)
            # Demo模式使用内置测试用例，需要特殊处理
            ocr_test_cases = [
                """
            第1页/共1页 尿液常规分析（尿10项/有形成分/镜检） 周栋强 采样时间：2024-09-1406:38\n北京大学人民医院检验报告单\nPeking University People's Hospital Laboratory Report 托：\n姓名： 周栋强 卡号/病案号： 4495893 标本编号： 124091401022 科别：血液科病房 床号： 027\n性别：男 年龄： 47岁 流水号： 2178 标本种类： 尿 病房：18A病区 申请医生：陈育红\n检验项目： 尿液常规分析（尿10项/有形 执行科室： 检验科病房临检 临床诊断： 异基因造血干细胞移植术\n检验项目 结果 单位 参考区间 检验项目 结果 单位 参考区间\nSG *（干化学）比重 6101 1.003-1.030 SPERM（尿流式）精子 0 /ul 0-0\nGLU *（干化学）葡萄糖 阴性 阴性 SRC （尿流式）小圆上皮细胞 0.4 /ul 0-3\nPRO *（干化学）蛋白 + 阴性 EC （尿流式)上皮细胞 0 /uL 0-5\nPH *（干化学）酸碱度 7.5 4.5-8.0 M
                """,
            ]
            for i, ocr_text in enumerate(ocr_test_cases):
                processed_result = process_medical_ocr(ocr_text)
                results[f"demo_case_{i+1}"] = (ocr_text, processed_result)
        else:
            print(f"❌ 不支持的模式: {args.mode}")
            
        # 在所有处理完成后，输出test_name统计
        if results:
            all_test_names, unique_test_names = collect_test_names(results)
            print_test_names_report(all_test_names, unique_test_names)
            # 输出所有项目的JSON格式
            print_all_items_json(results)
            
    except Exception as e:
        logger.critical(f"程序执行过程中发生严重错误: {e}")
        raise


def collect_test_names(results):
    """
    收集所有检验项目名称并去重
    
    Args:
        results: 处理结果字典 {filename: (ocr_text, processed_result)}
        
    Returns:
        tuple: (所有test_name列表, 去重后的test_name列表)
    """
    all_test_names = []
    
    # 收集所有test_name
    for filename, (_, processed_result) in results.items():
        if processed_result:
            for item in processed_result:
                if hasattr(item, 'test_name') and item.test_name:
                    all_test_names.append(item.test_name)
    
    # 去重
    unique_test_names = sorted(list(set(all_test_names)))
    
    return all_test_names, unique_test_names

def print_test_names_report(all_test_names, unique_test_names):
    """
    打印检验项目名称报告
    
    Args:
        all_test_names: 所有test_name列表
        unique_test_names: 去重后的test_name列表
    """
    print("\n" + "=" * 80)
    print("📋 检验项目名称统计")
    print("=" * 80)
    
    print(f"总共识别到 {len(all_test_names)} 个检验项目名称")
    print(f"去重后共有 {len(unique_test_names)} 个不同的检验项目名称")
    
    # 统计每个检验项目名称出现的次数
    name_counts = {}
    for name in all_test_names:
        if name in name_counts:
            name_counts[name] += 1
        else:
            name_counts[name] = 1
    
    # 按出现次数排序
    sorted_names = sorted(name_counts.items(), key=lambda x: x[1], reverse=True)
    
    # 打印出现频率最高的前20个检验项目名称
    print("\n📊 出现频率最高的检验项目名称:")
    headers = ["检验项目名称", "出现次数"]
    table_data = []
    
    for name, count in sorted_names[:20]:  # 只显示前20个
        table_data.append([name, count])
    
    print(tabulate(table_data, headers=headers, tablefmt="grid"))
    
    # 打印所有去重后的检验项目名称
    print("\n📋 所有去重后的检验项目名称:")
    for i, name in enumerate(unique_test_names):
        print(f"{i+1}. {name}")
    
    print("=" * 80)

def main():
    """主程序 - 医疗检验单OCR处理系统入口"""
    # 用于收集所有结果的全局变量
    global_results = {}
    
    # 如果没有命令行参数，使用默认配置
    if len(sys.argv) == 1:
        print("📝 未提供命令行参数，使用默认配置运行...")
        print("💡 提示: 可以修改代码中的 DEFAULT_CONFIG 来调整默认配置")
        print("💡 或者使用命令行参数: python main.py test <文件路径>")
        print()
        run_with_default_config()
        return
    
    parser = argparse.ArgumentParser(
        description="医疗检验单OCR处理系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 直接运行（使用默认配置）
  python main.py
  
  # 测试模式 - 处理单个图片文件
  python main.py test D:/data/ocr/13张图片/13tu/1尿液常规分析.png
  
  # 测试模式 - 批量处理文件夹
  python main.py test D:\\data\\ocr\\13张图片\\13tu
  
  # 测试模式 - 批量处理文件夹（指定10个并发线程）
  python main.py test D:\\data\\ocr\\13张图片\\13tu --max-workers 10
  
  # 评估模式 - 单个文件与答案对比
  python main.py eval image.jpg --ground-truth answers.xlsx
  
  # 评估模式 - 批量处理与答案对比（指定8个并发线程）
  python main.py eval /path/to/images/ --ground-truth answers.xlsx --max-workers 8
  
默认配置:
  - 模式: test
  - 输入: D:\\data\\ocr\\13张图片\\13tu\\1尿液常规分析.png
  - 答案文件: D:\\data\\ocr\\13张图片\\答案.xlsx
  
文件名匹配规则:
  - 图片文件名应与Excel答案Sheet名称对应
  - 例如: Sheet1.jpg 对应 Excel中的Sheet1
  - 或者: 1.jpg 对应 Excel中的Sheet1
        """
    )
    
    # 添加子命令
    subparsers = parser.add_subparsers(dest='mode', help='运行模式', required=True)
    
    # 测试模式
    test_parser = subparsers.add_parser('test', help='测试模式（不进行答案对比）')
    test_parser.add_argument('input', help='输入图片文件或文件夹路径')
    test_parser.add_argument('--max-workers', '-w', type=int, default=5,
                           help='并发线程数（默认5）')
    
    # 评估模式
    eval_parser = subparsers.add_parser('eval', help='评估模式（与正确答案对比）')
    eval_parser.add_argument('input', help='输入图片文件或文件夹路径')
    eval_parser.add_argument('--ground-truth', '-g', required=True, 
                           help='正确答案Excel文件路径')
    eval_parser.add_argument('--max-workers', '-w', type=int, default=5,
                           help='并发线程数（默认5）')
    
    # 演示模式（保持原有功能）
    demo_parser = subparsers.add_parser('demo', help='演示模式（使用内置测试用例）')
    demo_parser.add_argument('--ground-truth', '-g', 
                           default=r"D:\data\ocr\13张图片\答案.xlsx",
                           help='正确答案Excel文件路径')
    demo_parser.add_argument('--max-workers', '-w', type=int, default=5,
                           help='并发线程数（默认5）')
    
    args = parser.parse_args()
    
    if not args.mode:
        parser.print_help()
        return
    
    print("医疗检验单OCR处理系统")
    print("=" * 50)
    
    try:
        results = {}
        
        if args.mode == 'test':
            results = run_test_mode(args)
                
        elif args.mode == 'eval':
            # 处理输入并收集结果
            if os.path.isfile(args.input):
                # 单个文件
                filename = Path(args.input).stem
                ocr_client = OCRClient()
                ocr_text, processed_result = process_single_image(args.input, ocr_client)
                results[filename] = (ocr_text, processed_result)
            else:
                # 文件夹
                ocr_client = OCRClient()
                results = process_image_directory(args.input, ocr_client, getattr(args, 'max_workers', 5))
            
            run_evaluation_mode(args)
            
        elif args.mode == 'demo':
            run_demo_mode(args)
            # Demo模式使用内置测试用例，需要特殊处理
            ocr_test_cases = [
                """
            第1页/共1页 尿液常规分析（尿10项/有形成分/镜检） 周栋强 采样时间：2024-09-1406:38\n北京大学人民医院检验报告单\nPeking University People's Hospital Laboratory Report 托：\n姓名： 周栋强 卡号/病案号： 4495893 标本编号： 124091401022 科别：血液科病房 床号： 027\n性别：男 年龄： 47岁 流水号： 2178 标本种类： 尿 病房：18A病区 申请医生：陈育红\n检验项目： 尿液常规分析（尿10项/有形 执行科室： 检验科病房临检 临床诊断： 异基因造血干细胞移植术\n检验项目 结果 单位 参考区间 检验项目 结果 单位 参考区间\nSG *（干化学）比重 6101 1.003-1.030 SPERM（尿流式）精子 0 /ul 0-0\nGLU *（干化学）葡萄糖 阴性 阴性 SRC （尿流式）小圆上皮细胞 0.4 /ul 0-3\nPRO *（干化学）蛋白 + 阴性 EC （尿流式)上皮细胞 0 /uL 0-5\nPH *（干化学）酸碱度 7.5 4.5-8.0 M
                """,
            ]
            for i, ocr_text in enumerate(ocr_test_cases):
                processed_result = process_medical_ocr(ocr_text)
                results[f"demo_case_{i+1}"] = (ocr_text, processed_result)
        else:
            print(f"❌ 不支持的模式: {args.mode}")
            
        # 在所有处理完成后，输出test_name统计
        if results:
            all_test_names, unique_test_names = collect_test_names(results)
            print_test_names_report(all_test_names, unique_test_names)
            # 输出所有项目的JSON格式
            print_all_items_json(results)
            
    except Exception as e:
        logger.critical(f"程序执行过程中发生严重错误: {e}")
        raise


if __name__ == "__main__":
    main()