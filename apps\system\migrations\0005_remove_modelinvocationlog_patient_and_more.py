# Generated by Django 4.1.5 on 2025-05-16 13:35

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("system", "0004_rename_out_text_modelinvocationlog_output_text_and_more"),
    ]

    operations = [
        migrations.Remove<PERSON>ield(
            model_name="modelinvocationlog",
            name="patient",
        ),
        migrations.RemoveField(
            model_name="modelinvocationlog",
            name="project",
        ),
        migrations.RemoveField(
            model_name="modelinvocationlog",
            name="project_site",
        ),
        migrations.RemoveField(
            model_name="modelinvocationlog",
            name="subject",
        ),
        migrations.RemoveField(
            model_name="modelinvocationlog",
            name="subject_epoch_id",
        ),
        migrations.RemoveField(
            model_name="modelinvocationlog",
            name="subject_item_id",
        ),
        migrations.RemoveField(
            model_name="modelinvocationlog",
            name="subject_visit_id",
        ),
        migrations.AddField(
            model_name="modelinvocationlog",
            name="business_id",
            field=models.<PERSON>r<PERSON>ield(
                blank=True, max_length=100, null=True, verbose_name="业务ID"
            ),
        ),
    ]
