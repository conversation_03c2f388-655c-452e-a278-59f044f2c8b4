#!/bin/bash
set -e

MODE=${1:-web}  # 默认是 web 模式

echo "Running in ENV: $DJANGO_SETTINGS_MODULE"
echo "Start mode: $MODE"

if [ "$MODE" = "airflow" ]; then
  cp /app/etc/supervisord_airflow.conf /app/etc/supervisord.conf
else
  cp /app/etc/supervisord_web.conf /app/etc/supervisord.conf

  # 仅在 web 模式 + 特定环境下追加 airflow 配置
  if [ "$DJANGO_SETTINGS_MODULE" = "smo.settings.test" ] || [ "$DJANGO_SETTINGS_MODULE" = "smo.settings.uat" ]; then
    cat <<EOF >> /app/etc/supervisord.conf

[program:airflow_db_migrate]
command=airflow db migrate
directory=/app
autostart=true
autorestart=false
startsecs=0 
priority=40
redirect_stderr=true
stdout_logfile=/app/log/supervisor/airflow_db_migrate.log

[program:airflow_scheduler]
command=airflow scheduler
directory=/app
autostart=true
autorestart=unexpected
priority=60
redirect_stderr=true
stdout_logfile=/app/log/supervisor/airflow_scheduler.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=100
startsecs=5

[program:airflow_webserver]
command=airflow webserver --port 3012
directory=/app
autostart=true
autorestart=unexpected
priority=90
redirect_stderr=true
stdout_logfile=/app/log/supervisor/airflow_webserver.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=100
startsecs=5
EOF
  fi
fi

# 启动 supervisord
exec supervisord -c /app/etc/supervisord.conf
