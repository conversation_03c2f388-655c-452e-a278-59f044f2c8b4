# Generated by Django 4.1.5 on 2025-05-16 16:06

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("external", "0001_initial"),
    ]

    operations = [
        migrations.AlterField(
            model_name="maskingtask",
            name="masked_file",
            field=models.OneToOneField(
                db_column="masked_file_id",
                db_constraint=False,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="subject_medical_info",
                to="external.maskingmaskedfile",
                verbose_name="外部系统脱敏文件",
            ),
        ),
        migrations.AlterField(
            model_name="maskingtask",
            name="original_file",
            field=models.OneToOneField(
                db_column="original_file_id",
                db_constraint=False,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="subject_medical_info",
                to="external.maskingoriginalfile",
                verbose_name="外部系统文件ID",
            ),
        ),
    ]
