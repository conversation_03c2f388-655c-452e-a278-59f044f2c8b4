import os
import django
from django.utils import timezone

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smo.settings.test')
django.setup()

from apps.subject.models import SubjectVisit, SubjectItem
from django.db import transaction
import psycopg2

pg_conn_params = {
    'host': '**************',
    'database': 'oceanus_trial_clinplus',
    'user': 'postgres',
    'password': 'Qr@77.88',
    'port': 5432
}

PAGE_SIZE = 10000


def migrate_subject():
    pg_conn = psycopg2.connect(**pg_conn_params)
    pg_cursor = pg_conn.cursor()

    try:
        last_id = ''
        last_sub_id = ''
        last_sub_version = -1

        total_count = 0
        batch_num = 1

        processed_visit_ids = set()

        while True:
            print(f"\n📄 Processing batch after ({last_id}, {last_sub_id}, {last_sub_version})")

            pg_query = f"""
            SELECT
                t1.id as source_id,
                t1.sub_id as subject_id,
                t1.sub_version,
                t1.visit->>'epochId' as subject_epoch_source_id,
                t1.visit->>'visitId' as visit_id,
                t1.visit_date,
                t1.status,
                dictconstantname('e888888', t1.status) as status_text,
                t1.visit->>'visitType' as type,
                dictconstantname('e888888', t1.visit->>'visitType') as type_text,
                t1.label,
                t1.skip_reason,
                t1.prepare_date,
                t1.project_id,
                t1.project_site_id,
                t1.create_time as ot_create_time,
                t1.update_time as ot_update_time
            FROM subject_visit AS t1
            WHERE
                (t1.id, t1.sub_id, t1.sub_version) > (%s, %s, %s)
                AND t1.visit->>'visitType' != 'SUBJECT_VISIT_TYPE$UNPLAN'
            ORDER BY t1.id, t1.sub_id, t1.sub_version
            LIMIT {PAGE_SIZE}
            """

            pg_cursor.execute(pg_query, (last_id, last_sub_id, last_sub_version))
            subject_data = pg_cursor.fetchall()
            if not subject_data:
                print("✅ No more records to process.")
                break

            column_names = [desc[0] for desc in pg_cursor.description]

            visit_list = []
            item_list = []

            for row in subject_data:
                row_dict = dict(zip(column_names, row))
                row_dict['subject_visit_id'] = f"{row_dict['source_id']}-{row_dict['subject_id']}"
                row_dict['subject_epoch_id'] = f"{row_dict['subject_epoch_source_id']}-{row_dict['subject_id']}"
                row_dict.pop('subject_epoch_source_id')
                row_dict.pop('sub_version')
                visit_list.append(row_dict)
                processed_visit_ids.add(row_dict['subject_visit_id'])
                # item_list.append({
                #     'subject_item_id': f"{row_dict['subject_visit_id']}-{row_dict['subject_id']}",
                #     'subject_visit_id': row_dict['subject_visit_id'],
                #     'source_id': row_dict['subject_visit_id'],
                #     'item_id': 'prs0000000000',
                #     'label': '访视病历',
                #     'status': 'SUBJECT_FORM_STATUS$2',
                #     'status_text': '已完成',
                #     'seq': '999',
                #     'item_type': 3,
                #     'project_id': row_dict['project_id'],
                #     'project_site_id': row_dict['project_site_id'],
                #     'subject_id': row_dict['subject_id'],
                # })

            with transaction.atomic():
                existing_visits = SubjectVisit.objects.filter(
                    subject_visit_id__in=[v['subject_visit_id'] for v in visit_list]
                )
                existing_visits_dict = {
                    (v.subject_visit_id, v.subject_id): v for v in existing_visits
                }

                to_create = []
                to_update = []

                for v in visit_list:
                    key = (v['subject_visit_id'], v['subject_id'])
                    if key in existing_visits_dict:
                        obj = existing_visits_dict[key]
                        changed = False
                        for field, value in v.items():
                            if getattr(obj, field) != value:
                                setattr(obj, field, value)
                                changed = True
                        if changed:
                            to_update.append(obj)
                    else:
                        to_create.append(SubjectVisit(**v))

                if to_create:
                    SubjectVisit.objects.bulk_create(to_create, batch_size=1000)
                    print(f"✅ Created {len(to_create)} SubjectVisit records")
                if to_update:
                    SubjectVisit.objects.bulk_update(to_update, fields=list(visit_list[0].keys()), batch_size=1000)
                    print(f"🔄 Updated {len(to_update)} SubjectVisit records")

                # existing_items = SubjectItem.objects.filter(
                #     subject_item_id__in=[i['subject_item_id'] for i in item_list]
                # )
                # existing_ids = set(i.subject_item_id for i in existing_items)
                #
                # new_items = [
                #     SubjectItem(**i) for i in item_list if i['subject_item_id'] not in existing_ids
                # ]
                #
                # if new_items:
                #     SubjectItem.objects.bulk_create(new_items)
                #     print(f"✅ Created {len(new_items)} SubjectItem records")

            print(f"✅ 第 {batch_num} 批：更新 {len(to_update)} 条，新增 {len(to_create)} 条")

            # 更新last_id, last_sub_id, last_sub_version
            last_row = subject_data[-1]
            last_id = last_row[column_names.index('source_id')]
            last_sub_id = last_row[column_names.index('subject_id')]
            last_sub_version = last_row[column_names.index('sub_version')]

            total_count += len(subject_data)
            batch_num += 1

        # 软删除源系统中已删除的数据 - 分批处理避免内存问题
        print("🔍 Checking for records to delete...")
        # 分批处理删除操作，避免一次性加载大量数据到内存
        batch_size = 10000
        deleted_count = 0
        current_time = timezone.now()
        # 分批获取本地未删除的记录ID
        offset = 0
        while True:
            # 获取一批本地未删除的记录ID
            local_visit_ids_batch = list(SubjectVisit.objects.filter(
                delete_flag=0
            ).values_list('subject_visit_id', flat=True)[offset:offset + batch_size])

            if not local_visit_ids_batch:
                break

            # 找出这批记录中需要删除的（即在源系统中不存在的）
            to_delete_batch = [vid for vid in local_visit_ids_batch if vid not in processed_visit_ids]

            if to_delete_batch:
                # 将这些记录标记为已删除 (软删除)
                count = SubjectVisit.objects.filter(subject_visit_id__in=to_delete_batch).update(delete_flag=1, update_time=current_time)
                deleted_count += count
                print(f"🗑️  Soft deleted {count} records in current batch")

            offset += batch_size

        print(f"🗑️  Total soft deleted {deleted_count} records that no longer exist in source system")

        print(f"\n🎉 同步完成，共处理 {total_count} 条 subject_visit 数据")

    except Exception as e:
        print(f"\n❌ Error during migration: {str(e)}")

    finally:
        pg_cursor.close()
        pg_conn.close()
        print("🔚 PostgreSQL connection closed.")


if __name__ == "__main__":
    # python -m script.sync_ot_subject_visit_all
    migrate_subject()
