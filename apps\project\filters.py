
from django.db.models import Q
from django_filters import rest_framework as filters

from . import models


class ProjectFilter(filters.FilterSet):
    project_no = filters.CharFilter(field_name='project_no', lookup_expr='icontains', label='项目号（模糊匹配）')
    project_name = filters.CharFilter(field_name='project_name', lookup_expr='icontains', label='项目名称（模糊匹配）')
    stage = filters.CharFilter(field_name='stage', lookup_expr='exact', label='项目分期')
    indication_text = filters.CharFilter(field_name='indication_text', lookup_expr='icontains', label='适应症（模糊匹配）')
    pm_text = filters.CharFilter(field_name='pm_text', lookup_expr='icontains', label='PM（模糊匹配）')

    query = filters.CharFilter(method='filter_by_query', label='综合查询（模糊匹配）')

    def filter_by_query(self, queryset, name, value):
        """
        综合查询，OR 关系
        """
        return queryset.filter(
            Q(project_no__icontains=value) | Q(project_name__icontains=value)
        )


class ProjectSiteFilter(filters.FilterSet):
    project_id = filters.CharFilter(field_name='project__project_id', label='项目ID')
    project_no = filters.CharFilter(field_name='project__project_no', lookup_expr='exact', label='项目号')
    hosp_name = filters.CharFilter(field_name='hosp_name', lookup_expr='icontains', label='中心名称（模糊查询）')
    hosp_department_no = filters.CharFilter(field_name='hosp_department_no',
                                            lookup_expr='icontains', label='中心编号（模糊查询）')
    query = filters.CharFilter(method='filter_by_query', label='综合查询（模糊匹配）')

    def filter_by_query(self, queryset, name, value):
        """
        综合查询，OR 关系
        """
        return queryset.filter(
            Q(hosp_name__icontains=value) | Q(hosp_department_no__icontains=value)
        )


class ProjectMaterialInfoFilter(filters.FilterSet):
    project_id = filters.CharFilter(field_name='project__project_id', label='项目ID')
    epoch_id = filters.CharFilter(field_name='epoch_id', label='阶段ID')

    class Meta:
        model = models.ProjectMaterialInfo
        fields = ['project_id', 'category','epoch_id']


class ProjectMaterialFileFilter(filters.FilterSet):
    project_id = filters.CharFilter(field_name='material_info__project_id', label='项目ID')
    category = filters.CharFilter(field_name='material_info__category', label='素材分类')
    epoch_id = filters.CharFilter(field_name='material_info__epoch_id', label='阶段ID') 

    class Meta:
        model = models.ProjectMaterialFile
        fields = ['project_id', 'category', 'epoch_id']
