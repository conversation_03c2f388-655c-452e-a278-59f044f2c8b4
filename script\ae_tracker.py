import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smo.settings.test')
django.setup()

import os
import shutil
import datetime
import random
import time
import logging
import mimetypes
import uuid
import hashlib
import pymysql
from django.db import connection
from django.conf import settings
from apps.ae_tracker.models import AeTrackerTask, TestResult
from apps.medical.models import MedicalInfo, MedicalFile
# from apps.subject.models import Subject
# from apps.project.models import ProjectMaterialInfo, ProjectMaterialFile
from common.minio_client import get_minio_client, download_file_from_minio
from common.crf_llm_processor import generate_crf_file  # 大模型处理 CRF 任务
from concurrent.futures import ThreadPoolExecutor, as_completed

# 初始化 MinIO 客户端
client = get_minio_client()

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 获取项目根目录路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))


class AETrackerTaskProcessor:
    def __init__(self, medical_file_folder, temp_folder):
        self.client = get_minio_client()
        self.logger = logging.getLogger(f"task_processor_{id(self)}")  # 为每个实例创建唯一的logger
        self.medical_file_folder = medical_file_folder  # OCR—masked 文件夹
        self.temp_folder = temp_folder

    def process_task(self, task):
        """处理单个任务的逻辑"""
        try:
            self.logger.info(f"开始处理任务 {task.id}...")
            connect_db()
            task.status = 'IN_PROGRESS'
            task.save()

            self.logger.info(f"当前任务为：{task.category}，当前状态为：{task.status}，准备下载文件...")

            # 获取材料
            medical_file_list = self.get_medical_files(task)
            self.llm_deal(task, medical_file_list=medical_file_list)
                
            self.logger.info(f"当前任务为：{task.category}，当前状态为：{task.status}，正在处理文件...")

            self.logger.info(f"✅ 【回顾性研究-{task.category}生成任务已经完成】！")

        except Exception as e:
            connect_db()
            task.status = 'ERROR'  # 修正: 使用赋值而非比较
            task.save()
            self.logger.error(f"Error occurred during task scanning: {e}")
        finally:
            # 所有任务完成后 删除临时文件中的所有文件
            self.clear_dir()
            self.logger.info(f"✅ 临时文件夹中的所有文件已清理完成！")

    def clear_dir(self):
        try:
            # 检查是否为文件夹
            if os.path.isdir(self.temp_folder):
                print(f"正在清理文件夹: {self.temp_folder}")
                shutil.rmtree(self.temp_folder)
                print(f"文件夹 {self.temp_folder} 已全部删除。")
        except Exception as e:
            self.logger.error(f"❌ Error occurred during directory deletion: {e}")
            raise

    # def get_medical_template_file(self, task):
        """下载 CRF 文件和病历文件"""
        try:
            medical_file_list, template_local_file = self.download_medical_template_files(task)
            self.logger.info(f"✅ 获取病历和模板文件成功")
            return medical_file_list, template_local_file
        except Exception as e:
            connect_db()
            task.status = 'ERROR'
            task.save()
            self.logger.error(f"Error processing task {task.id}: {e}")
            raise

    def get_medical_files(self, task):
        try:
            medical_file_list = self.download_medical_files_for_task(task)
            self.logger.info(f"✅ 病史文件获取成功.")
            return medical_file_list
        except Exception as e:
            self.logger.error(f"Error processing task {task.id}: {e}")
            raise

    def download_medical_files_for_task(self, task):
        """下载 医疗文件 文件"""
        medical_infos = MedicalInfo.objects.filter(
            subject_id=task.subject_id, 
            visit_id=task.visit_id, 
            item_id=task.item_id
            )
        if not medical_infos.exists():  # 使用 existis() 只执行 SELECT EXISTS 查询
            self.logger.warning(f'No medical_info found for subject_id: {task.subject_id}')
            raise Exception("No medical files found.")

        medical_file_list = []  # 文件路径
        for medical_info in medical_infos:
            # medical_file = MedicalFile.objects.filter(id=medical_info.file_id).first()
            medical_file = medical_info.medicalfile.first()
            if medical_file.exists():
                file_path = self.download_file(medical_file, self.medical_file_folder)
                self.logger.info(f"{file_path} 已经成功下载")
                medical_file_list.append({"medical_info_id": medical_info.id , "file_path":file_path})  # 添加文件路径
        # medical_file_list = self.medical_file_folder

        return medical_file_list

    def download_file(self, file_obj, download_file_path):
        """下载文件"""
        download_file_path = os.path.join(download_file_path, file_obj.original_filename)
        try:
            download_file_from_minio(self.client, file_obj.bucket_name, file_obj.object_name,
                                     download_file_path)
            self.logger.info(f"✅ 文件下载成功：{file_obj.original_filename}")
            return download_file_path
        except Exception as e:
            self.logger.error(f"❌ 文件下载失败: {e}")
            raise


    def llm_deal(self, task, medical_file_list=None):
        """调用大模型进行处理并上传结果"""
        try:
            self.logger.info(f"大模型处理 ....")
            medical_info_ids = [medi_item["medical_info_id"] for medi_item in medical_file_list if medi_item]
            message_res = TestResult.objects.filter(
                medical_info_id__in=medical_info_ids
                ).values(
                "test_type", 
                "test_code",
                "test_name",
                "test_unit",
                "test_value",
                "reference_value",
                "reference_range_min",
                "reference_range_max"
                )
            message_res = list(message_res)
            
            print(message_res, medical_file_list, "大模型正在处理ing....")
            
            # 大模型返回的结果格式
            results = [
                {
                    "medical_info_id": medical_file_list.get("medical_info_id"),
                    "contexts": [
                        {
                            "model_name": "",
                            "字段1": "",
                            "字段2": "",
                            #  ...
                        },
                        {
                            "model_name": "",
                            "字段1": "",
                            "字段2": "",
                        }
                    ],
                    "flag":[0, 1, 1, 0]
                },
                {}
            ]

            """ 保存任务数据到 TestResult 表 """
            self.save_ae_results(results)
            connect_db()
            task.status = 'COMPLETED'
            task.save()
            self.logger.info(f"✅ 任务 {task.id} 处理完成，状态更新为 'COMPLETED'.")
            
        except Exception as e:
            connect_db()
            task.status = 'ERROR'
            task.save()
            self.logger.error(f"Error processing task with LLM: {e}")
            raise

    def save_ae_results(self, results):
        """保存检测结果到 TestResult 表"""
        try:
            connect_db()
            now = datetime.datetime.now()
            for item in results:
                TestResult.objects.filter(results["medical_info_id"]).update(
                    # 必填字段
                    update_time = now,
                    # 回填字段
                    test_type=item["contexts"]['test_type'],
                    test_code=item["contexts"]['检查代码'],
                    test_name=item["contexts"]['检查名称'],
                    test_unit=item["contexts"]['检查单位'],
                    test_value=item["contexts"]['检查结果'],
                    reference_value=item["contexts"]['参考值'],
                    reference_range_min=item["contexts"]['参考范围最小值'],
                    reference_range_max=item["contexts"]['参考范围最大值'],
                    collect_time=item["contexts"]['采集时间'],
                    report_time=item["contexts"]['report_time'],
                    ae_name=item["contexts"][0]['不良事件名称'],  # 这个是必须更新的
                    ae_grade=item["contexts"][0]['CS等级'],
                    ae_desc=item["contexts"][0]['不良事件定义'],
                    ae_edit_flag=0,
                    ae_ai_result_list=item["contexts"],  # 这个是必须更新的
                    ae_ai_result_flag=item["flag"],  # 这个是必须更新的
                    medical_history_flag=1,
                )
        except Exception as e:
            self.logger.error(f"Error saving AE results: {e}")
            raise


def connect_db():
    if connection.connection is None:  # 检查 connection 是否为 None
        connection.connect()  # 如果是 None，则重新建立连接
    else:
        try:
            connection.connection.ping()  # 检查连接是否仍然有效
        except Exception:
            connection.close()  # 先关闭可能失效的连接
            connection.connect()  # 重新建立连接


def create_file():
    temp_name = f"{uuid.uuid4().hex}"
    temp_folder = os.path.join(project_root, temp_name)  # 生成唯一临时目录名称
    medical_file_folder = os.path.join(temp_folder, 'medical_files')

    os.makedirs(medical_file_folder, exist_ok=True)

    return {
        'medical_file_folder': medical_file_folder,
        'temp_folder': temp_folder,
    }


def run_task(task):
    try:
        task_processor = AETrackerTaskProcessor(**create_file())
        task_processor.process_task(task)

    except Exception as e:
        # print(f"Error processing task: {e}")
        logger.error(f"Error processing task {task.id}: {e}")
        raise


def main_threading(max_workers=5):
    while True:
        # 创建线程池
        executor = ThreadPoolExecutor(max_workers=max_workers)
        try:
            connect_db()
            tasks = AeTrackerTask.objects.filter(status="TODO", category="AE_RECOGNITION")
            futures = {}

            for task in tasks:
                futures[executor.submit(run_task, task)] = task

            # 等待所有任务完成
            for future in as_completed(futures):
                task = futures[future]  # 获取对应的任务对象
                try:
                    future.result()  # 获取结果，如果有异常会在此处抛出
                    task.refresh_from_db()  # 重新查询任务状态，确保是最新的
                    if task.status == 'ERROR':
                        logger.error(f'Skipping task {task.id} due to status updated to ERROR.')
                        continue
                except Exception as e:
                    logger.error(f"Task processing failed: {e}")

            # time.sleep(5)  # 每隔一段时间扫描一次任务
            break  # 决定是否要继续循环
        except Exception as e:
            logger.error(f"Error occurred during task scanning: {e}")
            time.sleep(random.randint(5, 15))  # 随机回退


if __name__ == "__main__":
    main_threading()
