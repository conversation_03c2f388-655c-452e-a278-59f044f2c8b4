<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS 测试页面</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            margin-bottom: 15px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background-color: #45a049;
        }
        pre {
            background-color: #f4f4f4;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            max-height: 200px;
            margin-top: 10px;
        }
        .response-area {
            margin-top: 15px;
        }
        .tab-container {
            display: flex;
            margin-bottom: 10px;
        }
        .tab {
            padding: 8px 15px;
            cursor: pointer;
            background-color: #eee;
            border: 1px solid #ddd;
            margin-right: 5px;
            border-radius: 4px 4px 0 0;
        }
        .tab.active {
            background-color: #fff;
            border-bottom-color: white;
        }
        .code-section {
            background-color: #272822;
            color: #f8f8f2;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>CORS 跨域请求测试工具</h1>
        
        <div class="code-section">
            <p>// 设置目标地址，确保这不是当前页面的域名</p>
            <p>// 例如如果当前页面是 http://localhost:3000，则目标可以是 http://localhost:8000</p>
        </div>
        
        <div class="test-section">
            <h2>请求设置</h2>
            <label for="apiUrl">目标 URL:</label>
            <input type="text" id="apiUrl" placeholder="例如: http://localhost:8000/api/data" value="">
            
            <label for="customHeader">自定义请求头 (将触发预检请求):</label>
            <input type="text" id="customHeader" placeholder="例如: X-Custom-Header: 自定义值" value="X-Custom-Header: test-value">
            
            <div>
                <label>请求方法:</label>
                <select id="requestMethod">
                    <option value="GET">GET (简单请求)</option>
                    <option value="POST">POST (可能是简单请求)</option>
                    <option value="PUT" selected>PUT (会触发预检请求)</option>
                    <option value="DELETE">DELETE (会触发预检请求)</option>
                    <option value="PATCH">PATCH (会触发预检请求)</option>
                </select>
            </div>
            
            <div>
                <label>Content-Type:</label>
                <select id="contentType">
                    <option value="text/plain">text/plain (简单请求)</option>
                    <option value="application/x-www-form-urlencoded">application/x-www-form-urlencoded (简单请求)</option>
                    <option value="multipart/form-data">multipart/form-data (简单请求)</option>
                    <option value="application/json" selected>application/json (会触发预检请求)</option>
                </select>
            </div>
            
            <label for="requestBody">请求体 (JSON):</label>
            <textarea id="requestBody" rows="4" style="width: 100%">{"name": "测试数据", "value": 123}</textarea>
            
            <div style="margin-top: 15px;">
                <button id="sendRequest">发送请求</button>
                <button id="clearLogs" style="background-color: #f44336; margin-left: 10px;">清除日志</button>
            </div>
        </div>
        
        <div class="test-section">
            <h2>请求日志</h2>
            <div class="tab-container">
                <div class="tab active" data-target="request-log">请求详情</div>
                <div class="tab" data-target="response-log">响应详情</div>
            </div>
            
            <div id="request-log" class="log-content">
                <pre id="requestDetails">请求信息将显示在这里...</pre>
            </div>
            
            <div id="response-log" class="log-content" style="display: none;">
                <pre id="responseDetails">响应信息将显示在这里...</pre>
            </div>
        </div>
        
        <div class="test-section">
            <h2>CORS 预检请求说明</h2>
            <p>以下情况会触发预检请求 (OPTIONS):</p>
            <ul>
                <li>使用 PUT, DELETE, PATCH 等非简单方法</li>
                <li>设置 Content-Type 为 application/json 等非简单类型</li>
                <li>添加自定义请求头 (如 X-Custom-Header)</li>
            </ul>
            <p>预检请求由浏览器自动发送，使用 OPTIONS 方法，服务器需要正确响应才能进行后续实际请求。</p>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 标签页切换
            document.querySelectorAll('.tab').forEach(tab => {
                tab.addEventListener('click', function() {
                    // 移除所有active类
                    document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                    // 隐藏所有内容
                    document.querySelectorAll('.log-content').forEach(content => content.style.display = 'none');
                    
                    // 激活当前标签
                    this.classList.add('active');
                    // 显示对应内容
                    const targetId = this.getAttribute('data-target');
                    document.getElementById(targetId).style.display = 'block';
                });
            });

            // 发送请求按钮
            document.getElementById('sendRequest').addEventListener('click', function() {
                const requestUrl = document.getElementById('apiUrl').value.trim();
                if (!requestUrl) {
                    alert('请输入目标 URL');
                    return;
                }
                
                const method = document.getElementById('requestMethod').value;
                const contentType = document.getElementById('contentType').value;
                const requestBody = document.getElementById('requestBody').value;
                const customHeaderInput = document.getElementById('customHeader').value;
                
                // 解析自定义请求头
                const headers = {
                    'Content-Type': contentType
                };
                
                if (customHeaderInput) {
                    const headerParts = customHeaderInput.split(':');
                    if (headerParts.length >= 2) {
                        const headerName = headerParts[0].trim();
                        const headerValue = headerParts.slice(1).join(':').trim();
                        headers[headerName] = headerValue;
                    }
                }
                
                // 准备请求信息显示
                const requestDetails = {
                    url: requestUrl,
                    method: method,
                    headers: headers,
                    body: method !== 'GET' ? JSON.parse(requestBody) : undefined
                };
                
                document.getElementById('requestDetails').textContent = JSON.stringify(requestDetails, null, 2);
                document.getElementById('responseDetails').textContent = '正在等待响应...';
                
                // 执行实际请求，使用Fetch API
                const fetchOptions = {
                    method: method,
                    headers: headers,
                    credentials: 'omit', // 不发送cookies
                };
                
                if (method !== 'GET' && method !== 'HEAD') {
                    fetchOptions.body = requestBody;
                }
                
                const startTime = performance.now();
                
                fetch(requestUrl, fetchOptions)
                    .then(response => {
                        const endTime = performance.now();
                        const duration = (endTime - startTime).toFixed(2);
                        
                        // 收集响应头
                        const responseHeaders = {};
                        response.headers.forEach((value, name) => {
                            responseHeaders[name] = value;
                        });
                        
                        return response.text().then(text => {
                            return {
                                status: response.status,
                                statusText: response.statusText,
                                headers: responseHeaders,
                                body: text,
                                duration: `${duration}ms`
                            };
                        });
                    })
                    .then(responseData => {
                        document.getElementById('responseDetails').textContent = JSON.stringify(responseData, null, 2);
                        
                        // 自动切换到响应标签页
                        document.querySelector('.tab[data-target="response-log"]').click();
                    })
                    .catch(error => {
                        const endTime = performance.now();
                        const duration = (endTime - startTime).toFixed(2);
                        
                        document.getElementById('responseDetails').textContent = JSON.stringify({
                            error: error.message,
                            duration: `${duration}ms`,
                            note: "请检查控制台获取更多信息。如果是CORS错误，预检请求可能已失败。"
                        }, null, 2);
                        
                        // 自动切换到响应标签页
                        document.querySelector('.tab[data-target="response-log"]').click();
                        
                        console.error('请求错误:', error);
                    });
            });
            
            // 清除日志按钮
            document.getElementById('clearLogs').addEventListener('click', function() {
                document.getElementById('requestDetails').textContent = '请求信息将显示在这里...';
                document.getElementById('responseDetails').textContent = '响应信息将显示在这里...';
            });
        });
    </script>
</body>
</html>