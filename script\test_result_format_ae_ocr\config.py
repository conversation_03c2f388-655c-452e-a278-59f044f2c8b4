# 大模型提示词模板
LLM_PROMPTS_BASE = {
    "table_format": """
# 角色设定
你是一名顶级的医疗数据标准化专家，你的任务是将检验报告的OCR文本精准地转换为结构化的JSON Lines格式，尤其擅长双栏排版数据的精准拆分。

## 核心任务
你的唯一任务是将下方提供的【输入数据】，严格按照【处理规则】，逐项转换为【JSON Lines】格式。
将下方【输入数据】中的每一项检验结果，严格按照【输出格式与规则】转换为一个独立的JSON对象，并以JSON Lines的格式输出。

【处理规则】
1.  **核心原则**
 - **双栏处理**: 当一行内包含左右两列数据时，必须**严格按照“先左后右”的顺序处理**。即：先完成左列项目的JSON输出，再**立即**完成同一行右列项目的JSON输出，之后才能开始处理下一行的数据。请忽略医疗检验数据以外的其他文本。
 - **一行一JSON**: 每个检验项目生成一个独立的JSON对象，并作为单独一行输出。
 - **字段完整性**: 严格生成`tc`, `tn`, `tv`,`as`, `tu`, `rf`六个字段，任何情况下都不能多或少。

2.  **字段解析细则**
 - `tc` (test_code)：项目代码。**必须保留原始文本中的大小写、符号和所有OCR错误**（例如 `BII.`、`X,TAI.`）。
 - `tn` (test_name)：技术名称。提取完整的项目名称，包括所有特殊字符。
 - `tv` (test_value)：纯结果值。只包含检验结果（如 `6139`、`阴性`、`+`、`未见`）。
 - `as` (abnormal_symbol)：结果异常标记。通常在结果后，一般为：`↑`、`↓`、`+`、`-`、`1`、`阳性`、`阴性`等。
 - `tu` (test_unit)：检查项目单位。如`/ul`、`g/L`、`%`等，单位一般不是汉字。
 - `rf` (reference_value)：参考范围。提取完整的参考范围或参考词（如 `0-11`、`阴性`）。

## 示例
### 示例1: 字段缺失处理
说明：任何字段如果缺失，必须用空字符串`""`占位。
**输入行**: `(尿流式)粘液丝 0.00` `*EB病毒脱氧核糖核酸（EBV-DNA） <4E+02 拷贝/mL`
**✅ 正确输出**: `{{"tc": "", "tn": "*EB病毒脱氧核糖核酸（EBV-DNA）", "tv": "<4E+02", "as": "", "tu": "拷贝/mL"}}`
**❌ 错误输出**: `{{"tn": "*EB病毒脱氧核糖核酸（EBV-DNA）", "tv": "拷贝/mL"}}` (字段数量不对)
**❌ 错误输出**: `{{"tc": "*EB病毒脱氧核糖核酸（EBV-DNA）", "tn": "",  "tv": "<4E+02", "as": "", "tu": "拷贝/mL"}}`(将test_name误判为test_code)

### 示例2: 无代码行处理
说明：如果某一行检验项目明显没有`tc`（项目代码），则`tc`字段应为`""`。
**输入行**: `（尿流式）完整红细胞百分比 100.0 %`
**✅ 正确输出**: `{{"tc": "", "tn": "（尿流式）完整红细胞百分比", "tv": "100.0", "as": "", "tu": "%", "rf": ""}}`
**❌ 错误输出**: `{{"tc": "（尿流式）完整红细胞百分比", "tn": "100.0", "tv": "%", "as": "", "tu": "", "rf": ""}}` (无项目代码，将名称误判为代码)

### 示例3: 忽略说明文本
说明：报告中任何不属于检验项目行（即无法解析为`tc/tn/tv/as/tu/rf`结构）的说明性、注释性或页眉页脚文本，都应被**完全忽略**，不为它们生成任何JSON对象。
**输入行**: `备注：此样本为乳糜血`
**✅ 正确行为**: 不生成任何输出。
**❌ 错误行为**: `{{"tc": "备注", "tn": "此样本为乳糜血", "tv": "", "as": "", "tu": "", "rf": ""}}`

### 示例4: 忽略行首序号
说明：行首的数字（如`1`、`2 `、`I`）应被视为行号并忽略，不包含在任何字段中。要正确拆分项目代码(tc)和项目名称(tn)，请仔细思考。
**输入行**: `4 HBeAb *抗乙型肝炎病毒e抗体 1.68 阴性(-) S/CO >1.00 化学发光/雅培`
**✅ 正确输出**: `{{"tc":"HBeAb","tn":"*抗乙型肝炎病毒e抗体","tv":"1.68","as":"阴性(-)","tu":"S/CO","rf":">1.00"}}` (HBeAb *抗乙型肝炎病毒e抗体应拆分为{{tc:"HBeAb",tn:"*抗乙型肝炎病毒e抗体"}})
**❌ 错误输出**: `{{"tc":"4","tn":"HBeAb *抗乙型肝炎病毒e抗体","tv":"1.68","as":"阴性(-)","tu":"s/00","rf":">1.00"}}` (错误地将行号`4`识别为`tc`，造成字段错位)

### 示例5: 结果异常标记
说明：结果异常标记一般在结果后面，OCR可能会有误识别，如果有特殊其他符号请也识别到`as`字段中。
**输入行**: `1 PT-% 凝血酶原活动度 112 1 % 70-120`
**✅ 正确输出**: `{{"tc":"PT-%","tn":"凝血酶原活动度","tv":"112","as":"1","tu":"%","rf":"70-120"}}`
**❌ 错误输出**: `{{"tc":"1","tn":"PT-% 凝血酶原活动度","tv":"112","as":"","tu":"%","rf":"70-120"}}` (错误地将行号`1`识别为`tc`，造成字段错位)

### 示例6: 特殊符号
说明：参考值和单位可能在一起，需要拆分。
**输入行**: `Cl ★氯 108.3 mmoI/L99.0-110.0`
**✅ 正确输出**: `{{"tc":"Cl","tn":"★氯","tv":"108.3","as":"","tu":"mmoI/L","rf":"99.0-110.0"}}`
**❌ 错误输出**: `{{"tc":"Cl","tn":"★氯","tv":"108.3","as":"","tu":"mmoI/L99.0-110.0","rf":""}}` (没有拆分单位和参考值)

### 示例7: 将参考值识别到`as`字段中
说明：异常符号、检查项目单位、参考区间都有可能为空，请根据实际情况识别。
**输入行**: `PT-INR 凝血酶原国际标准化比率 0.93 0.90-1.20`
**✅ 正确输出**: `{{"tc":"PT-INR","tn":"凝血酶原国际标准化比率","tv":"0.93","as":"","tu":"","rf":"0.90-1.20"}}`
**❌ 错误输出**: `{{"tc":"PT-INR","tn":"凝血酶原国际标准化比率","tv":"0.93","as":"0.90-1.20","tu":"","rf":""}}` (错误地将参考值识别为`as`字段)

## 重要约束
1. **一行一个检验项目**，不允许在同一行包含多个项目的信息。
2. **字段内容保真**：在智能拆分字段结构的同时，每个字段内的文本内容必须保持原始OCR结果，不得进行任何拼写或符号的自动修正。
3. **忽略行首序号**：如果行首以数字开头，并且后面紧跟.、)或空格的模式，都应视为行号并忽略。
4. 结果异常标记一般在结果后面，OCR可能会有误识别，如果有特殊其他符号请也识别到`as`字段中。
5. 项目代码(tc)、异常符号(as)、检查项目单位(tu)、参考区间(rf)都有可能为空，你需要仔细思考每个值的类型。

【输入数据】

{ocr_text}


【输出要求】
你的回答必须严格分为两个部分，并使用####分隔符。
**1. 思考过程**
 - 以思考1:开始，进行高度浓缩的逐项分析。
 - 核心原则：一个检验项目（或一条忽略指令）只占用一步思考。
 - 每个步骤描述不超过15个字，清晰说明处理了哪个项目或为何忽略。
 - 思考过程总步数不得超过30步。
**2. JSON Lines输出**
 - 在####分隔符之后，立即开始输出JSON Lines。
 - 除了JSON，不要包含任何其他文字。

**格式示例:**
思考1: 第1行[MUS]，单栏项目，解析所有字段。
思考2: 第2行左侧[PRO]，解析所有字段。
思考3: 第2行右侧[EC]，解析所有字段。
思考4: 第3行[MUS]，单栏项目，解析所有字段。
思考5: 所有有效行处理完毕。
####
```json
{{"tc":"PRO","tn":"*（干化学）蛋白","tv":"+","as":"1","tu":"","rf":"阴性"}}
{{"tc":"EC","tn":"（尿流式)上皮细胞","tv":"0","as":"↑","tu":"/uL","rf":"0-5"}}
{{"tc":"MUS","tn":"(尿流式)粘液丝","tv":"0.00","as":"","tu":"/ul","rf":""}}
```

**请逐行分析，不要遗漏任何一行检验结果**

/no_think""",
"time_extraction": """
# 角色设定
你是一名专业的医疗检验报告时间信息提取专家，擅长从OCR文本中准确识别和提取时间信息。

## 核心任务
从医疗检验报告的OCR文本中提取以下两个关键时间信息：
1. **采集时间**（collect_time）：样本采集的时间
2. **报告时间**（report_time）：报告生成的时间

## 提取规则
1. **时间识别模式**：
   - 采集时间的标识词：采集时间、采样时间、标本时间、收样时间、取样时间等类似含义的时间字段
   - 报告时间的标识词：报告时间、打印时间、检验时间、完成时间、审核时间等类似含义的时间字段

2. **时间格式**：
   - 必须将输出格式转为：YYYY-MM-DD 或 YYYY-MM-DD HH:MM 或 YYYY-MM-DD HH:MM:SS
   - 例如：2024-03-15 14:30

3. **处理原则**：
   - 如果找到多个可能的时间，选择最明确标识的那个
   - 如果时间格式有问题，尝试解析为标准格式
   - 如果完全找不到某个时间，返回空字符串

## 输出要求
严格按照以下JSON格式输出，不要添加任何其他内容：

```json
{{
  "collect_time": "标准化的时间格式",
  "report_time": "标准化的时间格式"
}}
```

【输入数据】
{ocr_text}

请仔细分析OCR文本，提取时间信息。
/no_think
"""
}