from django.db import models
from common.models import BaseModel, BaseFileModel


class MedicalCollectionCrfResult(BaseModel):
    # crf 生成结果落库
    medical_collection_task_id = models.BigIntegerField(default=0, verbose_name="病历归集文件id")
    result_text = models.TextField(blank=True, null=True, verbose_name="result文本")

    patient_id = models.CharField(max_length=255, verbose_name="患者ID")
    project_id = models.CharField(max_length=255, verbose_name="项目ID")
    project_site_id = models.CharField(max_length=255, verbose_name="项目中心ID")
    subject_id = models.CharField(max_length=50, verbose_name="受试者ID")
    subject_visit_id = models.CharField(max_length=100, null=True, blank=True, verbose_name="受试者访视ID")

    class Meta:
        managed = False
        db_table = 'medical_collection_crf_result' # 对应你已有的表名



class MedicalCollectionCrfLog(models.Model):
    medical_collection_crf_result_id = models.BigIntegerField(default=0, verbose_name="病历归集CRF结果id")
    title = models.CharField(max_length=20, default='', verbose_name="一级标题")
    item_key = models.CharField(max_length=20, default='', verbose_name="二级标题")
    content_text = models.CharField(max_length=20, default='', verbose_name="变更记录内容")
    delete_flag = models.SmallIntegerField(default=0, verbose_name="删除标志（0：未删除；1：已删除）")
    create_time = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    create_user = models.CharField(max_length=50, null=True, blank=True, verbose_name="创建人工号")
    create_name = models.CharField(max_length=50, null=True, blank=True, verbose_name="创建人姓名")

    class Meta:
        db_table = 'medical_collection_crf_log'  # 对应你已有的表名
        verbose_name = "病历归集"
        verbose_name_plural = "病历归集CRF变更日志"
        managed = False

class MedicalCollectionTask(BaseModel):
    """病历归集任务"""
    TODO = 'TODO'
    IN_PROGRESS = 'IN_PROGRESS'
    COMPLETED = 'COMPLETED'
    CANCELLED = 'CANCELLED'
    ERROR = 'ERROR'
    STATUS_CHOICES = [(TODO, '待办'), (IN_PROGRESS, '进行中'), (COMPLETED, '已完成'), (CANCELLED, '已取消'), (ERROR, '执行错误')]

    CRF = 'CRF'
    MEDICAL_RECORD = 'MEDICAL_RECORD'
    CATEGORY_CHOICES = [(CRF, 'CRF'), (MEDICAL_RECORD, '病历')]

    name = models.CharField(max_length=255, verbose_name="任务名称")
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES,
                                default=CRF, verbose_name="任务分类")
    description = models.TextField(blank=True, null=True, verbose_name="任务描述")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default=TODO, verbose_name="任务状态")
    start_time = models.DateTimeField(auto_now_add=True, verbose_name="开始时间")
    end_time = models.DateTimeField(blank=True, null=True, verbose_name="结束时间")

    # 一（患者信息）对（病历归集任务）多
    patient = models.ForeignKey(
        'patient.Patient',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='medical_collection_tasks',
        to_field='patient_id',
        db_column='patient_id',
        verbose_name="患者ID",
        db_index=True
    )

    # 一（受试者信息）对（病历归集任务）多
    subject = models.ForeignKey(
        'subject.Subject',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='medical_collection_tasks',
        to_field='subject_id',
        db_column='subject_id',
        verbose_name="受试者ID",
        db_index=True
    )

    project = models.ForeignKey(
        'project.Project',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='medical_collection_tasks',
        to_field='project_id',
        db_column='project_id',
        verbose_name="项目ID",
        db_index=True
    )

    project_site = models.ForeignKey(
        'project.ProjectSite',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='medical_collection_tasks',
        to_field='project_site_id',
        db_column='project_site_id',
        verbose_name="项目中心ID",
        db_index=True
    )

    subject_visit = models.ForeignKey(
        'subject.SubjectVisit',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        db_constraint=False,
        related_name='medical_collection_tasks',
        to_field='subject_visit_id',
        db_column='subject_visit_id',
        verbose_name="受试者访视ID",
        db_index=True
    )


    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "病历归集任务"
        verbose_name_plural = "病历归集任务"
        db_table = 'medical_collection_task'


class MedicalCollectionFile(BaseModel, BaseFileModel):
    """病历归集文件"""
    version = models.CharField(
        max_length=255,
        null=True,
        verbose_name="版本号"
    )
    model_name = models.CharField(max_length=100, null=True, blank=True, verbose_name="模型名称")
    # 一（病历归集任务）对（病历归集文件）多
    task = models.ForeignKey(
        MedicalCollectionTask,
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='files',
        to_field='id',
        db_column='task_id',
        verbose_name="病历归集结果ID",
        db_index=True
    )

    class Meta:
        verbose_name = "病历归集文件"
        verbose_name_plural = "病历归集文件"
        db_table = 'medical_collection_file'
