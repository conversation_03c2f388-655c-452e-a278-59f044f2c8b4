"""
医疗检验单OCR处理结果评测模块
负责评测OCR处理结果的准确性和性能
"""

import re
import time
import pandas as pd
from typing import List, Dict, Any
from datetime import datetime
from tabulate import tabulate
import logging

logger = logging.getLogger(__name__)


def clean_special_symbols(text: str) -> str:
    """
    清理test_code和test_name中的特殊符号
    移除 *、★、★、●、▲、▼、◆、■、□、+ 等特殊符号
    """
    # [FIX] 确保输入是字符串类型
    text = str(text) if text is not None else ''

    if not text:
        return text

    # 移除常见的特殊符号
    special_symbols = ['*', '★', '★', '●', '▲', '▼', '◆', '■', '□', '+', '△', '○', '◇']
    cleaned_text = text
    for symbol in special_symbols:
        cleaned_text = cleaned_text.replace(symbol, '')
    
    return cleaned_text.strip()


def normalize_unit(unit: str) -> str:
    """
    标准化单位表示
    将umol转换为μmol等
    """
    if not unit:
        return unit

    # 单位标准化映射
    unit_mappings = {
        'umol': 'μmol',
        'uL': 'μL',
        'ul': 'μL',
        'ug': 'μg',
        'umol/L': 'μmol/L',
        'ug/L': 'μg/L',
        'ug/mL': 'μg/mL',
        'ug/dL': 'μg/dL',
    }

    normalized = unit.strip()
    for old_unit, new_unit in unit_mappings.items():
        normalized = normalized.replace(old_unit, new_unit)

    return normalized


def clean_ground_truth_value(field_name: str, value) -> str:
    """
    清理标准答案中的特殊值
    
    Args:
        field_name: 字段名称
        value: 原始值
        
    Returns:
        清理后的字符串值
    """
    # 1. 处理None、nan等无效值
    if value is None:
        return ""
    
    # 检查pandas的NaN和NaT
    import pandas as pd
    import numpy as np
    if pd.isna(value) or (hasattr(value, 'isna') and value.isna()):
        return ""
    
    # 转换为字符串并处理pandas的NaN和字符串"nan", "None"等
    str_value = str(value).strip()
    if str_value.lower() in ['nan', 'none', '', 'null', 'nat']:
        return ""
    
    # 2. 替换中文括号为英文括号
    str_value = str_value.replace('（', '(').replace('）', ')')
    
    # 3. 处理单位字段：应用完整的单位标准化
    if field_name == 'test_unit':
        str_value = normalize_unit(str_value)
    
    # 4. 处理test_code字段：如果只包含数字，则设为空
    if field_name == 'test_code':
        if str_value.isdigit():
            return ""
    
    return str_value


def normalize_reference_value(reference_value: str) -> str:
    """
    标准化参考值，统一连字符和波浪号的表示
    将～、~、—等都统一为-
    """
    if not reference_value:
        return reference_value
    
    # 统一各种连字符和波浪号为标准连字符
    normalized = reference_value
    normalized = normalized.replace('～', '-')  # 全角波浪号
    normalized = normalized.replace('~', '-')   # 半角波浪号
    normalized = normalized.replace('—', '-')   # 长连字符
    normalized = normalized.replace('–', '-')   # 中等长度连字符
    
    return normalized


def compare_units_case_insensitive(unit1: str, unit2: str) -> bool:
    """
    大小写不敏感的单位比较
    """
    if not unit1 and not unit2:
        return True
    if not unit1 or not unit2:
        return False

    # 先进行单位标准化
    normalized_unit1 = normalize_unit(unit1)
    normalized_unit2 = normalize_unit(unit2)

    # 大小写不敏感比较
    return normalized_unit1.lower() == normalized_unit2.lower()


def calculate_abnormal_recall_optimized(evaluation_logs: List[Dict]) -> Dict[str, Any]:
    """
    计算优化的异常符号召回率，排除没有异常符号的用例
    """
    # 筛选出包含异常符号的用例
    cases_with_abnormal = []
    for log in evaluation_logs:
        abnormal_stats = log.get('abnormal_symbol_stats', {})
        if abnormal_stats.get('ground_truth_count', 0) > 0:
            cases_with_abnormal.append(log)

    if not cases_with_abnormal:
        return {
            'cases_with_abnormal_count': 0,
            'optimized_recall': 'N/A',
            'optimized_precision': 'N/A',
            'optimized_f1': 'N/A',
            'total_ground_truth_count': 0,
            'total_predicted_count': 0,
            'total_missed_count': 0,
            'total_false_positive_count': 0
        }

    # 计算总体统计
    total_ground_truth = sum(log['abnormal_symbol_stats']['ground_truth_count'] for log in cases_with_abnormal)
    total_predicted = sum(log['abnormal_symbol_stats']['predicted_count'] for log in cases_with_abnormal)
    total_missed = sum(log['abnormal_symbol_stats']['missed_count'] for log in cases_with_abnormal)
    total_false_positive = sum(log['abnormal_symbol_stats']['false_positive_count'] for log in cases_with_abnormal)

    # 计算优化的召回率和精确率
    optimized_recall = (total_ground_truth - total_missed) / total_ground_truth if total_ground_truth > 0 else 0.0
    optimized_precision = (total_predicted - total_false_positive) / total_predicted if total_predicted > 0 else 0.0
    optimized_f1 = 2 * (optimized_precision * optimized_recall) / (optimized_precision + optimized_recall) if (optimized_precision + optimized_recall) > 0 else 0.0

    return {
        'cases_with_abnormal_count': len(cases_with_abnormal),
        'optimized_recall': f"{optimized_recall:.2%}",
        'optimized_precision': f"{optimized_precision:.2%}",
        'optimized_f1': f"{optimized_f1:.2%}",
        'total_ground_truth_count': total_ground_truth,
        'total_predicted_count': total_predicted,
        'total_missed_count': total_missed,
        'total_false_positive_count': total_false_positive
    }


def calculate_test_flag_based_stats(predicted_items: List[Dict], expected_items: List[Dict], matched_indices: Dict) -> Dict[str, Any]:
    """
    计算基于test_flag的异常检测统计
    结合test_flag和abnormal_symbol进行异常检测评估
    """
    stats = {
        'ground_truth_abnormal_count': 0,  # 正确答案中的异常项目数
        'predicted_abnormal_count': 0,     # 预测结果中的异常项目数
        'true_positive': 0,                # 正确识别的异常项目
        'false_positive': 0,               # 误识别的异常项目
        'false_negative': 0,               # 漏识别的异常项目
        'true_negative': 0,                # 正确识别的正常项目
        'details': []                      # 详细记录
    }

    # 遍历匹配的项目
    for pred_idx, exp_idx in matched_indices.items():
        pred_item = predicted_items[pred_idx]
        exp_item = expected_items[exp_idx]

        # 获取正确答案的异常状态
        exp_abnormal_symbol = str(exp_item.get('abnormal_symbol', '')).strip()
        exp_is_abnormal = bool(exp_abnormal_symbol)

        # 获取预测结果的异常状态（结合test_flag和abnormal_symbol）
        pred_abnormal_symbol = str(pred_item.get('abnormal_symbol', '')).strip()
        pred_test_flag = pred_item.get('test_flag', 0)

        # 预测为异常的条件：有异常符号 OR test_flag != 0
        pred_is_abnormal = bool(pred_abnormal_symbol) or (pred_test_flag != 0)

        # 统计各种情况
        if exp_is_abnormal:
            stats['ground_truth_abnormal_count'] += 1
            if pred_is_abnormal:
                stats['true_positive'] += 1
            else:
                stats['false_negative'] += 1
        else:
            if pred_is_abnormal:
                stats['false_positive'] += 1
            else:
                stats['true_negative'] += 1

        if pred_is_abnormal:
            stats['predicted_abnormal_count'] += 1

        # 记录详细信息
        detail = {
            'test_code': pred_item.get('test_code', ''),
            'test_name': pred_item.get('test_name', ''),
            'ground_truth_abnormal': exp_is_abnormal,
            'ground_truth_symbol': exp_abnormal_symbol,
            'predicted_abnormal': pred_is_abnormal,
            'predicted_symbol': pred_abnormal_symbol,
            'predicted_test_flag': pred_test_flag,
            'classification': 'TP' if (exp_is_abnormal and pred_is_abnormal) else
                            'FP' if (not exp_is_abnormal and pred_is_abnormal) else
                            'FN' if (exp_is_abnormal and not pred_is_abnormal) else 'TN'
        }
        stats['details'].append(detail)

    # 计算性能指标
    total_positive = stats['true_positive'] + stats['false_negative']
    total_predicted_positive = stats['true_positive'] + stats['false_positive']

    recall = stats['true_positive'] / total_positive if total_positive > 0 else 0.0
    precision = stats['true_positive'] / total_predicted_positive if total_predicted_positive > 0 else 0.0
    f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0.0

    stats['recall'] = f"{recall:.2%}"
    stats['precision'] = f"{precision:.2%}"
    stats['f1_score'] = f"{f1_score:.2%}"

    return stats

# 评测所需字段
EVAL_FIELDS = [
    'test_code', 'test_name', 'test_value',
    'test_unit', 'reference_value',
    'abnormal_symbol', 'test_flag',
    # 'collect_time', 'report_time'  # 不需要评估时间字段
]


def _test_item_to_dict(test_item) -> Dict[str, Any]:
    """将 TestItem 对象转换为字典，只包含评测所需字段，并进行标准化处理"""
    item_dict = {}
    for field in EVAL_FIELDS:
        value = getattr(test_item, field, None)
        
        # test_flag字段特殊处理：确保是整数类型
        if field == 'test_flag':
            try:
                processed_value = int(value) if value is not None else 0
            except (ValueError, TypeError):
                processed_value = 0
        else:
            # 对其他字段应用统一的数据清理函数
            processed_value = clean_ground_truth_value(field, value)

        item_dict[field] = processed_value
    return item_dict


def load_ground_truth(excel_path: str) -> Dict[str, List[Dict[str, Any]]]:
    """
    从 Excel 文件中加载正确答案。
    Excel 包含多个 Sheet，每个 Sheet 名称对应一个图片文件名（不含扩展名）。
    每个 Sheet 中，只读取包含标题行 "test_code	test_name	test_value	abnormal_symbol	test_unit	reference_value" 之后的数据。

    Returns:
        Dict[str, List[Dict[str, Any]]]: 键为Sheet名称，值为该Sheet的数据列表
    """
    all_sheets_data = {}
    try:
        # 读取所有 Sheet
        xls = pd.ExcelFile(excel_path)
        logger.info(f"发现 {len(xls.sheet_names)} 个Sheet: {xls.sheet_names}")

        # 定义标题行内容
        expected_header = ["test_code", "test_name", "test_value", "abnormal_symbol", "test_unit", "reference_value"]

        for sheet_name in xls.sheet_names:
            logger.info(f"处理Sheet: {sheet_name}")

            # 读取整个Sheet，不指定header
            df_full = pd.read_excel(xls, sheet_name=sheet_name, header=None)

            # 查找标题行
            header_row_index = None
            for idx, row in df_full.iterrows():
                # 将行转换为字符串列表，去除空值和空白
                row_values = [str(cell).strip() for cell in row.values if pd.notna(cell) and str(cell).strip()]

                # 检查是否包含所有期望的标题
                if len(row_values) >= len(expected_header):
                    # 检查前6列是否匹配期望的标题
                    if row_values[:len(expected_header)] == expected_header:
                        header_row_index = idx
                        logger.info(f"在Sheet '{sheet_name}' 的第 {idx + 1} 行找到标题行")
                        break

            if header_row_index is None:
                logger.warning(f"在Sheet '{sheet_name}' 中未找到标题行，跳过该Sheet")
                continue

            # 读取标题行之后的数据
            df = pd.read_excel(xls, sheet_name=sheet_name, header=header_row_index)

            # 检查列名是否正确
            if list(df.columns[:len(expected_header)]) != expected_header:
                logger.warning(f"Sheet '{sheet_name}' 的列名不匹配期望的标题，跳过该Sheet")
                continue

            # 将 DataFrame 转换为字典列表
            sheet_data = df.to_dict(orient='records')

            # 确保所有字段都是字符串，并应用数据清理规则
            for row in sheet_data:
                for key, value in row.items():
                    # 应用统一的数据清理函数
                    row[key] = clean_ground_truth_value(key, value)

            # 过滤掉空行（所有字段都为空的行）
            sheet_data = [row for row in sheet_data if any(row.get(field, "") for field in expected_header)]

            all_sheets_data[sheet_name] = sheet_data
            logger.info(f"Sheet '{sheet_name}' 加载了 {len(sheet_data)} 行数据")

    except FileNotFoundError:
        logger.error(f"错误：未找到正确答案文件: {excel_path}")
        raise
    except Exception as e:
        logger.error(f"加载正确答案文件时发生错误: {e}")
        raise

    logger.info(f"总共加载了 {len(all_sheets_data)} 个Sheet的数据")
    return all_sheets_data


def evaluate_ocr_results(ocr_texts: List[str], ground_truth_data: List[List[Dict[str, Any]]], 
                        process_function) -> Dict[str, Any]:
    """
    评测 OCR 处理结果的准确性和性能。
    
    Args:
        ocr_texts: OCR文本列表
        ground_truth_data: 正确答案数据
        process_function: 处理OCR文本的函数
    
    Returns:
        评测报告字典
    """
    total_processing_time = 0.0
    total_test_cases = len(ocr_texts)
    
    overall_item_match_count = 0  # 成功匹配的项目总数
    overall_ground_truth_item_count = 0  # 正确答案的项目总数
    overall_correct_field_count = 0  # 所有对比字段中正确匹配的总数
    overall_expected_field_count = 0  # 所有对比字段中应对比的总数
    
    # 异常符号统计
    overall_abnormal_symbol_stats = {
        'ground_truth_count': 0,  # 答案中的异常符号总数
        'predicted_count': 0,     # 识别结果中的异常符号总数
        'missed_count': 0,        # 漏检数量（答案有异常符号，但识别结果没有）
        'false_positive_count': 0 # 误检数量（答案没有异常符号，但识别结果有）
    }
    
    evaluation_logs = [] # 记录每次评测的详细日志
    error_logs = [] # 记录所有不匹配的详细错误

    print(f"开始评测 {total_test_cases} 个OCR文本...")

    for i, ocr_text in enumerate(ocr_texts):
        case_log = {
            "case_index": i + 1,
            "ocr_text_preview": ocr_text[:100] + "...", # 记录部分OCR文本
            "processing_time": 0.0,
            "predicted_item_count": 0,
            "ground_truth_item_count": 0,
            "matched_item_count": 0,
            "field_accuracy_details": {},
            "unmatched_predicted_items": [],
            "unmatched_ground_truth_items": [],
            "field_mismatches": [],
            "abnormal_symbol_stats": {
                'ground_truth_count': 0,  # 答案中的异常符号总数
                'predicted_count': 0,     # 识别结果中的异常符号总数
                'missed_count': 0,        # 漏检数量（答案有异常符号，但识别结果没有）
                'false_positive_count': 0, # 误检数量（答案没有异常符号，但识别结果有）
                'details': []             # 详细记录每个项目的异常符号情况
            },
            "test_flag_stats": {
                'ground_truth_abnormal_count': 0,
                'predicted_abnormal_count': 0,
                'true_positive': 0,
                'false_positive': 0,
                'false_negative': 0,
                'true_negative': 0,
                'recall': '0.00%',
                'precision': '0.00%',
                'f1_score': '0.00%',
                'details': []
            }
        }

        expected_items_raw = ground_truth_data[i] if i < len(ground_truth_data) else []
        case_log["ground_truth_item_count"] = len(expected_items_raw)
        overall_ground_truth_item_count += len(expected_items_raw)

        predicted_items = []
        try:
            start_time = time.time()
            predicted_items_obj = process_function(ocr_text)
            end_time = time.time()
            call_duration = end_time - start_time
            total_processing_time += call_duration
            case_log["processing_time"] = call_duration
            
            predicted_items = [_test_item_to_dict(item) for item in predicted_items_obj]
            case_log["predicted_item_count"] = len(predicted_items)

        except Exception as e:
            logger.error(f"处理第 {i+1} 个OCR文本时发生错误: {e}")
            error_logs.append({
                "case_index": i + 1,
                "type": "Processing Error",
                "message": str(e),
                "ocr_text": ocr_text
            })
            # 确保设置field_accuracy字段，避免后续KeyError
            case_log["field_accuracy"] = 0.0
            evaluation_logs.append(case_log) # 即使失败也记录日志
            continue # 跳过当前用例的对比

        # --- 结果对比逻辑 ---
        matched_predicted_indices = set()
        matched_expected_indices = set()
        
        case_correct_fields = 0
        case_expected_fields = 0

        # 智能匹配：为每个预测项目找到最佳匹配的期望项目
        for pred_idx, pred_item in enumerate(predicted_items):
            best_match_idx = None
            
            pred_test_code = clean_special_symbols(pred_item.get('test_code', ''))
            pred_test_name = clean_special_symbols(pred_item.get('test_name', ''))
            
            # 寻找最佳匹配的期望项目
            for exp_idx, exp_item in enumerate(expected_items_raw):
                if exp_idx in matched_expected_indices:
                    continue  # 已经被匹配过的跳过
                
                exp_test_code = clean_special_symbols(exp_item.get('test_code', ''))
                exp_test_name = clean_special_symbols(exp_item.get('test_name', ''))
                
                # test_name优先匹配，如果test_name匹配则直接使用
                if pred_test_name and exp_test_name and pred_test_name == exp_test_name:
                    best_match_idx = exp_idx
                    break
                # 如果test_name不匹配或为空，则尝试test_code匹配
                elif pred_test_code and exp_test_code and pred_test_code == exp_test_code:
                    best_match_idx = exp_idx
                    # 继续寻找更好的匹配（test_name匹配优先级更高）
            
            # 如果找到匹配，进行字段对比
            if best_match_idx is not None:
                matched_predicted_indices.add(pred_idx)
                matched_expected_indices.add(best_match_idx)
                case_log["matched_item_count"] += 1
                
                exp_item = expected_items_raw[best_match_idx]

                # 统计异常符号情况
                pred_abnormal = str(pred_item.get('abnormal_symbol', '')).strip()
                exp_abnormal = str(exp_item.get('abnormal_symbol', '')).strip()
                
                # 记录异常符号详情
                abnormal_detail = {
                    'test_code': pred_item.get('test_code', ''),
                    'test_name': pred_item.get('test_name', ''),
                    'ground_truth_has_symbol': bool(exp_abnormal),
                    'predicted_has_symbol': bool(pred_abnormal),
                    'ground_truth_symbol': exp_abnormal,
                    'predicted_symbol': pred_abnormal
                }
                case_log["abnormal_symbol_stats"]["details"].append(abnormal_detail)
                
                # 更新异常符号统计
                if exp_abnormal:
                    case_log["abnormal_symbol_stats"]["ground_truth_count"] += 1
                    overall_abnormal_symbol_stats["ground_truth_count"] += 1
                    
                if pred_abnormal:
                    case_log["abnormal_symbol_stats"]["predicted_count"] += 1
                    overall_abnormal_symbol_stats["predicted_count"] += 1
                
                # 漏检：答案有异常符号，但识别结果没有
                if exp_abnormal and not pred_abnormal:
                    case_log["abnormal_symbol_stats"]["missed_count"] += 1
                    overall_abnormal_symbol_stats["missed_count"] += 1
                
                # 误检：答案没有异常符号，但识别结果有
                if not exp_abnormal and pred_abnormal:
                    case_log["abnormal_symbol_stats"]["false_positive_count"] += 1
                    overall_abnormal_symbol_stats["false_positive_count"] += 1
                
                for field in EVAL_FIELDS:
                    if field in ['abnormal_symbol', 'test_flag']:
                        continue  # 跳过异常符号和test_flag字段，它们有专门的统计
                    case_expected_fields += 1

                    pred_val = str(pred_item.get(field, '')).strip()
                    exp_val = str(exp_item.get(field, '')).strip()

                    # 对test_code和test_name字段应用特殊符号清理
                    if field in ['test_code', 'test_name']:
                        pred_val = clean_special_symbols(pred_val)
                        exp_val = clean_special_symbols(exp_val)

                    # 特殊处理 test_unit 字段的大小写不敏感比较
                    elif field == 'test_unit':
                        if compare_units_case_insensitive(pred_val, exp_val):
                            case_correct_fields += 1
                        else:
                            case_log["field_mismatches"].append({
                                "pred_index": pred_idx,
                                "exp_index": best_match_idx,
                                "field": field,
                                "predicted": pred_val,
                                "expected": exp_val,
                                "match_type": "unit_comparison",
                                "test_code": pred_item.get('test_code', ''),
                                "test_name": pred_item.get('test_name', '')
                            })
                            error_logs.append({
                                "case_index": i + 1,
                                "pred_index": pred_idx,
                                "exp_index": best_match_idx,
                                "field": field,
                                "predicted": pred_val,
                                "expected": exp_val,
                                "type": "Unit Mismatch",
                                "predicted_item": pred_item,
                                "expected_item": exp_item
                            })
                        continue

                    # 特殊处理 reference_value 字段，忽略连字符和波浪号的区别
                    elif field == 'reference_value':
                        normalized_pred = normalize_reference_value(pred_val)
                        normalized_exp = normalize_reference_value(exp_val)
                        if normalized_pred == normalized_exp:
                            case_correct_fields += 1
                        else:
                            case_log["field_mismatches"].append({
                                "pred_index": pred_idx,
                                "exp_index": best_match_idx,
                                "field": field,
                                "predicted": pred_val,
                                "expected": exp_val,
                                "match_type": "reference_value_comparison",
                                "test_code": pred_item.get('test_code', ''),
                                "test_name": pred_item.get('test_name', '')
                            })
                            error_logs.append({
                                "case_index": i + 1,
                                "pred_index": pred_idx,
                                "exp_index": best_match_idx,
                                "field": field,
                                "predicted": pred_val,
                                "expected": exp_val,
                                "type": "Reference Value Mismatch",
                                "predicted_item": pred_item,
                                "expected_item": exp_item
                            })
                        continue

                    # 特殊处理 test_value 字段的数字比较
                    if field == 'test_value' and pred_val and exp_val:
                        try:
                            # 尝试将两者都转换为浮点数进行比较
                            if float(pred_val) == float(exp_val):
                                case_correct_fields += 1
                            else:
                                case_log["field_mismatches"].append({
                                    "pred_index": pred_idx,
                                    "exp_index": best_match_idx,
                                    "field": field,
                                    "predicted": pred_val,
                                    "expected": exp_val,
                                    "match_type": "smart_match (numeric)",
                                    "test_code": pred_item.get('test_code', ''),
                                    "test_name": pred_item.get('test_name', '')
                                })
                                error_logs.append({
                                    "case_index": i + 1,
                                    "pred_index": pred_idx,
                                    "exp_index": best_match_idx,
                                    "field": field,
                                    "predicted": pred_val,
                                    "expected": exp_val,
                                    "type": "Field Mismatch (Numeric)",
                                    "predicted_item": pred_item,
                                    "expected_item": exp_item
                                })
                        except (ValueError, TypeError):
                            # 如果无法转换为数字，则按字符串比较
                            if pred_val == exp_val:
                                case_correct_fields += 1
                            else:
                                case_log["field_mismatches"].append({
                                    "pred_index": pred_idx,
                                    "exp_index": best_match_idx,
                                    "field": field,
                                    "predicted": pred_val,
                                    "expected": exp_val,
                                    "match_type": "smart_match (string)",
                                    "test_code": pred_item.get('test_code', ''),
                                    "test_name": pred_item.get('test_name', '')
                                })
                                error_logs.append({
                                    "case_index": i + 1,
                                    "pred_index": pred_idx,
                                    "exp_index": best_match_idx,
                                    "field": field,
                                    "predicted": pred_val,
                                    "expected": exp_val,
                                    "type": "Field Mismatch (String)",
                                    "predicted_item": pred_item,
                                    "expected_item": exp_item
                                })
                    else:
                        # 普通字符串比较
                        if pred_val == exp_val:
                            case_correct_fields += 1
                        else:
                            case_log["field_mismatches"].append({
                                "pred_index": pred_idx,
                                "exp_index": best_match_idx,
                                "field": field,
                                "predicted": pred_val,
                                "expected": exp_val,
                                "match_type": "smart_match",
                                "test_code": pred_item.get('test_code', ''),
                                "test_name": pred_item.get('test_name', '')
                            })
                            error_logs.append({
                                "case_index": i + 1,
                                "pred_index": pred_idx,
                                "exp_index": best_match_idx,
                                "field": field,
                                "predicted": pred_val,
                                "expected": exp_val,
                                "type": "Field Mismatch",
                                "predicted_item": pred_item,
                                "expected_item": exp_item
                            })

        # 计算基于test_flag的异常检测统计
        # 注意：这里需要根据实际的匹配逻辑来构建正确的索引映射
        # 由于当前的匹配逻辑比较复杂，我们暂时跳过test_flag统计
        # 在实际使用中，需要根据具体的匹配算法来正确映射索引
        test_flag_stats = {
            'ground_truth_abnormal_count': 0,
            'predicted_abnormal_count': 0,
            'true_positive': 0,
            'false_positive': 0,
            'false_negative': 0,
            'true_negative': 0,
            'recall': '0.00%',
            'precision': '0.00%',
            'f1_score': '0.00%',
            'details': []
        }
        case_log["test_flag_stats"] = test_flag_stats

        # 记录未匹配的项目
        for pred_idx, pred_item in enumerate(predicted_items):
            if pred_idx not in matched_predicted_indices:
                case_log["unmatched_predicted_items"].append(pred_item)
                error_logs.append({
                    "case_index": i + 1,
                    "type": "Unmatched Predicted Item",
                    "item": pred_item
                })
        
        for exp_idx, exp_item in enumerate(expected_items_raw):
            if exp_idx not in matched_expected_indices:
                case_log["unmatched_ground_truth_items"].append(exp_item)
                error_logs.append({
                    "case_index": i + 1,
                    "type": "Unmatched Ground Truth Item",
                    "item": exp_item
                })
        
        # 累加总体的正确字段数和期望字段数
        overall_correct_field_count += case_correct_fields
        overall_expected_field_count += case_expected_fields
        overall_item_match_count += case_log["matched_item_count"]

        # 计算当前用例的字段准确率
        case_log["field_accuracy"] = case_correct_fields / case_expected_fields if case_expected_fields > 0 else 0
        evaluation_logs.append(case_log)

    # --- 汇总结果 ---
    # 总体统计（基于所有项目和字段的总计）
    overall_average_processing_time = total_processing_time / total_test_cases if total_test_cases > 0 else 0.0
    overall_item_accuracy = overall_item_match_count / overall_ground_truth_item_count if overall_ground_truth_item_count > 0 else 0.0
    overall_field_accuracy = overall_correct_field_count / overall_expected_field_count if overall_expected_field_count > 0 else 0.0
    
    # 按用例统计（每个用例的准确率取平均）
    case_processing_times = [log["processing_time"] for log in evaluation_logs]
    case_field_accuracies = [log["field_accuracy"] for log in evaluation_logs]
    
    # 计算每个用例的项目准确率
    case_item_accuracies = []
    for log in evaluation_logs:
        if log["ground_truth_item_count"] > 0:
            case_item_accuracy = log["matched_item_count"] / log["ground_truth_item_count"]
        else:
            case_item_accuracy = 0.0
        case_item_accuracies.append(case_item_accuracy)
    
    # 计算各用例的平均值
    avg_case_processing_time = sum(case_processing_times) / len(case_processing_times) if case_processing_times else 0.0
    avg_case_item_accuracy = sum(case_item_accuracies) / len(case_item_accuracies) if case_item_accuracies else 0.0
    avg_case_field_accuracy = sum(case_field_accuracies) / len(case_field_accuracies) if case_field_accuracies else 0.0

    # 计算异常符号识别的准确率
    abnormal_symbol_recall = 0.0
    if overall_abnormal_symbol_stats['ground_truth_count'] > 0:
        abnormal_symbol_recall = (overall_abnormal_symbol_stats['ground_truth_count'] - 
                                overall_abnormal_symbol_stats['missed_count']) / overall_abnormal_symbol_stats['ground_truth_count']
    
    abnormal_symbol_precision = 0.0
    if overall_abnormal_symbol_stats['predicted_count'] > 0:
        abnormal_symbol_precision = (overall_abnormal_symbol_stats['predicted_count'] - 
                                   overall_abnormal_symbol_stats['false_positive_count']) / overall_abnormal_symbol_stats['predicted_count']
    
    # 计算F1分数
    abnormal_symbol_f1 = 0.0
    if abnormal_symbol_precision + abnormal_symbol_recall > 0:
        abnormal_symbol_f1 = 2 * (abnormal_symbol_precision * abnormal_symbol_recall) / (abnormal_symbol_precision + abnormal_symbol_recall)

    # 计算优化的异常符号召回率
    optimized_abnormal_stats = calculate_abnormal_recall_optimized(evaluation_logs)

    # 计算总体test_flag统计
    overall_test_flag_stats = {
        'ground_truth_abnormal_count': 0,
        'predicted_abnormal_count': 0,
        'true_positive': 0,
        'false_positive': 0,
        'false_negative': 0,
        'true_negative': 0
    }

    for log in evaluation_logs:
        test_flag_stats = log.get('test_flag_stats', {})
        for key in overall_test_flag_stats:
            overall_test_flag_stats[key] += test_flag_stats.get(key, 0)

    # 计算总体test_flag性能指标
    total_positive = overall_test_flag_stats['true_positive'] + overall_test_flag_stats['false_negative']
    total_predicted_positive = overall_test_flag_stats['true_positive'] + overall_test_flag_stats['false_positive']

    test_flag_recall = overall_test_flag_stats['true_positive'] / total_positive if total_positive > 0 else 0.0
    test_flag_precision = overall_test_flag_stats['true_positive'] / total_predicted_positive if total_predicted_positive > 0 else 0.0
    test_flag_f1 = 2 * (test_flag_precision * test_flag_recall) / (test_flag_precision + test_flag_recall) if (test_flag_precision + test_flag_recall) > 0 else 0.0

    report = {
        "total_test_cases": total_test_cases,
        
        # 总体统计（基于所有数据的汇总）
        "overall_metrics": {
            "total_processing_time_seconds": f"{total_processing_time:.4f}",
            "average_processing_time_seconds": f"{overall_average_processing_time:.4f}",
            "item_accuracy": f"{overall_item_accuracy:.2%}",
            "field_accuracy": f"{overall_field_accuracy:.2%}",
            "total_matched_items": overall_item_match_count,
            "total_ground_truth_items": overall_ground_truth_item_count,
            "total_correct_fields": overall_correct_field_count,
            "total_expected_fields": overall_expected_field_count
        },
        
        # 异常符号统计
        "abnormal_symbol_metrics": {
            "ground_truth_count": overall_abnormal_symbol_stats['ground_truth_count'],
            "predicted_count": overall_abnormal_symbol_stats['predicted_count'],
            "missed_count": overall_abnormal_symbol_stats['missed_count'],
            "false_positive_count": overall_abnormal_symbol_stats['false_positive_count'],
            "recall": f"{abnormal_symbol_recall:.2%}",
            "precision": f"{abnormal_symbol_precision:.2%}",
            "f1_score": f"{abnormal_symbol_f1:.2%}"
        },

        # 优化的异常符号统计（排除无异常符号的用例）
        "optimized_abnormal_symbol_metrics": optimized_abnormal_stats,

        # 基于test_flag的异常检测统计
        "test_flag_based_metrics": {
            "ground_truth_abnormal_count": overall_test_flag_stats['ground_truth_abnormal_count'],
            "predicted_abnormal_count": overall_test_flag_stats['predicted_abnormal_count'],
            "true_positive": overall_test_flag_stats['true_positive'],
            "false_positive": overall_test_flag_stats['false_positive'],
            "false_negative": overall_test_flag_stats['false_negative'],
            "true_negative": overall_test_flag_stats['true_negative'],
            "recall": f"{test_flag_recall:.2%}",
            "precision": f"{test_flag_precision:.2%}",
            "f1_score": f"{test_flag_f1:.2%}"
        },
        
        # 按用例平均统计（每个用例作为独立单位计算平均）
        "case_average_metrics": {
            "average_processing_time_seconds": f"{avg_case_processing_time:.4f}",
            "average_item_accuracy": f"{avg_case_item_accuracy:.2%}",
            "average_field_accuracy": f"{avg_case_field_accuracy:.2%}",
            "processing_time_std": f"{(sum([(t - avg_case_processing_time)**2 for t in case_processing_times]) / len(case_processing_times))**0.5 if case_processing_times else 0:.4f}",
            "item_accuracy_std": f"{(sum([(a - avg_case_item_accuracy)**2 for a in case_item_accuracies]) / len(case_item_accuracies))**0.5 if case_item_accuracies else 0:.4f}",
            "field_accuracy_std": f"{(sum([(a - avg_case_field_accuracy)**2 for a in case_field_accuracies]) / len(case_field_accuracies))**0.5 if case_field_accuracies else 0:.4f}"
        },
        
        "evaluation_logs": evaluation_logs,
        "error_summary": {
            "total_errors": len(error_logs),
            "errors": error_logs
        }
    }
    
    return report


def evaluate_ocr_results_with_cache(ocr_texts: List[str], ground_truth_data: List[List[Dict[str, Any]]], 
                                   cached_results: List[List]) -> Dict[str, Any]:
    """
    使用缓存的处理结果进行评测，避免重复调用大模型
    
    Args:
        ocr_texts: OCR文本列表
        ground_truth_data: 正确答案数据
        cached_results: 已缓存的处理结果列表
    
    Returns:
        评测报告字典
    """
    total_processing_time = 0.0  # 由于使用缓存，处理时间为0
    total_test_cases = len(ocr_texts)
    
    overall_item_match_count = 0  # 成功匹配的项目总数
    overall_ground_truth_item_count = 0  # 正确答案的项目总数
    overall_correct_field_count = 0  # 所有对比字段中正确匹配的总数
    overall_expected_field_count = 0  # 所有对比字段中应对比的总数
    
    # 异常符号统计
    overall_abnormal_symbol_stats = {
        'ground_truth_count': 0,  # 答案中的异常符号总数
        'predicted_count': 0,     # 识别结果中的异常符号总数
        'missed_count': 0,        # 漏检数量（答案有异常符号，但识别结果没有）
        'false_positive_count': 0 # 误检数量（答案没有异常符号，但识别结果有）
    }
    
    evaluation_logs = [] # 记录每次评测的详细日志
    error_logs = [] # 记录所有不匹配的详细错误

    print(f"开始评测 {total_test_cases} 个OCR文本（使用缓存结果）...")

    for i, ocr_text in enumerate(ocr_texts):
        case_log = {
            "case_index": i + 1,
            "ocr_text_preview": ocr_text[:100] + "...", # 记录部分OCR文本
            "processing_time": 0.0,  # 使用缓存，处理时间为0
            "predicted_item_count": 0,
            "ground_truth_item_count": 0,
            "matched_item_count": 0,
            "field_accuracy_details": {},
            "unmatched_predicted_items": [],
            "unmatched_ground_truth_items": [],
            "field_mismatches": [],
            "abnormal_symbol_stats": {
                'ground_truth_count': 0,  # 答案中的异常符号总数
                'predicted_count': 0,     # 识别结果中的异常符号总数
                'missed_count': 0,        # 漏检数量（答案有异常符号，但识别结果没有）
                'false_positive_count': 0, # 误检数量（答案没有异常符号，但识别结果有）
                'details': []             # 详细记录每个项目的异常符号情况
            },
            "test_flag_stats": {
                'ground_truth_abnormal_count': 0,
                'predicted_abnormal_count': 0,
                'true_positive': 0,
                'false_positive': 0,
                'false_negative': 0,
                'true_negative': 0,
                'recall': '0.00%',
                'precision': '0.00%',
                'f1_score': '0.00%',
                'details': []
            }
        }

        expected_items_raw = ground_truth_data[i] if i < len(ground_truth_data) else []
        
        # [FIX] 如果 expected_items_raw 是一个DataFrame，则转换为字典列表
        if isinstance(expected_items_raw, pd.DataFrame):
            # 将NaN值替换为None或空字符串
            expected_items_raw = expected_items_raw.where(pd.notna(expected_items_raw), None)
            expected_items_raw = expected_items_raw.to_dict('records')
            
            # 对DataFrame转换后的数据应用数据清理规则
            for row in expected_items_raw:
                for key, value in row.items():
                    row[key] = clean_ground_truth_value(key, value)

        case_log["ground_truth_item_count"] = len(expected_items_raw)
        overall_ground_truth_item_count += len(expected_items_raw)

        # 使用缓存的结果
        predicted_items = []
        if i < len(cached_results):
            predicted_items_obj = cached_results[i]
            predicted_items = [_test_item_to_dict(item) for item in predicted_items_obj]
            case_log["predicted_item_count"] = len(predicted_items)
        else:
            logger.warning(f"缓存结果不足，跳过第 {i+1} 个测试用例")
            case_log["field_accuracy"] = 0.0
            evaluation_logs.append(case_log)
            continue

        # --- 结果对比逻辑 ---
        matched_predicted_indices = set()
        matched_expected_indices = set()
        
        case_correct_fields = 0
        case_expected_fields = 0

        # 智能匹配：为每个预测项目找到最佳匹配的期望项目
        for pred_idx, pred_item in enumerate(predicted_items):
            best_match_idx = None
            
            pred_test_code = clean_special_symbols(pred_item.get('test_code', ''))
            pred_test_name = clean_special_symbols(pred_item.get('test_name', ''))
            
            # 寻找最佳匹配的期望项目
            for exp_idx, exp_item in enumerate(expected_items_raw):
                if exp_idx in matched_expected_indices:
                    continue  # 已经被匹配过的跳过
                
                exp_test_code = clean_special_symbols(exp_item.get('test_code', ''))
                exp_test_name = clean_special_symbols(exp_item.get('test_name', ''))
                
                # test_name优先匹配，如果test_name匹配则直接使用
                if pred_test_name and exp_test_name and pred_test_name == exp_test_name:
                    best_match_idx = exp_idx
                    break
                # 如果test_name不匹配或为空，则尝试test_code匹配
                elif pred_test_code and exp_test_code and pred_test_code == exp_test_code:
                    best_match_idx = exp_idx
                    # 继续寻找更好的匹配（test_name匹配优先级更高）
            
            # 如果找到匹配，进行字段对比
            if best_match_idx is not None:
                matched_predicted_indices.add(pred_idx)
                matched_expected_indices.add(best_match_idx)
                case_log["matched_item_count"] += 1
                
                exp_item = expected_items_raw[best_match_idx]

                # 统计异常符号情况
                pred_abnormal = str(pred_item.get('abnormal_symbol', '')).strip()
                exp_abnormal = str(exp_item.get('abnormal_symbol', '')).strip()
                
                # 记录异常符号详情
                abnormal_detail = {
                    'test_code': pred_item.get('test_code', ''),
                    'test_name': pred_item.get('test_name', ''),
                    'ground_truth_has_symbol': bool(exp_abnormal),
                    'predicted_has_symbol': bool(pred_abnormal),
                    'ground_truth_symbol': exp_abnormal,
                    'predicted_symbol': pred_abnormal
                }
                case_log["abnormal_symbol_stats"]["details"].append(abnormal_detail)
                
                # 更新异常符号统计
                if exp_abnormal:
                    case_log["abnormal_symbol_stats"]["ground_truth_count"] += 1
                    overall_abnormal_symbol_stats["ground_truth_count"] += 1
                    
                if pred_abnormal:
                    case_log["abnormal_symbol_stats"]["predicted_count"] += 1
                    overall_abnormal_symbol_stats["predicted_count"] += 1
                
                # 漏检：答案有异常符号，但识别结果没有
                if exp_abnormal and not pred_abnormal:
                    case_log["abnormal_symbol_stats"]["missed_count"] += 1
                    overall_abnormal_symbol_stats["missed_count"] += 1
                
                # 误检：答案没有异常符号，但识别结果有
                if not exp_abnormal and pred_abnormal:
                    case_log["abnormal_symbol_stats"]["false_positive_count"] += 1
                    overall_abnormal_symbol_stats["false_positive_count"] += 1
                
                # 对比所有评测字段（跳过abnormal_symbol和test_flag，因为有专门的统计）
                for field in EVAL_FIELDS:
                    if field in ['abnormal_symbol', 'test_flag']:
                        continue  # 跳过异常符号和test_flag字段，它们有专门的统计

                    case_expected_fields += 1

                    pred_val = str(pred_item.get(field, '')).strip()
                    exp_val = str(exp_item.get(field, '')).strip()

                    # 对test_code和test_name字段应用特殊符号清理
                    if field in ['test_code', 'test_name']:
                        pred_val = clean_special_symbols(pred_val)
                        exp_val = clean_special_symbols(exp_val)

                    # 特殊处理 test_unit 字段的大小写不敏感比较
                    elif field == 'test_unit':
                        if compare_units_case_insensitive(pred_val, exp_val):
                            case_correct_fields += 1
                        else:
                            case_log["field_mismatches"].append({
                                "pred_index": pred_idx,
                                "exp_index": best_match_idx,
                                "field": field,
                                "predicted": pred_val,
                                "expected": exp_val,
                                "match_type": "unit_comparison",
                                "test_code": pred_item.get('test_code', ''),
                                "test_name": pred_item.get('test_name', '')
                            })
                            error_logs.append({
                                "case_index": i + 1,
                                "pred_index": pred_idx,
                                "exp_index": best_match_idx,
                                "field": field,
                                "predicted": pred_val,
                                "expected": exp_val,
                                "type": "Unit Mismatch",
                                "predicted_item": pred_item,
                                "expected_item": exp_item
                            })
                        continue

                    # 特殊处理 reference_value 字段，忽略连字符和波浪号的区别
                    elif field == 'reference_value':
                        normalized_pred = normalize_reference_value(pred_val)
                        normalized_exp = normalize_reference_value(exp_val)
                        if normalized_pred == normalized_exp:
                            case_correct_fields += 1
                        else:
                            case_log["field_mismatches"].append({
                                "pred_index": pred_idx,
                                "exp_index": best_match_idx,
                                "field": field,
                                "predicted": pred_val,
                                "expected": exp_val,
                                "match_type": "reference_value_comparison",
                                "test_code": pred_item.get('test_code', ''),
                                "test_name": pred_item.get('test_name', '')
                            })
                            error_logs.append({
                                "case_index": i + 1,
                                "pred_index": pred_idx,
                                "exp_index": best_match_idx,
                                "field": field,
                                "predicted": pred_val,
                                "expected": exp_val,
                                "type": "Reference Value Mismatch",
                                "predicted_item": pred_item,
                                "expected_item": exp_item
                            })
                        continue

                    # 特殊处理 test_value 字段的数字比较
                    if field == 'test_value' and pred_val and exp_val:
                        try:
                            # 尝试将两者都转换为浮点数进行比较
                            if float(pred_val) == float(exp_val):
                                case_correct_fields += 1
                            else:
                                case_log["field_mismatches"].append({
                                    "pred_index": pred_idx,
                                    "exp_index": best_match_idx,
                                    "field": field,
                                    "predicted": pred_val,
                                    "expected": exp_val,
                                    "match_type": "smart_match (numeric)",
                                    "test_code": pred_item.get('test_code', ''),
                                    "test_name": pred_item.get('test_name', '')
                                })
                                error_logs.append({
                                    "case_index": i + 1,
                                    "pred_index": pred_idx,
                                    "exp_index": best_match_idx,
                                    "field": field,
                                    "predicted": pred_val,
                                    "expected": exp_val,
                                    "type": "Field Mismatch (Numeric)",
                                    "predicted_item": pred_item,
                                    "expected_item": exp_item
                                })
                        except (ValueError, TypeError):
                            # 如果无法转换为数字，则按字符串比较
                            if pred_val == exp_val:
                                case_correct_fields += 1
                            else:
                                case_log["field_mismatches"].append({
                                    "pred_index": pred_idx,
                                    "exp_index": best_match_idx,
                                    "field": field,
                                    "predicted": pred_val,
                                    "expected": exp_val,
                                    "match_type": "smart_match (string)",
                                    "test_code": pred_item.get('test_code', ''),
                                    "test_name": pred_item.get('test_name', '')
                                })
                                error_logs.append({
                                    "case_index": i + 1,
                                    "pred_index": pred_idx,
                                    "exp_index": best_match_idx,
                                    "field": field,
                                    "predicted": pred_val,
                                    "expected": exp_val,
                                    "type": "Field Mismatch (String)",
                                    "predicted_item": pred_item,
                                    "expected_item": exp_item
                                })
                    else:
                        # 普通字符串比较
                        if pred_val == exp_val:
                            case_correct_fields += 1
                        else:
                            case_log["field_mismatches"].append({
                                "pred_index": pred_idx,
                                "exp_index": best_match_idx,
                                "field": field,
                                "predicted": pred_val,
                                "expected": exp_val,
                                "match_type": "smart_match",
                                "test_code": pred_item.get('test_code', ''),
                                "test_name": pred_item.get('test_name', '')
                            })
                            error_logs.append({
                                "case_index": i + 1,
                                "pred_index": pred_idx,
                                "exp_index": best_match_idx,
                                "field": field,
                                "predicted": pred_val,
                                "expected": exp_val,
                                "type": "Field Mismatch",
                                "predicted_item": pred_item,
                                "expected_item": exp_item
                            })

        # 计算基于test_flag的异常检测统计
        # 注意：这里需要根据实际的匹配逻辑来构建正确的索引映射
        # 由于当前的匹配逻辑比较复杂，我们暂时跳过test_flag统计
        # 在实际使用中，需要根据具体的匹配算法来正确映射索引
        test_flag_stats = {
            'ground_truth_abnormal_count': 0,
            'predicted_abnormal_count': 0,
            'true_positive': 0,
            'false_positive': 0,
            'false_negative': 0,
            'true_negative': 0,
            'recall': '0.00%',
            'precision': '0.00%',
            'f1_score': '0.00%',
            'details': []
        }
        case_log["test_flag_stats"] = test_flag_stats

        # 记录未匹配的项目
        for pred_idx, pred_item in enumerate(predicted_items):
            if pred_idx not in matched_predicted_indices:
                case_log["unmatched_predicted_items"].append(pred_item)
                error_logs.append({
                    "case_index": i + 1,
                    "type": "Unmatched Predicted Item",
                    "item": pred_item
                })
        
        for exp_idx, exp_item in enumerate(expected_items_raw):
            if exp_idx not in matched_expected_indices:
                case_log["unmatched_ground_truth_items"].append(exp_item)
                error_logs.append({
                    "case_index": i + 1,
                    "type": "Unmatched Ground Truth Item",
                    "item": exp_item
                })
        
        # 累加总体的正确字段数和期望字段数
        overall_correct_field_count += case_correct_fields
        overall_expected_field_count += case_expected_fields
        overall_item_match_count += case_log["matched_item_count"]

        # 计算当前用例的字段准确率
        case_log["field_accuracy"] = case_correct_fields / case_expected_fields if case_expected_fields > 0 else 0
        evaluation_logs.append(case_log)

    # --- 汇总结果 ---
    # 总体统计（基于所有项目和字段的总计）
    overall_average_processing_time = 0.0  # 使用缓存，处理时间为0
    overall_item_accuracy = overall_item_match_count / overall_ground_truth_item_count if overall_ground_truth_item_count > 0 else 0.0
    overall_field_accuracy = overall_correct_field_count / overall_expected_field_count if overall_expected_field_count > 0 else 0.0
    
    # 按用例统计（每个用例的准确率取平均）
    case_processing_times = [0.0] * len(evaluation_logs)  # 使用缓存，处理时间为0
    case_field_accuracies = [log["field_accuracy"] for log in evaluation_logs]
    
    # 计算每个用例的项目准确率
    case_item_accuracies = []
    for log in evaluation_logs:
        if log["ground_truth_item_count"] > 0:
            case_item_accuracy = log["matched_item_count"] / log["ground_truth_item_count"]
        else:
            case_item_accuracy = 0.0
        case_item_accuracies.append(case_item_accuracy)
    
    # 计算各用例的平均值
    avg_case_processing_time = 0.0  # 使用缓存，处理时间为0
    avg_case_item_accuracy = sum(case_item_accuracies) / len(case_item_accuracies) if case_item_accuracies else 0.0
    avg_case_field_accuracy = sum(case_field_accuracies) / len(case_field_accuracies) if case_field_accuracies else 0.0

    # 计算异常符号识别的准确率
    abnormal_symbol_recall = 0.0
    if overall_abnormal_symbol_stats['ground_truth_count'] > 0:
        abnormal_symbol_recall = (overall_abnormal_symbol_stats['ground_truth_count'] - 
                                overall_abnormal_symbol_stats['missed_count']) / overall_abnormal_symbol_stats['ground_truth_count']
    
    abnormal_symbol_precision = 0.0
    if overall_abnormal_symbol_stats['predicted_count'] > 0:
        abnormal_symbol_precision = (overall_abnormal_symbol_stats['predicted_count'] - 
                                   overall_abnormal_symbol_stats['false_positive_count']) / overall_abnormal_symbol_stats['predicted_count']
    
    # 计算F1分数
    abnormal_symbol_f1 = 0.0
    if abnormal_symbol_precision + abnormal_symbol_recall > 0:
        abnormal_symbol_f1 = 2 * (abnormal_symbol_precision * abnormal_symbol_recall) / (abnormal_symbol_precision + abnormal_symbol_recall)

    # 计算优化的异常符号召回率
    optimized_abnormal_stats = calculate_abnormal_recall_optimized(evaluation_logs)

    # 计算总体test_flag统计
    overall_test_flag_stats = {
        'ground_truth_abnormal_count': 0,
        'predicted_abnormal_count': 0,
        'true_positive': 0,
        'false_positive': 0,
        'false_negative': 0,
        'true_negative': 0
    }

    for log in evaluation_logs:
        test_flag_stats = log.get('test_flag_stats', {})
        for key in overall_test_flag_stats:
            overall_test_flag_stats[key] += test_flag_stats.get(key, 0)

    # 计算总体test_flag性能指标
    total_positive = overall_test_flag_stats['true_positive'] + overall_test_flag_stats['false_negative']
    total_predicted_positive = overall_test_flag_stats['true_positive'] + overall_test_flag_stats['false_positive']

    test_flag_recall = overall_test_flag_stats['true_positive'] / total_positive if total_positive > 0 else 0.0
    test_flag_precision = overall_test_flag_stats['true_positive'] / total_predicted_positive if total_predicted_positive > 0 else 0.0
    test_flag_f1 = 2 * (test_flag_precision * test_flag_recall) / (test_flag_precision + test_flag_recall) if (test_flag_precision + test_flag_recall) > 0 else 0.0

    report = {
        "total_test_cases": total_test_cases,
        
        # 总体统计（基于所有数据的汇总）
        "overall_metrics": {
            "total_processing_time_seconds": f"{total_processing_time:.4f}",
            "average_processing_time_seconds": f"{overall_average_processing_time:.4f}",
            "item_accuracy": f"{overall_item_accuracy:.2%}",
            "field_accuracy": f"{overall_field_accuracy:.2%}",
            "total_matched_items": overall_item_match_count,
            "total_ground_truth_items": overall_ground_truth_item_count,
            "total_correct_fields": overall_correct_field_count,
            "total_expected_fields": overall_expected_field_count
        },
        
        # 异常符号统计
        "abnormal_symbol_metrics": {
            "ground_truth_count": overall_abnormal_symbol_stats['ground_truth_count'],
            "predicted_count": overall_abnormal_symbol_stats['predicted_count'],
            "missed_count": overall_abnormal_symbol_stats['missed_count'],
            "false_positive_count": overall_abnormal_symbol_stats['false_positive_count'],
            "recall": f"{abnormal_symbol_recall:.2%}",
            "precision": f"{abnormal_symbol_precision:.2%}",
            "f1_score": f"{abnormal_symbol_f1:.2%}"
        },

        # 优化的异常符号统计（排除无异常符号的用例）
        "optimized_abnormal_symbol_metrics": optimized_abnormal_stats,

        # 基于test_flag的异常检测统计
        "test_flag_based_metrics": {
            "ground_truth_abnormal_count": overall_test_flag_stats['ground_truth_abnormal_count'],
            "predicted_abnormal_count": overall_test_flag_stats['predicted_abnormal_count'],
            "true_positive": overall_test_flag_stats['true_positive'],
            "false_positive": overall_test_flag_stats['false_positive'],
            "false_negative": overall_test_flag_stats['false_negative'],
            "true_negative": overall_test_flag_stats['true_negative'],
            "recall": f"{test_flag_recall:.2%}",
            "precision": f"{test_flag_precision:.2%}",
            "f1_score": f"{test_flag_f1:.2%}"
        },
        
        # 按用例平均统计（每个用例作为独立单位计算平均）
        "case_average_metrics": {
            "average_processing_time_seconds": f"{avg_case_processing_time:.4f}",
            "average_item_accuracy": f"{avg_case_item_accuracy:.2%}",
            "average_field_accuracy": f"{avg_case_field_accuracy:.2%}",
            "processing_time_std": "0.0000",  # 使用缓存，处理时间为0
            "item_accuracy_std": f"{(sum([(a - avg_case_item_accuracy)**2 for a in case_item_accuracies]) / len(case_item_accuracies))**0.5 if case_item_accuracies else 0:.4f}",
            "field_accuracy_std": f"{(sum([(a - avg_case_field_accuracy)**2 for a in case_field_accuracies]) / len(case_field_accuracies))**0.5 if case_field_accuracies else 0:.4f}"
        },
        
        "evaluation_logs": evaluation_logs,
        "error_summary": {
            "total_errors": len(error_logs),
            "errors": error_logs
        }
    }
    
    return report


def print_evaluation_report(evaluation_report: Dict[str, Any]):
    """打印评测报告"""
    print("\n" + "="*80)
    print("评测报告")
    print("="*80)
    
    print(f"\n📊 基本信息")
    print(f"总测试用例数: {evaluation_report['total_test_cases']}")
    
    # 总体统计（基于所有数据汇总）
    overall = evaluation_report['overall_metrics']
    print(f"\n📈 总体性能指标（基于所有数据汇总）")
    print(f"├─ 总处理时间: {overall['total_processing_time_seconds']} 秒")
    print(f"├─ 平均处理时间: {overall['average_processing_time_seconds']} 秒")
    print(f"├─ 项目匹配准确率: {overall['item_accuracy']} ({overall['total_matched_items']}/{overall['total_ground_truth_items']})")
    print(f"└─ 字段准确率: {overall['field_accuracy']} ({overall['total_correct_fields']}/{overall['total_expected_fields']})")
    
    # 按用例平均统计
    case_avg = evaluation_report['case_average_metrics']
    print(f"\n🎯 用例平均性能指标（每个用例独立计算后取平均）")
    print(f"├─ 平均处理时间: {case_avg['average_processing_time_seconds']} ± {case_avg['processing_time_std']} 秒")
    print(f"├─ 平均项目准确率: {case_avg['average_item_accuracy']} ± {case_avg['item_accuracy_std']}")
    print(f"└─ 平均字段准确率: {case_avg['average_field_accuracy']} ± {case_avg['field_accuracy_std']}")
    
    # 异常符号统计报告
    abnormal = evaluation_report['abnormal_symbol_metrics']
    print(f"\n⚠️ 异常符号识别统计（基于abnormal_symbol字段）")
    print(f"├─ 答案中异常符号总数: {abnormal['ground_truth_count']}")
    print(f"├─ 识别结果中异常符号总数: {abnormal['predicted_count']}")
    print(f"├─ 漏检数量 (答案有但未识别): {abnormal['missed_count']}")
    print(f"├─ 误检数量 (答案无但识别有): {abnormal['false_positive_count']}")
    print(f"├─ 召回率 (Recall): {abnormal['recall']}")
    print(f"├─ 精确率 (Precision): {abnormal['precision']}")
    print(f"└─ F1分数: {abnormal['f1_score']}")

    # 优化的异常符号统计报告
    optimized_abnormal = evaluation_report.get('optimized_abnormal_symbol_metrics', {})
    if optimized_abnormal and optimized_abnormal.get('cases_with_abnormal_count', 0) > 0:
        print(f"\n⚠️ 优化的异常符号识别统计（排除无异常符号的用例）")
        print(f"├─ 包含异常符号的用例数: {optimized_abnormal['cases_with_abnormal_count']}")
        print(f"├─ 优化召回率 (Recall): {optimized_abnormal['optimized_recall']}")
        print(f"├─ 优化精确率 (Precision): {optimized_abnormal['optimized_precision']}")
        print(f"└─ 优化F1分数: {optimized_abnormal['optimized_f1']}")
    else:
        print(f"\n⚠️ 优化的异常符号识别统计: 无包含异常符号的用例")

    # 基于test_flag的异常检测统计报告
    test_flag_metrics = evaluation_report.get('test_flag_based_metrics', {})
    if test_flag_metrics:
        print(f"\n🔍 基于test_flag的异常检测统计（结合test_flag和abnormal_symbol）")
        print(f"├─ 答案中异常项目总数: {test_flag_metrics['ground_truth_abnormal_count']}")
        print(f"├─ 预测异常项目总数: {test_flag_metrics['predicted_abnormal_count']}")
        print(f"├─ 真阳性 (TP): {test_flag_metrics['true_positive']}")
        print(f"├─ 假阳性 (FP): {test_flag_metrics['false_positive']}")
        print(f"├─ 假阴性 (FN): {test_flag_metrics['false_negative']}")
        print(f"├─ 真阴性 (TN): {test_flag_metrics['true_negative']}")
        print(f"├─ 召回率 (Recall): {test_flag_metrics['recall']}")
        print(f"├─ 精确率 (Precision): {test_flag_metrics['precision']}")
        print(f"└─ F1分数: {test_flag_metrics['f1_score']}")

    print(f"\n🚨 错误统计")
    print(f"└─ 总错误数: {evaluation_report['error_summary']['total_errors']}")
    
    print("\n" + "="*80)
    
    print("\n--- 详细评测日志 ---")
    for log in evaluation_report['evaluation_logs']:
        # 计算当前用例的项目准确率
        case_item_accuracy = log['matched_item_count'] / log['ground_truth_item_count'] if log['ground_truth_item_count'] > 0 else 0.0
        
        print(f"\n--- 用例 {log['case_index']} ---")
        print(f"OCR文本预览: {log['ocr_text_preview']}")
        print(f"处理时间: {log['processing_time']:.4f} 秒")
        print(f"预测项目数: {log['predicted_item_count']}, 正确项目数: {log['ground_truth_item_count']}, 匹配项目数: {log['matched_item_count']}")
        print(f"项目准确率 (当前用例): {case_item_accuracy:.2%}")
        print(f"字段准确率 (当前用例): {log['field_accuracy']:.2%}")
        
        # 添加异常符号统计
        abnormal_stats = log.get('abnormal_symbol_stats', {})
        if abnormal_stats:
            # 计算召回率和精确率
            recall = 0.0
            if abnormal_stats.get('ground_truth_count', 0) > 0:
                recall = (abnormal_stats.get('ground_truth_count', 0) - abnormal_stats.get('missed_count', 0)) / abnormal_stats.get('ground_truth_count', 0)
            
            precision = 0.0
            if abnormal_stats.get('predicted_count', 0) > 0:
                precision = (abnormal_stats.get('predicted_count', 0) - abnormal_stats.get('false_positive_count', 0)) / abnormal_stats.get('predicted_count', 0)
            
            print(f"异常符号: 答案中{abnormal_stats.get('ground_truth_count', 0)}个, 识别到{abnormal_stats.get('predicted_count', 0)}个, "
                  f"漏检{abnormal_stats.get('missed_count', 0)}个, 误检{abnormal_stats.get('false_positive_count', 0)}个, "
                  f"召回率{recall:.2%}, 精确率{precision:.2%}")
            
            # 显示异常符号详情表格
            abnormal_details = abnormal_stats.get('details', [])
            if abnormal_details:
                # 只显示有异常符号的项目（答案或识别结果中至少有一个有异常符号）
                relevant_details = [d for d in abnormal_details if d.get('ground_truth_has_symbol') or d.get('predicted_has_symbol')]
                if relevant_details:
                    print("\n异常符号详情:")
                    headers = ["检查代码", "检查名称", "答案符号", "识别符号", "状态"]
                    table_data = []
                    for detail in relevant_details:
                        # 确定状态
                        status = "✓ 正确"
                        if detail.get('ground_truth_has_symbol') and not detail.get('predicted_has_symbol'):
                            status = "✗ 漏检"
                        elif not detail.get('ground_truth_has_symbol') and detail.get('predicted_has_symbol'):
                            status = "✗ 误检"
                        
                        table_data.append([
                            detail.get('test_code', ''),
                            detail.get('test_name', ''),
                            detail.get('ground_truth_symbol', ''),
                            detail.get('predicted_symbol', ''),
                            status
                        ])
                    print(tabulate(table_data, headers=headers, tablefmt="grid"))

        # test_flag统计详情
        test_flag_stats = log.get('test_flag_stats', {})
        if test_flag_stats and test_flag_stats.get('details'):
            print(f"\ntest_flag异常检测详情:")
            print(f"召回率: {test_flag_stats.get('recall', 'N/A')}, 精确率: {test_flag_stats.get('precision', 'N/A')}, F1: {test_flag_stats.get('f1_score', 'N/A')}")

            # 显示test_flag异常检测详情表格
            headers = ["检查代码", "检查名称", "答案异常", "预测异常", "test_flag", "分类"]
            table_data = []
            for detail in test_flag_stats['details'][:10]:  # 最多显示10个
                table_data.append([
                    detail.get('test_code', ''),
                    detail.get('test_name', '')[:15] + "..." if len(str(detail.get('test_name', ''))) > 15 else detail.get('test_name', ''),
                    "是" if detail.get('ground_truth_abnormal') else "否",
                    "是" if detail.get('predicted_abnormal') else "否",
                    detail.get('predicted_test_flag', 0),
                    detail.get('classification', '')
                ])
            print(tabulate(table_data, headers=headers, tablefmt="grid"))

            if len(test_flag_stats['details']) > 10:
                print(f"... 还有 {len(test_flag_stats['details']) - 10} 个项目")

        # 字段不匹配表格
        if log['field_mismatches']:
            print("\n字段不匹配详情:")
            headers = ["检查代码", "检查名称", "字段", "预测值", "期望值", "匹配类型"]
            table_data = []
            
            for mismatch in log['field_mismatches']:
                # 直接从mismatch中获取test_code和test_name
                test_code = mismatch.get('test_code', '未知')
                test_name = mismatch.get('test_name', '未知')
                
                table_data.append([
                    test_code,
                    test_name[:20] + "..." if len(str(test_name)) > 20 else test_name,
                    mismatch['field'],
                    mismatch['predicted'][:30] + "..." if len(str(mismatch['predicted'])) > 30 else mismatch['predicted'],
                    mismatch['expected'][:30] + "..." if len(str(mismatch['expected'])) > 30 else mismatch['expected'],
                    mismatch['match_type']
                ])
            print(tabulate(table_data, headers=headers, tablefmt="grid"))
        
        # 未匹配的预测项目表格
        if log['unmatched_predicted_items']:
            print(f"\n未匹配的预测项目 ({len(log['unmatched_predicted_items'])}):")
            headers = ["test_code", "test_name", "test_value", "test_unit"]
            table_data = []
            for item in log['unmatched_predicted_items']:
                table_data.append([
                    item.get('test_code', ''),
                    item.get('test_name', ''),
                    item.get('test_value', ''),
                    item.get('abnormal_symbol'),
                    item.get('test_unit', ''),
                    item.get('reference_value'),
                ])
            print(tabulate(table_data, headers=headers, tablefmt="grid"))

        # 未匹配的正确答案项目表格
        if log['unmatched_ground_truth_items']:
            print(f"\n未匹配的正确答案项目 ({len(log['unmatched_ground_truth_items'])}):")
            headers = ["test_code", "test_name", "test_value", "test_unit"]
            table_data = []
            for item in log['unmatched_ground_truth_items']:
                table_data.append([
                    item.get('test_code', ''),
                    item.get('test_name', ''),
                    item.get('test_value', ''),
                    # 异常符号
                    item.get('abnormal_symbol'),
                    item.get('test_unit', ''),
                    item.get('reference_value'),
                ])
            print(tabulate(table_data, headers=headers, tablefmt="grid"))

    print(f"\n--- 错误统计 ---")
    print(f"总错误数: {evaluation_report['error_summary']['total_errors']}")
    if evaluation_report['error_summary']['total_errors'] == 0:
        print("恭喜！没有发现任何错误。") 