"""
API触发的Airflow DAG (带详细参数)
-------------------
这个DAG可以通过Airflow REST API触发执行，并接收传递的参数用于Bash命令。
包含详细的配置参数和注释，特别强调参数传递和并发控制。

Features:
- 通过API触发而非定时调度
- 支持从API到任务的参数传递
- 钉钉通知成功/失败状态

Usage:
通过API触发:
airflow dags trigger subject_medical_file_mask --conf '{"task_id": "your_task_id"}'
"""

import os
import sys
sys.path.insert(0, '/app')

from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.bash import BashOperator
from airflow.utils.trigger_rule import TriggerRule
from airflow.utils.dates import days_ago

# 导入自定义通知工具
from common.dingtalk import airflow_notify_success_dingtalk, airflow_notify_failure_dingtalk

# 默认参数配置
default_args = {
    'owner': 'ai',                   # DAG所有者(便于组织管理)
    'depends_on_past': False,               # 是否依赖上一次执行结果
    'start_date': datetime(2025, 4, 5),     # DAG开始时间
    'email': ['<EMAIL>'],        # 失败通知邮箱(请更新为您的邮箱)
    'email_on_failure': False,               # 失败时发送邮件
    'email_on_retry': False,                # 重试时不发送邮件
    'retries': 1,                           # 失败后重试次数
    'retry_delay': timedelta(minutes=3),    # 重试间隔时间
    'execution_timeout': timedelta(hours=1),  # 任务执行超时时间
    'queue': 'default',                     # 任务队列
    'pool': 'default_pool',                 # 资源池
    'priority_weight': 10,                  # 任务优先级权重
    'end_date': None,                       # DAG停止调度时间
    'wait_for_downstream': False,           # 等待下游任务完成
}

# 创建DAG
dag = DAG(
    'external_masking_task',                 # DAG ID (唯一标识符)
    default_args=default_args,              # 应用默认参数
    description='带详细参数的API触发Bash任务，支持参数传递',
    schedule_interval=None,                 # 不设置调度，仅通过API触发
    catchup=False,                          # 不执行历史任务
    max_active_runs=4,                      # 最大同时运行的DAG实例数(适合API触发场景)
    max_active_tasks=10,                    # 所有DAG实例中的最大并发任务数
    tags=['api', 'trigger', 'parameterized'],  # UI中组织DAG的标签

    # 默认参数(可通过API覆盖)
    params={
        'task_id': None,                    # 从API传递的任务ID参数
    },

    # UI和文档设置
    orientation='TB',                       # UI中图表方向(TB: 上到下)
    default_view='graph',                   # UI中默认视图模式
    is_paused_upon_creation=False,          # 创建后不暂停
    doc_md=__doc__,                         # 使用模块文档字符串作为文档
)

# 定义主要任务，支持参数传递
process_task = BashOperator(
    task_id='process_task',                 # 任务ID
    cwd='/app',
    bash_command='python3.9 -m script.external_masking_task --task_id={{ params.task_id }}',
    on_success_callback=airflow_notify_success_dingtalk,  # 自定义成功通知
    on_failure_callback=airflow_notify_failure_dingtalk,  # 自定义失败通知
    dag=dag,
)

# 任务特定设置，覆盖默认值
# process_task.executor_config = {
#     'KubernetesExecutor': {
#         'request_memory': '2Gi',            # 内存请求(使用Kubernetes时)
#         'request_cpu': '1',                 # CPU请求(使用Kubernetes时)
#         'limit_memory': '4Gi',              # 内存限制(使用Kubernetes时)
#         'limit_cpu': '2',                   # CPU限制(使用Kubernetes时)
#     }
# }

# 工作流程中添加更多任务的示例
# prep_task = BashOperator(
#     task_id='prepare_data',
#     bash_command='python3.9 -m script.prepare_data --task_id={{ params.task_id }}',
#     on_success_callback=airflow_notify_success_dingtalk,
#     on_failure_callback=airflow_notify_failure_dingtalk,
#     dag=dag,
# )
#
# post_process_task = BashOperator(
#     task_id='post_processing',
#     bash_command='python3.9 -m script.post_process --task_id={{ params.task_id }}',
#     on_success_callback=airflow_notify_success_dingtalk,
#     on_failure_callback=airflow_notify_failure_dingtalk,
#     dag=dag,
# )
#
# prep_task >> process_task >> post_process_task

# CLI测试和调试
if __name__ == "__main__":
    dag.test()  # dag.cli()的现代替代方案
