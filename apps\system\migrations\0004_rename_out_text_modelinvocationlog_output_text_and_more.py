# Generated by Django 4.1.5 on 2025-05-15 16:27

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("system", "0003_rename_model_type_modelinvocationlog_model_name"),
    ]

    operations = [
        migrations.RenameField(
            model_name="modelinvocationlog",
            old_name="out_text",
            new_name="output_text",
        ),
        migrations.RemoveField(
            model_name="modelinvocationlog",
            name="subject_epoch",
        ),
        migrations.RemoveField(
            model_name="modelinvocationlog",
            name="subject_item",
        ),
        migrations.RemoveField(
            model_name="modelinvocationlog",
            name="subject_visit",
        ),
        migrations.AddField(
            model_name="modelinvocationlog",
            name="subject_epoch_id",
            field=models.CharField(
                blank=True, max_length=100, null=True, verbose_name="受试者阶段ID"
            ),
        ),
        migrations.AddField(
            model_name="modelinvocationlog",
            name="subject_item_id",
            field=models.<PERSON><PERSON><PERSON><PERSON>(
                blank=True, max_length=100, null=True, verbose_name="受试者操作项ID"
            ),
        ),
        migrations.AddField(
            model_name="modelinvocationlog",
            name="subject_visit_id",
            field=models.Char<PERSON>ield(
                blank=True, max_length=100, null=True, verbose_name="受试者访视ID"
            ),
        ),
    ]
