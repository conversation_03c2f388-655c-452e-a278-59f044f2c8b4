import os
import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smo.settings.test')
django.setup()

from apps.subject.models import SubjectEpoch
from django.db import transaction
import psycopg2


# 'postgresql://postgres:Qr%4077.88@**************:5432/oceanus_trial_clinplus'
pg_conn_params = {
    'host': '**************',
    'database': 'oceanus_trial_clinplus',
    'user': 'postgres',
    'password': 'Qr@77.88',
    'port': 5432
}

PAGE_SIZE = 100  # 每次查询的记录数，可以根据实际情况调整

with open('./script/test_project_no.txt') as f:
    PROJECT_NO_LIST = f.readlines()

PROJECT_NO_LIST = list(set([i.strip() for i in PROJECT_NO_LIST if i.strip()]))
print(PROJECT_NO_LIST)


def migrate_subject():
    # Connect to PostgreSQL
    pg_conn = psycopg2.connect(**pg_conn_params)
    pg_cursor = pg_conn.cursor()

    try:
        # Get total record count
        count_query = f"SELECT COUNT(*) FROM subject_epoch where project_id in (SELECT project_id FROM project where project_no in {tuple(PROJECT_NO_LIST)})"
        pg_cursor.execute(count_query)
        total_count = pg_cursor.fetchone()[0]

        # Calculate total pages
        total_pages = (total_count + PAGE_SIZE - 1) // PAGE_SIZE

        for page in range(total_pages):
            offset = page * PAGE_SIZE

            # Query to fetch subject_epoch data from PostgreSQL
            pg_query = f"""
            SELECT
                t1.id as source_id,
                t1.ext->>'epochId' as epoch_id,
                t1.status,
                dictconstantname('e888888', t1.status) as status_text,
                t1.label,
                t1.ext->>'epochSeq' as seq,
                t1.project_id,
                t1.project_site_id,
                t1.sub_id as subject_id
            FROM
                subject_epoch AS t1
            WHERE 
                project_id in (SELECT project_id FROM project where project_no in {tuple(PROJECT_NO_LIST)})
            LIMIT {PAGE_SIZE} OFFSET {offset}
            """
            pg_cursor.execute(pg_query)
            subject_data = pg_cursor.fetchall()

            # Get column names
            column_names = [desc[0] for desc in pg_cursor.description]

            # Process each row and insert/update to MySQL
            for row in subject_data:
                with transaction.atomic():
                    # Create a dictionary of subject_epoch data
                    subject_epoch_dict = dict(zip(column_names, row))
                    subject_epoch_dict['subject_epoch_id'] = subject_epoch_dict['source_id'] + '-' + subject_epoch_dict['subject_id']

                    try:
                        # Try to get the existing subject
                        subject_epoch = SubjectEpoch.objects.get(subject_id=subject_epoch_dict['subject_id'], source_id=subject_epoch_dict['source_id'])

                        # Update existing subject
                        for key, value in subject_epoch_dict.items():
                            setattr(subject_epoch, key, value)
                        subject_epoch.save()

                        print(f"Updated subject_epoch: {subject_epoch.subject_id}")

                    except SubjectEpoch.DoesNotExist:
                        # Create new subject
                        subject_epoch = SubjectEpoch.objects.create(**subject_epoch_dict)

                        print(f"Created new subject_epoch: {subject_epoch_dict['subject_id']}")

        print(f"Migration completed successfully. Processed {total_count} subject_epoch records.")

    except Exception as e:
        print(f"Error during migration: {str(e)}")
    finally:
        pg_cursor.close()
        pg_conn.close()


if __name__ == "__main__":
    # python -m script.sync_ot_subject_epoch
    migrate_subject()
