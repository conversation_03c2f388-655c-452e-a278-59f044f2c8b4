import os
import django
from django.utils import timezone
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smo.settings.test')
django.setup()

from apps.subject.models import SubjectEpoch
from django.db import transaction
import psycopg2

PAGE_SIZE = 10000

pg_conn_params = {
    'host': '**************',
    'database': 'oceanus_trial_clinplus',
    'user': 'postgres',
    'password': 'Qr@77.88',
    'port': 5432
}


def migrate_subject_epoch():
    pg_conn = psycopg2.connect(**pg_conn_params)
    pg_cursor = pg_conn.cursor()

    try:
        last_id = ''
        last_sub_id = ''
        last_sub_version = -1

        total_count = 0
        batch_num = 1
        # 用于跟踪本次同步处理的所有 subject_epoch_id
        processed_epoch_ids = set()

        while True:
            print(f"\n▶️ 正在拉取第 {batch_num} 批数据...")

            pg_query = """
                SELECT
                    t1.id as source_id,
                    t1.ext->>'epochId' as epoch_id,
                    t1.status,
                    dictconstantname('e888888', t1.status) as status_text,
                    t1.label,
                    t1.ext->>'epochSeq' as seq,
                    t1.project_id,
                    t1.project_site_id,
                    t1.sub_id as subject_id,
                    t1.sub_version
                FROM subject_epoch AS t1
                WHERE (t1.id, t1.sub_id, t1.sub_version) > (%s, %s, %s)
                ORDER BY t1.id, t1.sub_id, t1.sub_version
                LIMIT %s
            """
            pg_cursor.execute(pg_query, (last_id, last_sub_id, last_sub_version, PAGE_SIZE))
            rows = pg_cursor.fetchall()

            if not rows:
                break  # 没有更多数据了

            column_names = [desc[0] for desc in pg_cursor.description]
            subjects = []

            for row in rows:
                item = dict(zip(column_names, row))
                item['subject_epoch_id'] = f"{item['source_id']}-{item['subject_id']}"
                item['seq'] = int(item['seq']) if item['seq'] else item['seq']
                item.pop('sub_version')  # Django表里应该没有sub_version字段
                subjects.append(item)
                # 收集本次处理的所有 subject_epoch_id
                processed_epoch_ids.add(item['subject_epoch_id'])

            subject_epoch_ids = [i['subject_epoch_id'] for i in subjects]

            existing_subjects = SubjectEpoch.objects.filter(subject_epoch_id__in=subject_epoch_ids)
            existing_map = {s.subject_epoch_id: s for s in existing_subjects}

            to_update = []
            to_create = []

            for data in subjects:
                key = data['subject_epoch_id']
                if key in existing_map:
                    obj = existing_map[key]
                    changed = False
                    for field, value in data.items():
                        if getattr(obj, field, None) != value:
                            setattr(obj, field, value)
                            changed = True
                    if changed:
                        to_update.append(obj)
                else:
                    to_create.append(SubjectEpoch(**data))

            with transaction.atomic():
                if to_create:
                    SubjectEpoch.objects.bulk_create(to_create, batch_size=1000)
                    print(f"✅ 新增 {len(to_create)} 条")
                if to_update:
                    SubjectEpoch.objects.bulk_update(to_update, fields=data.keys(), batch_size=1000)
                    print(f"🔄 更新 {len(to_update)} 条")

            # 记录最后一条
            last_row = rows[-1]
            last_id = last_row[column_names.index('source_id')]
            last_sub_id = last_row[column_names.index('subject_id')]
            last_sub_version = last_row[column_names.index('sub_version')]

            total_count += len(rows)
            batch_num += 1
        # 软删除源系统中已删除的数据
        print("🔍 Checking for records to delete...")
        # 获取当前时间用于更新 update_time 字段
        current_time = timezone.now()

        # 分批处理删除操作，避免一次性加载大量数据到内存
        batch_size = 10000
        deleted_count = 0
        offset = 0

        while True:
            # 获取一批本地未删除的记录ID
            local_epoch_ids_batch = list(SubjectEpoch.objects.filter(
                delete_flag=0
            ).values_list('subject_epoch_id', flat=True)[offset:offset + batch_size])

            if not local_epoch_ids_batch:
                break

            # 找出这批记录中需要删除的（即在源系统中不存在的）
            to_delete_batch = [eid for eid in local_epoch_ids_batch if eid not in processed_epoch_ids]

            if to_delete_batch:
                # 将这些记录标记为已删除 (软删除) 并更新 update_time
                count = SubjectEpoch.objects.filter(subject_epoch_id__in=to_delete_batch).update(
                    delete_flag=1,
                    update_time=current_time
                )
                deleted_count += count
                print(f"🗑️  Soft deleted {count} records in current batch")

            offset += batch_size

        print(f"🗑️  Total soft deleted {deleted_count} records that no longer exist in source system")

        print(f"\n🎉 同步完成，共处理 {total_count} 条 subject_epoch 数据")

    except Exception as e:
        print(f"\n❌ 出错：{e}")
        import traceback
        traceback.print_exc()
    finally:
        pg_cursor.close()
        pg_conn.close()


if __name__ == "__main__":
    # python -m script.sync_ot_subject_epoch_all
    migrate_subject_epoch()
