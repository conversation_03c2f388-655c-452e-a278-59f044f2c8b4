# -------- 设置 Django 项目环境 --------
import os
import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smo.settings.test')
django.setup()
# -------------------------------------

from django.conf import settings
from django.core.cache import cache
from common.dingtalk import get_access_token


def refresh_access_token():
    """
    获取新的 access_token 并更新到 Django 缓存
    """
    dingtalk_access_token = get_access_token(settings.DINGTALK_APP_KEY, settings.DINGTALK_APP_SECRET)
    dingtalk_access_token = dingtalk_access_token['accessToken']

    # 设置缓存
    cache.set('dingtalk_access_token', dingtalk_access_token, timeout=60*120)  # 缓存120分钟
    print("Access token has been updated and cached.")


if __name__ == '__main__':
    # python -m script.refresh_access_token
    refresh_access_token()
