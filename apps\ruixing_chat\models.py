from django.db import models
from common.models import BaseFileModel, BaseModel


# Create your models here.
class ConversationFeedback(BaseModel):
    knowledge_base_name = models.CharField("知识库名称", max_length=100, blank=True, null=True)
    conversation_id = models.Char<PERSON>ield("对话ID", max_length=100)
    message_id = models.CharField("消息ID", max_length=100)
    question = models.TextField("对话问题")
    answer = models.TextField("对话答案")
    chat_name = models.CharField("应用名称", max_length=50, blank=True, null=True)
    realname = models.CharField("用户姓名", max_length=50, blank=True, null=True)
    username = models.CharField("用户工号", max_length=50, blank=True, null=True)
    feedback_type = models.CharField("反馈类型", max_length=50, blank=True, null=True)
    error_type = models.CharField("错误类型", max_length=100, blank=True, null=True)
    feedback_content = models.TextField("反馈的内容")
    submitted_time = models.DateTimeField("反馈提交时间", auto_now_add=True)
    review_status = models.CharField("审核状态", max_length=20, default="UNREVIEWED")
    reviewer_name = models.CharField("审核人姓名", max_length=50, blank=True, null=True)
    reviewer_id = models.CharField("审核人工号", max_length=50, blank=True, null=True)
    reviewed_time = models.DateTimeField("审核时间", blank=True, null=True)

    class Meta:
        verbose_name = "对话问题反馈"
        verbose_name_plural = "对话问题反馈"
        db_table = 'conversation_feedback'
