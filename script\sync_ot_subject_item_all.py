import os
import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smo.settings.test')
django.setup()

from apps.subject.models import SubjectItem
from django.db import transaction
import psycopg2

PAGE_SIZE = 10000

pg_conn_params = {
    'host': '**************',
    'database': 'oceanus_trial_clinplus',
    'user': 'postgres',
    'password': 'Qr@77.88',
    'port': 5432
}


def migrate_all_subject_items():
    pg_conn = psycopg2.connect(**pg_conn_params)
    pg_cursor = pg_conn.cursor()

    try:
        last_id = ''
        last_sub_id = ''
        last_sub_version = -1  # sub_version 是 int 类型

        total_count = 0
        batch_num = 1

        while True:
            print(f"\n▶️ 正在拉取第 {batch_num} 批数据...")

            pg_query = """
                SELECT
                    t1.id as source_id,
                    t1.visit->>'subVstId' as subject_visit_source_id,
                    t1.item->>'itemId' as item_id,
                    t1.status,
                    dictconstantname('e888888', t1.status) as status_text,
                    t1.item->>'itemLabel' as label,
                    t1.seq,
                    t1.project_id,
                    t1.project_site_id,
                    t1.sub_id as subject_id,
                    t1.sub_version
                FROM subject_item AS t1
                WHERE
                    (t1.id, t1.sub_id, t1.sub_version) > (%s, %s, %s)
                ORDER BY t1.id, t1.sub_id, t1.sub_version
                LIMIT %s
            """
            pg_cursor.execute(pg_query, (last_id, last_sub_id, last_sub_version, PAGE_SIZE))
            rows = pg_cursor.fetchall()

            if not rows:
                break  # 没有更多数据了

            column_names = [desc[0] for desc in pg_cursor.description]
            subject_item_dicts = []

            for row in rows:
                item = dict(zip(column_names, row))
                item['subject_item_id'] = item['source_id'] + '-' + item['subject_id']
                item['subject_visit_id'] = item['subject_visit_source_id'] + '-' + item['subject_id']
                item.pop('subject_visit_source_id')
                item.pop('sub_version')  # Django表里应该没有sub_version字段
                subject_item_dicts.append(item)

            subject_item_ids = [i['subject_item_id'] for i in subject_item_dicts]

            existing_items = SubjectItem.objects.filter(subject_item_id__in=subject_item_ids)
            existing_map = {item.subject_item_id: item for item in existing_items}

            to_update = []
            to_create = []

            for data in subject_item_dicts:
                key = data['subject_item_id']
                if key in existing_map:
                    item = existing_map[key]
                    changed = False
                    for field, value in data.items():
                        if getattr(item, field, None) != value:
                            setattr(item, field, value)
                            changed = True
                    if changed:
                        to_update.append(item)
                else:
                    to_create.append(SubjectItem(**data))

            with transaction.atomic():
                if to_update:
                    SubjectItem.objects.bulk_update(to_update, fields=data.keys(), batch_size=1000)
                if to_create:
                    SubjectItem.objects.bulk_create(to_create, batch_size=1000)

            print(f"✅ 第 {batch_num} 批：更新 {len(to_update)} 条，新增 {len(to_create)} 条")

            # 关键：记录最后一条
            last_row = rows[-1]
            last_id = last_row[column_names.index('source_id')]
            last_sub_id = last_row[column_names.index('subject_id')]
            last_sub_version = last_row[column_names.index('sub_version')]

            total_count += len(rows)
            batch_num += 1

        print(f"\n🎉 同步完成，共处理 {total_count} 条 subject_item 数据")

    except Exception as e:
        print(f"\n❌ 出错：{e}")
        import traceback
        traceback.print_exc()
    finally:
        pg_cursor.close()
        pg_conn.close()


if __name__ == "__main__":
    # python -m script.sync_ot_subject_item_all
    migrate_all_subject_items()
