import io
import os
import re
import uuid
import base64
import json
import math
import cv2
import time
import ast
import subprocess
import math
import platform
from typing import Union
from pathlib import Path
import requests
import fitz  # PyMuPDF库用于处理PDF
import pillow_heif
import pandas as pd
from PIL import Image
from openpyxl import load_workbook
from openpyxl.utils import get_column_letter
from cv2.typing import MatLike
import numpy as np
from PIL import Image, ImageDraw


sponsor_list = ['上海劢司达医疗科技有限公司', '热景（廊坊）生物技术有限公司', '杭州深睿智能医疗科技有限公司', '艾昆纬医药科技(上海)有限公司', '深圳君圣泰生物技术有限公司', '诺一迈尔（山东）医学科技有限公司', '金瑞凯利生物科技有限公司', 'Medelis Inc', '北京东方运嘉药业有限公司', '鹏瓴医疗科技（杭州）有限公司', '北京五和大诚医药科技有限公司', '北京易思迈科技有限责任公司', '北京医方科技发展有限公司', '江西宝承医药有限公司', '上海正大天晴医药科技开发有限公司', '深圳鞍石生物科技有限公司', 'CTI-Clinical Trial Services, Inc.,', '南宁米度医药技术有限公司', '丽珠集团新北江制药股份有限公司', '杭州圣域生物医药科技有限公司', '广东东阳光', '博致生物科技（上海）有限公司', '浙江国际招投标有限公司', '上海汉科生物有限公司', '科望（苏州）生物医药科技有限公司', '北京茵诺医药科技有限公司', '英诺欧奇生物医药（苏州）有限公司', '昆泰企业管理（上海）有限公司', '长沙科乐维医药科技有限公司', '上海迪赛诺医药集团股份有限公司', '湖南南新制药股份有限公司', 'AO Clincal Investigation and Documentation', '广州泳利康医疗咨询服务有限公司', '九芝堂股份有限公司', '上海潓美医疗科技有限公司', '聚协昌（北京）药业有限公司', '北京卓越天使医药科技发展有限公司', 'Arla Foods Amba', 'BeiGene,Ltd.', 'BeiGene，Ltd.', '南京优科制药有限公司', '南昌大学第二附属医院', '卫材（中国）药业有限公司上海分公司', '厦门大学', '吉林大学第二医院', '嘉兴安谛康生物科技有限公司', '嘉兴锐奇服饰有限公司', '四川大学华西医院（四川省国际医院）', '四川自豪时代药业有限公司', '复旦大学附属妇产科医院', '大鹏药品信息咨询（北京）有限公司', '天津世纪天龙药业有限公司', '天津君安生物制药有限公司', '天津市海科医药技术开发中心', '天津市阳权医疗器械有限公司', '奥索假肢矫形康复器材（上海）有限公司', '安进生物医药研发（上海）有限公司', '山东博士伦福瑞达制药有限公司', '山东吉威医疗制品有限公司', '山东大学第二医院', '山东大学齐鲁医院', '山东新华医疗器械股份有限公司', '山东省千佛山医院', '山东第一医科大学附属肿瘤医院（山东省肿瘤防治研究院、山东省肿瘤医院）', '山东第一医科大学附属肿瘤医院（山东省肿瘤防治研究院、山东省肿瘤医院）', '山西医科大学第一医院', '山西振东制药股份有限公司', '常熟市如保医疗器械销售有限公司', '常熟雷允上制药有限公司', '广东东阳光药业有限公司', '广东天普生化医药股份有限公司', '广州医科大学附属市八医院（广州市第八人民医院、广州市肝病医院、广州市传染病研究所）', '广州汉光药业股份有限公司', '康辰医药股份有限公司', '强生（苏州）医疗器材有限公司', '德州金约应医疗器械有限公司', '成都康弘生物科技有限公司', '捷古斯（上海）婴幼儿用品有限公司', '新基医药咨询（上海）有限公司', '新奇康药业股份有限公司', '昆翎（天津）医药发展有限公司', '晖致制药（大连）有限公司', '杭州养生堂生物医药有限公司', '杭州百济神州生物科技有限公司', '杭州默沙东制药有限公司', '楷图（上海）商贸有限公司', '武汉光谷人福生物医药有限公司', '江苏康禾生物制药有限公司', '江苏康缘药业股份有限公司', '江苏铼泰医药生物技术有限公司', '江西青峰药业有限公司上海分公司', '泰州新生源生物医药有限公司', '济川药业集团有限公司', '浙江大学', '浙江大学医学院附属儿童医院（浙江省儿童医院、浙江省儿童保健院）', '浙江大学医学院附属邵逸夫医院（浙江省邵逸夫医院）', '深圳健安医药有限公司', '深圳市东阳农牧有限公司', '深圳市北科生物科技有限公司', '深圳市和路元电子有限公司', '深圳市富邦瑞博科技有限公司', '深圳市贝斯达医疗股份有限公司', '深圳福诺生物技术有限公司', '湖南洞庭药业股份有限公司', '烟台普罗吉生物科技发展有限公司', '爱尔康（中国）眼科产品有限公司', '爱恩康临床医学研究（北京）有限公司', '王氏港建科技设备（深圳）有限公司', '珠海通桥医疗科技有限公司', '瑞博奥（广州）生物科技股份有限公司', '瑞士雅培制药有限公司上海代表处', '甘肃泰康制药有限责任公司', '百互润贸易（上海）有限公司', '百时美施贵宝（上海）贸易有限公司', '百济神州（苏州）生物科技有限公司', '礼来苏州制药有限公司上海分公司', '第一三共（中国）投资有限公司', '绿谷（上海）医药科技有限公司', '罗氏研发（中国）有限公司', '美国天龙医疗咨询责任有限公司开封代表处', '美赞臣婴幼儿营养品研发中心（中国）有限公司', '艾威药业（珠海横琴）有限公司', '艾威药业（珠海横琴）有限公司', '苏州大学附属第一医院', '苏州旺山旺水生物医药有限公司', '苏州碧迪医疗器械有限公司', '苏州科林利康医药科技有限公司', '蓬莱诺康药业有限公司', '西安新通药物研究股份有限公司', '西比曼生物科技（上海）有限公司', '语坤（北京）网络科技有限公司', '诺纳生物（苏州）有限公司', '谱高医疗科技（南京）有限公司', '贝朗医疗（苏州）有限公司', '贵州心意药业有限责任公司', '贵州泰邦生物制品有限公司', '贵州麦迪逊健康咨询有限公司', '赣南医科大学第一附属医院', '赫格雷（大连）制药有限公司', '辉凌制药（中国）有限公司', '辉瑞国际贸易（上海）有限公司', '辛迪思（苏州）医疗器械有限公司', '辽宁海一制药有限公司', '辽宁海思科制药有限公司', '辽宁省人民医院', '辽宁省肿瘤医院', '通用电气医疗系统（中国）有限公司', '重庆医科大学附属第二医院', '重庆赛英思医疗器械股份有限公司', '锐珂（上海）医疗器材有限公司', '锦州九泰药业有限责任公司', '雅培贸易（上海）有限公司', '青岛黄海制药有限责任公司', '首都医科大学附属北京儿童医院', '驻马店市中心医院', '默克雪兰诺有限公司', '默克雪兰诺（北京）医药研发有限公司', '齐腾医药科技咨询（上海）有限公司', '上海盛迪医药有限公司', '上海医药集团生物治疗技术有限公司', '中国医学科学院阜外医院国家临床研究中心', '无锡智胜医疗科技有限公司', '杭州养生堂生物医药有限公司', '四川卓蓝生物科技有限公司', '心镜云影医疗科技（枣庄）有限责任公司', '江苏中新医药有限公司', '嘉兴匠鑫医疗科技有限公司', '欣可丽美学（上海）医疗科技有限公司', '天津星魅生物科技有限公司', '武汉大学中南医院', '国重医疗科技（重庆）有限公司', '深圳市珈钰生物科技有限公司', '引正基因', '成都简则医药科技有限公司', '深圳瑞邦基因科技有限公司', '通桥医疗科技（苏州）有限公司', '安徽赛德盛医药科技股份有限公司', '浙江大学医学院附属邵逸夫医院', '首都医科大学附属北京佑安医院', '烟台德胜海洋生物科技有限公司', '成都第一制药有限公司', '北京哲源科技有限责任公司', '广州瑞领医药有限公司', '安徽赛德盛医药科技股份有限公司', '医麦洛（上海）医药科技有限公司', '上海问云生物科技有限公司', '合肥亿帆生物医药有限公司', '蕊心（赣州）医药科技开发有限公司', '南通本草八达医药科技香港有限公司', '深圳市健心医疗科技有限公司', '通化济达医药有限公司', '辽宁海思科制药有限公司', '杭州药成医药科技有限公司', '上海复星医药（集团）股份有限公司', '000823000001', '000837000001', 'SBF0000197', 'SBF0000288', 'SBF0000673', '湖南麦济生物技术股份有限公司', '牡丹江友搏药业有限责任公司', '上海鹏冠生物医药科技有限公司', '广州诺诚健华医药科技有限公司', '领泰生物医药（绍兴）有限公司', '北京大学医学部', '先声药业集团有限公司', '北京普瑞斯恩医疗科技有限公司', '上海宇道生物技术有限公司', '北京赛德盛医药科技股份有限公司', '南京康友医疗科技有限公司', '康方汇科（上海）生物有限公司', '万邦德制药集团有限公司', '重庆恒誉康医药科技有限公司', '重庆恒誉康医药科技', '箕星药业科技（上海）有限公司', '常州朗合医疗器械有限公司', '星源徳生物医药（上海）有限公司', '杭州派金生物医药科技有限公司', '纽欧申医药(上海)有限公司', '上海联迈德医疗科技发展有限公司', '深圳市贝斯达医疗股份有限公司', '上海赛尔欣生物医疗科技有限公司', '北京引正基因科技有限公司', '成都优赛诺生物科技有限公司', '深圳欧科健生物医药科技有限公司', '祐儿医药科技(上海)有限公司', '东方国际招标有限责任公司', '华润守正招标有限公司', '江苏研码科技有限公司', '北京肿瘤医院', '上海爱医舟医疗科技有限公司', '武汉科泰生物技术有限公司', '上海微创微航机器人有限公司', '维泰医疗科技（常州）有限公司', '合肥阿法纳安科生物科技有限公司', '苏州和淳医疗科技有限公司', '广州汉科生物有限公司', '广州美斯医药科技有限公司', '广州瑞麟医药科技发展有限公司', '广州凯卓医药科技有限公司', '广州青云医疗科技有限公司', '南京阿尔法医学有限公司', '南京瑟维思医药科技有限公司', '南京方腾医药技术有限公司', '重庆康领医药科技有限公司', '杭州盾恩医学检验实验室有限公司', '合肥易临医药科技有限公司', '合肥市康腾医学科技有限公司', '沈阳欣诺医药信息咨询有限公司', '河北彬高医药科技有限公司', '诚羿医药科技（山东）有限公司', '山东嘉元医院科技有限公司', '山东德远宽谷医药科技有限公司', '南昌临研通医药科技有限公司', '吉林蕊康医药科技有限公司', '长春心研医药科技有限公司', '临研通（无锡）科技有限公司', '无锡西斯莱克医学科技有限公司', '哈尔滨桥天医药科技有限公司', '意志（哈尔滨）医药科技有限公司', '山东易方科腾医药科技有限公司', '长沙先领医药科技有限公司', '睿玟（北京）医药科技有限公司', '北京玄弧医药科技有限公司', '北京新唯医药科技有限公司', '北京天誉远医学技术发展有限公司', '北京卓越天使医药科技发展有限公司', '北京紫冬科技有限公司', '北京助研医学技术有限公司', '北京中南创盟医药科技有限公司', '北京医佳润医药科技有限公司', '北京首辅医学研究有限公司', '北京柯林泰科医疗科技有限公司', '北京九州知达科技发展有限公司', '北京华腾慧科医药科技有限公司', '北京善芃科技发展有限公司', 'Sigma-Tau', 'SillaJen', 'Steri-Pharma', 'Sunovion', 'Temmler', 'Transgene', 'Vanton Research Laboratory', 'ViiV Healthcare', 'Wasserburger', 'Zambon', '北京万泰生物药业股份有限公司', '万特制药（海南）有限公司', '万邦德制药集团股份有限公司', '广东双林生物制药有限公司', '北京三元基因药业股份有限公司', '广州汉科生物有限公司', '三金集团湖南三金制药有限责任公司', '上海万兴生物制药有限公司', '上海万盛生物科技有限公司', '上海中科生龙达生物技术（集团）有限公司', '上海乐昱生物科技有限公司', '上海仑胜医药科技有限公司', 'R.P.Scherer', '上海仲时药业有限公司', '上海倍而达药业有限公司', '上海再新医药科技有限公司', '上海凯宝药业股份有限公司', '上海凯茂生物医药有限公司', '上海创诺制药有限公司', '上海医药集团股份有限公司', '上海华奥泰生物药业股份有限公司', '上海华智天然药物科技发展有限公司', '上海华汇拓医药科技有限公司', '上海博唯生物科技有限公司', '上海厚森医药科技有限公司', '上海双基药业有限公司', '上海和臣医药工程有限公司', '上海嘉坦医药科技有限公司', '上海国创医药有限公司', '上海上药交联医药科技有限公司', '上海复宏汉霖生物技术股份有限公司', '上海复旦复华药业有限公司', '上海奥测医疗科技有限公司', '上海复星医药（集团）股份有限公司', '上海天伟生物制药有限公司', '上海天赐福生物工程有限公司', '上海天龙药业有限公司', '上海奥科达生物医药科技有限公司', '上海安必生制药技术有限公司', '上海宣泰医药科技有限公司', '重庆求善医疗器械有限公司', '上海延藜生物技术有限公司', '上海强邦生物科技有限公司', '上海惠盾生物技术有限公司', '上海捌加壹医药科技有限公司', '上海新华联制药有限公司', '上海新生源医药集团有限公司', '上海新黄河制药有限公司', '上海方予健康医药科技有限公司', '上海日新医药发展有限公司', '天津凯芾医药科技有限公司', '天津康鼎科技有限公司', '苏州普蒂德生物医药科技有限公司', '四川科盟医创医学研究有限公司', '成都锦苓医药有限公司', '成都辘轳互联网医院有限公司', '山西云真医药科技有限公司', '碳基慧联（深圳）科技有限公司', '台州睿思医药科技有限公司', '广西安岐医药科技有限公司', '厦门科态医药科技有限公司', '锦州晟恩医药科技有限公司', '北京智医通科技有限公司', '四川西部医药技术转移中心', '恒瑞医药', '北京斯特睿格医药技术有限责任公司', '温州布肯迭医药科技有限责任公司', '安进公司', '江苏万高药业', 'Test', '阅微基因', '四川赛卓药业', '3M医疗有限公司（美国）', 'AB科学', '活性生物科技AB', '戴芬', '伊士卡', '阿吉尔', '阿尔法韦士曼制药公司', '阿尔尼拉姆制药公司', '阿拉贡制药公司', '阿瑞娜生物制药公司', '亚洲狮制药公司', 'Ber.', 'BHRPharma', 'Biovail', 'Blueprint Medicines Corporation', 'CELLTRION', 'Celsion Corporation', 'Creapharm', 'EgisPharmaceuticalsPlc', 'FertinPharma', 'Fisher Clinical Services', 'Five Prime Therapeutics, Inc.', 'Godecke', 'GP-PHARM', 'hameln', 'HBP', '熙德隆制药', '久光製薬', '輝瑞大藥廠', '爱必妥', 'Industrias Farmacéuticas Almirall', 'JSCGrindeks', 'Jubilant', 'Karyopharm Therapeutics', 'KRKA', 'Leading BioSciences', 'Linical Accelovance Chi.', 'M W Encap Limited', '梅恩制药集团有限公司', 'Medice', 'Mochida Pharmaceutical', 'NERPHARMA', 'Oramed', 'Pharma Stulln GmbH', 'Pharmacosmos', 'Plexxikon', 'PTC Therapeutics', '上海日馨生物科技有限公司', '上海旭东海普药业有限公司', '上海昊海生物科技', '上海明聚生物科技有限公司', '上海复星星泰医药科技有限公司', '上海朝晖药业有限公司', '上海歌佰德生物技术有限公司', '上海汇伦江苏药业', '上海汉龙医药科技有限公司', '上海沪丰生物科技有限公司', '上海泽生科技开发股份有限公司', '上海津曼特生物科技有限公司', '上海海天医药科技', '上海海抗中医药科技发展有限公司', '上海海欣生物技术有限公司', '上海添年堂健康科技有限公司', '上海清松制药有限公司', '上海爱博医药科技有限公司', '上海特化医药科技有限公司', '上海玉瑞生物科技（安阳）药业有限公司', '上海现代制药股份有限公司', '上海璎黎药业有限公司', '上海百迈博制药有限公司', '上海益生源药业有限公司', '上海睿星基因技术有限公司', '上海福达制药有限公司', '上海科州药物研发有限公司', '上海美烨生物科技', '上海美雅珂生物技术有限责任公司', '上海翰森生物医药科技有限公司', '上海艾力斯医药科技股份有限公司', '上海荣盛生物药业有限公司', '上海药谷药业有限公司', '上海莱士血液制品股份有限公司', '上海赛伦生物技术股份有限公司', '上海迈泰亚博生物技术有限公司', '上海迪诺医药科技有限公司', '上海青平药业有限公司', '上海驺虞医药科技有限公司', '上海高科联合生物技术研发有限公司', '世贸天阶制药（江苏）有限责任公司', '丘以思（上海）医药信息咨询有限公司', '东丽', '东亚制药', '东光药品工业', '东北制药集团股份有限公司', '重庆求善医疗器械有限公司', '东莞宝丽健生物工程研究开发有限公司', '东莞市金美济药业有限公司', '东莞达信生物技术有限公司', '烟台东诚药业集团股份有限公司', '中国化学制药股份有限公司新丰工厂', '中国药材集团', '中国药科大学制药有限公司', '中外制药', '中孚药业股份有限公司（曾用名：山东潍坊制药厂有限公司）', '中山市恒生药业', '中山康方生物医药有限公司', '中帅医药', '中美天津史克制药有限公司', '中美福源', '临沂山松药业有限公司', '丹诺医药', '丽珠医药集团股份有限公司', '乌鲁木齐家和医药生物有限公司', '乐普（北京）医疗器械股份有限公司', '乐普药业', '云南希陶绿色药业', '云南生物谷创新药物', '云南省玉溪市维和制药', '云南绿野生物医药', '云南龙海天然植物药业', '云屹药业（上海）有限公司', '云济华美药业（北京）有限公司', '亚太药业', '亚宝药业集团股份有限公司', '交晨生物医药', '人福药业', '亿腾医药', '仁和药业', '仙琚制药', '仟源医药', '优时比制药', '优生制药厂', '优胜美特制药有限公司', '佐力药业', '佛山安普泽', '佛山德芮可制药', '佛山手心制药有限公司', '佛山瑞迪奥医药有限公司', '佛慈制药', '依格斯', '保定天浩制药有限公司', '保灵药业', '信东生技', '信泰制药', '信谊', '信达生物制药（苏州）有限公司', '健民药业集团股份有限公司', '元森肽德生物医药科技（天津）有限公司', '兆科药业（合肥）有限公司', '江苏先声药业有限公司', '兰州和盛堂制药股份有限公司', '兰索斯医学影像公司', '兴安药业有限公司', '元和药业股份有限公司', '内蒙古双奇药业股份有限公司', '内蒙古奥特奇蒙药股份有限公司', '内蒙古蒙药股份有限公司', '军事医学科学院毒物药物研究所', '利奥制药', '前沿生物药业(南京)股份有限公司', '力品药业（厦门）有限公司', '天津力生制药股份有限公司', '勃林格殷格翰', '包头中药有限责任公司', '北京万生药业有限责任公司', '北京三有利和泽生物科技有限公司', '北京世佳九生源药业科技有限公司', '北京世博金都医药科技发展有限公司', '北京世桥生物制药有限公司', '华北制药股份有限公司', '北京世纪康医药科技开发有限公司', '北京世贸东瑞医药科技有限公司', '北京东旭利医药科技有限公司', '北京中惠药业有限公司', '北京神州细胞生物技术集团股份公司', '北京九九方元保健品经销有限公司', '北京五和博澳药业有限公司', '北京京卫信康医药科技发展有限公司', '北京京卫燕康药物研究所有限公司', '北京京师维康医药科技有限公司', '北京伟德杰生物科技有限公司', '北京佳诚医药有限公司', '北京修正创新药物研究院有限公司', '北京健达康新药开发有限公司', '北京六盛合医药科技有限公司', '北京兴大医药研究有限责任公司', '北京凌翰生物医药科技有限公司', '北京凯因科技股份有限公司', '北京凯奕医药科技发展有限公司', '北京凯晋科技有限公司', '爱恩康临床医学研究（北京）有限公司', '北京凯莱天成医药科技有限公司', '北京北大维信生物科技有限公司', '北京华世天富生物医药科技有限公司', '北京华夏医胜创新科技有限责任公司', '北京华昊中天生物技术有限公司', '北京华睿鼎信科技有限公司', '北京华禧联合科技发展有限公司', '北京协和药厂', '北京博之音科技有限公司', '北京博士理医药技术有限公司', '北京博康健基因科技有限公司', '北京同源医药科技有限公司', '北京安康嘉和医药有限公司', '北京嘉事联博医药科技有限公司', '北京嘉林药业股份有限公司', '北京四环生物制药有限公司', '北京盈科瑞生物医药研究有限公司', '北京国仁堂医药科技发展有限公司', '北京国联诚辉医药技术有限公司', '北京圣永制药有限公司', '北京大安生物技术有限公司', '北京天坛生物制品股份有限公司', '北京天诚医药科技有限公司', '北京太洋药业股份有限公司', '北京奥新生物科技有限公司', '北京安百胜生物科技有限公司', '北京宝泰宁堂医药科技有限公司', '北京富龙康泰生物技术有限公司', '北京山青医药技术有限公司', '北京市丰硕维康技术开发有限责任公司', '北京希而欧生物医药开发有限公司', '北京康乐卫士生物技术股份有限公司', '北京康利华咨询服务有限公司', '北京康茂峰科技有限公司', '北京康蒂尼药业有限公司', '北京康辰药业股份有限公司', '北京康钰垚生物科技', '北京强新生物科技有限公司', '北京德丰瑞生物技术有限公司', '北京德众万全医药科技有限公司', '北京思睦瑞科医药信息咨询有限公司', '北京恒瑞康达医药科技发展有限公司', '北京悦康凯悦制药有限公司', '北京悦康润泰国际商贸有限公司', '北京扬新科技有限公司', '北京新华联协和药业有限责任公司', '北京新领先医药科技发展有限公司', '北京旭泽医药科技有限公司', '北京春风药业有限公司', '北京普德康利医药科技发展有限公司', '北京林特医药科技有限公司', '北京民海生物科技有限公司', '北京永泰生物制品有限公司', '北京汇智泰康医药技术有限公司', '北京汇诚瑞祥医药技术有限公司', '北京汉典制药有限公司', '北京法马苏提克咨询有限公司', '北京洪天力药业有限公司', '北京浦润奥生物科技有限责任公司', '北京海德润医药集团有限公司', '北京海泰联合医药科技发展有限公司', '北京润德康医药技术有限公司', '北京澳合药物研究院有限公司', '北京爱康维健医药科技有限公司', '北京生物制品研究所有限责任公司', '北京百奥药业有限责任公司', '北京盈科瑞创新医药股份有限公司', '北京睿创康泰医药研究院有限公司', '北京元气知药科技有限公司', '北京祈福瑞草医药科技有限公司', '北京祥瑞生物制品有限公司', '北京福瑞康正医药技术研究所', '北京科兴生物制品有限公司', '北京精诚泰和医药信息咨询有限公司', '北京红太阳药业有限公司', '北京红惠新医药科技有限公司', '北京红林制药有限公司', '北京绿竹生物技术股份有限公司', '北京美福源生物医药科技有限公司', '北京美迪康信医药科技有限公司', '北京羚锐伟业科技有限公司', '北京莱瑞森医药科技有限公司', '北京诺东高科技发展有限责任公司', '北京诺维康医药科技有限公司', '北京诺诚健华医药科技有限公司', '北京贝力源医药科技有限公司（注销）', '北京赛德维康医药研究院', '北京赛林泰医药技术有限公司', '北京金本草中药科技发展有限公司', '北京阳光诺和药物研究有限公司', '北京马力喏生物科技有限公司', '北大医药股份有限公司', '北海康成（北京）医药科技有限公司', '华兰生物工程股份有限公司', '上海华拓医药科技发展有限公司', '华润三九医药股份有限公司', '华润双鹤药业股份有限公司', '华润昂德生物药业有限公司', '华润赛科药业有限责任公司', '费森尤斯卡比华瑞制药有限公司', '华裕（无锡）制药有限公司', '华辉安健（北京）生物科技有限公司', '华领医药技术（上海）有限公司', '南京一心和医药科技有限公司', '南京世豪医药科技有限公司', '中科健康产业集团股份有限公司', '南京亿华药业', '南京优科制药有限公司', '南京健友生化制药股份有限公司', '南京双科医药开发有限公司', '南京合祁医药科技有限公司', '南京圣和药业股份有限公司', '南京大东医药科技', '南京天印健华医药科技有限公司', '南京威斯特康生物技术有限公司', '南京宸翔医药研究', '南京崇原生物科技有限责任公司', '南京康海药业有限公司', '南京弘景医药科技有限公司', '南京恒生制药', '南京恒通医药', '南京振华医药', '南京斯康生物医药科技有限公司', '南京明生医药', '南京易亨制药有限公司', '南京正科制药有限公司', '南京海光应用化学研究所', '南京海纳医药科技股份有限公司', '南京海美生物医药', '南京海辰药业股份有限公司', '南京海陵中药制药工艺技术研究有限公司', '南京瑞天医药', '南京一如医药科技有限公司', '南京瑞年百思特制药有限公司', '南京瑞捷医药科技有限公司', '南京生命能', '南京绿叶制药有限公司', '南京臣功制药', '南京赛诺医药科技有限公司', '南京逐陆医药科技有限公司', '南京长澳医药科技有限公司', '南宁枫叶药业', '南岳生物制药有限公司', '南昌济顺制药', '南昌百济制药有限公司', '南昌立健药业', '南海朗肽制药', '南通久和药业', '南通华夏中医药', '河南利欣制药股份有限公司', '博仲盛景医药', '博济医药', '博瑞生物医药(苏州)股份有限公司', '博生医药', '博赏医药', '博雅生物', '卡南吉医药科技', '卫光生物', '卫材药业', '厦门万泰沧海生物技术有限公司', '厦门北大之路生物工程', '厦门市医药研究所', '厦门德乐勤生物技术有限公司', '厦门恩成制药有限公司', '厦门力卓药业有限公司', '参天制药(中国)有限公司', '双成药业', '双鹭药业', '台州波美乐', '台湾微脂体', '史达德', '合一生技股份有限公司', '合肥七星医药科技有限公司', '合肥久诺医药科技', '合肥兆峰科大药业', '合肥创新医药', '石家庄沃泰生物科技有限公司', '合肥永生制药', '合肥立方制药', '合肥经方医药科技', '合肥英太', '吉林一心制药', '吉林万通药业', '吉林亚泰生物药业', '吉林修正药业新药', '吉林华康药业股份有限公司', '吉林双药药业', '吉林吉尔吉药业有限公司', '吉林市吴太感康药业有限公司', '吉林康乃尔药业有限公司', '吉林敖东', '吉林显锋科技制药有限公司', '吉林津升制药有限公司', '吉林益民堂制药', '吉林省七星山药业', '吉林省力胜制药有限公司', '吉林省博大制药', '吉林省西点药业科技', '吉林草还丹药业', '吉林道君药业', '吉林金恒制药股份有限公司', '吉瑞大药厂', '同仁堂', '同溢堂药业有限公司', '同路生物制药', '上海君实生物医药科技股份有限公司', '和记黄埔', '哈尔滨怡康药业', '哈尔滨誉衡药业股份有限公司', '哈药集团三精明水药业有限公司', '嘉和生物药业', '四川制药制剂有限公司', '四川国为制药', '四川好医生药业', '四川思路迪药业', '四川新斯顿制药', '四川百利药业有限责任公司', '四川省宜宾五粮液宜宾制药', '四川省百草生物药业有限公司', '四川省长征药业', '四川禾亿制药有限公司', '杭州迪安生物技术有限公司', '四川科伦药业股份有限公司', '四川维奥制药', '四川远大蜀阳药业', '四环医药控股集团有限公司', '中国医药集团有限公司', '国药集团容生制药有限公司', '国药集团致君（深圳）制药有限公司', '国药集团致君（深圳）坪山制药有限公司', '培力（南宁）药业有限公司', '心亚生物科技股份有限公司', 'BeyondSpring Pharmaceuticals', '多多药业有限公司', '大连奇运生制药有限公司', '大连富生制药有限公司', '富祥（大连）制药有限公司', '大道隆达（北京）医药科技发展有限公司', '天士力医药集团股份有限公司', '贵州天安药业股份有限公司', '天方药业有限公司', '天津天安药业有限公司', '天津太平洋制药有限公司', '天津市中央药业有限公司', '天津医药集团津康制药有限公司', '天津怀仁制药有限公司', '天津生物化学制药有限公司', '天津金耀药业有限公司', '天长亿帆制药有限公司', '奥罗宾多制药公司', '威世药业（如皋）有限公司', '宁夏康亚药业股份有限公司', '宁波健卫药业有限公司', '宁波大红鹰药业股份有限公司', '安丘市鲁安药业有限责任公司', '安徽丰原药业股份有限公司', '安徽安科恒益药业有限公司', '安徽环球药业股份有限公司', '安徽省先锋制药有限公司', '安徽贝克生物制药有限公司', '安斯泰来制药（中国）有限公司', '宜昌东阳光长江药业股份有限公司', '寿光富康制药有限公司', '山东京卫制药有限公司', '山东凤凰制药股份有限公司', '山东华鲁制药有限公司', '山东司邦得制药有限公司', '山东威高药业股份有限公司', '山东孔府制药有限公司', '山东宏济堂制药集团股份有限公司', '山东新华制药股份有限公司', '山东步长制药股份有限公司', '山东步长神州制药有限公司', '山东海山药业有限公司', '山东特珐曼药业有限公司', '山东益康药业股份有限公司', '山东绿因药业有限公司', '山东裕欣药业有限公司', '山东达因海洋生物制药股份有限公司', '山西兰花药业股份有限公司', '山西华元医药生物技术有限公司', '山西同达药业有限公司', '山西振东安特生物制药有限公司', '山西宝泰药业有限责任公司', '山西康宝生物制品股份有限公司', '山西振东制药股份有限公司', '天圣制药集团山西有限公司', '巴克斯特国际公司', '帝国制药株式会社', '河北常山生化药业股份有限公司', '江苏佳尔科药业集团股份有限公司', '常州兰陵制药有限公司', '常州制药厂有限公司', '常州市盛辉药业有限公司', 'ReckittBenckiserHealthcare(UK)Ltd', '常州市阳光药业有限公司', '常州康普药业有限公司', '常州金远药业制造有限公司', '常熟雷允上制药有限公司', '广东一力集团制药股份有限公司', '广东三才石岐制药股份有限公司', '广东世信药业有限公司', '广东东阳光药业有限公司', '广东众生药业股份有限公司', '广东八加一医药有限公司', '广东华南药业集团有限公司', '广东博洲药业有限公司', '广东卫伦生物制药有限公司', '广东嘉博制药有限公司', '广东天普生化医药股份有限公司', '广东安诺药业股份有限公司', '广东岭南制药有限公司', '广东康大制药有限公司', '广东思济药业有限公司', '广东怡康制药有限公司', '广东恒健制药有限公司', '广东赛康制药厂有限公司', '广东隆赋药业股份有限公司', '广州一品红制药有限公司', '广州市赛普特医药科技股份有限公司', '广州市香雪制药股份有限公司', '广州朗圣药业有限公司', '广州柏赛罗药业有限公司', '广州王老吉药业股份有限公司', '广州白云山医药集团股份有限公司', '广州诺诚生物制品股份有限公司', '广西厚德大健康产业股份有限公司', '康哲医药研究（深圳）有限公司', '康宁杰瑞（吉林）生物科技有限公司', '康希诺生物股份公司', '开封制药（集团）有限公司', '彪马生物科技公司', '徐诺药业（南京）有限公司', '德国Haupt制药', '德国赫曼大药厂', '悦康药业集团股份有限公司', '惠氏制药有限公司', '意大利依赛特大药厂', '成都倍特药业有限公司', '成都利尔药业有限公司', '成都力思特制药股份有限公司', '成都圣诺生物制药有限公司', '北京海盛医疗器械有限公司', '成都恒瑞制药有限公司', '成都恩威药业有限公司', '成都生物制品研究所有限责任公司', '成都百裕制药股份有限公司', '成都盛迪医药有限公司', '成都诺迪康生物制药有限公司', '成都锦华药业有限责任公司', '浙江我武生物科技股份有限公司', '扬子江药业集团江苏紫龙药业有限公司', '扬州市三药制药有限公司', '上海新亚药业闵行有限公司', '新疆华世丹药业股份有限公司', '新疆德源生物工程有限公司', '新疆银朵兰维药股份有限公司', '昆翎（天津）医药发展有限公司', '施泰福大药厂', '施维雅（天津）制药有限公司', '无锡凯夫制药有限公司', '无锡嘉菱医药有限公司', '无锡曙辉药业有限公司', '日本BCG制造', '日本大冢制药', '日本新药株式会社', '旭化成制药', '昆山培力药品有限公司', '昆山龙灯瑞迪制药有限公司', '昆明中药厂有限公司', '昆明华润圣火药业有限公司', '昆明源瑞制药有限公司', '昆明积大制药股份有限公司', '昆明银诺医药技术有限公司', '昆药集团股份有限公司', '深圳明赛瑞霖药业有限公司', '北京星昊医药股份有限公司', '晋城海斯制药有限公司', '晟德大药厂股份有限公司', '香港普乐药业有限公司', '普洛药业股份有限公司', '湖南景峰医药股份有限公司', '重庆智飞生物制品股份有限公司', '本元正阳基因技术股份有限公司', '本溪恒康制药有限公司', '黄石李时珍药业集团武汉李时珍药业有限公司', 'BeyondSpring Pharmaceuticals,Inc.', '杨凌生物医药科技股份有限公司', '杭州九源基因工程有限公司', '杭州华东医药集团有限公司', '杭州多禧生物科技有限公司', '杭州天诚药业有限公司', '杭州奥默医药股份有限公司', '杭州容立医药科技有限公司', '杭州民生药业有限公司', '杭州澳亚生物技术有限公司', '杭州鸿运华宁生物医药工程有限公司', '北京杰华生物技术有限责任公司', '柯菲平医药', 'Rockwell Medica', '江苏正大清江制药', '株洲千金药业股份有限公司', '株洲康圣堂药业有限公司', '格兰泰', '重庆桐君阁大药房连锁有限责任公司', '梯瓦制药', '上海欣凯医药发展有限公司', '欧加农', '正大天晴药业集团股份有限公司', '正大青春宝药业有限公司', '武汉中原瑞德生物制品', '国药集团武汉中联四药药业有限公司', '武汉五景药业', '武汉先路医药科技有限公司', '武汉双龙药业有限公司', '武汉启瑞药业有限公司', '武汉大安制药有限公司', '武汉市华本汇医药发展有限公司', '武汉康裕医药科技有限公司', '武汉武药制药有限公司', '武汉爱民制药股份有限公司', 'Covance Inc.', '武汉钧安制药有限公司', '天津武田药品有限公司', '永信药品工业（昆山）股份有限公司', '天津金耀集团河北永光制药有限公司', '永昕生物醫藥股份有限公司', '湖南汉森制药股份有限公司', '汕头经济特区鮀滨制药厂', '汕头金石粉针剂有限公司', '江中药业股份有限公司', '江苏七0七天然制药', '江苏万川医疗健康产业集团有限公司', '江苏云阳集团药业有限公司', '江苏亚盛医药', '江苏亚虹医药科技', '江苏信孚药业', '湖北华世通生物医药科技有限公司', '江苏华泰晨光药业有限公司', '江苏华阳制药', '江苏吉贝尔药业股份有限公司', '江苏吴中医药', '江苏国丹生物制药有限公司', '江苏天士力帝益药业有限公司', '江苏太平洋美诺克生物药业', '江苏太瑞生诺生物医药科技有限公司', '江苏奥赛康药业股份有限公司', '江苏宏锦天药业', '江苏平光制药（焦作）有限公司', '江苏康宁杰瑞生物制药有限公司', '江苏康润生物科技有限公司', '江苏康禾', '江苏康缘药业股份有限公司', '江苏弘瑞创业', '江苏德源药业', '江苏恒瑞医药股份有限公司', '江苏恒谊药业', '江苏恩华药业股份有限公司', '江苏晨牌药业集团股份有限公司', '江苏普莱医药生物技术', '江苏杜瑞制药', '江苏正大丰海制药', '江苏润邦药业有限公司', '江苏瑞科生物技术有限公司', '江苏知原药业有限公司', '江苏神尔洋高', '江苏神龙药业', '江苏美通制药有限公司', '江苏艾迪药业', '江苏苏中药业集团股份有限公司', '北京博晖创新生物技术集团股份有限公司', '江苏华源誉康药业有限公司', '浙江诺泰生物药业有限公司', '江苏豪森药业股份有限公司', '江苏远恒药业', '江苏铼泰医药', '江苏长泰药业', '江苏灵豹药业股份有限公司', '江苏飞马药业有限公司', '江苏鹏鹞药业', '江苏黄河药业', '江西亿友药业有限公司', '江西制药有限责任公司', '江西心正药业', '江西捷众制药有限公司', '江西新赣江药业股份有限公司', '江西汇仁药业股份有限公司', '江西赣南海欣药业股份有限公司', '江西银涛药业有限公司', 'E.R.Squibb & Sons,L.L.C.', '沈阳兴齐眼药股份有限公司', '沈阳华泰药物研究有限公司', '沈阳圣元药业有限公司', '沈阳福宁药业有限公司', '沈阳红旗制药有限公司', '沈阳诺亚荣康生物制药技术有限责任公司', '河北中唐医药有限公司', '河北凯威制药有限责任公司', '河北创健药业有限公司', '河北医科大学制药厂', '河北大安制药有限公司', '河北山姆士药业有限公司', '河北菲尼斯生物技术有限公司', '河北龙海药业有限公司', '河南中帅医药科技股份有限公司', '河南中杰药业有限公司', '河南九势制药股份有限公司', '河南全宇制药股份有限公司', '河南普瑞制药有限公司', '郑州泰丰制药有限公司', '上海凯宝新谊（新乡）药业有限公司', '河南省洛正药业有限责任公司', '河南福森药业有限公司', '河南羚锐制药股份有限公司', '泰州亿佰康医药科技有限公司', '泰州亿腾景昂药业有限公司', '泰州医药城一方药物研发有限公司', '泰州厚德奥科科技有限公司', '泰州复旦张江药业', '泰州天德药业有限公司', '苏州闻泰医药科技有限公司', '泰州贝今生物技术有限公司', '北京泰德制药股份有限公司', '洛尔托福制药', '派格生物医药（苏州）有限公司', '山东康众宏医药科技开发有限公司', '山东百诺医药股份有限公司', '济南高华制药有限公司', '济宁华能制药厂有限公司', '成都奇璞生物科技有限公司', '浙江万晟药业有限公司', '浙江三叶草生物制药有限公司', '浙江中奇生物药业股份有限公司', '浙江丽水众益药业', '浙江亚克药业有限公司', '浙江京新药业股份有限公司', '浙江众益药业有限公司', '浙江医药股份有限公司', '浙江医药股份有限公司新昌制药厂', '浙江华海药业股份有限公司', '浙江华阳药业有限公司', '浙江南洋药业有限公司', '艾美卫信生物药业（浙江）有限公司', '浙江天宇药业股份有限公司', '浙江奥托康制药集团股份有限公司', '浙江奥翔药业股份有限公司', '浙江宏元药业股份有限公司', '浙江导明医药科技有限公司', '浙江尖峰药业有限公司', '浙江巨泰药业有限公司', '浙江康恩贝制药股份有限公司', '浙江得恩德制药股份有限公司', '浙江昂利康制药股份有限公司', '浙江普洛康裕制药有限公司', '浙江杭康药业有限公司', '浙江永宁药业股份有限公司', '浙江江北药业有限公司', '浙江浙北药业有限公司', '浙江海力生制药有限公司', '浙江海正药业股份有限公司', '浙江特瑞思药业股份有限公司', '浙江贝得药业有限公司', '浙江越甲药业有限公司', '浙江远力健药业有限责任公司', '浙江震元股份有限公司', '海南中化联合制药工业股份有限公司', '海南中玉药业有限公司', '海南亚洲制药股份有限公司', '海南伊顺药业有限公司', '海南华拓天涯制药有限公司', '海南卫康制药（潜山）有限公司', '海南合瑞制药股份有限公司', '海南国瑞制药有限公司', '海南大西洋制药厂有限公司', '海南惠普森医药生物技术有限公司', '海南日中天制药有限公司', '海南海神同洲制药有限公司', '海南灵康制药有限公司', '海南皇隆制药股份有限公司', '海南碧凯药业有限公司', '海南绿岛制药有限公司', '海南赞邦制药有限公司', '海南锦瑞制药有限公司', '海南长安国际制药有限公司', '海口奇力制药股份有限公司', '上海海和药物研究开发有限公司', '海思科医药集团股份有限公司', '深圳市海王生物工程股份有限公司', '海达舍画阁药业有限公司', '润和生物医药科技（汕头）有限公司', '涿州东乐制药有限公司', '淄博万杰制药有限公司', '深圳万乐药业有限公司', '深圳信立泰药业股份有限公司', '苏州恒瑞医疗器械有限公司', '深圳太太药业有限公司', '深圳奥萨制药有限公司', '深圳市中兴扬帆生物工程有限公司', '深圳市中核海得威生物科技有限公司', '深圳市北科联药业科技有限公司', '深圳市思沃生命科技有限公司', '深圳市泛谷药业股份有限公司', '深圳康泰生物制品股份有限公司', '深圳海创医药科技发展有限公司', '深圳市海滨制药有限公司', '深圳立健药业有限公司', '深圳赛保尔生物药业有限公司', '深圳高卓药业有限公司', '深圳龙瑞药业有限公司', '清远华能制药有限公司', '湖北东信药业有限公司', '湖北四环制药有限公司', '湖北广济药业股份有限公司', '湖北济川药业股份有限公司', '湖北百科亨迪药业有限公司', '湖北科益药业股份有限公司', '湖北齐进药业有限公司', '湖南中威制药有限公司', '湖南五洲通药业有限责任公司', '湖南健朗药业有限责任公司', '湖南千金湘江药业股份有限公司', '湖南华纳大药厂股份有限公司', '天地恒一制药股份有限公司', '湖南康尔佳药业管理集团有限公司', '湖南方盛制药股份有限公司', '湖南明瑞制药', '湖南春光九汇现代中药有限公司', '湖南有色凯铂生物药业', '湖南洞庭药业股份有限公司', '湖南省中医药研究院中药研究所', '湖南省湘中制药有限公司', '湖南赛隆药业有限公司', '湖南迪诺制药股份有限公司', '武汉滨会生物科技股份有限公司', '漳州片仔癀药业股份有限公司', '澳美制药', '澳诺（中国）制药有限公司', '灵北（北京）医药信息咨询有限公司', '烟台万润药业有限公司', '烟台东诚大洋制药有限公司', '烟台巨先药业有限公司', '荣昌生物制药（烟台）有限公司', '烟台鲁银药业有限公司', '爱可泰隆医药贸易（上海）有限公司', '牡丹江友搏药业有限责任公司', '特一药业集团股份有限公司', '珠海亿邦制药股份有限公司', '珠海同源药业有限公司', '珠海星光制药有限公司', '珠海横琴新区中珠正泰医疗管理有限公司', '珠海润都制药股份有限公司', '联邦制药国际控股有限公司', '琪宝制药（广州）有限公司', '瑞士赫尔森保健股份公司北京代表处', '瑞石生物医药有限公司', '瑞迪博士（无锡）制药有限公司', '瑞阳制药有限公司', '甘李药业有限公司', '甘肃定西扶正制药有限公司', '甫康(上海)健康科技有限责任公司', '田边三菱制药', '电气化学工业', '百健艾迪', '百奥泰生物科技（广州）有限公司', '解放军总医院第四医学中心', '百泰生物药业有限公司', '百济神州（北京）生物科技有限公司', '百特国际', '皮拉马尔医疗公司', '贵州益佰制药股份有限公司', '盐野义制药株式会社', '石家庄以岭药业股份有限公司', '石家庄市华新药业有限责任公司', '石家庄智时医药科技有限公司', '石药集团欧意药业有限公司', '石家庄藏诺药业股份有限公司', '石家庄龙泽制药股份有限公司', '石药控股集团有限公司', '石药集团中奇制药技术（石家庄）有限公司', '石药集团中诺药业（石家庄）有限公司', '石药集团恩必普药业有限公司', '神威药业集团有限公司', '神州细胞工程有限公司', '福安药业集团庆余堂制药有限公司', '福州闽海药业有限公司', '福建太平洋制药有限公司', '福建广生堂药业股份有限公司', '福建海西新药创制有限公司', '福建省泉州亚泰制药有限公司', '福建省融恒医药有限公司', '福建科瑞药业有限公司', '福建金山生物制药股份有限公司', '科文斯医药研发（上海）有限公司', '立业制药股份有限公司', '华润紫竹药业有限公司', '天津红日药业股份有限公司', '福建省维达康生物科技有限公司', '山东绿叶制药有限公司', '上海罗氏制药有限公司', '罗益（无锡）生物制药有限公司', '万全万特制药（厦门）有限公司', '美罗药业股份有限公司', '宁波美诺华药业股份有限公司', '深圳翰宇药业股份有限公司', '江苏联环药业股份有限公司', '肇庆星湖制药有限公司', '上海腾瑞制药有限公司', '舒泰神（北京）生物制药股份有限公司', '艾昆纬医药科技（上海）有限公司', '安徽长江药业有限公司', '苏州东瑞制药有限公司', '苏州中化药品工业有限公司', '苏州二叶制药有限公司', '苏州兰鼎生物制药有限公司', '苏州威尔森药业有限公司', '苏州弘森药业有限公司', '苏州思坦维生物技术股份有限公司', '苏州思源天然产物研发有限公司', '苏州沪云新药研发股份有限公司', '苏州盛达药业有限公司', '苏州盛迪亚生物医药有限公司', '苏州第四制药厂有限公司', '成都苑东生物制药股份有限公司', '茂祥集团吉林制药有限公司', '萌蒂（中国）制药有限公司', '蓬莱诺康药业有限公司', '西南药业股份有限公司', '西安万隆制药股份有限公司', '西安力邦制药有限公司', '华东医药(西安)博华制药有限公司', '西安大唐制药集团有限公司', '西安天一秦昆制药有限责任公司', '西安恒泰本草科技有限公司', 'Green Cross Corporation', '西安汉丰药业有限责任公司', '西安碑林药业股份有限公司', '西安葛蓝新通制药有限公司', '西普拉', '诺华', '诺思格医药', '贵州云峰药业有限公司', '贵州圣济堂制药有限公司', '贵州威门药业股份有限公司', '贵州百灵企业集团制药股份有限公司', '贵阳新天药业股份有限公司', '贵阳润丰制药有限公司', '赛诺菲', '上海赛金生物医药有限公司', '辅仁药业集团制药股份有限公司', '辉瑞制药', '辽宁盛生医药集团有限公司制药分公司', '辽宁远大诺康生物制药有限公司', '辽宁鑫善源药业有限公司', '辽源市迪康药业有限责任公司', '连云港杰瑞药业有限公司', '迪沙药业', '迪瑞医疗科技股份有限公司', '通化万通药业股份有限公司', '通化东宝药业股份有限公司', '通化安睿特生物制药股份有限公司', '通化永仓药业有限公司', '通化茂祥制药有限公司', '通化金恺威药业有限公司', '通药制药集团股份有限公司', '通辽市华邦药业有限公司', '邛崃天银制药有限公司', '重庆圣华曦药业股份有限公司', '重庆天地药业有限责任公司', '重庆希尔安药业有限公司', '重庆康刻尔制药有限公司', '重庆惠源医药有限公司', '重庆植恩药业有限公司', '重庆科瑞制药（集团）有限公司', '重庆药友制药有限责任公司', '重庆赛维药业有限公司', '重庆赛诺生物药业股份有限公司', '重庆青阳药业有限公司', '重庆麦克福新制药有限公司', '金陵药业股份有限公司', '锦州九泰药业有限责任公司', '锦州奥鸿药业有限责任公司', '长春生物制品研究所有限责任公司', '长春祈健生物制品有限公司', '长春迪瑞制药有限公司', '长春长生生物科技有限责任公司', '长春高新技术产业（集团）股份有限公司', '长白山制药股份有限公司', '长风药业股份有限公司', '阿马林制药有限公司', '陕西东泰制药有限公司', '陕西博森生物制药股份集团有限公司', '陕西合成药业股份有限公司', '陕西方舟制药有限公司', '陕西步长制药有限公司', '美国雅培制药有限公司', '雷杰纳荣制药', '青岛佳诺华医药科技有限公司', '青岛黄海制药有限责任公司', '韩国大化制药股份有限公司', '香港康恩堂制药厂有限公司', '马应龙药业集团股份有限公司', '鲁南制药集团股份有限公司', '山东鲁抗医药股份有限公司', '黄山中皇制药有限公司', '黑龙江天宏药业股份有限公司', '黑龙江天翼药业有限公司', '黑龙江成功药业有限公司', '黑龙江江世药业有限公司', '黑龙江葵花药业股份有限公司', '默沙东（中国）投资有限公司', '齐鲁制药有限公司', '苏州新旭医药有限公司', '苏州信诺维医药科技股份有限公司', '昭衍（北京）医药科技有限公司', '徕博科医药研发（北京）有限公司', '上海脉全医疗器械有限公司', 'PhaseBio Pharmaceuticals, Inc.', '南京北恒生物科技有限公司', '华博生物医药技术（上海）有限公司', '广州百瑞生物医药科技有限公司', '普米斯生物技术（珠海）有限公司', '复星凯瑞（上海）生物科技有限公司', '凯复（苏州）生物医药有限公司', '石药集团百克（山东）生物制药股份有限公司', '珐博进', '广州杨森生物科技有限公司', 'ImmunoGen Inc', '昆泰医药发展（上海）有限公司', '浙江劲方药业有限公司', '北京博纳西亚医药科技有限公司', '杭州瑞臻医药有限公司', '强生(中国)投资有限公司', '上海观合医药科技有限公司', '捷迈(上海)医疗国际贸易有限公司', '欧康维视生物医药（上海）有限公司', '浙江博锐生物制药有限公司', 'Green Cross Corporation\xa0', '成都先导药物开发股份有限公司', 'Swedish Orphan Biovitrum AG', '上海再极医药科技有限公司', '天境生物', '四川思路康瑞药业有限公司', '北海康成(上海)生物科技有限公司', 'Nektar Therapeutics', '晨兴（南通）医疗器械有限公司', 'PPD, Inc.', '杰诺医学研究（北京）有限公司 ', '杭州高光制药有限公司', '贝达药业股份有限公司', '思派（北京）网络科技有限公司', '亘喜生物科技（上海）有限公司', 'Loxo Oncolog', 'Monash University', 'PAREXEL China Co.,Ltd.', '三生国健药业（上海）股份有限公司', '亘喜生物科技(上海)有限公司', '广州凌腾生物医药有限公司', 'PRA Health Sciences China,Inc.,Shanghai Branch', '上海则正医药科技股份有限公司', '重庆智翔金泰生物制药有限公司', '上海有临医药科技有限公司', '宜昌人福药业有限责任公司', '上海康德弘翼医学临床研究有限公司', '长春金赛药业有限责任公司', '赛诺神畅医疗科技有限公司', '祐和医药科技有限公司', '极目峰睿（上海）生物科技有限公司', '南京征祥医药有限公司', '正腾康生物科技（上海）有限公司 ', '昆拓信诚医药研发（北京）有限公司', 'Debiopharm International S.A.', '杭州启函生物科技有限公司', '康融东方（广东）医药有限公司', '上海先博生物科技有限公司', '上海银诺医药技术有限公司', '上海纽脉医疗科技有限公司', 'Belite Bio, Inc', '康诺亚生物医药科技（成都）有限公司', 'Johnson & Johnson (China) Investment Ltd.', '苏州普蒂德生物医药科技有限公司', '上海信致医药科技有限公司', '上海德琪医药科技有限公司', '瓴路药业（上海）有限责任公司', '塔吉瑞生物医药有限公司', '甘莱制药有限公司', '辉瑞投资有限公司', '烨辉医药科技(上海)有限公司', '药华医药股份有限公司', '轩竹生物科技股份有限公司', '第一三共制药有限公司 ', '信立泰（苏州）药业有限公司', '苏州亚盛药业有限公司', '铂生卓越生物科技（北京）有限公司', '上海形状记忆合金材料有限公司', '上海细胞治疗集团有限公司', 'Genmab US, Inc.', '北京泛生子基因科技有限公司', '上海驯鹿生物技术有限公司', '应世生物科技（南京）有限公司', '上海骊霄医疗技术有限公司', '骨圣元化机器人(深圳)有限公司', 'RPS医药科技（北京）有限公司', '凌科药业（杭州）有限公司', 'Sagimet Biosciences Inc.', '上海微创电生理医疗科技股份有限公司', 'Incyte/Syneos', 'Abbive', '杭州德晋医疗科技有限公司', 'Bayer AG', '博纳西亚（合肥）医药科技有限公司', '未知君（深圳）科技有限公司', '西威埃医药技术（上海）有限公司', '上海临领医药科技有限公司', '葛兰素史克（中国）投资有限公司', '北京海莎咨询有限公司', '葛兰素史克研发有限公司', '深圳开立生物医疗科技股份有限公司', '上海绿谷制药有限公司', '武田发展中心美洲公司', 'Worldwide Clinical Trials Limited', '拜耳医药保健有限公司', '上海中医药大学附属岳阳中西医结合医院', 'Overland/PRA', '合源生物科技（天津）有限公司', '上海医药集团股份有限公司北京医药研究分公司', '上海复一会展管理有限公司', '上海玄宇医疗器械有限公司', 'Won Sik Lee, MD, PhD', '吉林惠升生物制药有限公司', '武汉联影智融医疗科技有限公司', '北京罗森博特科技有限公司', '维昇药业（上海）有限公司', '上海杰视医疗科技有限公司', '上海联拓生物科技有限公司', '深圳普罗吉医药科技有限公司', '徕博科医药研发（上海）有限公司', '成都因诺生物医药科技有限公司', 'MacroGenics,Inc', '新基公司', 'HAODE', '江苏荃信生物医药股份有限公司', '再创生物医药（苏州）有限公司', '缔脉生物医药科技（上海）有限公司', '北京海金格医药科技股份有限公司', '琅铧医药（上海）有限公司', '北京新码科技有限公司', '华熙生物科技股份有限公司', '北京佰仁医疗科技股份有限公司', '上海药明津石医药科技有限公司', '上海复旦大学附属肿瘤医院', 'PRA Health Sciences', '嘉兴易迪希计算机技术有限公司', '湃郎瑞医药科技（北京）有限公司', 'Seagen Inc.', '德琪（浙江）医药科技有限公司', '山东丝琳医药科技有限公司', '重庆精准生物技术有限公司', '杭州奕安济世生物药业有限公司', '昆翎(北京)医药科技发展有限公司', '珠海联邦制药股份有限公司', '科笛生物医药（上海）有限公司', '强生（中国）投资有限公司', '南京驯鹿生物技术股份有限公司', '轩竹（北京）医药科技有限公司', '山东轩竹医药科技有限公司 ', '心诺普医疗技术（北京）有限公司', '青岛博益特生物材料股份有限公司', '逸达生物科技股份有限公司', '上海津石医药科技有限公司', '正大天晴药业集团南京顺欣制药有限公司', '北京思睦瑞科医药科技股份有限公司', '皮尔法伯', '杭州诺为医疗技术有限公司', '上海天泽云泰生物医药有限公司', '华兰基因工程有限公司', '深圳微点生物技术股份有限公司', '北京易启医药科技有限公司', 'argenx BV', 'KBP BioSciences PTE. Ltd.', '归创通桥医疗科技股份有限公司', '康宁杰瑞（苏州）生物医药有限公司', 'SK Life Science', '北京阿迈特医疗器械有限公司', '瓴路药业(上海)有限责任公司', '万瑞飞鸿（北京）医疗器材有限公司', '诺和诺德（中国）制药有限公司', '北京颐合恒瑞医疗科技有限公司', '翼思生物医药(上海)有限公司', '武汉唯柯医疗科技有限公司', '浙江同源康医药股份有限公司', '兆科（广州）眼科药物有限公司', '北京创立科创医药技术开发有限公司', '成都欧林生物科技股份有限公司', '瑞士欧姆制药有限公司（OM Pharma SA）', '广东颂德制药有限公司', '上海微创心脉医疗科技（集团）股份有限公司', '北京以岭药业有限公司', '北京永泰瑞科生物科技有限公司', '恒翼生物医药科技（上海）有限公司', '华辉安健(北京)生物科技有限公司', '上海蓝脉医疗科技有限公司', '北京领健医疗科技有限公司', '北京亦度正康健康科技有限公司', '安徽柏拉阿图医药科技有限公司', '上海盟科药业股份有限公司', '上海瀛科隆医药开发有限公司', '上海市第一人民医院', '苏州创胜医药集团有限公司', '映恩生物制药（苏州）有限公司', '北京天智航医疗科技股份有限公司', '苏州卓恰医疗科技有限公司', '北京精诚通医药科技有限公司', '科笛生物医药（无锡）有限公司', '杭州唯强医疗科技有限公司', '首药控股(北京)有限公司', '上海理欧医药科技有限公司', '江苏海莱新创医疗科技有限公司', '达石药业（广东）有限公司', 'Janssen Research& Development LLC', 'F. Hoffmann-La Roche Ltd', 'Alexion Pharmaceuticals, Inc.', '法荟（北京）医疗科技有限公司', '南京维立志博生物科技股份有限公司', '上海交通大学医学院附属瑞金医院', '中国医学科学院北京协和医院', '上海和誉生物医药科技有限公司', '天津凯诺医药科技发展有限公司', '烨辉医药科技（上海）有限公司', '珠海宇繁生物科技有限责任公司', '上海科济制药有限公司', '爱博诺德（北京）医疗科技股份有限公司', '奕拓医药', '广州市基准医疗有限责任公司', '无锡和誉生物医药科技有限公司', '重庆华邦制药有限公司', '圣湘生物科技股份有限公司', '诺思格（北京）医药科技股份有限公司', '上海申淇医疗科技有限公司', '广东天科雅生物医药科技有限公司', '百济神州（上海）生物科技有限公司', '昆翎（天津） 医药发展有限公司', '云顶药业（苏州）有限公司', '北京唯迈医疗设备有限公司', '石药集团巨石生物制药有限公司', '杭州德适生物科技有限公司', '上海竞捷医疗科技股份有限公司', 'Millennium Pharmaceuticals，Inc.', '住友制药（苏州）有限公司', '浙江艾森药业有限公司', '百济神州（苏州）生物科技有限公司', '上海百试达医药科技有限公司', '江苏威凯尔医药科技有限公司', '北京华科恒生医疗科技有限公司', '杭州嘉因生物科技有限公司', 'Aerovate Therapeutics Inc', '北京博润阳光科技有限公司（天津环湖）', '南通罗伯特医疗科技有限公司', '上海药明巨诺生物科技有限公司', '上海腾复医疗科技有限公司', '上海复星医药产业发展有限公司', '北京盛迪医药有限公司', '南京西格玛医学技术股份有限公司', '北京贝来生物科技有限公司', '通桥医疗科技有限公司', '中派科技（深圳）有限责任公司', '北京三诺佳邑生物技术有限责任公司', '迈威（上海）生物科技股份有限公司', '北京卓凯生物技术有限公司', '南京驯鹿生物技术股份有限公司', '晟科药业（江苏）有限公司', '湖南瑞康通科技发展有限公司', '绍兴臻乐医药科技有限公司', '北京朗桢医药科技有限公司', '北京化药科创医药科技发展有限公司', '北京硕佰医药科技有限责任公司', '上海隆耀生物科技有限公司', '科越医药(苏州)有限公司', '科弈（浙江）药业科技有限公司', '南京思元医疗技术有限公司', '上海岱宗制药有限公司', '国典（北京）医药科技有限公司', '联邦生物科技（珠海横琴）有限公司', '洛阳尚德药缘科技有限公司', '上海百迈博制药有限公司', '苏州旺山旺水生物医药股份有限公司', 'Sharp Corporation', '上海汇伦医药股份有限公司', '北京锦篮基因科技有限公司', '上海赛远生物科技有限公司', '乔治（北京）临床医学研究有限公司', '拓创生物科技（江苏）有限公司', '上海贺维斯特医药科技有限公司', '上海强生制药有限公司', '昆翎企业管理（上海）有限公司', '苏州宏元生物科技有限公司', '西门子医学产品诊断（上海）有限公司', '百济神州（广州）创新科技有限公司', '广州百济神州生物制药有限公司', '上海海雁医药科技有限公司', '甘李药业股份有限公司', '优诺维（武汉）医疗科技有限公司', '广东尚德药缘科技有限公司', '杭州广科安德生物科技有限公司', '合肥海脉通衡生物科技有限公司', '苏州康乃德生物医药有限公司', '上海遥健医疗科技有限公司', '济南川成医药科技开发有限公司', '广州燃石医学检验所有限公司', '和铂医药（苏州）有限公司', '罗欣药业集团股份有限公司', '宜明昂科生物医药技术(上海)有限公司', '歌礼生物科技（杭州）有限公司', '礼来', '渤健生物科技（上海）有限公司', '上海赛金生物医药有限公司', '广州麓鹏制药科技有限公司', '应世生物科技（上海）有限公司', '佛山市原力生物科技有限公司', '明慧医药（杭州）有限公司', '思路迪医药（青岛）有限公司', '博济医药科技股份有限公司', '上海恒瑞医药有限公司', '再鼎医药(上海)有限公司', '北京先通国际医药科技股份有限公司', '北京加科思新药研发有限公司', '北京天广实生物技术股份有限公司', '珐博进（中国）医药技术开发有限公司', '北京诺思兰德生物技术股份有限公司', '合肥华方医药科技有限公司', '四川普锐特医药科技有限责任公司', '上海钛米机器人科技有限公司', '天大药业（中国）有限公司', '天津市医药集团有限公司', '天津滨江药物研发有限公司', '天演药业（苏州）有限公司', '太安堂集团有限公司', '太极集团有限公司', '葛兰素史克（上海）医药研发有限公司', '北京迈瑞生医药科技有限公司', '迈思睿（北京）医药咨询有限公司', '迈迪思创（北京）科技发展有限公司', '华信领航(北京)医疗科技有限公司', '昆拓信诚医药研发(北京)有限公司', '汉佛莱医药顾问有限公司', '国信医药科技（北京）有限公司', '上海长海医院', '北京语禾医学科技有限公司', '医来医往（北京）科技有限公司', '北京康慧东方医药科技有限公司', '北京博瑞世安科技有限公司', '北京兴德通医药科技股份有限公司', '北京康派特医药科技开发有限公司', '医之邦（北京）医学研究有限公司', '北京推想科技有限公司', '北京科林利康医学研究有限公司', '北京精驰医疗科技有限公司', '北京华氏康源医药科技有限公司', '北京国铭博思医药科技有限公司', '北京阜康仁生物制药科技有限公司', '同泽合信（北京）医药科技有限公司', '北京春天医药科技发展有限公司', '北京博润阳光科技有限公司', '北京奥泰康医药技术开发有限公司', '宝石花医药科技（北京）有限公司', '北京卓越未来国际医药科技发展有限公司', '北京众力君成医药科技有限公司', '上海长海医院药物临床试验机构', '北京元和创生科技有限公司', '北京盈科瑞创新医药股份有限公司', '普瑞盛（北京）医药科技开发有限公司', '欧亚汇智（北京）医学研究有限公司', '诺思格（北京）医药科技股份有限公司', '北京亦度正康健康科技有限公司', '北京伊司普特医药科技有限公司', '北京遥领医疗科技有限公司', '北京阳光德美医药科技有限公司', '北京新领先医药科技发展有限公司', '北京水木菁创医药科技有限公司', '北京赛德盛医药科技股份有限公司', '北京明华智德医疗科技有限公司', '北京夸克侠科技有限公司', '北京科信必成医药科技发展有限公司', '北京创立科创医药技术开发有限公司', '北京凯普顿医药科技开发有限公司', '永铭诚道(北京)医学科技股份有限公司', '北京宝丽永昌医药科技有限公司', '盛恩（北京）医药科技有限公司', '永铭诚道（北京）医学科技股份有限公司', '则正（上海）生物科技有限公司', '上海康德弘翼医学临床研究有限公司', '上海朴岸医药科技有限公司', '英翰医药科技（上海）有限公司', '百试达（上海）医药科技股份有限公司', '维蔻（上海）医疗科技有限公司', '圣方（上海）医药研发有限公司', '上海用正医药科技有限公司', '上海医药临床研究中心有限公司', '上海药明康德新药开发有限公司', '上海现代药物制剂工程研究中心有限公司', '上海韧致医学研究有限公司', '上海创达医药科技（上海）有限公司', '上海丰能医药科技有限公司', '上海博济康生物医药科技有限公司', '润东医药研发（上海）有限公司', '上海梅斯医药科技有限公司', '君岳医药科技（上海）有限公司', '斐卫（上海）医药科技有限公司', '翰博瑞强（上海）医药科技有限公司', '上海有临医药科技有限公司', '上海合川医药咨询有限公司', '驭临君（广州）医药科技有限公司', '广州奥为康医药有限公司', '博济医药科技股份有限公司', '广州美迪高生物医药科技有限公司', '广州领晟乔治临床医学研究有限公司', '广州九泰药械技术有限公司', '广州汇智成功药物研究有限公司', '广州科创医药科技有限公司', '南京西格玛医学技术股份有限公司', '南京沃特生物科技有限公司', '南京宁拓医药科技有限公司', '南京引光医药科技有限公司', '江苏鼎泰药物研究(集团)股份有限公司', '钛和海沁（杭州）医药科技有限公司', '杭州盾恩医学检验实验室有限公司', '杭州百诚医药科技股份有限公司', '成都润泽医药科技开发有限公司', '成都华西临床研究中心有限公司', '四川尚锐分析检测有限公司', '四川华氏康源医药科技有限公司', '夸克医药（中国）有限公司', '成都凡微析医药科技有限公司', '康龙化成(成都)临床研究服务有限公司', '长沙晶易医药科技股份有限公司', '长沙都正生物科技股份有限公司', '湖南慧泽生物医药科技有限公司', '长春艾仁医药科技有限公司', '南昌华凯生物技术有限公司', '则正（济南）生物科技有限公司', '宝丽永昌医药科技（深圳）有限公司', '苏州代蒙迪医药科技有限公司', '苏州旭辉检测有限公司', '博纳西亚（合肥）医药科技有限公司', '合肥聚信医药科技有限公司', '天津开心生活科技有限公司', '天津凯诺医药科技发展有限公司', '天津艾迪研医药科技有限公司', '石家庄凯瑞德医药科技发展有限公司', '常州德大康成医学科技有限公司', '郑州弘新医疗科技有限公司', '浙江汇泽医药科技有限公司', '山东致臻医药科技有限公司', '济南智同医药科技有限公司', '山东安嘉生物科技有限公司', '福州齐致医药有限公司', 'Charité Research Organisation', '上海柯西医药发展有限公司', '合肥科颖医药科技有限公司', '北京斯丹姆赛尔技术有限责任公司', '广州翔康医学研究有限责任公司', '中国医药工业研究总院临床研究中心', '北京凯芮特医药科技有限公司', '北京禾玥医药科技有限公司', '北京安科智泰生物医药科技有限公司', '宝石花医药科技（北京）有限公司', '北京原素生物医药科技有限公司', '北京欣康研医药科技有限公司', '北京星宿光大科技有限公司', '上海药明津石医药科技有限公司', '上海智谦医药科技有限公司', '上海兆汇医药科技有限公司', '上海蔚然深秀医药科技有限公司', '艾瑞嘉医药研发（上海）有限公司', '上海迈兰医药咨询有限公司', '上海慧恩医药科技有限公司', '上海合川医药咨询有限公司', '上海砝码斯医药生物科技有限公司', '泰莱生物医药科技（广州）有限公司', '广州赛恩医药科技服务有限公司', '广州唐捷医疗科技有限公司', '广州创赢生物医药科技有限公司', '曜研智能（南京）医疗科技有限公司', '湖南研致医药科技有限公司', '河南开研医药科技有限公司', '西安循证医药科技有限公司', '武汉精石医药科技发展有限公司', '天津泰吉医药科技有限公司', '康弘（天津）生物医药科技有限公司', '南京复瑞吉医疗科技有限公司', '江苏礼华生物技术有限公司', '江苏礼恩医药科技有限公司', '重庆歌汭医院临床研究中心有限公司', '重庆恒誉康医药科技有限公司', '武汉致众科技股份有限公司', '武汉普渡生物医药有限公司', '浙江尧惠医药科技有限公司', '杭州泰格医药科技股份有限公司', '上海麦济生物技术有限公司', '东南大学附属中大医院', '中南大学湘雅三医院', '中国人民解放军总医院', '中国人民解放军海军军医大学第三附属医院', '中美上海施贵宝制药有限公司', '丽珠集团利民制药厂', '乳源东阳光医疗器械有限公司', '乳源东阳光药业有限公司', '北京博润阳光科技有限公司', '云济华美医药（北京）有限公司', '北京复星医药科技开发有限公司', '云顶新耀医药科技有限公司', '亚宝药业集团股份有限公司', '优效（北京）医学研究有限公司', '北京中科盛康科技有限公司', '北京新唯医药科技有限公司', '北京时代优创科技有限公司', '北京创新国信医药科技有限公司', '北京利久医药科技有限公司', '北京北大维信生物科技有限公司', '北京禾润东方医药科技有限公司', '北京卓诚惠生生物科技股份有限公司', '北京四环制药有限公司', '北京大学肿瘤医院', '北京大学附属第三医院', '北京岐黄药品临床研究中心', '北京斯丹姆赛尔技术有限责任公司', '北京药海宁康医药科技有限公司', '泰格医药', '杭州翰思生物医药有限公司', '乔治(北京)临床医学研究有限公司', 'F2G Ltd. Lankro Way Eccles, Manchester, M30 0LX United Kingdom（英国）', 'Biohaven Pharmaceuticals holding company limited', '上海联拓生物科技有限公司', '普众发现医药科技（上海）有限公司', '上海医药集团股份有限公司', '湖北生物医药产业技术研究院有限公司', '上海伊诺瑞康生物医药科技有限公司', '北京诺和德美医药技术有限公司', '北京法马苏提克咨询有限公司', '北京珅奥基医药科技有限公司', '北京鑫康合生物医药科技有限公司', '北京科林利康医学研究有限公司', '北京诺为力创医药科技发展有限公司', '北京赛德盛医药科技股份有限公司', '北京赛德盛医药科技股份有限公司 ', '南京希麦迪医药科技有限公司', '北京辅仁瑞辉生物医药研究院有限公司', '北京鑫诺美迪基因检测技术有限公司', '华大生物科技（武汉）有限公司', '南京优科制药有限公司', '南京医科大学第一附属医院', '南京大学医学院附属鼓楼医院', '南京驯鹿医疗技术有限公司', '国信医药科技（北京）有限公司', '圣兰格（北京）医药科技开发有限公司', '南昌大学第二附属医院', '天津冠勤医药科技有限公司', '厦门大学', '合肥天汇孵化科技有限公司', '吉林四环制药有限公司', '吉林省肿瘤医院', '嘉兴太美医疗科技有限公司', '嘉兴安谛康生物科技有限公司', '四川思路迪药业有限公司', '四川自豪时代药业有限公司', '希米科医药技术发展（北京）有限公司', '复星凯特生物科技有限公司', '天津医科大学总医院', '天津大学总医院', '天翊微创医疗科技（常州）有限公司', '奥索假肢矫形康复器材（上海）有限公司', '斯丹姆（北京）医药技术集团股份有限公司', '宁夏康亚药业股份有限公司', '宁波圣健生物医药科技有限公司', '安进生物医药研发（上海）有限公司', '杭州泰格医药科技股份有限公司', '宜宾市第二人民医院', '富螺（上海）医疗器械有限公司', '山东博士伦福瑞达制药有限公司', '山东新华医疗器械股份有限公司', '山东省千佛山医院', '比逊（广州）医疗科技有限公司', '山西康宝生物制品股份有限公司', '湖北生物医药产业技术研究院有限公司', '北京图灵至亦医疗科技有限公司', '汇每极致健康科技（北京）有限公司', '赣州和美药业股份有限公司', '合源医疗器械(上海)有限公司', '晋城海斯制药有限公司', '苏州澳宗生物科技有限公司', '广州悦清再生医学科技有限公司', '江苏复星医药销售有限公司', '希麦迪（南京）生物科技有限公司', '广东天普生化医药股份有限公司', '广东安普泽生物医药股份有限公司', '广州博济医药生物技术股份有限公司', '广州市第八人民医院', '润东医药研发（上海）有限公司', '湃朗瑞医药科技（北京）有限公司', '广州瑞博奥生物科技有限公司', '百时益医药研发（北京）有限公司', '开封制药（集团）有限公司', '盛恩（北京）医药科技有限公司', '强生（苏州）医疗器材有限公司', '方恩（天津）医药发展有限公司', '精鼎医药研究开发（上海）有限公司', '日本大鹏药品工业株式会社', '昆翎（天津）医药发展有限公司', '武汉光谷人福生物医药有限公司', '艾瑞嘉医药研发（上海）有限公司', '江苏怡科生物医疗科技有限公司', '江苏晨泰医药科技有限公司', '江苏艾迪药业有限公司', '西斯比亚（北京）医药技术研究有限责任公司', '江苏艾迪药业股份有限公司', '江苏铼泰医药生物技术有限公司', '赛纽仕医药信息咨询（北京）有限公司', '泰州新生源生物医药有限公司', '浙江大学', '海正生物制药有限公司', '深圳中科乐普医疗技术有限公司', '郑州深蓝海生物医药科技有限公司', '深圳华润九新药业有限公司', '深圳市贝斯达医疗股份有限公司', '湖南省肿瘤医院', '长沙都正生物科技股份有限公司', '鼎泰（南京）临床医学研究有限公司', '爱恩希（北京）医疗科技有限公司', '爱恩康临床医学研究（北京）有限公司', '上海瑛泰医疗器械股份有限公司', '牡丹江友搏药业有限责任公司', '施贵宝', '苏州信诺维', '成都微芯药业有限公司', '瑞阳', '药明康德', '智康弘义', '北京复星医药科技开发有限公司', '苏州盛迪亚生物医药有限公司', '华道生物', 'HiFiBiO Inc.', '北京诺和德美医药技术有限公司', '珠海通桥医疗科技有限公司', '百时美施贵宝（上海）贸易有限公司', '科文斯医药研发（北京）有限公司', '科文斯医药研发（北京）有限公司上海分公司', '第二军医大学长海医院', '维眸生物科技（浙江）有限公司', '绿谷（上海）医药科技有限公司', '罗氏研发（中国）有限公司', '腾盛华创医药技术（北京）有限公司', '艾尔建信息咨询（上海）有限公司', '中山大学肿瘤防治中心（中山大学附属肿瘤医院、中山大学肿瘤研究所）', '思康睿奇（苏州）药业有限公司', '强生（中国）投资有限公司', '常州恒邦药业有限公司', '广州赛隽生物科技有限公司', '艾斯拓康医药科技（北京）有限公司', '中国人民解放军海军第九0五医院', '上海君拓生物医药科技有限公司', '江苏奥赛康生物医药有限公司', '宜明昂科生物医药技术（上海）股份有限公司', '复旦大学附属肿瘤医院', '上海心瑞医疗科技有限公司', '河南省肿瘤医院', '北京协和医院', '艾斯拓康生物医药（天津）有限公司', '北京肿瘤医院', '西藏海思科制药有限公司', '浙江太美医疗科技股份有限公司', '上海泽德曼医药科技有限公司', '同宜医药（合肥）有限公司', '翼思生物医药（苏州）有限公司', '南京鼓楼医院', '大连医科大学附属第一医院', '北京艾斯默医药科技有限公司', '上海市胸科医院', '合肥天汇生物科技有限公司', '微境生物医药科技（上海）有限公司', '上海慧达医疗器械有限公司', '江苏冬泽特医食品有限公司', '康德弘毅', '成都微芯药业有限公司', '聚领瑞科', '普方生物（美国）公司', '苏州克睿基因生物科技有限公司', '苏州大学附属第一医院', '苏州心擎医疗技术有限公司', '苏州新波生物技术有限公司', '苏州景昱医疗器械有限公司', '苏州碧迪医疗器械有限公司', '苏州西克罗制药有限公司', '藥華醫藥股份有限公司', '西比曼生物科技（上海）有限公司', '语坤（北京）网络科技有限公司', '脑全康(成都)医疗器械科技有限公司', '海正生物制药有限公司', '北京天坛医院', '阿斯利康投资（中国）有限公司', '北京艺妙神州医药科技有限公司', '上海市肺科医院', '中国医学科学院肿瘤医院', '上海上药交联医药科技有限公司', '赣州市第五医院', '百时美施贵宝（中国）投资有限公司', '上药帛康生物医药（上海）有限公司', '吉林大学第一医院', '上海市第五人民医院', '上海市第六人民医院', '浙江养生堂生物科技有限公司', '西安新通药物研究股份有限公司', '北京惠之衡生物科技有限公司', '北京生命绿洲公益服务中心', 'E.R. SQUIBB AND SONS LLC', '杭州堃博生物科技有限公司', '泰州复旦张江药业有限公司', '波科国际医疗贸易（上海）有限公司', '柯惠医疗器材国际贸易（上海）有限公司', '浙江大学医学院附属邵逸夫医院', '苏州康乃德生物医药有限公司 ', '中国医科大学附属第一医院', '广州昂科免疫生物技术有限公司', '江苏瑞尔医疗科技有限公司', '苏州仁东生物工程有限公司', '苏州沪云新药研发股份有限公司', '深圳市海普洛斯生物科技有限公司', '晶核生物医药科技(上海)有限公司', '广州瑞风生物科技有限公司', '力品药业（厦门）股份有限公司', '正大天晴药业集团南京顺欣制药有限公司', '甘李药业股份有限公司', '普蕊斯', '上海首歌生物科技有限公司', '先声药业', '谱高医疗科技（南京）有限公司', '贵州麦迪逊健康咨询有限公司', '赣南医学院第一附属医院', '轩竹（海南）医药科技有限公司', '辉瑞制药有限公司', '辉瑞国际贸易（上海）有限公司', '辽宁省人民医院', '重庆医科大学附属第二医院', '重庆天科雅生物科技有限公司', '长沙市中心医院', '驻马店市中心医院', '黑龙江省肿瘤医院', '默克雪兰诺（北京）医药研发有限公司', '齐腾医药科技咨询（上海）有限公司', '皖南医学院附属弋矶山医院', '科睿驰（深圳）医疗科技发展有限公司', '南京再明医药有限公司', '上海神奕医疗科技有限公司', '鼎泰（南京）临床医学研究有限公司', '正大天晴（广州）医药有限公司', '杭州浩博医药有限公司', '北京大学第一医院', '广东省人民医院', '中国中医科学院西苑医院', '首都医科大学附属北京地坛医院', '上海微荷医学检验实验室有限公司', '上海麦德医疗设备科技有限公司', '江苏拓弘康恒医药有限公司', '百泰生物药业有限公司', '北京博医臻研医药科技开发有限公司', '赛德特生物制药有限公司', '辉凌制药(中国)有限公司', '同泽合信（北京）医药科技有限公司', '上海博奥明赛生物科技有限公司', '上海惠盾生物技术有限公司', '丝纳特（苏州）生物科技有限公司', '浙江迈同生物医药有限公司', '苏州信迈医疗科技股份有限公司', '青岛华晟世达生物制药有限公司', '四川远大蜀阳药业有限责任公司', '北京先通国际医药科技股份有限公司', '深圳市理邦精密仪器股份有限公司', '成都优洛生物科技有限公司', '河北晟宇医疗器械有限公司', '青岛圣桐营养食品有限公司', '康霖生物科技(杭州)有限公司', '平安盐野义有限公司', '默沙东有限责任公司', '诺诚健华医药科技有限公司', 'CoreAalst BV', '华润三九医药股份有限公司', 'SMO ClinPlus Co., Ltd.', '上海小荷医学检验实验室有限公司', '上海百利佳生物医药科技有限公司', '广州威溶特医药科技有限公司', '唯智医疗科技（佛山）有限公司', '晟临生物医药(上海)有限公司', '英百瑞(杭州)生物医药有限公司', '湖南康晴生物科技有限公司', '上海交通大学医学院附属仁济医院', '上海医药临床研究中心', '兰州西京医院有限公司', '欣凯医药科技（上海）有限公司', '北京信然宜诚医疗科技有限公司', '优瑞科（北京）生物技术有限公司', '天津可康医药技术开发有限公司', '碧迪医疗器械（上海）有限公司', '铨融（上海）医药科技开发有限公司', '中国抗体制药有限公司', '中国抗体制药有限公司', '深圳中科精诚医学科技有限公司', '通用电气医疗系统（天津）有限公司', '山东泰邦生物制品有限公司', '上海宏普医疗器械有限公司', '北京易临医药科技有限公司', '昆拓信诚医药研发（北京）有限公司', '江苏申命医疗科技有限公司', '石河子市玺泰股权投资合伙企业（有限合伙）', '石河子市睿泽盛股权投资有限公司', '石河子睿新股权投资合伙企业（有限合伙）', '江西青峰药业有限公司上海分公司', '赖春宝', '阿斯利康全球研发（中国）有限公司', 'Bayer Pharma AG', 'Elite Neurovascular', 'ISIS PHARMACEUTICALS INC', 'PHARMAESSENTIA CORP.', 'SFJ LungCancer,Ltd', '丁洋', '上海东松医疗科技股份有限公司', '上海益泰医药科技有限公司', '中国科学院上海药物研究所', '李娜', '杨玉丽', '王云杰（吉林大学第二医院）', '上海细胞治疗集团药物技术有限公司', '罗氏', '昆翎企业管理（上海）有限公司', 'CoreAalst BV', '精鼎医药研究开发(上海)有限公司', '百济神州（苏州）生物科技有限公司', '博瑞新创生物医药科技（无锡）有限公司', '希米科医药技术发展（北京）有限公司', 'Sequent Medical Inc.', '湖南埃普特医疗器械有限公司', 'Ipsen Pharma', '成都倍特药业有限公司', '诺爱药业（上海）有限公司', '勃林格殷格翰（中国）投资有限公司', '安进生物技术咨询（上海）有限公司', '北京诺和德美医药技术有限公司', '石药集团中奇制药技术（石家庄）有限公司', '华夏英泰（北京）医药科技有限公司', '成都康弘制药有限公司', '江苏冬泽特医食品有限公司', '北京MEDPACE医药科技有限公司', '富启睿医药研发（北京）有限公司上海分公司', '广东广纳安疗科技有限公司', '浙江博也生物', '上海君赛生物科技有限公司', '唐传生物科技（厦门）有限公司', '天津药物研究院有限公司', '广州市润林生物科技有限公司', '正序（上海）生物医药科技有限公司', '诺桥医疗器械科技(烟台)有限公司', '北京米瑞克科技有限公司', '柏达（北京）医药科技有限公司', '青岛博益特生物材料股份有限公司', '施维雅（北京）医药研发有限公司', '浙江大学医学院附属第二医院', '前沿生物药业（南京）股份有限公司', '广东东阳光药业有限公司', '无锡和誉生物医药科技有限公司', '澳美制药厂有限公司', '上海三维生物技术有限公司', '上海珩畅医疗科技有限公司', '江苏奥赛康生物医药有限公司', '赛诺菲（中国）投资有限公司上海分公司', '北京鑫康合生物医药科技有限公司', '普瑞盛（北京）医药科技开发股份有限公司', '昆翎', '江苏恒瑞', 'Ipsen Pharma', '陕西麦科奥特科技有限公司', '山西纳安生物科技股份有限公司', '深圳核心医疗科技有限公司', '武汉睿健医药科技有限公司', '中国科学院上海药物研究所', '信念医药科技（上海）有限公司', '北京纳米维景科技有限公司', '安徽万邦医药科技股份有限公司', '利安康（北京）生物技术有限公司', '临研通（无锡）科技有限公司', '太美智研（上海）医药研发有限公司', '福建省大福瑞装饰材料有限公司', '河南泰丰生物科技有限公司', '迈得诺医疗科技集团有限公司', '劲威生物医药科技有限公司', '上海顿慧医疗科技发展有限公司', '原研药港生命科学（辽宁）集团有限公司', '小蓝花（无锡）健康科技有限公司', '浙江康恩贝中药有限公司', '北京瞳沐医疗器械有限公司', '浙江昂利康制药股份有限公司', '苏州迈得诺意医疗技术有限公司', '南京泽漆生物医药有限公司', '转录本（上海）生物科技有限公司', '重庆医药（集团）股份有限公司', '海南百迈科医疗科技股份有限公司', '北京精医和生医药科技有限公司', '北京中泰邦医药科技有限公司', '上海赛默罗生物科技有限公司', '烟台昊鼎生物科技有限公司', '星锐医药（苏州）有限公司', '深圳市菲鹏生物制药股份有限公司', '苏州新云医疗设备有限公司', '临麒医药科技（上海）有限公司', '科霸生物(江苏)有限公司', '广州白云山中一药业有限公司', '上海寻百会生物技术有限公司', '昆翎（天津）医药发展有限公司', '箕星藥業香港有限公司', '杭州德同生物技术有限公司', '德勤管理咨询(上海)有限公司', '西安敦博医疗器械有限公司', '云核医药（天津）有限公司', '北京再极医药科技有限公司', 'PharmaResearchCo.,Ltd.', '北京鑫康合生物医药科技有限公司', 'NUOHUA', '葛兰素史克医药研发有限公司', 'ABIVAX', '智元柏迈（杭州）科技有限公司', '武汉科福新药有限责任公司', '北京康辰', '北京康', '默沙东', '开心', '上海罗氏制药有限公司', '广州白云山医药集团股份有限公司白云山制药总厂', '北京禾璞医疗科技有限公司', '江苏豪森药业集团有限公司', '昌明生物科技(苏州)有限公司', '费森尤斯医疗投资（中国）有限公司', '轩竹（北京）医药科技有限公司', '山东轩竹医药科技有限公司', '北京康众时代医药科技集团有限公司', '上海市第一人民医院', '上海吉宣生物科技有限公司', '上海药苑生物科技有限公司', 'ICON Clinical Research Limited', '哈尔滨医科大学附属肿瘤医院', '江苏释研医药科技有限公司', '北京普德康利医药科技有限公司', '深圳翰宇药业股份有限公司', '威沃克商务信息咨询（广州）有限公司', '山东盛迪医药有限公司', '暨南大学附属第一医院(广州华侨医院)', '无忧跳动医疗科技(深圳)有限公司', '南京西格玛医学技术股份有限公司', '北京五和博澳药业股份有限公司', '科笛生物医药（无锡）有限公司', '瑞利迪（上海）生物医药有限公司', '首都医科大学宣武医院', '深圳市龙曜生物有限公司', '南京友德邦医疗科技有限公司', '珃诺生物医药科技（杭州）有限公司', '苏州复恩特药业有限公司', '长沙晶易医药科技股份有限公司', '北京欧格林咨询有限公司', '湖南先赛生物科技有限公司', '杭州远大生物制药有限公司', '广东瑞顺生物技术有限公司', '北京儿童医院顺义妇儿医院', '中食安康（北京）科技发展有限公司', 'ICON', '瑞阳制药股份有限公司', '葛兰素史克医药研发有限公司', 'ABIVAX', '北京康辰', '广州医科大学附属第一医院', '浙江格物致知生物科技有限公司', '上海环码生物医药有限公司', '上海汉通医疗科技有限公司', '岸迈生物科技（苏州）有限公司', '北京宝丽永昌医药科技有限公司', '上海欣吉特生物科技有限公司', '深圳普瑞金生物药业股份有限公司', '精医和生医药（广东横琴粤澳深度合作区）有限公司', '上海瑞奕澄医药科技有限公司', '宜坤行健生物医药(南京)有限公司', 'Celgene International Sàrl', '复星医药产业发展（深圳）有限公司', '赛诺哈勃药业（成都）有限公司', '曜研智能（南京）医疗科技有限公司', '嘉晨西海（杭州）生物技术有限公司', '深圳核心医疗科技股份有限公司', '鲁南厚普制药有限公司', '丹源医学科技(杭州)有限公司', '深圳市图微安创科技开发有限公司', '厦门瑞聚医学科技有限公司', '中南大学湘雅医院', '杭州市第一人民医院', '北京鞍石生物科技有限公司', '新加坡国立癌症中心', '杭州泰格', '北京烁星生物医药科技有限公司', 'CSL Behring GmbH', '施慧达药业集团（吉林）有限公司', '南京纽诺英特医疗科技有限公司', '源方（北京）医药科技有限公司', '广州南鑫药业有限公司', '广州南鑫药业有限公司', '江苏威凯尔医药科技股份有限公司', '江苏豪森药业集团有限公司', '长春金赛药业有限责任公司', '上药帛康生物医药（上海）有限公司', '江苏荃信生物医药股份有限公司', '北京华亘安邦科技有限公司', '上海银诺医药技术有限公司', '广州银诺医药集团股份有限公司', '礼进生物医药科技（苏州）有限公司', '上海圣馨医药科技有限公司', '广东东阳光药业股份有限公司', '济川药业集团有限公司', '亿腾医药（苏州）有限公司', '博瑞新创生物医药科技（无锡）有限公司', '四川济生堂药业有限公司', '上海炫脉医疗科技有限公司', '烟台东诚药业集团股份有限公司', '上海辐联医药科技有限公司', '苏州君境生物医药科技有限公司', '领博生物科技（杭州）有限公司', '谱高医疗', '重庆派金生物科技有限公司', '中国人民解放军海军军医大学第一附属医院', 'Denovo Biopharma LLC', '上海迈兰医药咨询有限公司', '北京德诺公益基金会', '陕西佰傲再生医学有限公司', '北京和泽品昭医药技术有限公司', '青岛万明赛伯药业有限公司', 'ANTHOS THERAPEUTICS, INC.', '安谛康', '智康弘义生物科技有限公司', '日本大鹏', '缔脉', '东曜药业有限公司', '合肥天麦生物科技', '江西济民可信集团有限公司', '江西青峰药业有限公司', '来凯医药科技(上海)有限公司', '礼来（中国）研发有限公司', '上海岸迈生物科技有限公司', '上海复旦张江生物医药股份有限公司', '沈阳三生制药有限责任公司', '泰州翰中生物医药有限公司', '武汉禾元生物科技股份有限公司', 'Gilead Science', 'IDRx, Inc', '罗氏（中国）投资有限公司', '北海康成（苏州）生物制药有限公司', '富启睿医药研发（北京）有限公司', '和记黄埔医药（上海）有限公司', '康方赛诺医药有限公司', '礼来苏州制药有限公司', '诺华（中国）生物医学研究有限公司', '赛诺菲（北京）制药有限公司', '山东新时代药业有限公司', '上海普珩生物技术有限公司', '天境生物科技（上海）有限公司', '宜明昂科生物医药技术（上海）有限公司', '智翔（上海）医药科技有限公司', '默沙东有限责任公司', 'IDRX-42', '君实', 'IDRx, Inc.', '罗氏（中国）投资有限公司', '百时益医药研发（北京）有限公司', '赛诺菲（中国）投资有限公司', '江苏亚虹医药科技股份有限公司', '诺华（中国）生物医学研究有限公司', '赛诺菲（北京）制药有限公司', '贝达', '艾伯维医药贸易（上海）有限公司', '百时美施贵宝公司', '迪哲（江苏）医药股份有限公司', '富启睿医药研发（北京）有限公司', '基石药业（苏州）有限公司', '康方药业有限公司', '四川科伦博泰生物医药股份有限公司', '腾盛博药医药技术（北京）有限公司', 'SFJ', 'G1 Therapeutics', 'Beigene', 'ImmunoGen, Inc. 830 Winter Street Waltham, MA 02451 USA', '渤健生物科技(上海)有限公司', 'Boehringer Ingelheim', 'IQVIA', 'PPD', '昆泰医药发展（上海）有限公司', 'PXL', '徕博科', '科文斯', 'PRA', 'PPC', 'Takeda Development Center Americas, Inc.', '住友制药（苏州）有限公司', '江苏奥赛康药业有限公司', '富启睿医药研发（北京）有限公司', '杭州德睿智药科技有限公司', '复星诊断科技(上海)有限公司', '北京高斯医疗管理有限公司', 'Incyte Corporation', '贝达药业股份有限公司', '安斯泰来制药株式会社', '扬州一洋制药有限公司', 'Takeda Development Center Americas, Inc', '浙江大学医学院附属邵逸夫医院', '四川思路康瑞药业有限公司', '默克', '西安杨森医学事务部', 'Loxo Oncology, Inc.', 'Provention Bio, Inc.', '浙江迈同生物医药有限公司', 'AstraZeneca AB', '北京精诚通医药科技有限公司', '成都虹炬生物科技有限公司', '苏州仁东生物工程有限公司', '诺灵生物医药科技（杭州）有限公司', '苏州华毅乐健生物科技有限公司', '中食安康（北京）科技发展有限公司', '青岛圣桐营养食品有限公司', '长沙赋妍医疗科技有限公司', '苏州圣因生物医药有限公司', '江苏浩欧博生物医药股份有限公司', '北京荷清和创医疗科技有限公司', '翰博瑞强（上海）医药科技有限公司', '新桥通科技（大连）有限公司', '上海贝斯昂科生物科技有限公司', '上海海雁医药科技有限公司', '史赛克（北京）医疗器械有限公司', '腾盛博药医药技术（北京）有限公司', '君实润佳（上海）医药科技有限公司', '首药', '三生', '博纳西亚', '泰格捷通', '领博生物科技（杭州）有限公司', '深圳市海普洛斯生物科技有限公司', 'UCB Biopharma SPRL', '西藏海思科制药有限公司', '上海民为生物技术有限公司', '北京赛舒特医疗器械有限公司', '福建省闽东力捷迅药业股份有限 公司', '福建省闽东力捷迅药业股份有限公司', '上海炫脉医疗科技有限公司', '康融东方（广州）生物医药有限公司', '豪夫迈·罗氏有限公司', '鼎泰（南京）临床医学研究有限公司', '珠海通桥医疗科技有限公司', '诺桥医疗器械科技(烟台)有限公司', '北京协和药厂有限公司', '苏州海宇新辰医疗科技有限公司', '平安盐野义有限公司', '上海瑞宏迪医药有限公司', '苏州朗信医药科技有限公司', '常州药物研究所', '北京医睿达医药咨询有限公司', '江苏赛腾医疗科技有限公司', '万川医疗健康产业集团有限公司', '富士胶片（中国）投资有限公司', '强生（上海）医疗器材有限公司', '迈得派斯(上海)医药科技有限公司', '深圳福沃', '利洁时家化(中国)有限公司', '巴德医疗科技(上海)有限公司', '爱尔康(中国)眼科产品有限公司', '三星', '通用', '雅培(全球医疗健康公司)', '韩国大熊制药株式会社', '医科达（上海）医疗器械有限公司', '山德士（中国）制药有限公司', '蓝鸟', '罗氏诊断', '美纳里尼国际贸易(上海)有限公司', '欧加隆(上海)医药科技有限公司', '百特国际有限公司', 'A.O.史密斯（A.O.Smith）', '科赴Kenvue', '凯西 - Chiesi Pharmaceutical (Shangai) Co., Ltd', '以明生物医药科技（杭州）有限公司', '爱乔(上海)医疗科技有限公司', 'AQTIS Medical B.V.', '施慧达药业集团(吉林)有限公司', '中国医学科学院血液病医院（中国医学科学院血液学研究所）', '元羿生物科技（上海）有限公司', 'I-Mab Biopharma, US, Limited', '杭州舶临医药科技有限公司', '上海宝济药业股份有限公司', '加科思药业', '华融科创生物科技(天津）有限公司', '宝济药业', '维眸生物科技(上海)有限公司', '上海联影微电子科技有限公司', '山东华铂凯盛生物科技有限公司', '珠海市丽珠微球科技有限公司', '法伯远晨', '杭州禹泓医药科技有限公司', '石药集团巨石生物制药有限公司', '广东天科雅生物医药科技有限公司', '江西科睿药业有限公司', '鲁南制药集团股份有限公司', '铂生卓越生物科技（北京）有限公司', '北京索普兴大医药研究有限公司', '揽月生物医药科技（杭州）有限公司', '爱博睿美(成都)医疗科技有限公司', '广州天之恒医疗技术有限公司', '长沙海柯生物科技有限公司', '上海远诺恩医疗科技有限公司', '中国科学技术大学', '安立玺荣（上海）生物医药科技有限公司', '上海盛迪医药有限公司', '星奕昂（上海）生物科技有限公司', '上海荣瑞医药科技有限公司', '北京智能决策医疗科技有限公司', '启德医药科技（苏州）有限公司', '凯诺威医疗科技（武汉）有限公司', '无锡诺宇医药科技有限公司', '苏州冰晶智能医疗科技有限公司', '上海兆汇医药科技有限公司', '烟台蓝纳成生物技术有限公司', '北京中惠药业有限公司', '米度（南京）生物技术有限公司', '宜明凯尔生物医药技术（上海）有限公司', '成都微芯药业有限公司', '以明', '谱高医疗科技（南京）有限公司', '泰格', 'Pliant Therapeutics Inc', '白时益医药研发（北京）有限公司', '百时益医药研发（北京）有限公司', '星奕昂生物科技（上海）有限公司', '星奕昂生物科技（上海）有限公司', '上海兆汇医药科技有限公司', '康德弘翼', '广东龙创基药业有限公司', '广东龙创基药业有限公司', '法荟（北京）医疗科技有限公司', '罗氏诊断产品（上海）有限公司', '内蒙古康恩贝药业有限公司', '四川大学', '苏州丁孚靶点生物技术有限公司', '浙江鑫康合生物医药科技有限公司', '扬州一洋制药有限公司', '科睿克（北京）临床医学研究有限公司', '罗氏诊断产品（上海）有限公司', '波士顿科学', '上海益信佳生物科技有限公司', 'Wockhardt Bio AG', '浙江三生蔓迪药业有限公司', '上海复宏汉霖生物医药有限公司', '北京度衡之道医药科技有限公司', '深圳市纬康医疗科技有限公司', 'Gilead Sciences, Inc.', '北京赛特明强医药科技有限公司', '山东威高手术机器人有限公司', '上海泽纳仕生物科技有限公司', '广州来恩生物医药有限公司', '杏林中医药科技（广州）有限公司', '长沙泰和医院', '和铂医药（苏州）有限公司', '国科中子医疗科技有限公司', '上海领检科技有限公司', '广州再极医药科技有限公司', '上海贺维斯特医药科技有限公司', '江苏赛腾医疗科技有限公司', '山东第一医科大学第一附属医院', '成都青山利康药业股份有限公司', '上海荻硕贝肯基因科技有限公司', '上海康德弘翼', '福沃', '上海方予健康医药科技有限公司', '江苏先声', '正大天晴1', '上海恒润达生生物科技股份有限公司', '山东省肿瘤防治研究院（山东省肿瘤医院）', '合肥天汇孵化科技有限公司', '上海交通大学医学院附属新华医院', '合肥尚德药缘生物科技有限公司', '康融东方（广州）生物医药有限公司', '北京基石京准诊断科技有限公司 ', '精鼎医药研究开发（上海）有限公司', '深圳科兴药业有限公司', 'TRANSLATIONAL RESEARCH IN ONCOLOGY', '广州辑因医疗科技有限公司', '上海艾莎医学科技有限公司', '耀视（苏州）医疗科技有限公司', '吉林大学中日联谊医院', 'Profoundbio US Co.', '山东威高集团医用高分子制品股份有限公司', '晶核生物医药科技（上海）有限公司', '北京诺为力创医药科技发展有限公司 ', '北京复星医药科技开发有限公司', '赛德特生物制药有限公司', '太极集团重庆中药二厂有限公司', '上海太美星云数字科技有限公司', '北京康衍美辰医药科技有限公司', '康霖生物', '复旦大学附属中山医院', '杭州圣石科技股份有限公司', '北京豪迈东方医药科技发展有限公司', '天津恒瑞医药有限公司', '上海璞兮医疗技术服务有限公司', '凌意（杭州）生物科技有限公司', '浙江莎普爱思药业股份有限公司', 'Celgene Corporation', '杭州认识科技有限公司', '无锡赛比曼生物科技有限公司', '北京优迅医疗器械有限公司', '和径医药科技(上海)有限公司', '浙江大学医学院附属妇产科医院', '中山大学肿瘤防治中心（中山大学附属肿瘤医院、中山大学肿瘤研究所）', '艾威药业（珠海横琴）有限公司', '凯西', '金弗康生物科技（上海）股份有限公司 ', '茂行制药（苏州）有限公司', '浙江省台州医院', '上海本导基因技术有限公司', '来凯制药（宁波）有限公司', '上海瑞吉康生物医药有限公司', '上海洛启生物医药技术有限公司', '北京市普惠生物医学工程有限公司', '北京维斯迈医疗科技有限公司', '新羿制造科技（北京）有限公司', '君实润佳（上海）医药科技有限公司', '江苏恰瑞生物科技有限公司', '丹诺医药（苏州）有限公司', '爱科诺生物医药（香港）有限公司', '爱科诺生物医药（苏州）有限公司', '上海蔼睦医疗科技有限公司', '上海康抗生物技术有限公司', '上海挚盟医药科技有限公司', '广州知易生物科技有限公司', '广州凌腾生物医药有限公司', '上海倍而达药业有限公司', '典晶生物医药科技（苏州）有限公司', '江苏新元素医药科技有限公司', '阿斯利康全球研发（中国）有限公司', '上海首嘉医学临床研究有限公司', '图斯邦柯生物科技(苏州)有限公司', '北京泰杰伟业科技股份有限公司', '凡恩世制药（北京）有限公司', '尧唐（上海）生物科技有限公司', '凤麟核中科超精(南京)科技有限公司', '成都施贝康生物医药科技有限公司', '上海柯君医药科技有限公司', '南京再明医药有限公司', '靖因药业（上海）有限公司', '山东良福制药有限公司', '苏州普乐康医药科技有限公司', '上海生物制品研究所有限责任公司', '上海瑞宏迪医药有限公司', '天津同怡汉康医药科技有限公司', '益承康泰（厦门）生物科技有限公司', '广州市微眸医疗器械有限公司', '上海骊霄医疗技术有限公司', '北京万泰生物药业股份有限公司', '春立医疗', '艾尔建美学Allergan', '新基', '赫力昂', '泰尔茂株式会社（Terumo Corporation）', '上海壹典医药科技开发有限公司', '利奥制药 (LEO Pharma) ', '北京禾璞医疗科技有限公司', '威海洁瑞医用制品有限公司', '江苏势通生物科技有限公司', '江苏普力优创科技有限公司', '广州六顺生物科技有限公司', '北京舒曼德医药科技开发有限公司', 'MICROVENTION,INC.', '泰格捷通（北京）医药科技有限公司', '北京金瑞基业医药科技有限公司', '上海超阳药业有限公司', '北京赛升药业股份有限公司', '华润双鹤药业股份有限公司', '北京迈得诺医疗技术有限公司', '隆耀生物（宜兴）有限公司', '诺华（中国）生物医学研究有限公司', '阿斯利康全球研发（中国）有限公司', '广州医科大学附属市八医院', '标新生物医药科技（上海）有限公司', '易慕峰生物科技有限公司', '领航基因科技（杭州）有限公司', '上海生物制品研究所有限责任公司', '福欣药业', '四川安可康生物医药有限公司', '西安交通大学第一附属医院', '天津市肿瘤医院', '平顶山市第一人民医院', '体必康生物科技（广东）股份有限公司', '上海津曼特生物科技有限公司', '武汉兰丁智能医学股份有限公司', '上海佰翊医疗科技有限公司', '倍亮生技医药 (上海) 有限公司', '上海斯丹姆医药开发有限责任公司', '浙江特瑞思药业股份有限公司', '武汉普渡生物医药有限公司', '上海罗科医药信息咨询有限公司', '凯信远达医药（中国）有限公司', '杭州麦微医疗科技有限公司', '苏州诺洁贝生物技术有限公司', '艾维可生物科技有限公司', 'E.R.Squibb & Sons,L.L.C.', '福建省闽东力捷迅药业股份有限公司', '海南斯达制药有限公司', '湖南泰和医院管理有限公司', '上海复宏汉霖生物制药有限公司', '迈诺威（无锡）医药科技有限公司', '武汉金激光医疗科技有限公司', '北京智愈医疗科技有限公司', '武汉滨会生物科技股份有限公司生物创新园分公司', '泰州迈博太科药业有限公司', '康威（广州）生物科技有限公司', '上海上药中西制药有限公司', '浙江博崤生物制药有限公司', '浙江柏拉阿图医药科技有限公司', 'Pliant Therapeutics Inc', '苏州星明优健生物技术有限公司', '佛山瑞迪奥医疗系统有限公司', '浙江乾合畅脉医疗科技有限公司', '北京麦思迪国际医药科技有限公司', '科士华（南京）生物技术有限公司', '上海睿跃生物科技有限公司', '礼来', '华芢生物科技（青岛）有限公司', '斯丹姆', '深圳市星辰海医疗科技有限公司', 'Areteia Therapeutics, Inc', '北京世纪鼎晟国际生物技术有限公司', '北京抗创联生物制药技术研究有限公司', '南京海纳医药科技股份有限公司', '北京精医和生医疗科技有限公司', '浙江杭煜制药有限公司', '武汉和润瑞康生物科技有限公司', '成都景泽生物制药有限公司', '南京诺和欣医药科技有限公司', '浙江生创精准医疗科技有限公司', '天津艾迪研医药科技有限公司', '沛嘉医疗科技（苏州）有限公司', '北京景达生物科技有限公司', '华中科技大学同济医学院附属协和医院', '北京助研医学技术有限公司', 'BradyKnows', 'Chiesi Farmaceutici S.p.A.', '石药控股集团有限公司', '西安杨森制药有限公司', '河北菲尼斯生物技术有限公司', '武汉光谷中源药业有限公司', '恺兴生命科技（上海）有限公司', '柯惠医疗器材国际贸易（上海）有限公司', '阳光安津（南京）生物医药科技有限公司', '西安交通大学第二附属医院', '北京烁星生物医药科技有限公司', '北京奥泰康医药技术开发有限公司', '安徽中盛溯源生物科技有限公司', '上海焕擎医疗科技有限公司', '安锐生物医药科技(广州)有限公司', '广西金嗓子有限责任公司', '天新福（北京）医疗器材股份有限公司', '武汉朗来科技发展有限公司', '杭州圣庭医疗科技有限公司', '华夏生生药业（北京）有限公司', '三福生技股份有限公司', '杭州星赛瑞真生物技术有限公司', '威尚（上海）生物医药有限公司', '南京济斯贝医药科技有限公司', '杭州圣域生物医药科技有限公司', '鼎泰（南京）临床医学研究有限公司', '辰欣药业股份有限公司', '合源泓骐生物科技（上海）有限公司', '成都弘基生物科技有限公司', '辉瑞', '深圳市中科海世御生物科技有限公司', '浙江泉生生物科技有限公司', '百时美施贵宝（中国）投资有限公司', '精鼎医药研究开发（上海）有限公司', '元羿生物科技（上海）有限公司', '台州恩泽医疗中心（集团）', '厦门艾德生物医药科技有限公司', '北京强新制药有限公司', '鑫君特（苏州）医疗科技有限公司', '成都威斯津生物医药科技有限公司', '江苏得康生物科技有限公司', '山东省戴庄医院', '上海捷诺生物科技股份有限公司', '深圳市原力生命科学有限公司', '湖州惠中济世生物科技有限公司', '成都诺和晟泰生物科技有限公司', '湖南麦济生物技术有限公司', '北京盈凯赛威生物技术有限公司', '广东欢太科技有限公司', '上海核舟医药有限公司', '华普生物技术（河北）股份有限公司', '成都西岭源药业有限公司', '北京大学第三医院', '上海镔铁生物科技有限责任公司', '阿斯利康全球研发（中国）有限公司', '拜耳（中国）有限公司', '上海舶望制药有限公司', '默沙东研发（中国）有限公司', 'Anji Pharmaceuticals Inc安济药业', '江苏奥赛康药业有限公司', '葆元生物医药科技（杭州）有限公司', '四川海思科制药有限公司', '康方天成（广东）制药有限公司', '丽珠集团丽珠医药研究所', '北京诺华制药有限公司', '厦门特宝生物工程股份有限公司', '山东丰金生物医药有限公司', '山东轩竹医药科技有限公司', '苏州润新生物科技有限公司', '苏州铸正机器人有限公司', '爱恩康临床医学研究（北京）有限公司', '三菱商事（中国）有限公司', '大冢制药研发（北京）有限公司', '日本住友林业株式会社', '安徽安科生物工程（集团）股份有限公司', '大连万春布林医药有限公司', '第一三共制药（北京）有限公司', '缔脉生物医药科技（上海）有限公司', '北京东方略生物医药科技股份有限公司', '广州喜鹊医药有限公司', '上海海和药物研究开发股份有限公司', '杭州依图医疗技术有限公司', '上海昊海生物科技股份有限公司', '恒瑞源正（上海）生物科技有限公司', '恒翼生物医药（上海）股份有限公司', '嘉和生物药业有限公司', '交晨生物医药技术（上海）有限公司', '朗煜医药科技（杭州）有限公司', '立力科阿克赛诺（北京）医药研发咨询有限公司', '联药（上海）生物科技有限公司', '山东罗欣药业集团股份有限公司', '泰州迈博太科药业有限公司', '深圳市瑞迪生物医药有限公司', '上海来恩生物医药有限公司', '北京复星医药科技开发有限公司', '山东新时代药业有限公司', '深圳市亦诺微医药科技有限公司', '施慧达药业集团', '四川九章生物科技有限公司', '太景医药研发（北京）有限公司', '武汉朗来科技发展有限公司', '西藏晨泰医药科技有限公司', '北京艺妙医疗科技有限公司', '武汉友芝友生物制药股份有限公司', '广州誉衡生物科技有限公司', '勃林格殷格翰（中国）投资有限公司', 'Boston Scientific', '赛纽仕医药信息咨询（北京）有限公司', '成都艾伟孚生物科技有限公司', '北京爱尔默医药技术开发有限公司', '上海安臻医疗科技有限公司', '北京福爱乐科技发展有限公司', '北京凯吉特医药科技发展有限公司', '贝朗医疗（上海）国际贸易有限公司', '杭州贝瑞和康基因诊断技术有限公司', '北京泛亚同泽生物医学研究院有限公司', '方润医疗器械科技(上海)有限公司', '福州拓新天成生物科技有限公司', '郑州晟斯生物科技有限公司', '高德美医药（江苏）有限公司', '关节动力安达（天津）生物科技有限公司', '广东尊荣生物科技有限公司', '广州奥咨达医疗器械技术股份有限公司', '国药中生生物技术研究院有限公司', '北京海金格医药科技股份有限公司', '上海蕙新医疗科技有限公司', '吉林省博大伟业制药有限公司', '江苏亚邦强生药业有限公司', '国药控股江苏药事服务有限公司', '江西博恩锐尔生物科技有限公司', '上海锦葵医疗器械股份有限公司', '北京久事神康医疗科技有限公司', '德国卡特医疗有限公司', '北京凯瑞科德药物技术研究有限公司 ', '上海科棋药业科技有限公司', '杭州库博医药科技有限公司', '葵花药业集团股份有限公司', '北京乐维创信医药科技有限公司', '迈博斯生物医药（苏州）有限公司', '梅里埃（上海）生物制品有限公司', '北京诺禾致源科技股份有限公司', '南京诺加医药科技有限公司', '青岛威高医药科技有限公司', '杭州尚健生物技术有限公司', '沈阳海王生物技术有限公司', '施乐辉医用产品国际贸易（上海）有限公司', '斯丹姆（北京）医药技术集团股份有限公司', '上海斯丹赛生物技术有限公司', '苏州亚宝药物研发有限公司', '苏州茵络医疗器械有限公司', '天津冠勤医药科技有限公司', '微创神通医疗科技（上海）有限公司', '西安艾凯尔医疗科技有限公司', '西安蓝极医疗电子科技有限公司', '先健科技（深圳）有限公司', '苏州亿腾药品销售有限公司', '益方生物科技（上海）股份有限公司', '北京优尼康通医疗科技有限公司', '浙江新码生物医药有限公司', '天津致为医药科技有限公司', '广东众生睿创生物科技有限公司', '堃博生物科技（上海）有限公司', '江苏迈度药物研发有限公司', '丘以思（上海）医药信息咨询有限公司', '合肥天麦生物科技发展有限公司', '协和麒麟（中国）制药有限公司', '科望（苏州）生物医药科技有限公司', '苏州瑞博生物技术股份有限公司', '莫纳什大学', '上海艾迈医疗科技有限公司', '兴盟生物医药（苏州）有限公司', '海爱科百发生物医药技术有限公司', '爱萨尔生物科技有限公司', '百力司康生物医药（杭州）有限公司', '武汉博纳维科医疗器械有限公司', '沈阳天邦药业有限公司', '常州至善医疗科技有限公司', '兆科（广州）肿瘤药物有限公司', '派格生物医药（苏州）股份有限公司', '再鼎医药（上海）有限公司', '上海赛比曼生物科技有限公司', '南京传奇生物科技有限公司', '四川普锐特药业有限公司', '杭州艾森医药研究有限公司', '劲方医药科技（上海）有限公司', '南京从一医药科技有限公司', '上海仁会生物制药股份有限公司', '苏州欧米尼医药有限公司', '菲洋生物科技（吉林）有限公司', '北京合源汇丰医药科技有限公司', '美敦力（上海）管理有限公司', 'FIBROGEN Inc/纤维蛋白原公司', '上海益临思医药开发有限公司', '艾昆纬医药科技（上海）有限公司', '苏州泽璟生物制药股份有限公司', '无锡药明康德新药开发股份有限公司', '上海奥全生物医药科技有限公司', '苏州开拓药业股份有限公司', '四川国为制药有限公司', '天津索玛科技有限公司', '诺纳生物（苏州）有限公司', '杭州翰思生物医药有限公司', '广州再极医药科技有限公司', '安济盛生物医药技术（广州）有限公司', '上海玉曜生物医药科技有限公司', '昭衍（北京）医药科技有限公司', '泰州越洋医药开发有限公司', '北京卡替医疗技术有限公司', '凯莱英医药集团（天津）股份有限公司', '宝船生物医药科技（上海）有限公司', '首药控股（北京）股份有限公司', '来凯医药科技（上海）有限公司', '凯捷健生物技术咨询（北京）有限公司', '武汉楚精灵医疗科技有限公司', '泛海控股股份有限公司', '华东医药股份有限公司', '赛诺微医疗科技（北京）有限公司', '上海微创医疗器械（集团）有限公司', '宁波梅傲生物科技有限公司', '北京科创鼎诚医药科技有限公司', '礼进生物医药科技（上海）有限公司', '杭州思默医药科技有限公司', '天津麦迪唯美科技有限公司', '广州卫视博生物科技有限公司', '成都华健未来科技有限公司', '上海腾瑞制药股份有限公司', '百奥泰生物制药股份有限公司', '广州视景医疗软件有限公司', '银谷制药有限责任公司', 'Arog Pharmaceuticals/Arog制药公司', '南京希麦迪医药科技有限公司', 'Baxter Healthcare Corporation', '重庆复创医药研究有限公司', '武田药业有限公司', '广州麓鹏制药有限公司', '荣昌生物制药（烟台）股份有限公司', '乔治（北京）临床医学研究有限公司', '盛世泰科生物医药技术（苏州）有限公司', '通用电气医疗系统贸易发展（上海）有限公司', '博济医药科技股份有限公司', 'UCB Pharma优时比', '长春钻智制药有限公司', '北京觅瑞科技有限公司', '天津合美医药科技有限公司', '深圳微芯生物科技股份有限公司', '四川三叶草生物制药有限公司', '广州中恩医疗科技有限公司', '开拓者医学研究（上海）有限公司', '贝克曼库尔特商贸（中国）有限公司', '吉林敖东药业集团股份有限公司', '广州铭康生物工程有限公司', '深圳市塔吉瑞生物医药有限公司', '珠海丽凡达生物技术有限公司', '杭州优替济生科技有限公司', '南京丹瑞生物科技有限公司', '广州安好医药科技有限公司', '深圳市库珀科技发展有限公司', '上海鹰瞳医疗科技有限公司', '天津维泰科技有限公司', '合肥市佳和美康医疗技术有限公司', '北京度衡之道医药科技有限公司', '华志微创医疗科技（北京）有限公司', '吉林省科英激光股份有限公司', '北京西尔思科技有限公司', '苏州润迈德医疗科技有限公司', '苏州卡迪默克医疗器械有限公司', '北京莱尔生物医药科技有限公司', '北京至真堂中医科技有限公司', '青岛九远医疗科技有限公司', '格源致善（上海）生物科技有限公司', '远大医药（中国）有限公司', '北京迅识生物科技有限公司', '南方医科大学南方医院', '祐和医药科技（北京）有限公司', '上海渊兮医疗科技有限公司', '广东美捷威通生物科技有限公司', '北京凯芮特医药科技有限公司', '捷仕通医疗设备有限公司', '上海复硕生物科技有限公司', '甫康（上海）健康科技有限责任公司', '江苏省人民医院（南京医科大学第一附属医院）', '杭州索元生物医药股份有限公司', '中山市中智药业集团有限公司', '浙江星月生物科技股份有限公司', '鼎莘医药科技（杭州）有限公司', '深圳未知君生物科技有限公司', '北京斯特睿格医药技术有限责任公司', '达星悦通（大连）生物科技有限公司', '北京翰兰德医药科技发展有限公司', '上海嘉奥信息科技发展有限公司', '辟埃赛医药科技（上海）有限公司', '浙江杜比医疗科技有限公司', '北京优迅医疗器械有限公司', '上海凌仕医疗科技有限公司', '倍朝医疗科技（上海）有限公司', '烟台益诺依生物医药科技有限公司', '南京爱德程医药科技有限公司', '苏州韬略生物科技有限公司', '杰诺医学研究（北京）有限公司', '苏州吉美瑞生医学科技有限公司', '江苏柯菲平医药股份有限公司', '恩康药业科技（广州）有限公司', '卓阮医疗科技（苏州）有限公司', '石家庄智康弘仁新药开发有限公司', '成都凡诺西生物医药科技有限公司', '北京普祺医药科技有限公司', '卡尔·蔡司股份公司', '上海联合赛尔生物工程有限公司', '爱尔博（上海）医疗器械有限公司', '上海优卡迪生物医药科技有限公司', '日本脏器制药株式会社', '苏州中天医疗器械科技有限公司', '武汉华大吉诺因生物科技有限公司', '宁波胜杰康生物科技有限公司', '天津金匙医学科技有限公司', '鸿运华宁（杭州）生物医药有限公司', '上海家化研发中心', '重庆美莱德生物医药有限公司', '西安宇繁生物科技有限责任公司', '安徽埃克索医疗机器人有限公司', '北京佳德和细胞治疗技术有限公司', '深圳华大智造科技股份有限公司', '诺尔医疗（深圳）有限公司', '北京术锐技术有限公司', '赛生医药（中国）有限公司', '扬子江药业集团有限公司', '重庆宸安生物制药有限公司', '天津恒宇医疗科技有限公司', '深圳艾欣达伟医药科技有限公司', '威海威高激光医疗设备股份有限公司', '成都蓉生药业有限责任公司', '南盾科贸发展有限公司', '盛仑（天津）环保科技发展有限公司', '广州维力医疗器械股份有限公司', '北京时代优创科技有限公司', '上海凯利泰医疗科技股份有限公司', '山东泰泽惠康生物医药有限公司', '香雪生命科学技术（广东）有限公司', '上海元熙医药科技有限公司', '宁波健世科技股份有限公司', '瑞博奥（广州）生物科技股份有限公司', '山西振东药业有限公司', '脉金生物医药科技（上海）有限公司', '上海同联制药有限公司', '上海联影医疗科技股份有限公司', '北京先瑞达医疗科技有限公司', '无锡杰西医药股份有限公司', '邯郸制药股份有限公司', 'Medelis Inc', '吉利德科学公司', '盛禾（中国）生物制药有限公司', '安徽万邦医药科技有限公司', '北京利安康医药用品有限公司', '北京精诚医药科技有限公司', '盛泰康生命科学研究（山东）有限公司', '宁波熙健医药科技有限公司', '上海麦济生物技术有限公司', '天津凯诺医药科技发展有限公司', '西门子医学诊断产品（上海）有限公司', '凯德诺医疗器械 (武汉 )有限公司', '杭州协和医疗用品有限公司', '广州易介医疗科技有限公司', '杭州键嘉机器人有限公司', '北京因诺瑞康生物医药科技有限公司办', '北京博奥晶方生物科技有限公司', '艾棣维欣(苏州)生物制药有限公司', '上海尚睿医药科技有限公司', '卫拉诊断（新加坡）私人贸易有限公司', '武汉康复得生物科技股份有限公司', '正腾康生物科技（上海）有限公司', '嘉兴和剂药业有限公司', '北京丹序生物制药有限公司', '北京捷通康诺医药科技有限公司', '四川泸州步长生物制药有限公司', '迈杰转化医学研究(苏州)有限公司', '明济生物制药（北京）有限公司', '杭州瑞维特医疗科技有限公司', '元心科技（深圳）有限公司', '上海奕拓医疗科技有限公司', '青岛奥克生物开发有限公司', '南京优科生物医药股份有限公司', '上海魅丽纬叶医疗科技有限公司', '重庆秋纹生物技术有限公司', '丰晟医药科技有限公司', '卫材（中国）药业有限公司', '心擎医疗（苏州）股份有限公司', '北京泰杰伟业科技股份有限公司', '上海恒润达生生物科技股份有限公司', '北京兴宇中科科技开发股份有限公司', '上海韧致医学研究有限公司', '上海壹典医药科技开发有限公司', '飞利浦（中国）投资有限公司', '江苏启灏医疗科技有限公司', '珠海贝海生物技术有限公司', '广州爱思迈生物医药科技有限公司', '上海健信生物医药科技有限公司', '珠海泰诺麦博生物技术有限公司', '江西龙昌药业有限公司', '杭州颐源医药科技有限公司', '勤浩医药(苏州)有限公司', '深圳市康哲药业有限公司', '苏州赞荣医药科技有限公司', '北京东方百泰生物科技股份有限公司', '昆翎医药ClinChoice', '乐普(北京)医疗器械股份有限公司', '北京国信创新科技股份有限公司', '杭州德诺电生理医疗科技有限公司', '上海心志医疗科技有限公司', '北京阅影科技有限公司', '徐州思路迪药业有限公司', '上海凌先医药科技有限公司', '博生吉医药科技（苏州）有限公司', '中日友好医院', 'Tyligand Bioscience (Shanghai) Limited ', '杭州维纳安可医疗科技有限责任公司', '吉林亚泰中科医疗器械工程技术研究院股份有限公司', '杭州爱德程医药科技有限公司', '广州万孚卡蒂斯生物技术有限公司', '上海科赐医疗技术有限公司', '捷迈（上海）医疗国际贸易有限公司', '兰晟生物医药(苏州)有限公司', '益普生(天津)医药商贸有限公司', '同润生物医药（上海）有限公司', '北京迈迪顶峰试验医疗科技有限公司', '上海爱科百发生物医药技术股份有限公司', '锐得麦医药', '珠海贝斯昂科科技有限公司', '北京岐黄科技有限公司', '郑州深蓝海生物医药科技有限公司', '阿呆科技(北京)有限公司', '上海行深生物科技有限公司', '丹麦灵北制药有限公司', '北京京瑞天合医药科技发展有限公司', '辉大(上海)生物科技有限公司', '华氏医药', '广州众智汇健康科技有限公司', '中国罕见病联盟', '上海华禹生物科技有限公司', '广州达博生物制品有限公司', '上海沙砾生物科技有限公司', '上海汇伦医药股份有限公司', '杏国新药股份有限公司', '苏州康宁杰瑞生物科技有限公司', 'CASI pharmaceuticals.com', 'IBA Proton Therapy, Inc.', '凯博斯(上海)生物医药有限公司', '苏州宜联生物医药有限公司', '北京深睿博联科技有公司', '上海扶正天植生物科技有限公司', '杭州亿科医疗科技有限公司', '上海玮沐医疗科技有限公司', '北京华瑞康源生物科技发展有限公司', '上海赛默罗德生物科技有公司', '上海瑛派药业有限公司', '上海纳为生物技术有限公司', '礼新医药科技（上海）有限公司', '浙江狄赛生物科技有限公司', '无锡智康弘义生物科技有限公司', '北京博诺威医药科技发展有限公司', '山东博安生物技术股份有限公司', '烟台正海生物科技股份有限公司', '杰科（天津）生物医药有限公司', '广州百暨基因科技有限公司', '晟科药业 （江苏）有限公司', '广州昂科免疫科技有限公司', '江苏晟斯生物制药有限公司', '杭州阿诺生物医药科技有限公司', '华科精准（北京）医疗科技有限公司', '北京裕恒佳科技有限公司', '箕星药业科技（上海）有限公司', '博际生物医药科技（杭州）有限公司', '杭州和正医药有限公司', '杭州端佑医疗科技有限公司', '百多力股份有限公司', '兰州生物制品研究所有限责任公司', '武汉生物制品研究所有限责任公司', '艾昆纬企业管理咨询（上海）有限公司', '中派科技（深圳）有限责任公司', '捷思英达医药技术（上海）有限公司', '同宜医药（苏州）有限公司', '海杰亚(北京)医疗器械有限公司', '北京化药科创医药科技发展有限公司', '上海睿刀医疗科技有限公司', '广州海博特医药科技有限公司', '广州循证医药科技有限公司', '深圳惠泰医疗器械股份有限公司', '北京联斯达医药科技发展有限公司', '珠海岐微生物科技有限公司', '上海杏脉信息科技有限公司', '上海则正医药科技股份有限公司', '上海默沙东医药贸易有限公司', '西斯比亚（北京）医药技术研究有限责任公司', '凯理斯医药科技发展（上海）有限公司', '湃朗瑞医药科技（北京）有限公司', '深圳君圣泰生物技术有限公司', '歌礼药业（浙江）有限公司', '山东先声生物制药有限公司', '珠海市丽珠单抗生物技术有限公司', '四川科伦药物研究院有限公司', '信立泰（成都）生物技术有限公司', '广州顺健生物医药科技有限公司', '深圳市海普瑞药业集团股份有限公司', '上海昀怡健康科技发展有限公司', '上海怀越生物科技有限公司', '江苏怀瑜药业有限公司', '山东龙昌药业有限公司', '福建和瑞基因科技有限公司', '上海海天医药科技开发有限公司', '深圳半岛医疗有限公司', '重庆华邦制药股份有限公司', '美林美邦(厦门)生物科技有限公司', '药捷安康（南京）科技股份有限公司', '深圳市真兴医药技术有限公司', '上海莱馥医疗科技有限公司', '瑞创生物技术有限公司', '海创药业股份有限公司', '广东省临床试验协会', '北京中因科技有限公司', '上海歌斐木生物医药科技有限公司', '健艾仕生物医药科技(杭州)有限公司', '上海导向医疗系统有限公司', '上海微盾医疗科技有限公司', '天劢源和生物医药（上海）有限公司', '浙江省肿瘤医院', '上海益思妙医疗器械有限公司', '上海岸阔医药科技有限公司', '武汉纽福斯生物科技有限公司', '南京圣德医疗科技有限公司', '普瑞基准生物医药(苏州)有限公司', '北京奕华医院管理有限公司', '北京禾润东方医药科技有限公司', '北京和信康科技有限公司', '成都金瑞基业生物科技有限公司', '上海睿昂基因科技股份有限公司', '凯信远达医药(中国)有限公司', '轩竹生物科技股份有限公司', '天士力国际基因网络药物创新中心有限公司', '北京大学人民医院（北京大学第二临床医学院）', '江苏正大丰海制药有限公司', '北京合生基因科技有限公司', '思路迪生物医药（上海）有限公司', '芯视界（北京）科技有限公司', '重庆迪康尔乐制药有限公司', '星汉德生物医药（大连）有限公司', '乐普生物科技股份有限公司', '成都赛拉诺医疗科技有限公司', '北京阿迈特医疗器械有限公司 ', '医渡云（北京）技术有限公司', 'Merck Healthcare KGaA', '泰普生物科学（中国）有限公司', '南京凯地医疗技术有限公司', '国家高性能医疗器械研究中心', '应脉医疗科技（上海）有限公司', '北京禾玥医药科技有限公司', '谱创医疗科技（上海）有限公司', '北京新唯医药科技有限公司', '锐讯博星生物医药（苏州）有限公司', '深圳市赛禾医疗技术有限公司', '杭州中美华东制药有限公司', '新领医药技术（深圳）有限公司', '上海邦耀生物科技有限公司', '浙江康德莱医疗器械股份有限公司', '上海深至信息科技有限公司', '上海诗健生物科技有限公司', '上海泉生生物科技有限公司', '诺合泰生物科技（重庆）有限公司', '江苏华放医疗科技有限公司', '深圳市南药科技有限公司', '深圳高性能医疗器械国家研究院有限公司', '上海朗昇生物科技有限公司', '华西临床研究中心有限公司', '成都百利多特生物药业有限责任公司', '上海葆正医药科技有限公司', '杭州觅因生物科技有限公司', '好一生（北京）医药科技有限公司', '北京易普康达医疗科技有限公司', '广东鹍鹏肽灵生物科技有限公司', '深圳闪量科技有限公司', '上海中耀生物科技有限公司', '南京济群医药科技股份有限公司', '南昌弘益科技药业有限公司', '北京同仁医院', '深圳福沃药业有限公司', '杭州高田生物医药有限公司', '圣兰格（北京）医药科技开发有限公司', '上海御瓣医疗科技有限公司', '瀚晖制药有限公司', '浙江养生堂生物科技有限公司', '杭州华迈医疗科技有限公司', '湖南九典制药股份有限公司', '北京星职场网络科技有限公司', '北京遥领医疗科技有限公司', '青岛华赛伯曼医学细胞生物有限公司', '上海佳沐垚医疗科技有限公司', '优锐医药科技（上海）有限公司', '上海融脉医疗科技有限公司', '兰州生物技术开发有限公司', '杭州天龙药业有限公司', '宁波圣健责任有限公司', '深圳市铱硙医疗科技有限公司', '山东亨利医药科技有限责任公司', '中山莱博瑞辰生物医药有限公司', '天津高斯科技有限公司', '江苏豪思生物科技有限公司', '上海康景生物医药科技有限公司', '广州嘉越医药科技有限公司', '昌郁医药（上海）有限公司', '拜奥新管理（上海）有限公司', '乐普医学电子仪器股份有限公司', '启元生物（杭州）有限公司', '北京光辉天成医疗科技有限公司', '杭州剂泰医药科技有限责任公司', '重庆恒真维实医药科技有限公司', '江西山香药业有限公司', '杭州翱锐生物科技有限公司', '国健呈诺生物科技（北京）有限公司', '苏州智核生物医药科技有限公司', '济南合泰医药技术有限公司', '中生复诺健生物科技(上海)有限公司', '上海瑞金医院', '源方（北京）医药科技有限公司', '冰洲石生物科技(上海)有限公司', '杭州阿特瑞科技有限公司', '慕恩（广州）生物科技有限公司', '微泰医疗器械（杭州）股份有限公司', '上海奕谱生物科技有限公司', '北京儿童医院', '广东中昊药业有限公司', '仁景(苏州)生物科技有限公司', '北京惠每云科技有限公司', '深圳善康医药科技股份有限公司', '臻悦生物科技江苏有限公司', '北京伯汇生物技术有限公司', '丰凯利医疗器械（上海）有限公司', '齐聚医疗科技(上海)有限公司', '上海微创心通医疗科技有限公司', '苏州旺山旺水生物医药股份有限公司', '北京品驰医疗设备股份有限公司', '杭州神络医疗科技有限公司', '深圳麦克韦尔科技有限公司', '普棋', '北京志道生物科技有限公司', '安徽中盛溯源生物科技有限公司', '浙江霍德生物工程有限公司', '宁波微影', '丽彩甘肃西峰制药有限公司', '苏州新格元生物科技有限公司', '湖南恒天医药科技有限公司', '泰州泰格捷通医药科技有限公司', '南京立顺康达医药科技有限公司', '上海华聆人工耳医疗科技有限公司', '北京珅诺基医药科技有限公司 ', '南京宽诚科技有限公司', '赛诺哈勃药业(成都)有限公司', '北京志健金瑞生物医药科技有限公司', '安徽中科拓苒药物科学研究有限公司', '北京大熊伟业医药科技有限公司', '无锡济煜山禾药业股份有限公司', '上海礼邦医药科技有限公司', '医介信息技术(上海)有限公司', '神济昌华(北京)生物科技有限公司', '杭州睿笛生物科技有限公司', '深圳医克生物医药有限公司', '浙江海昶生物医药技术有限公司', '君乐宝乳业集团有限公司', '华道（上海）生物医药有限公司', '达孜县君合科技有限公司', '上海捷易生物科技有限公司', '爱美客技术发展股份有限公司', '北京珅诺基医药科技有限公司', '上海日馨医药科技股份有限公司', '杭州先为达生物科技有限公司', '长沙慧维智能医疗科技有限公司', '世亦临研(北京)医药科技有限公司', '成都纳海高科生物科技有限公司', '天津尚德药缘科技股份有限公司', '上海鑫律通生命科技有限公司', '杭州璞睿生命科技有限公司', '康德（深圳）生物技术有限公司', '重庆智翔金泰生物制药股份有限公司', '深圳正岸健康科技有限公司', '联仁健康医疗大数据科技股份有限公司', '广州安必平医药科技股份有限公司', '博品骨德生物医药科技(上海)有限公司', '北京可瑞生物科技有限公司', '北京华昊中天生物医药股份有限公司', '科凯(南通)生命科学有限公司', '北京市普惠生物医学工程有限公司', '深圳康诺思腾科技有限公司', '上海景泽生物技术有限公司', '上海启功医疗科技有限公司', '江苏美克医学技术有限公司', '凯思凯迪(上海)医药科技有限公司', '北京纳通医用机器人科技有限公司', '浙江微度医疗器械有限公司', '骏实生物科技（上海）有限公司', '北京康派特医疗器械有限公司', '上海嘉葆药银医药科技有限公司', '冠昊生物科技股份有限公司', '山东鲁抗好丽友生物技术开发有限公司', '上海瑞岸医药科技发展有限公司', '扬州中宝药业股份有限公司', '北京联众泰克科技有限公司', '维申医药科技（上海）有限公司', '合肥医工医药股份有限公司', '浙江大学医学院附属第一医院', '海南博研医学研究有限公司', '湖北梦阳药业股份有限公司', '湘北威尔曼制药股份有限公司', '河南真实生物科技有限公司', '合肥瀚科迈博生物技术有限公司', '齐育医疗科技(泰州)有限公司', '羿尊生物医药(浙江)有限公司', '杭州邦顺制药有限公司', '药研社(江苏)医药科技有限公司', '深圳市先康达生命科学有限公司', '君岳医药科技(上海)有限公司', '大连富生天然药物开发有限公司', '吉林省集安益盛药业股份有限公司', '禾木（中国）生物工程有限公司', '上海呈源录本生物技术有限公司', '江苏拉曼医疗设备有限公司', '东莞市东阳光生物药研发有限公司', '杭州旸顺医疗科技有限公司', '润生药物有限公司', '上海汇禾医疗科技有限公司', '杭州佳量医疗科技有限公司', '北京中关村水木医疗科技有限公司', '南京申基医药科技有限公司', '北京善芃科技发展有限公司', '广州来恩生物医药有限公司', '上海博脉安医疗科技有限公司', '天津橡鑫医疗器械有限公司', '江苏势通生物科技有限公司', '深圳市绘云生物科技有限公司', '景昱医疗科技（苏州）股份有限公司', '苏州鑫康合生物医药科技有限公司', '北京中域科祥医药科技有限公司', '海雅美生物技术（珠海）有限公司', '河北森朗生物科技有限公司', '科济生物医药（上海）有限公司', '浙江中创生物医药有限公司', '安徽丹大生物科技有限公司', '北京长木谷医疗科技有限公司', '润佳（苏州）医药科技有限公司', '浙江道尔生物科技有限公司', '上海济煜医药科技有限公司', '深圳市瓴方生物医药科技有限公司', '吉瑞医药（中国）有限公司', '杭州瑞普基因科技有限公司', '艾瑞嘉医药研发（上海）有限公司', '深圳扬厉医药技术有限公司', '江西孚创医疗科技有限公司', '安立玺荣（上海）生物医药科技有限公司', '湖南安泰康成生物科技有限公司', '成都纽瑞特医疗科技股份有限公司', '江苏知原药业股份有限公司', '北京易启医药科技有限公司', '上海微知卓生物科技有限公司', '广州润尔信息科技有限公司', '瀚芯医疗科技(深圳)有限公司', '睿诺医疗科技（上海）有限公司', '浙江萃泽医药科技有限公司', '福建盛迪医药有限公司', '北京盈科瑞生物医药研究有限公司', '科兴生物制药股份有限公司', '四川厌氧生物科技有限责任公司', '浙江圣兆药物科技股份有限公司', '深圳创芯技术股份有限公司', '参天制药（中国）公司', '微创优通医疗科技（嘉兴）有限公司', '康龙化成(北京)新药技术股份有限公司', '艾斯拓康医药(北京)有限公司', '上海上药创新医药技术有限公司', '零氪科技（北京）有限公司', '诺为泰医药科技（上海）有限公司', '浙江孕橙医疗科技有限公司', '湖南光琇高新生命科技有限公司', '苏州浦合医药科技有限公司', '华领医药', '河北揽月生物科技有限公司', '广州凯普医药科技有限公司', '广州凯瑞医药有限公司', '浙江太美医疗科技股份有限公司', '嘉兴安帝康生物科技有限公司', '前沿生物药业（南京）股份有限公司', '四川弘合生物科技有限公司', '深圳市资福医疗技术有限公司', '北京美欧斯医疗科技有限公司', '诺迈西（上海）医药科技有限公司', '江苏人民医院', '北京阳光诺和药物研究股份有限公司', '成都赜灵生物医药科技有限公司', '北京君科华元医药科技有限公司', '广州市丹蓝生物科技有限公司', '武汉康录生物技术有限公司', '北京上予医药科技有限公司', '天津键凯科技有限公司', '君合盟生物制药（杭州）有限公司', '南芯芯仪(广州)制造有限公司', '四川至善唯新生物科技有限公司', 'Edwards(Shanghai) Medical Products Co, Ltd.', '斯微（上海）生物科技有限公司', '信瑞诺医药(上海)有限公司', '密尔医疗科技（深圳）有限公司', '上海勋和医药科技有限公司', '锐正基因（苏州）有限公司', '南京世和基因生物技术股份有限公司', '浙江时迈药业有限公司', '基立福医药科技（上海）有限公司', '苏州恒瑞宏远医疗科技有限公司', '上海百润医药科技有限公司', '博雅辑因（北京）生物科技有限公司', '上海微创旋律医疗科技有限公司', '成都赜灵', '北京善行医疗科技有限公司', '长春欧邦生物科技有限公司', '河北普尼医疗科技有限公司', '重庆核欣医药科技有限公司', '深圳市晋百慧生物有限公司', '江苏佳时泰医药生物科技有限公司', '启愈生物技术(上海)有限公司', '深圳惟德精准医疗科技有限公司', '杭州艾氪医药科技有限公司', '上海赛傲生物技术有限公司', '杏树林信息技术（北京）有限公司', '北京康蒂尼药业股份有限公司', '无锡泰格医药科技有限公司', '上海普延医疗设备有限公司', '北京韩美药品有限公司', '北京药海宁康医药科技有限公司', '广东博迈医疗科技股份有限公司', '河南赛美视生物科技有限公司', '杭州糖吉医疗科技有限公司', '上海埃秀马生物科技有限公司', '璧辰（上海）医药科技有限公司', '南京清普生物科技有限公司', '北京基石京准诊断科技有限公司', '北京诺康达医药科技股份有限公司', '瑅安生物医药（杭州）有限公司', '成都海博为药业有限公司', '东莞天天向上医疗科技有限公司', '新景智源生物科技（苏州）有限公司', '安徽省新星药物开发有责任公司', '合肥一攻医药股份有限公司', '神州医疗生物科技（北京）有限公司', '上海弼领生物技术有限公司', '天辰生物医药（苏州）有限公司', '长春博迅生物技术有限责任公司', '一力制药股份有限公司', '广州市首歌生物科技有限公司', '雄志医疗设备科技（苏州）有限公司', '苏州贝康医疗器械有限公司', '青龙高科技术股份有限公司', '无锡肺畅医疗器械有限责任公司', '普方生物制药(苏州)有限公司', '北京怡成生物电子技术股份有限公司', '英诺湖医药(杭州)有限公司', '上海先祥医药科技有限公司', '北京科鹏医疗器械有限公司', '普众发现医药科技（上海）有限公司', '杭州凡泰生物科技有限公司', '江苏万邦生化医药集团有限责任公司', '北京思睦瑞科医药科技股份有限公司', '浙江顶健生物细胞管理有限公司', '南京诺惟生物科技有限公司', '江苏复星医药销售有限公司', '杭州安道药业有限公司', '北京安龙生物医药有限公司', '杭州梧桐树药业有限公司', '北京清医泰克医药科技有限公司', '上海联影医疗科技有限公司', '广州达安基因股份有限公司', '瑞阳制药股份有限公司', '惠和生物技术（上海）有限公司', '苏州艾博生物科技有限公司', '沪创医疗科技（上海）有限公司', 'QIVA GLOBAL LIMITED', '重庆市药研院制药有限公司', '广州金石科技发展有限公司', '上海溯湃医疗科技有限公司', '北京天助瑞畅医疗技术有限公司', '北京鞍石生物科技股份有限公司', '艾缇亚（上海）制药有限公司', '艾威药业（珠海横琴）有限公司', '原启生物科技（上海）有限责任公司', '深圳赛陆医疗科技有限公司', '福建海西新药创制股份有限公司', '苏州复融生物技术有限公司', '上海柏全生物科技有限公司', '江苏中新医药有限公司', '辽宁垠艺生物科技股份有限公司', '四川汇宇制药股份有限公司', '杭州小牛医药科技有限公司', '科力美拓（北京）科技发展有限公司', '天翊永聚医疗科技（苏州）有限公司', '盘锦辽油宝石花医院', '江苏康缘药业股份有限公司 ', '清源泰硕', '上海畅德医疗科技有限公司', '西湖制药（杭州）有限公司', '天津恒瑞医药有限公司', '杭州格博生物医药有限公司', '优领医药科技（上海）有限公司', '苏州澳宗生物科技有限公司', '苏州沙砾生物科技有限公司', '上海美悦生物科技发展有限公司', '重庆誉颜制药有限公司', '澜智译', '广州康近医疗技术有限公司', '瑞士欧姆制药有限公司', '北京瑷格干细胞科技有限公司', '珠海市立通生物科技有限公司', '惠升生物制药股份有限公司', '广州保瑞医疗技术有限公司', '深圳市新樾生物科技有限公司', '成都科岭源医药技术有限公司', '安达生物药物开发（深圳）有限公司', '北京卓越未来国际医药科技发展有限公司', '晶核生物医药科技（上海）有限公司', '广州必贝特医药股份有限公司', '夸克侠科技有限公司', '上海聚领瑞科医药有限责任公司', '成都分迪药业有限公司', '江苏孟德尔基因科技有限公司', '武汉波睿达生物科技有限公司', '杭州纽安津生物科技有限公司', '浙江康佰裕生物科技有限公司', '美珞医学科技(上海)有限公司', '杭州矩正医疗科技有限公司', '上海妙灵生物工程有限公司', '海南苏生生物科技有限公司', '山东泉港药业有限公司', '上海术之道机器人有限公司', '北京层浪生物科技有限公司', '明澈生物科技（苏州）有限公司', '南京昱众医药科技有限公司', '赛岚医药科技(深圳)有限公司', '湖南九典制药', '北京透彻未来科技有限公司', '亿一生物制药(北京)有限公司', '上海亲合力生物医药科技股份有限公司', '重庆科润生物医药研发有限公司', '成都可恩生物科技有限公司', '同光（昆山）生物科技有限公司', '江苏硕世生物科技股份有限公司', '北京尚宁科智医疗器械有限公司', '江苏康禾生物制药有限公司', '道恩可医疗科技（上海）有限公司', '南京宁丹新药技术有限公司', '诺一迈尔（苏州）医学科技有限公司', '年衍药业（上海）有限公司', '江苏华越医疗器械投资有限公司', '安庆毕方医疗科技有限公司', '鼐济医药科技有限公司', '四川锦江电子医疗器械科技股份有限公司', '辽宁康辰诺信医药科技有限公司', '北京奥迪特医药科技有限公司', '甲骨文(中国)有限公司', 'Cerner Enviza an Oracle Company', '苏州神曦兴盛生物医药有限公司', '武汉凯德基诺生物技术有限公司', '浙江柏拉阿图医药科技有限公司', '纽迪希亚(医疗保健公司)', '博尔诚（北京）科技有限公司', '深圳硅基传感科技有限公司', '法荟(北京)医疗科技有限公司', '武汉亚洲医通临床医学研究有限公司', '上海卓昕医疗科技有限公司', '天津济坤医药科技有限公司', '浙江崇山生物制品有限公司', '丹娜（天津）生物科技股份有限公司', '江苏星盛新辉医药有限公司 ', '常州集硕医疗器械有限公司', '北京禾璞医疗科技有限公司', '上海跃赛生物科技有限公司', '臻赫医药(杭州)有限公司', '爱德华（上海）医疗用品有限公司', '比逊（广州）医疗科技有限公司', '广东恒瑞医药有限公司', '科弈（浙江）药业科技有限公司', '科塞尔医疗科技（苏州）有限公司', '上海怡豪生物科技有限公司', '九天生物医药（上海）有限公司', 'Abivax', 'Medelis Inc', '一力制药股份有限公司', '上海凌仕医疗科技有限公司', '上海凯利泰医疗科技股份有限公司', '上海则正医药科技股份有限公司', '上海康德弘翼医学临床研究有限公司', '上海有临医药科技有限公司', '上海梅斯医药科技有限公司', '上海瑞岸医药科技发展有限公司', '上海用正医药科技有限公司', '上海百利佳生物医药科技有限公司', '上海砝码斯医药生物科技有限公司', '上海药明津石医药科技有限公司', '上海谊众药业股份有限公司', '上海韧致医学研究有限公司', '世亦临研(北京)医药科技有限公司', '中国农业大学', '中派科技（深圳）有限责任公司', '中生尚健生物医药（杭州）有限公司', '丰晟医药科技有限公司', '乔治（北京）临床医学研究有限公司', '倍朝医疗科技（上海）有限公司', '健康元药业集团股份有限公司', '健康元药业集团股份有限公司', '凯莱英医药集团（天津）股份有限公司', '北京MEDPACE医药科技有限公司', '北京乐维创信医药科技有限公司', '北京亦度正康健康科技有限公司', '北京兴德通医药科技股份有限公司', '北京凯普顿医药科技开发有限公司', '北京凯芮特医药科技有限公司', '北京创立科创医药技术开发有限公司', '北京华氏康源医药科技有限公司', '北京卓越未来国际医药科技发展有限公司', '北京博润阳光科技有限公司', '北京善芃科技发展有限公司', '北京复星医药科技开发有限公司', '北京奥迪特医药科技有限公司', '北京捷通康诺医药科技有限公司', '北京斯特睿格医药技术有限责任公司', '北京新唯医药科技有限公司', '北京新领先医药科技发展有限公司', '北京时代优创科技有限公司', '北京星职场网络科技有限公司', '北京春天医药科技发展有限公司', '北京水木菁创医药科技有限公司', '北京禾润东方医药科技有限公司', '北京禾玥医药科技有限公司', '北京科林利康医学研究有限公司', '北京精诚医药科技有限公司', '北京精诚通医药科技有限公司', '北京美欧斯医疗科技有限公司', '北京翰兰德医药科技发展有限公司', '北京药海宁康医药科技有限公司', '北京诺和德美医药技术有限公司', '北京遥领医疗科技有限公司', '北京鑫康合生物医药科技有限公司', '北海康成（苏州）生物制药有限公司', '医来医往（北京）科技有限公司', '医渡云（北京）技术有限公司', '南京从一医药科技有限公司', '南京宁丹新药技术有限公司', '南京希麦迪医药科技有限公司', '南京引光医药科技有限公司', '南京方腾医药技术有限公司', '南京正大天晴制药有限公司', '南京诺加医药科技有限公司', '南京阿尔法医学有限公司', '博济医药科技股份有限公司', '博纳西亚（合肥）医药科技有限公司', '合肥科颖医药科技有限公司', '国信医药科技（北京）有限公司', '圣兰格（北京）医药科技开发有限公司', '圣方（上海）医药研发有限公司', '天津冠勤医药科技有限公司', '天津凯诺医药科技发展有限公司', '天津开心生活科技有限公司', '天津致为医药科技有限公司', '天津麦迪唯美科技有限公司', '天翊微创医疗科技(常州)有限公司', '夸克侠科技有限公司', '安庆毕方医疗科技有限公司', '安徽万邦医药科技有限公司', '富启睿医药研发（北京）有限公司上海分公司', '希米科医药技术发展（北京）有限公司', '希米科医药技术发展（北京）有限公司', '广州九泰药械技术有限公司', '广州奥咨达医疗器械技术股份有限公司', '广州循证医药科技有限公司', '开拓者医学研究（上海）有限公司', '恺兴生命科技（上海）有限公司', '成都赛拉诺医疗科技有限公司', '斯丹姆（北京）医药技术集团股份有限公司', '无锡泰格医药科技有限公司', '昆翎医药ClinChoice', '普瑞盛（北京）医药科技开发股份有限公司', '杭州德晋医疗科技有限公司', '杭州泰格医药科技股份有限公司', '杭州艾氪医药科技有限公司', '杭州觅因生物科技有限公司', '杰诺医学研究（北京）有限公司', '武汉亚洲医通临床医学研究有限公司', '武汉人福药业有限责任公司', '武汉致众科技股份有限公司', '比逊（广州）医疗科技有限公司', '永铭诚道（北京）医学科技股份有限公司', '江苏复星医药销售有限公司', '江苏奥赛康药业有限公司', '江苏礼华生物技术有限公司', '江苏诺泰澳赛诺生物制药股份有限公司', '法荟(北京)医疗科技有限公司', '泛海控股股份有限公司', '济南智同医药科技有限公司', '海南博研医学研究有限公司', '润东医药研发（上海）有限公司', '湃朗瑞医药科技（北京）有限公司', '湖北生物医药产业技术研究院有限公司', '湖北生物医药产业技术研究院有限公司', '湖南慧泽生物医药科技有限公司', '瑞诵(上海) 医疗科技有限公司', '百时益医药研发（北京）有限公司', '百时益医药研发（北京）有限公司', '百试达（上海）医药科技股份有限公司', '盛恩（北京）医药科技有限公司', '礼新医药科技（上海）有限公司', '立力科阿克赛诺（北京）医药研发咨询有限公司', '精鼎医药研究开发（上海）有限公司', '美珞医学科技(上海)有限公司', '联仁健康医疗大数据科技股份有限公司', '艾昆纬企业管理咨询（上海）有限公司', '艾瑞嘉医药研发（上海）有限公司', '苏州普蒂德生物医药科技有限公司', '西安循证医药科技有限公司', '西安艾凯尔医疗科技有限公司', '西斯比亚（北京）医药技术研究有限责任公司', '诺为泰医药科技（上海）有限公司', '诺思格（北京）医药科技股份有限公司', '赛生医药研发（上海）有限公司', '赛纽仕医药信息咨询（北京）有限公司', '辟埃赛医药科技（上海）有限公司', '辽宁康辰诺信医药科技有限公司', '达孜县君合科技有限公司', '迈威（上海）生物科技股份有限公司', '迈迪思创（北京）科技发展有限公司', '郑州深蓝海生物医药科技有限公司', '重庆恒真维实医药科技有限公司', '重庆美莱德生物医药有限公司', '长沙先领医药科技有限公司', '长沙都正生物科技股份有限公司', '阿斯利康投资（中国）有限公司', '鼎泰（南京）临床医学研究有限公司', 'Abivax', 'Medelis Inc', '一力制药股份有限公司', '上海凌仕医疗科技有限公司', '上海凯利泰医疗科技股份有限公司', '上海则正医药科技股份有限公司', '上海康德弘翼医学临床研究有限公司', '上海有临医药科技有限公司', '上海梅斯医药科技有限公司', '上海瑞岸医药科技发展有限公司', '上海用正医药科技有限公司', '上海百利佳生物医药科技有限公司', '上海砝码斯医药生物科技有限公司', '上海药明津石医药科技有限公司', '上海谊众药业股份有限公司', '上海韧致医学研究有限公司', '世亦临研(北京)医药科技有限公司', '中国农业大学', '中派科技（深圳）有限责任公司', '中生尚健生物医药（杭州）有限公司', '丰晟医药科技有限公司', '乔治（北京）临床医学研究有限公司', '倍朝医疗科技（上海）有限公司', '凯莱英医药集团（天津）股份有限公司', '北京MEDPACE医药科技有限公司', '北京乐维创信医药科技有限公司', '北京亦度正康健康科技有限公司', '北京兴德通医药科技股份有限公司', '北京凯普顿医药科技开发有限公司', '北京凯芮特医药科技有限公司', '北京创立科创医药技术开发有限公司', '北京华氏康源医药科技有限公司', '北京卓越未来国际医药科技发展有限公司', '北京博润阳光科技有限公司', '北京善芃科技发展有限公司', '北京复星医药科技开发有限公司', '北京奥迪特医药科技有限公司', '北京捷通康诺医药科技有限公司', '北京斯特睿格医药技术有限责任公司', '北京新唯医药科技有限公司', '北京新领先医药科技发展有限公司', '北京时代优创科技有限公司', '北京星职场网络科技有限公司', '北京春天医药科技发展有限公司', '北京水木菁创医药科技有限公司', '北京禾润东方医药科技有限公司', '北京禾玥医药科技有限公司', '北京精诚医药科技有限公司', '北京精诚通医药科技有限公司', '北京美欧斯医疗科技有限公司', '北京翰兰德医药科技发展有限公司', '北京药海宁康医药科技有限公司', '北京诺和德美医药技术有限公司', '北京遥领医疗科技有限公司', '北京鑫康合生物医药科技有限公司', '医来医往（北京）科技有限公司', '医渡云（北京）技术有限公司', '南京从一医药科技有限公司', '南京宁丹新药技术有限公司', '南京希麦迪医药科技有限公司', '南京引光医药科技有限公司', '南京方腾医药技术有限公司', '南京正大天晴制药有限公司', '南京诺加医药科技有限公司', '南京阿尔法医学有限公司', '博济医药科技股份有限公司', '博纳西亚（合肥）医药科技有限公司', '合肥科颖医药科技有限公司', '国信医药科技（北京）有限公司', '圣兰格（北京）医药科技开发有限公司', '圣方（上海）医药研发有限公司', '天津冠勤医药科技有限公司', '天津凯诺医药科技发展有限公司', '天津开心生活科技有限公司', '天津致为医药科技有限公司', '天津麦迪唯美科技有限公司', '天翊微创医疗科技(常州)有限公司', '夸克侠科技有限公司', '安庆毕方医疗科技有限公司', '安徽万邦医药科技有限公司', '富启睿医药研发（北京）有限公司上海分公司', '希米科医药技术发展（北京）有限公司', '广州九泰药械技术有限公司', '广州奥咨达医疗器械技术股份有限公司', '广州循证医药科技有限公司', '开拓者医学研究（上海）有限公司', '成都赛拉诺医疗科技有限公司', '斯丹姆（北京）医药技术集团股份有限公司', '无锡泰格医药科技有限公司', '普瑞盛（北京）医药科技开发股份有限公司', '杭州泰格医药科技股份有限公司', '杭州艾氪医药科技有限公司', '杭州觅因生物科技有限公司', '杰诺医学研究（北京）有限公司', '武汉亚洲医通临床医学研究有限公司', '武汉人福药业有限责任公司', '武汉致众科技股份有限公司', '比逊（广州）医疗科技有限公司', '永铭诚道（北京）医学科技股份有限公司', '江苏复星医药销售有限公司', '江苏礼华生物技术有限公司', '江苏诺泰澳赛诺生物制药股份有限公司', '法荟(北京)医疗科技有限公司', '泛海控股股份有限公司', '济南智同医药科技有限公司', '海南博研医学研究有限公司', '润东医药研发（上海）有限公司', '湃朗瑞医药科技（北京）有限公司', '湖南慧泽生物医药科技有限公司', '瑞诵(上海) 医疗科技有限公司', '百时益医药研发（北京）有限公司', '百试达（上海）医药科技股份有限公司', '盛恩（北京）医药科技有限公司', '礼新医药科技（上海）有限公司', '立力科阿克赛诺（北京）医药研发咨询有限公司', '精鼎医药研究开发（上海）有限公司', '美珞医学科技(上海)有限公司', '联仁健康医疗大数据科技股份有限公司', '艾昆纬企业管理咨询（上海）有限公司', '艾瑞嘉医药研发（上海）有限公司', '苏州普蒂德生物医药科技有限公司', '西安循证医药科技有限公司', '西安艾凯尔医疗科技有限公司', '西斯比亚（北京）医药技术研究有限责任公司', '诺为泰医药科技（上海）有限公司', '诺华（北京）生物医学研究有限公司', '诺思格（北京）医药科技股份有限公司', '赛生医药研发（上海）有限公司', '赛纽仕医药信息咨询（北京）有限公司', '辟埃赛医药科技（上海）有限公司', '辽宁康辰诺信医药科技有限公司', '达孜县君合科技有限公司', '迈迪思创（北京）科技发展有限公司', '郑州深蓝海生物医药科技有限公司', '重庆恒真维实医药科技有限公司', '重庆美莱德生物医药有限公司', '长沙先领医药科技有限公司', '长沙都正生物科技股份有限公司', '鼎泰（南京）临床医学研究有限公司', '君实润佳 （上海）医药科技有限公司', '元羿生物科技（上海）有限公司', '上海瀛科隆医药开发有限公司', '武汉人福药业有限责任公司', '天翊微创医疗科技(常州)有限公司', '诺华（北京）生物医学研究有限公司', '罗氏(中国)投资有限公司', '中生尚健生物医药（杭州）有限公司', 'Incyte Corporation', '宜昌人福药业有限公司', '江苏先祥药业有限公司', '南京正大天晴制药有限公司', '石药集团中奇制药股份有限公司', '赛生医药研发（上海）有限公司', '亦康亦久医药科技有限责任公司', 'Croma-Pharma GmbH', '乳源东阳光医疗器械有限公司', '迈博斯（生物医药）苏州有限公司', '武田(中国)投资有限公司', '安济药业（美国）有限公司', 'VenatoRx制药公司', '石药集团中奇制药股份有限公司', '上海瀛科隆医药开发有限公司', '远大赛威信生命科学（南京）有限公司', '盖斯特利（商贸）北京有限公司', '英矽智能科技（上海）有限公司', '上海瑞宏迪医药有限公司', 'LG Chem Ltd.', 'Omeros Corporation', 'Eli Lilly and Company', '先声药业有限公司', '重庆百迈腾世医药科技有限公司', '上海润石医药科技有限公司', '乘典（苏州）生物医药有限公司', '广东华南新药创制有限公司', '海口市制药厂有限公司', '云南白药集团股份有限公司', '优乐康（湖州）医疗科技有限公司', '映恩生物科技（上海）有限公司', '广州科恩泰生物医药科技有限公司', '杭州隐秀生物科技有限公司', '麦迪领先医疗科技（深圳）有限公司', '烟台德胜海洋生物科技有限公司', '柏康（湖北）医药科技有限公司', '合肥希达思进医药科技有限公司', '上海妙一生物科技有限公司', '上海妙一生物科技有限公司', '南京艾美斐生物医药科技有限公司', '士泽生物医药（苏州）有限公司', '米德西普生物医药(南京)有限公司', '苏州美创医疗科技有限公司', '上海迈临科技有限公司', '宜明昂科生物医药技术（上海）股份有限公司', '和其瑞医药科技有限公司', '山东威智百科药业有限公司', '湖南埃普特医疗器械有限公司', '戴铭（上海）信息咨询有限公司', '上海安领科生物医药有限公司', '上海心恒睿医疗科技有限公司', '北京锐得麦医药科技有限公司', '湖南美柏生物医药有限公司', '北京博雅鸿翔医疗科技有限公司', '苏州科林利康医药科技有限公司', '西安新通药物研究股份有限公司', 'ALK-Abello香港代表处', 'AO Documentation and Clavadelerstyasse', 'Arla Foods Amba', 'Bayer Pharma AG', 'Becton Dickinson', 'BeiGene，Ltd.', 'BeyondSpring Pharmaceuticals', 'Bristol-Myers Squibb (CHINA)Investment Co,. Ltd', 'Fibrogen，Inc', 'Flen Pharma N.V.', 'Flen Pharma NV', 'Gilead Sciences Inc.', 'Gloria MED SPA', 'Implandata Ophthalmic Products GmbH', 'ISIS PHARMACEUTICALS,INC', 'Kawasumi Laboratories,Inc', 'LABORATOIRE HRA', 'Luqa ventures Co.,Limited', 'MICROVENTION INC', 'Monash University', 'NEW PATH INTERNATIONAL LLC', 'NOVN NORDISK  （China） Pharmaceuticals  Co., Ltd.', 'PAREXEL China Co.,Ltd.', 'Perfint Healthcare', 'PROTEUS BIOMEDICAL.INC', 'WKK EMS EQUIPMENT', 'Worldwide Clinical Trials Limited', '上海万和堂药业有限公司', '上海东松医疗科技股份有限公司', '上海中医药大学附属岳阳中西医结合医院', '上海光电医用电子仪器有限公司', '上海医药集团股份有限公司北京医药研究分公司', '上海市第五人民医院（上海市闵行区传染病医院）', '上海康奥医疗科技有限公司', '君岳医药', '上海新生源医药集团有限公司', '上海理欧医药科技有限公司', '上海百迈博制药有限公司', '上海药谷药业有限公司', '上海诺华贸易有限公司', '上海赛金生物医药有限公司', '上海长征医院', '中兆永业（深圳）科技有限公司', '中南大学湘雅三医院', '中国中医科学院广安门医院', '中国科学院上海药物研究所', '中山大学附属第三医院（中山大学肝脏病医院）', '中科迪高投资（北京）有限公司', '中美上海施贵宝制药有限公司', '丹麦爱尔开--阿贝优公司北京代表处', '丽珠集团利民制药厂', '乌鲁木齐吉鼎盛医药科技服务有限公司', '乐普（北京）医疗器械股份有限公司', '乳源东阳光医疗器械有限公司', '云南省肿瘤医院（昆明医科大学第三附属医院）', '云南龙海天然植物药业有限公司', '亚宝北中大（北京）制药有限公司', '亚宝药业集团股份有限公司', '亚宝药业集团股份有限公司北京药物研究院', '优效（北京）医学研究有限公司', '信立泰（苏州）药业有限公司', '利安康（北京）生物技术有限公司', '勃林格殷格翰智慧医疗科技（上海）有限责任公司', '北京世贸东瑞医药科技有限公司', '北京丰睿堂医药信息咨询有限公司', '北京乔治医学研究有限公司', '北京创新国信医药科技有限公司', '北京华脉泰科医疗器械股份有限公司', '北京双鹤药业经营有限责任公司', '北京四环制药有限公司', '北京太乙方略顾问有限公司', '北京普罗吉生物科技发展有限公司', '北京杰华生物技术有限责任公司', '北京沙东生物技术有限公司', '北京清源伟业生物组织工程科技有限公司', '北京珅奥基医药科技有限公司', '北京美中双和医疗器械股份有限公司', '北京肿瘤医院（北京大学肿瘤医院）', '北京鑫诺美迪基因检测技术有限公司', '北京阿迈特医疗器械有限公司', '华中科技大学同济医学院附属协和医院肿瘤中心', '华中科技大学同济医学院附属同济医院', '华大生物科技（武汉）有限公司', '铨融（苏州）医药科技开发股份有限公司', '普蕊斯（上海）医药科技开发股份有限公司', '西安杨森制药有限公司', '昆翎企业管理(上海)有限公司', '礼来苏州制药有限公司', '竞标失败', '康融东方（广州）生物医药有限公司', '徕博科医药研发（北京）有限公司', '诺和诺德（中国）制药有限公司', '上海海和药物研究开发股份有限公司', '宁波健世科技股份有限公司', '四川百利药业有限责任公司', '艾昆纬', '无，申办方自己', '江苏康宁杰瑞生物制药有限公司', '广东东阳光药业有限公司', '厦门特宝生物工程股份有限公司', '长春金赛药业有限责任公司', '嘉兴太美医疗科技有限公司', '江苏奥赛康药业有限公司', '泰格医药科技有限公司', '基石药业（苏州）有限公司', '浙江圣兆药物科技股份有限公司', '山东新时代药业有限公司', '上海复星医药产业发展有限公司', '杭州诺为医疗技术有限公司', '无', '正大天晴药业集团股份有限公司', '迪哲（江苏）医药股份有限公司', '上海泰格医药科技有限公司', '兆科（广州）肿瘤药物有限公司', '北京海金格医药科技开发股份有限公司', '山东威高集团医用高分子制品股份有限公司', '海金格', '北京诺诚健华医药科技有限公司', 'NA直接和CRO艾昆玮签合同', '上海凌先医药科技有限公司', '北京卡替医疗技术有限公司', 'Denovo Biopharma LLC', '阿斯利康药业（中国）有限公司', '上海和誉生物医药科技有限公司', '百时益医药研究（苏州）有限公司', '拜耳（中国）有限公司/拜耳医药保健有限公司', 'MSD直管无CRO', '无锡和誉生物医药科技有限公司', '天境生物科技（杭州）有限公司', '江苏德能医学科技有限公司', 'Medpace', '精鼎', '信达生物制药（苏州）有限公司', '福建盛迪医药有限公司', '广州再极医药科技有限公司', '无CRO', '西藏海思科制药有限公司', '北京博医臻研医药科技开发有限公司', '丽珠医药集团股份有限公司', '杭州高田生物医药有限公司', '康方药业有限公司', '微境生物医药科技（上海）有限公司', '上海先祥医药科技有限公司', '赛诺菲（中国）投资有限公司', '上海君实生物医药科技股份有限公司', '杭州翰思生物医药有限公司', '罗氏（中国）投资有限公司', '百奥泰生物制药股份有限公司', '正大天晴药业集团南京顺欣制药有限公司', '重庆誉颜制药有限公司', '杭州思默医药科技有限公司', '苏州亚盛药业有限公司', '苏州泽璟生物制药股份有限公司', '辉瑞投资有限公司', '四川弘合生物科技有限公司', '拜耳（中国）有限公司', 'name', '康方天成（广东）制药有限公司', '百时美施贵宝（中国）投资有限公司', '江西科睿药业有限公司', '上海泽德曼医药科技有限公司', '四川海思科制药有限公司', '无，申办方自己的', '成都金瑞基业生物科技有限公司', '杭州泰格医药有限公司', '科林利康', '乐普生物科技股份有限公司', '广东恒瑞医药有限公司', '深圳赛保尔生物药业有限公司', '默沙东研发（中国）有限公司', '北京豪迈东方医药科技发展有限公司', '杭州认识科技有限公司', '重庆智翔金泰生物制药股份有限公司', '诺和诺泰生物制药有限公司', '再鼎医药（上海）有限公司', '北京艺妙医疗科技有限公司', '上海普珩生物技术有限公司', '浙江同源康医药股份有限公司', '北京优迅医疗器械有限公司', '明慧医药（杭州）有限公司', '江苏恒江苏恒瑞医药股份有限公司', 'ICON Clinical Research Limited', '艾昆玮', '泰州翰中生物医药有限公司', '首药控股（北京）股份有限公司', '成都盛迪医药有限公司', '阿斯利康全球研发（中国）有限公司', '舒泰神（北京）生物制药股份有限公司', '北京斯特睿格医药技术有限责任公司', '耀视（苏州）医疗科技有限公司', '北京海莎咨询有限公司', '百泰生物药业有限公司', '葛兰素史克（上海）医药研发有限公司', '乐普', '柏达（北京）医药科技有限公司', '苏州润新生物科技有限公司', '阿斯利康投资（中国）有限公司', '上海银诺医药技术有限公司', '江西青峰药业有限公司', '中山康方生物医药有限公司', ' Medpace Pharmaceutical Science (Shanghai) Co. Ltd.', '来凯医药科技（上海）有限公司', '广州顺健生物医药科技有限公司', '恒瑞', '百济神州（上海）生物科技有限公司', '上海迈兰医药咨询有限公司', '海思科医药集团股份有限公司', '科文斯医药研发（北京）有限公司', '精鼎医院研究开发（上海）有限公司', '赛纽仕医药咨询有限公司', '上海复宏汉霖生物技术股份有限公司', '山东盛迪医药有限公司', '上海济煜医药科技有限公司', '北京科伦瑞医药科技有限公司', '成都百利多特生物药业有限责任公司', '上海津曼特生物科技有限公司', '上药帛康生物医药（上海）有限公司', '江苏恩华药业股份有限公司', '博纳西亚医药科技有限公司', '杭州唯强医疗科技有限公司', 'PAREXEL China CO.,Ltd.精鼎医药研究开发（上海）有限公司', '好一生（北京）医药科技有限公司', '先健科技（深圳）有限公司', '轩竹（北京）医药科技有限公司', '北京奥泰康', '艾伯维医药贸易（上海）有限公司', '深圳微芯生物科技股份有限公司', '昆拓信诚医药研发（北京）有限公司', '南京维立志博生物科技股份有限公司', 'AZ', '甫康（上海）健康科技有限责任公司', '思路迪医药（青岛）有限公司', '北京新唯医药科技有限公司', '四川科伦博泰生物医药股份有限公司', '阿斯利康全球研发研发（中国）有限公司', '强生（中国）投资有限公司', '海创药业股份有限公司', '苏州君境生物医药科技有限公司', '南京再明医药有限公司', '阿斯利康制药有限公司', '北京强新生物科技有限公司', '康方赛诺医药有限公司', '石药', '深圳信立泰药业股份有限公司', '武汉生物制品研究所有限责任公司', '安进生物技术咨询（上海）有限公司', '北京盛迪医药有限公司', '四川科伦药物研究院有限公司', '君实生物', '合肥瀚科迈博生物技术有限公司', '开心生活科技有限公司', 'LOST未合作', '南京方腾', '康缔亚医药科技有限公司', '江苏冬泽特医食品有限公司', '上海璎黎药业有限公司', '锐讯博星生物医药（苏州）有限公司', '上海艾莎医学科技有限公司', '江苏恒瑞医药股份有限公司', '北京生物制品研究所有限责任公司', '齐鲁制药有限公司', '丽珠集团新北江制药股份有限公司', '爱博诺德（北京）医疗科技股份有限公司', '和记黄埔医药（上海）有限公司', '来凯制药（宁波）有限公司', '北京创新国信医药科技有限公司', '无，直接对接申办方', '昆拓', '北京复兴医药科技开发有限公司', ' 广州九泰药械技术有限公司', '上海罗科医药信息咨询有限公司', '恺兴生命科技（上海）有限公司', '苏州信诺维医药科技股份有限公司', '精鼎医药', '天境生物科技（上海）有限公司', '南京圣和药业股份有限公司', '浙江太美医疗科技股份有限公司', '恒瑞源正（上海）生物科技有限公司', '上海益临思医药开发有限公司']


def hw_ocr_general_text(
    image_path=None,
    image_url=None,
    detect_direction=True,
    quick_mode=False,
    character_mode=False,
    language="zh",
    single_orientation_mode=True,
    pdf_page_number=1
):
    """
    调用统一的OCR服务进行文字识别。
    :param image_path: 本地图片路径（与 image_url 二选一）
    :param image_url: 远程图片 URL（与 image_path 二选一）
    :param detect_direction: 是否校正图片倾斜角度
    :param quick_mode: 是否开启快速模式（统一OCR服务暂不支持）
    :param character_mode: 是否开启单字符模式（统一OCR服务暂不支持）
    :param language: 语言选择（统一OCR服务固定为中文）
    :param single_orientation_mode: 是否开启单朝向模式（统一OCR服务暂不支持）
    :param pdf_page_number: 指定 PDF 识别的页码（统一OCR服务暂不支持）
    :return: 识别结果
    """
    try:
        if image_path:
            # 处理图片路径
            with open(image_path, "rb") as file:
                image_bytes = file.read()
        elif image_url:
            # 处理远程图片URL
            response = requests.get(image_url)
            response.raise_for_status()
            image_bytes = response.content
        else:
            raise ValueError("image_path 或 image_url 其中之一必须提供")

        # 使用统一OCR服务，映射detect_direction参数到use_correction
        use_correction = detect_direction  # 映射参数
        from common.clients.ocr_service import process_ocr
        result = process_ocr(image_bytes, use_correction=use_correction)
        
        # 返回原始OCR结果格式，保持向后兼容
        return result["ocr_result"]
        
    except Exception as e:
        print(f"❌ OCR服务调用失败: {e}")
        raise


def rotate_point(point, Cx, Cy, theta):
    """计算旋转后的坐标"""
    x, y = point

    # 平移到中心
    x_prime = x - Cx
    y_prime = y - Cy

    # 应用旋转公式
    x_double_prime = x_prime * math.cos(theta) + y_prime * math.sin(theta)
    y_double_prime = -x_prime * math.sin(theta) + y_prime * math.cos(theta)

    # 平移回原坐标系
    x_rot = x_double_prime + Cx
    y_rot = y_double_prime + Cy

    return [x_rot, y_rot]


def sort_text_blocks(ocr_result):
    """从左到右从上到下排序文本块"""
    words_block_list = ocr_result["words_block_list"]

    # Calculate the top-left point for each text block
    for block in words_block_list:
        # Find the top-left point (minimum x and y)
        x_coords = [point[0] for point in block['location']]
        y_coords = [point[1] for point in block['location']]

        # Calculate the center point of each block
        # This approach is often more reliable than using just the top-left point
        block['center_x'] = sum(x_coords) / len(x_coords)
        block['center_y'] = sum(y_coords) / len(y_coords)

    # Define threshold for same line detection
    y_threshold = 10  # Adjust based on your text block size

    # Group blocks by lines (blocks with similar y coordinates)
    lines = []
    sorted_blocks = sorted(words_block_list, key=lambda block: block['center_y'])

    current_line = [sorted_blocks[0]]
    for i in range(1, len(sorted_blocks)):
        if abs(sorted_blocks[i]['center_y'] - current_line[0]['center_y']) < y_threshold:
            # Same line
            current_line.append(sorted_blocks[i])
        else:
            # New line
            lines.append(sorted(current_line, key=lambda block: block['center_x']))
            current_line = [sorted_blocks[i]]

    # Don't forget to add the last line
    if current_line:
        lines.append(sorted(current_line, key=lambda block: block['center_x']))

    # Flatten the result
    result = []
    for line in lines:
        result.extend(line)

    # Update the original list
    ocr_result["words_block_list"] = result

    return ocr_result


def load_image(image_path=None, image_url=None):
    """
    加载图片并返回 PIL.Image 对象。

    参数：
    - image_path (str, 可选): 本地图片文件路径。
    - image_url (str, 可选): 在线图片的 URL。

    返回：
    - PIL.Image 对象。

    异常：
    - ValueError: 当两个参数都未提供时抛出。
    """
    if image_path:
        return Image.open(image_path)
    elif image_url:
        response = requests.get(image_url)
        response.raise_for_status()  # 确保请求成功
        return Image.open(io.BytesIO(response.content))
    else:
        raise ValueError("Either image_path or image_url must be provided.")


def concat_ocr_text(result, image_path, separator=" "):
    """
    拼接 OCR 识别结果中的文本。

    参数：
    - result (dict): OCR 识别的结果。
    - image_path (str): 处理的图片路径。
    - separator (str, 可选): 用于拼接文本的分隔符，默认为空格。

    返回：
    - str: 拼接后的文本字符串。
    """
    if "result" in result and "words_block_list" in result["result"]:
        ocr_result = result["result"]
        direction = ocr_result["direction"]

        if direction:
            print(f"direction: {direction}")
            image = Image.open(image_path)
            # 获取图片宽高
            W, H = image.size
            Cx, Cy = W / 2, H / 2  # 中心点

            # 顺时针旋转角度（弧度制）
            theta = math.radians(direction)

            for box in ocr_result["words_block_list"]:
                location = []
                for point in box['location']:
                    # 旋转单个点
                    rotated_point = rotate_point(point, Cx, Cy, theta)
                    location.append(rotated_point)
                box['location'] = location

            ocr_result = sort_text_blocks(ocr_result)
    return separator.join([i['words'] for i in ocr_result["words_block_list"]])


def format_ocr_text(result, image_path):
    """
    格式化 OCR 识别结果中的文本。

    参数：
    - result (dict): OCR 识别的结果。
    - image_path (str): 处理的图片路径。

    返回：
    - str: 返回格式化的文本。
    """
    # 提取带位置信息的文本块
    text_blocks = []
    if "result" in result and "words_block_list" in result["result"]:
        ocr_result = result["result"]
        direction = ocr_result["direction"]

        if direction:
            print(f"direction: {direction}")
            image = Image.open(image_path)
            # 获取图片宽高
            W, H = image.size
            Cx, Cy = W / 2, H / 2  # 中心点

            # 顺时针旋转角度（弧度制）
            theta = math.radians(direction)

            for box in ocr_result["words_block_list"]:
                location = []
                for point in box['location']:
                    # 旋转单个点
                    rotated_point = rotate_point(point, Cx, Cy, theta)
                    location.append(rotated_point)
                box['location'] = location

        # 处理文本块
        for block in result["result"]["words_block_list"]:
            if "words" in block and "location" in block:
                text = block["words"]
                location = block["location"]

                # 计算文本块中心点坐标
                center_x = sum(point[0] for point in location) / 4
                center_y = sum(point[1] for point in location) / 4

                # 计算高度 (使用左上角和左下角的y坐标差)
                # 这里我们使用中心点高度作为参考
                min_y = min([p[1] for p in location])
                max_y = max([p[1] for p in location])
                height = max_y - min_y

                text_blocks.append({
                    "text": text,
                    "center_y": center_y,
                    "center_x": center_x,
                    "height": height if height > 0 else 1  # 确保高度为正
                })

    if not text_blocks:
        return ""

    # 基于文本块高度分析确定行高容差
    heights = [block["height"] for block in text_blocks]

    if not heights:
        return ""

    # avg_height = sum(heights) / len(heights)

    # 使用中位数可能比平均值更稳健
    heights.sort()
    median_height = heights[len(heights) // 2]

    # 根据中位数计算行高容差
    line_height_tolerance = median_height * 0.7  # 可以根据实际情况调整系数

    # 使用确定的line_height_tolerance分组文本块
    rows = {}

    for block in text_blocks:
        # 查找最近的行
        found_row = False
        for row_y in rows.keys():
            if abs(block["center_y"] - row_y) < line_height_tolerance:
                rows[row_y].append(block)
                found_row = True
                break

        # 如果没有找到匹配的行，创建新行
        if not found_row:
            rows[block["center_y"]] = [block]

    # 对每一行按x坐标排序，然后将行按y坐标排序
    formatted_text = ""
    for row_y in sorted(rows.keys()):
        row_blocks = sorted(rows[row_y], key=lambda b: b["center_x"])
        line_text = " ".join([block["text"] for block in row_blocks])
        formatted_text += line_text + "\n"

    return formatted_text.strip()


def extract_text_from_image(image_path, format=True, separator=" "):
    """
    提取图片中的文本
    :param image_path: 图片路径
    :return: 提取的文本
    """
    result = hw_ocr_general_text(image_path)
    if format:
        text = ""
        if "result" in result and "markdown_result" in result["result"]:
            text += result["result"]["markdown_result"]
        elif "result" in result and "words_block_list" in result["result"]:
            for block in result["result"]["words_block_list"]:
                if "words" in block:
                    text += block["words"] + " "
    else:
        text = concat_ocr_text(result, image_path, separator)
    return text


def extract_text_from_pdf(pdf_path, format=True, separator=" "):
    """
    将PDF每页转换为图片，然后提取文本
    :param pdf_path: PDF文件路径
    :param temp_dir: 临时存储图片的目录
    :return: 提取的文本
    """
    # 创建临时目录
    temp_dir = os.path.abspath(os.path.join(__file__, '../../tmp', f"{uuid.uuid4().hex}"))
    os.makedirs(temp_dir, exist_ok=True)

    text = ""
    try:
        # 打开PDF文件
        pdf_document = fitz.open(pdf_path)

        # 遍历PDF的每一页
        for page_num in range(len(pdf_document)):
            page = pdf_document.load_page(page_num)

            # 将页面渲染为图片
            # image_path = os.path.join(temp_dir, f"page_{page_num + 1}.png")
            # pix = page.get_pixmap()
            # pix.save(image_path)

            # img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
            # img.save(image_path)

            from PIL import Image, ImageOps
            
            A4_WIDTH, A4_HEIGHT = 1654, 2339 

            # 设置缩放倍率，让渲染更清晰
            zoom = 2  # 2倍清晰度
            mat = fitz.Matrix(zoom, zoom)  # fitz 是 PyMuPDF
            
            zoom_x = 200.0 / 72
            zoom_y = 200.0 / 72
            mat = fitz.Matrix(zoom_x, zoom_y)
        
            pix = page.get_pixmap(matrix=mat)

            # 保存临时图像
            image_path = os.path.join(temp_dir, f"page_{page_num + 1}.png")
            pix.save(image_path)

            # 打开图片
            img = Image.open(image_path)

            # 调整为A4大小，保持内容居中（会自动缩放，不失真）
            img = ImageOps.pad(img, (A4_WIDTH, A4_HEIGHT), color='white')

            # 保存覆盖原图
            img.save(image_path)

            # 提取图片中的文本
            page_text = extract_text_from_image(image_path, format, separator)
            text += f"[Page {page_num + 1}]\n{page_text}\n\n"

            # 删除临时图片文件
            os.remove(image_path)

        pdf_document.close()
    except Exception as e:
        print(f"PDF处理出错: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        # 删除临时目录
        try:
            # os.rmdir(temp_dir)
            import shutil
            shutil.rmtree(temp_dir)
        except:
            pass

    return text


def extract_text_from_excel(excel_path, format=True, separator=" "):
    """
    提取Excel文件中的文本内容
    :param excel_path: Excel文件路径
    :return: 提取的文本
    """
    text = ""
    try:
        # 读取Excel文件
        excel_file = pd.ExcelFile(excel_path)

        # 遍历所有工作表
        for sheet_name in excel_file.sheet_names:
            df = pd.read_excel(excel_file, sheet_name=sheet_name)

            # 添加工作表名称
            text += f"[Sheet: {sheet_name}]\n"

            # 将DataFrame转换为字符串
            sheet_text = df.to_string(index=False)
            text += sheet_text + "\n\n"
    except Exception as e:
        print(f"Excel处理出错: {str(e)}")

    return text


def word_to_pdf(word_path):
    """
    Word转PDF
    """
    try:
        output_pdf = os.path.splitext(word_path)[0] + '.pdf'
        subprocess.run(['libreoffice', '--headless', '--convert-to', 'pdf', '--outdir',
                       os.path.abspath(os.path.dirname(word_path)), word_path], check=True)
        return output_pdf
    except subprocess.CalledProcessError as e:
        print(f"Word 转 PDF 出错: {str(e)}")
        return None
    except FileNotFoundError:
        print("libreoffice 未安装，请先安装 libreoffice 工具。")
        return None


def doc_to_docx(word_path):
    """
    doc转docx
    """
    try:
        output_pdf = os.path.splitext(word_path)[0] + '.docx'
        subprocess.run(['libreoffice', '--headless', '--convert-to', 'docx', '--outdir',
                       os.path.abspath(os.path.dirname(word_path)), word_path], check=True)
        return output_pdf
    except subprocess.CalledProcessError as e:
        print(f"doc 转 docx 出错: {str(e)}")
        return None
    except FileNotFoundError:
        print("libreoffice 未安装，请先安装 libreoffice 工具。")
        return None


def extract_text(file_path: Union[str, Path], format=True, separator=" "):
    """
    提取文件中的文本内容
    """
    all_text = ''
    # 图片文件扩展名
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.gif']
    file_path = Path(file_path)
    
    if file_path.is_file():
        file_extension = file_path.suffix.lower()
        file_path_str = str(file_path)

        # 处理图片文件
        if file_extension in image_extensions:
            print(f"处理图片: {file_path}")
            file_text = extract_text_from_image(file_path_str, format, separator)
            all_text += f"[File: {file_path.name}]\n{file_text}\n\n"

        # 处理PDF文件
        elif file_extension == '.pdf':
            print(f"处理PDF: {file_path}")
            file_text = extract_text_from_pdf(file_path_str, format, separator)
            all_text += f"[File: {file_path.name}]\n{file_text}\n\n"

        # 处理Excel文件
        # elif file_extension in ['.xlsx', '.xls']:
        #     print(f"处理Excel: {file_path}")
        #     file_text = extract_text_from_excel(file_path_str)
        #     all_text += f"[File: {file_path.name}]\n{file_text}\n\n"

        # 处理 Word 文件
        elif file_extension in ['.doc', '.docx']:
            print(f"处理 Word: {file_path}")
            pdf_path = word_to_pdf(file_path_str)
            if pdf_path:
                file_text = extract_text_from_pdf(pdf_path, format, separator)
                all_text += f"[File: {file_path.name}]\n{file_text}\n\n"
    return all_text


def extract_text_from_folder(folder_path, format=True, separator=" "):
    """
    提取指定文件夹中所有支持的文件的文本
    :param folder_path: 文件夹路径
    :return: 提取的所有文本
    """
    all_text = ""

    # 遍历文件夹中的所有文件
    for file_path in Path(folder_path).rglob('*'):
        all_text += extract_text(file_path, format, separator)

    return all_text.strip()


# 修改 heic_to_jpg_stream 函数以接收文件流
def heic_to_jpg_stream(heic_stream):
    try:
        # 注册 HEIC 支持
        pillow_heif.register_heif_opener()
        # 打开 HEIC 文件流
        image = Image.open(heic_stream)
        # 创建内存流
        jpg_stream = io.BytesIO()
        # 保存为 JPEG 到流中
        image.save(jpg_stream, format="JPEG")
        # 将流指针移到开头
        jpg_stream.seek(0)
        return jpg_stream
    except Exception as e:
        print(f"转换出错: {e}")
        return None


class CustomFileObject:
    def __init__(self, file_data, name):
        self._file_data = file_data
        self._name = name
        self._stream = io.BytesIO(file_data)
        if name.lower().endswith('.jpg') or name.lower().endswith('.jpeg'):
            self.content_type = 'image/jpeg'
        elif name.lower().endswith('.docx'):
            self.content_type = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        elif name.lower().endswith('.pdf'):
            self.content_type = 'application/pdf'
        else:
            self.content_type = 'application/octet-stream'

    @property
    def name(self):
        return self._name

    @property
    def size(self):
        return len(self._file_data)

    def read(self, size=-1):
        return self._stream.read(size)

    def seek(self, offset, whence=0):
        return self._stream.seek(offset, whence)

    def tell(self):
        return self._stream.tell()

    def chunks(self, chunk_size=io.DEFAULT_BUFFER_SIZE):
        while True:
            data = self.read(chunk_size)
            if not data:
                break
            yield data


def heic_to_jpg_file(file):
    if file.name.lower().endswith('.heic'):
        try:
            # 注册 HEIC 支持
            pillow_heif.register_heif_opener()
            # 打开 HEIC 文件
            image = Image.open(file)
            # 创建内存流
            jpg_stream = io.BytesIO()
            # 保存为 JPEG 到流中
            image.save(jpg_stream, format="JPEG")
            # 获取转换后的 JPG 数据
            jpg_data = jpg_stream.getvalue()
            # 生成新的文件名
            new_name = file.name.rsplit('.', 1)[0] + '.jpg'
            # 创建自定义文件对象
            return CustomFileObject(jpg_data, new_name)
        except Exception as e:
            print(f"转换出错: {e}")
            return None
    return file


# import pythoncom
# import win32com.client
def doc_to_docx_file(file):
    if file.name.lower().endswith('.doc'):
        try:
            # pythoncom.CoInitialize()
            # # 创建临时 .doc 文件
            temp_doc_path = 'tempdoctodocx.doc'
            # # directory = os.path.dirname(temp_doc_path)
            # # if not os.path.exists(directory):
            # #     os.makedirs(directory)
            # with open(temp_doc_path, 'wb') as f:
            #     f.write(file.read())
            system = platform.system()
            print(system)
            # if system == 'Windows':
            #     # Windows 系统使用 pywin32
            #     word = win32com.client.gencache.EnsureDispatch('Word.Application')
            #     doc = word.Documents.Open(temp_doc_path)
            #     print(111)
            #     temp_docx_path = os.path.splitext(temp_doc_path)[0] + '.docx'
            #     doc.SaveAs(temp_docx_path, FileFormat=16)
            #     doc.Close()
            #     word.Quit()
            if system == 'Linux':
                # Linux 系统使用 libreoffice
                subprocess.run(['libreoffice', '--headless', '--convert-to', 'docx', temp_doc_path])
                temp_docx_path = os.path.splitext(temp_doc_path)[0] + '.docx'
            else:
                print(f"不支持的操作系统: {system}")
                os.remove(temp_doc_path)
                return None

            # 读取转换后的 .docx 文件内容
            with open(temp_docx_path, 'rb') as f:
                docx_data = f.read()

            # 删除临时文件
            os.remove(temp_doc_path)
            os.remove(temp_docx_path)

            # 生成新的文件名
            new_name = file.name.rsplit('.', 1)[0] + '.docx'
            # 创建自定义文件对象
            return CustomFileObject(docx_data, new_name)
        except Exception as e:
            print(f"转换出错: {e}")
            return None
        # finally:
        #     # 清理 COM 库
        #     pythoncom.CoUninitialize()
    return file


import tempfile
import shutil


def bytes_word_to_pdf(file, file_extension):
    """
    Word 流转 PDF 流，支持 doc 和 docx
    """
    try:
        # 使用 uuid 生成唯一的临时目录名称
        unique_dir_name = str(uuid.uuid4())
        temp_dir = os.path.join(os.getcwd(), unique_dir_name)
        os.makedirs(temp_dir)

        # 创建临时 Word 文件
        temp_word_path = os.path.join(temp_dir, f'temp{file_extension}')
        with open(temp_word_path, 'wb') as f:
            f.write(file.read())

        # 生成输出 PDF 文件路径
        output_pdf_path = os.path.splitext(temp_word_path)[0] + '.pdf'

        # 调用 libreoffice 进行转换
        subprocess.run(['libreoffice', '--headless', '--convert-to', 'pdf', '--outdir',
                        os.path.abspath(os.path.dirname(temp_word_path)), temp_word_path], check=True)

        # 读取转换后的 PDF 文件内容作为流
        with open(output_pdf_path, 'rb') as f:
            pdf_stream = io.BytesIO(f.read())

        # 删除临时目录
        shutil.rmtree(temp_dir)

        jpg_data = pdf_stream.getvalue()
        # 生成新的文件名
        new_name = file.name.rsplit('.', 1)[0] + '.pdf'
        # 创建自定义文件对象
        return CustomFileObject(jpg_data, new_name)

        # return pdf_stream
    except subprocess.CalledProcessError as e:
        print(f"Word 转 PDF 出错: {str(e)}")
        return None
    except FileNotFoundError:
        print("libreoffice 未安装，请先安装 libreoffice 工具。")
        return None
    except Exception as e:
        print(f"发生未知错误: {str(e)}")
        return None


def rotate_and_resize_image(image: MatLike, angle: float) -> MatLike:
    """旋转图像并调整尺寸"""
    (h, w) = image.shape[:2]
    center = (w // 2, h // 2)

    # 获取旋转矩阵
    M = cv2.getRotationMatrix2D(center, angle, 1.0)

    # 计算旋转后的图像尺寸
    abs_cos = abs(M[0, 0])
    abs_sin = abs(M[0, 1])

    new_w = int(h * abs_sin + w * abs_cos)
    new_h = int(h * abs_cos + w * abs_sin)

    # 调整旋转矩阵的平移部分
    M[0, 2] += (new_w / 2) - center[0]
    M[1, 2] += (new_h / 2) - center[1]

    # 旋转图像并调整尺寸
    rotated_image = cv2.warpAffine(
        image,
        M,
        (new_w, new_h),
        flags=cv2.INTER_CUBIC,
        borderMode=cv2.BORDER_CONSTANT,
        borderValue=(255, 255, 255),
    )
    return rotated_image


def getBase64(data):
    if isinstance(data, str):
        # 如果传入的是文件路径
        with open(data, "rb") as image_file:
            encoded_string = base64.b64encode(image_file.read()).decode()
    elif isinstance(data, bytes):
        # 如果传入的是字节流
        encoded_string = base64.b64encode(data).decode()
    else:
        raise ValueError("传入的数据类型必须是字符串（文件路径）或字节类型。")
    return encoded_string


def getOcrResult(url, payload, headers):
    response = requests.request("POST", url, headers=headers, json=payload, verify=False)
    return response.text


# 解析 OCR 结果的 JSON 格式，提取文本内容并拼接为一个字符串，附加 prompt
def getKeyWords(ocrResult):
    text = ""
    for result in ast.literal_eval(ocrResult)["result"]["words_block_list"]:
        words = result["words"]
        text += words
    # return text + "只需帮我提取文本中的名字和就诊科室，输出格式为json，key值为需提取的字段"
    # return text + "只需帮我提取文本中的名字、年龄、电话号码和地址信息，输出格式为json，key值为需提取的字段"  # 提示词
    # return text + "帮我提取文本中的名字、年龄、电话号码、身份证号码、地址信息、就诊号、住院号、性别、年龄、出生年月、籍贯、婚姻、工作单位、家庭地址、永久地址、单位电话、联系人姓名、检验者、审核者、医生姓名、联系人电话、电子邮箱、医保卡号、社保账号、商业保险单号、病理标本号、基因检测样本号、影像的编号等所有私人信息，输出格式为非嵌套json，key值为需提取的字段"
    # return text + "帮我提取文本中的名字、年龄、电话号码、身份证号码、地址信息、就诊号、住院号、性别、年龄、出生年月、籍贯、婚姻、工作单位、家庭地址、永久地址、单位电话、联系人姓名、联系人电话、电子邮箱、医保卡号、社保账号、商业保险单号、病理标本号、基因检测样本号、影像的编号、URL、IP地址、指纹、DNA、新生儿姓名、新生儿出生地址、车辆编号、保险编号、银行卡账号、社保卡账号、病案号、门诊号、住院号、申办方公司名称、CRO名称、等所有私人信息，输出格式为非嵌套json，key值为需提取的字段"
    # return text + "帮我提取文本中的名字、姓名、患者姓名、电话号码、联系电话、手机号码、传真号码、邮箱地址、QQ、微信、身份证号码、证件号码、地址信息、就诊号、住院号、性别、籍贯、工作单位地址、家庭地址、现住址、出生地址、户口地址、永久地址、单位电话、联系人姓名、检验者、审核者、采集人、医生姓名、研究者姓名、联系人电话、电子邮箱、医保卡号、社保账号、商业保险单号、URL、IP地址、指纹、DNA、新生儿姓名、新生儿出生地址、车辆编号、保险编号、银行卡账号、社保卡账号、病案号、门诊号、住院号、申办方名称、CRO名称、等所有私人信息，输出格式为非嵌套json，key值为需提取的字段"
    return text + "帮我提取文本中的个人姓名、民族、国籍、家庭关系、住址、电话号码、电子邮箱地址、新生儿姓名、新生儿出生地址、职业、职位、工作单位、学历、学位、教育经历、工作经历、培训记录、成绩单、通信记录和内容、短信、彩信、电子邮件、以及描述个人通信的数据、微信、电话号码、联系电话、手机号码、传真号码、QQ、微信、联系方式、IP地址、URL、通讯录、好友列表、群列表、电子邮件地址列表、个人基因、指纹、声纹、掌纹、耳廓、虹膜、面部识别特征、DNA、银行账户、鉴别信息、存款信息（包括资金数量、支付收款记录等）、房产信息、信贷信息、征信信息、交易和消费信息、流水记录、银行卡号、商业保险单号、病案号、门诊号、住院号、床号、身份证、军官证、护照、驾驶证、工作证、社保卡号、居住证、车辆编号、保险编号、医保卡号、性取向、婚史、宗教信仰、未公开的违法犯罪记录、通信记录和内容、通讯录、好友列表、群组列表、行踪轨迹、网页浏览记录、住宿信息、精准定位信息、申办方名称、CRO名称、医院名称、中心名称，只提取我要求的字段，输出格式为非嵌套json，key值为需提取的字段"


# 定义 LLM 调用 API，通过 HTTP POST 请求调用 LLM，输入文本并返回生成结果
def useLlmApi(text):
    start_time = time.time()
    # url =r"https://192.168.230.105:30334/v1/infer/6586eec4-d5ae-460f-9756-0accbb6b3db2/v1/chat/completions"
    url = r"https://192.168.230.105:30334/v1/infer/46c6c41b-ae45-4362-92f9-152e6cc67975/v1/chat/completions"
    headers = {
        "Content-Type": "application/json",
        "Auth-Username": "mauser",
        "Auth-Password": "Prs@123456"
    }
    # body ={
    #     "model": "pangu",  # Qwen-model",
    #     "messages": [
    #         {
    #             "role": "user",
    #             "content": text # promptList[i]
    #                           # "你是谁？"
    #         }
    #         ],
    #         "max_tokens": 8000,
    #         "presence_penalty": 1.03,
    #         "frequency_penalty": 1.0,
    #         "seed": NULL,
    #         "temperature": 0,
    #         "top_p": 0.95,
    #         "stream": False
    #         }

    body = {
        "model": 'DeepSeek-R1-Distill-Qwen-32B',
        "messages": [{"role": "user", "content": text}],
        "max_tokens": 8192,
        "temperature": 0.0,
        "top_p": 0.95,
        "stream": False
    }

    response = requests.post(url, json=body, headers=headers, verify=False)    # proxies=proxies
    obj = json.loads(response.text)
    # print(obj)
    end_time = time.time()

    if response.status_code == 200:
        # 输出调用时间
        elapsed_time = end_time - start_time
        print(f"✅ 大模型 API 调用成功！耗时: {elapsed_time:.2f}秒")
        return response.json()
    else:
        print(f"❌ 大模型 API 调用失败，状态码：{response.status_code}")
        print(f"错误信息：{response.text}")
        return None

def ocr_desensitive(input_img):
    # import ocr_tools_extract
    # return ocr_tools_extract.ocr_desensitive(input_img, masked_img)
    from ocr_mask.main import  ocr_desensitive
    return ocr_desensitive(input_img)


    # start_time = time.time()
    # # print(input_img)
    # imgpath = input_img  # r"D:\proj\PythonApplication1\PythonApplication1\page8-45.png"
    # encoded_str = getBase64(imgpath)  # base64编码
    #
    # run_time01 = time.time()
    #
    # # url = "https://ocr.cn-north-4.myhuaweicloud.com/v2/0c038707a20026892f51c001ae871efa/ocr/general-text"
    # url = "https://192.168.230.105:30334/v1/infer/0215d06f-a89f-410a-9043-a8d1b70ebf29/v2/123/ocr/general-text"
    # # logInfo = "{\r\n    \"image\": \"base64\",\r\n    \"detect_direction\": false,\r\n    \"quick_mode\": false,\r\n    \"language\": \"zh\"\r\n}"
    # # payload = logInfo.replace("base64", encoded_str)
    #
    # payload = {"image": encoded_str}
    # payload.update({
    #     "detect_direction": True,
    #     "quick_mode": False,
    #     "character_mode": False,
    #     "language": "zh",
    #     "single_orientation_mode": True,
    #     "pdf_page_number": 1
    # })
    #
    #
    # headers = {
    #     'Content-Type': 'application/json',
    #     'Auth-Username': 'mauser',
    #     'Auth-Password': 'Prs@123456',
    #     'Authorization': 'Basic Og=='
    # }
    #
    # # response = requests.request("POST", url, headers=headers, data=payload,verify=False)
    # # print(response.text)
    # ocrResult = getOcrResult(url, payload, headers)
    # try:
    #     angle = ast.literal_eval(ocrResult)["result"]["direction"]
    #     angle_float = float(angle)
    #     if angle_float > 5:
    #         print(angle_float)
    #         print('需要旋转！！！')
    #         image_bytess = io.BytesIO(input_img)
    #         images = Image.open(image_bytess)
    #         image_np = np.array(images)
    #         # OpenCV默认使用BGR颜色通道顺序，而PIL使用RGB，因此需要进行转换
    #         images = cv2.cvtColor(image_np, cv2.COLOR_RGB2BGR)
    #         rotated_images = rotate_and_resize_image(images, angle)
    #         imagesss = Image.fromarray(rotated_images)
    #
    #         byte_stream = io.BytesIO()
    #         # 将图像保存到字节流中
    #         imagesss.save(byte_stream, format='JPEG')
    #         # 获取字节流数据
    #         input_img = byte_stream.getvalue()
    #         encoded_str = getBase64(input_img)
    #         payload.update({
    #             "image": encoded_str
    #         })
    #         ocrResult = getOcrResult(url, payload, headers)
    # except Exception as e:
    #     pass
    #
    # run_time02 = time.time()
    # inputText = getKeyWords(ocrResult)
    # keywords_to_mask = useLlmApi(inputText)
    # outputText = keywords_to_mask
    # think = keywords_to_mask["choices"][0]["message"]["content"]
    # start_index = think.find("<think>") + len("<think>")
    # end_index = think.find("</think>")
    # think = think[start_index:end_index]
    # generated_tokens = keywords_to_mask.get('usage', {})
    # key = keywords_to_mask["choices"][0]["message"]["content"].split("</think>")[-1].split("json")[1].rstrip("```'") + ""
    # key = key.replace("'\n", "", 1).strip()
    # print(key)
    # print("*****************************************"+"\n")
    # try:
    #     # print('关键字提取结果:#######################', keywords_to_mask["choices"][0]["message"]["content"].split("<think>")[-1])
    #     print(json.loads(key))
    #     print("\n" + "*****************************************" )
    #     # image = Image.open(imgpath)
    #     # draw = ImageDraw.Draw(image)
    #     A = io.BytesIO(input_img)
    #     image = Image.open(A)
    #     draw = ImageDraw.Draw(image)
    #
    #     # for keywords in keywords_to_mask:
    #     #     mask_image_by_location(draw, imgpath, ocrResult, keywords)
    #     tag = []
    #     for k, v in json.loads(key).items():
    #         tag.append(str(v))
    #     print(tag)
    #     aa = (0, 0, 0)
    #     tag = list(set([s for s in tag if s and s != 'None' and s != '无' and s != '未提及']))
    #     print(tag)
    #     print(1111)
    #     tag = tag + sponsor_list
    #     for keywords in tag:
    #         # mask_image_by_location(draw, input_img2, ocrResult, keywords)
    #         for block in ast.literal_eval(ocrResult)["result"]["words_block_list"]:
    #             location = block["location"]
    #             # print(block["words"], location)
    #             a, b, c, d = location[0][0], location[0][1], location[2][0], location[2][1]
    #             if keywords in block["words"]:
    #                 draw.rectangle([min(a, c), min(b, d), max(a, c), max(b, d)], fill=aa)
    # except Exception as e:
    #     print(e)
    #     A = io.BytesIO(input_img)
    #     image = Image.open(A)
    #
    # end_time = time.time()
    # print(
    #     f"执行完成, 编码转换时间：{run_time01 - start_time}s, API调用时间：{run_time02 - run_time01}s, 执行总时间：{end_time - start_time}")
    # return image, inputText, outputText, think, generated_tokens


if __name__ == "__main__":
    # python -m common.ocr_tools
    # image_path = r'c:\Users\<USER>\Desktop\检验单(1).png'
    # result = hw_ocr_general_text(image_path)
    # text = format_ocr_text(result, image_path)
    # print(text)

    # text = extract_text_from_folder(
    #     r'c:\Users\<USER>\Desktop\SMO-2023-8590-CHN013013\files', format=True, separator='QQ')
    text = extract_text_from_folder(
        r'c:\Users\<USER>\Desktop\SMO-2023-8590-CHN013013\files')
    print(text)
