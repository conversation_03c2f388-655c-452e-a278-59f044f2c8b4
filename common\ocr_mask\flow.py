"""
OCR脱敏处理的PocketFlow流程定义
"""
from pocketflow import Flow
from .nodes import (
    OCRProcessingNode,
    MaskImageNode,
    HippaDeidentifyNode
)
import time


def create_ocr_desensitive_flow():
    """
    创建OCR脱敏处理流程

    流程：OCR处理 -> HIPAA脱敏识别 -> 图片遮挡

    Returns:
        Flow: 配置好的HIPAA流程对象
    """
    print("🏥 创建HIPAA脱敏流程")

    # 创建节点
    ocr_node = OCRProcessingNode(max_retries=3, wait=10.0)
    hippa_node = HippaDeidentifyNode(max_retries=3, wait=5.0)
    mask_image_node = MaskImageNode()

    # HIPAA流程：OCR -> HIPAA脱敏 -> 图片遮挡
    ocr_node >> hippa_node >> mask_image_node

    flow = Flow(start=ocr_node)
    return flow


def initialize_shared_store(input_img, project_no=None):
    """
    初始化共享存储

    Args:
        input_img: 输入的图片字节数据
        project_no: 项目编号，将作为project_code传递给脱敏服务

    Returns:
        dict: 初始化的共享存储字典
    """
    return {
        # 输入数据
        "input_img": input_img,
        "project_no": project_no,

        # OCR处理结果
        "ocr_result": None,
        "ocr_box": None,  # 新增：带坐标的OCR信息
        "input_text": "",
        "markdown_text": "",
        "corrected_image": None,
        "corrected_image_bytes": None,
        "has_correction": False,
        "direction": 0.0,
        "image_for_masking": None,  # 用于脱敏的图片（矫正后或原始）

        # HIPAA脱敏结果
        "hippa_entities": [],           # HIPAA识别的敏感实体
        "hippa_deidentified_text": "",  # HIPAA脱敏后文本
        "char_to_block_mapping": [],    # 字符位置映射表
        "hippa_success": False,         # HIPAA处理是否成功
        "hippa_error": "",              # HIPAA错误信息

        # 最终关键词（为了保持接口兼容性）
        "final_keywords": [],

        # 处理结果
        "masked_text": "",
        "final_image": None,
        "mask_stats": {},

        # 脱敏后的OCR坐标数据
        "masked_ocr_box": None,

        # 时间统计
        "start_time": time.time(),
        "timings": {}
    }


def run_ocr_desensitive_flow(input_img, project_no=None):
    """
    运行完整的OCR脱敏流程

    Args:
        input_img: 输入图片的字节数据
        project_no: 项目编号，将作为project_code传递给脱敏服务

    Returns:
        dict: 包含所有处理结果的字典
    """
    print(f"🚀 开始OCR脱敏处理流程 - HIPAA模式")
    print("=" * 60)

    start_time = time.time()

    # 初始化共享存储
    shared = initialize_shared_store(input_img, project_no=project_no)

    # 创建HIPAA流程
    flow = create_ocr_desensitive_flow()

    try:
        # 运行流程
        flow.run(shared)

        # 计算总耗时
        total_time = time.time() - start_time
        shared["total_time"] = total_time

        # 输出执行摘要
        print_execution_summary(shared)
        
        # 输出时间统计
        print_timing_summary(shared)

        # 构建返回结果（保持与原接口兼容）
        result = {
            "success": True,
            "image": shared["final_image"],
            "input_text": shared["input_text"],
            "masked_text": shared["masked_text"],
            "final_keywords": shared["final_keywords"],
            "markdown_text": shared["markdown_text"],
            "ocr_box": shared.get("masked_ocr_box") or shared["ocr_box"],  # 优先返回脱敏后的ocr_box
            "ai_response": None,  # HIPAA模式不使用AI响应
            "prompt": "",         # HIPAA模式不使用提示词
            "hippa_entities": shared["hippa_entities"],
            "hippa_success": shared["hippa_success"],
            "stats": {
                "total_time": total_time,
                "mask_stats": shared["mask_stats"],
                "hippa_entities_count": len(shared["hippa_entities"]),
                "char_mapping_blocks": len(shared["char_to_block_mapping"])
            }
        }

        return result

    except Exception as e:
        error_time = time.time() - start_time
        print(f"❌ 流程执行失败: {e}")
        print(f"⏱️ 错误发生时间: {error_time:.2f}s")
        import traceback
        traceback.print_exc()

        return {
            "success": False,
            "error": str(e),
            "mode": "HIPAA脱敏",
            "stats": {
                "error_time": error_time
            }
        }


def print_execution_summary(shared):
    """
    打印详细的执行摘要 - 

    Args:
        shared: 共享存储字典
    """
    print("\n" + "="*60)
    print(f"📊 OCR脱敏处理完成 - 执行摘要 (HIPAA模式)")
    print("="*60)

    # 基本信息
    print(f"⏱️ 总耗时: {shared.get('total_time', 0):.2f}s")
    print(f"📝 识别文本长度: {len(shared.get('input_text', ''))}")
    print(f"🔍 识别文本块数: {len(shared.get('ocr_result', {}).get('result', {}).get('words_block_list', []))}")

    # HIPAA模式统计
    hippa_entities = shared.get('hippa_entities', [])
    char_mapping = shared.get('char_to_block_mapping', [])
    hippa_success = shared.get('hippa_success', False)

    print(f"\n🏥 HIPAA脱敏统计:")
    print(f"  处理状态: {'✅ 成功' if hippa_success else '❌ 失败'}")
    print(f"  识别敏感实体: {len(hippa_entities)} 个")
    print(f"  字符映射块数: {len(char_mapping)} 个")
    print(f"  最终敏感词: {len(shared.get('final_keywords', []))} 个")

    if hippa_entities:
        print(f"  敏感实体类型分布:")
        entity_types = {}
        for entity in hippa_entities:
            entity_type = entity.get('entity_type', 'UNKNOWN')
            entity_types[entity_type] = entity_types.get(entity_type, 0) + 1
        for entity_type, count in entity_types.items():
            print(f"    {entity_type}: {count} 个")

    if not hippa_success:
        error_msg = shared.get('hippa_error', '未知错误')
        print(f"  错误信息: {error_msg}")

    # 遮挡统计
    mask_stats = shared.get('mask_stats', {})
    print(f"\n🖼️ 图片遮挡统计 (HIPAA模式):")
    print(f"  总遮挡处数: {mask_stats.get('total', 0)}")
    print(f"  精确遮挡: {mask_stats.get('precise', 0)}")
    print(f"  降级遮挡: {mask_stats.get('fallback', 0)}")

    # 节点执行状态
    print(f"\n✅ 所有节点执行成功")

    print("="*60)


def print_timing_summary(shared):
    """
    打印时间统计摘要
    
    Args:
        shared: 共享存储字典
    """
    print(f"⏱️ 时间统计:")
    print(f"  OCR处理时间: {shared.get('ocr_time', 0):.2f}s")
    print(f"  脱敏请求时间: {shared.get('deidentify_time', 0):.2f}s")
    print(f"  任务总时间: {shared.get('total_time', 0):.2f}s")