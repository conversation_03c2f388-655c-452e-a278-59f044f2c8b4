from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>out<PERSON>, SimpleRouter

from . import views

router = DefaultRouter(trailing_slash=False)
router.register(r'/privacy-rule', views.PrivacyRuleConfigViewSet)
router.register(r'/privacy-rule-list', views.PrivacyRuleConfigListViewSet)
router.register(r'/privacy-rule-template', views.PrivacyRuleImportExportViewSet)


urlpatterns = []

urlpatterns = router.urls + urlpatterns
