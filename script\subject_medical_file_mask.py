import pandas as pd
import re
import ast
import io
import fitz
from sqlalchemy import text
import hashlib
import argparse
from datetime import datetime, timedelta
import base64
import os
import django
from django.db import connections
from django.conf import settings

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smo.settings.test')
os.environ["DJANGO_ALLOW_ASYNC_UNSAFE"] = "true"

django.setup()
# mails = settings.MAILS
# DATABASES = settings.DATABASES
from sqlalchemy import create_engine
from sqlalchemy.pool import NullPool
from common.tools import get_db_engin_url
from django.db import transaction
from apps.medical.models import MedicalFileMasked
from apps.subject_medical.models import SubjectMedicalFileMasked
# 创建数据库引擎对象
from apps.ae_tracker.models import AeTrackerTask
from common.tools import sql_to_df
from common.ocr_mask.main import ocr_desensitive
from common.minio_client import get_minio_client
# from apps.system.models import ModelInvocationLog  # Disabled: HIPAA mode doesn't use AI model logging


db_erp = create_engine(get_db_engin_url('default'), poolclass=NullPool)


def close_db_connection():
    # 关闭数据库连接
    db_erp.dispose()
    print("Database connection closed")


def find_all_indices(s, target_char):
    """
    找到所有字符串中指定字符的下标
    s：输入字符串
    target_char：要查找的字符
    return：所有下标的列表
    """
    if len(target_char)!=1:
        raise ValueError("target_char 必须是单个字符")
    return [i for i, char in enumerate(s) if char == target_char]


def mask_image_by_location(draw, image_path, ocrResult, keywords):
    """
    根据给定的位置信息对图像中的特定字符进行打码。

    :param image_path: 原始图像路径
    :param ocr_result: OCR识别结果
    :param keywords: 需要打码的关键字列表
    """

    # 提取位置信息
    for block in ast.literal_eval(ocrResult)["result"]["words_block_list"]:
        words = block["words"]
        locations = block["location"]
        if not locations:
            continue
        num_chars = len(words)

        # 计算每个字符的起始和结束位置
        x_coords = [point[0] for point in locations]
        y_coords = [point[1] for point in locations]

        left = min(x_coords)
        right = max(x_coords)
        top = min(y_coords)
        bottom = max(y_coords)

        # 使用线性插值计算每个字符的边界
        char_width = (right - left) / num_chars

        if keywords in words:
            previous_index = -1
            for keyword in keywords:
                indices = find_all_indices(words, keyword)
                if len(indices) == 1:
                    start_index = words.find(keyword)
                    if start_index != -1:
                        end_index = start_index + len(keyword)

                        for i in range(start_index, end_index):
                            if previous_index == -1 or i > previous_index:
                                char_left = left + i * char_width
                                char_right = left + (i + 1) * char_width

                                top_y, bottom_y = linear_interpolate(char_left, char_right, top, bottom, x_coords, y_coords)

                                draw.rectangle([char_left, top_y, char_right, bottom_y], fill='black')

                                previous_index = start_index
                else:
                    start_index = previous_index + 1
                    if start_index != -1:
                        end_index = start_index + len(keyword)

                        for i in range(start_index, end_index):
                            if previous_index == -1 or i > previous_index:
                                char_left = left + i * char_width
                                char_right = left + (i + 1) * char_width

                                top_y, bottom_y = linear_interpolate(char_left, char_right, top, bottom, x_coords, y_coords)

                                draw.rectangle([char_left, top_y, char_right, bottom_y], fill='black')

                                previous_index = start_index


def linear_interpolate(x_start, x_end, y_top, y_bottom, x_coords, y_coords):
    """
    使用线性插值计算顶部和底部的y坐标。

    :param x_start: 字符的左边界
    :param x_end: 字符的右边界
    :param y_top: 多边形的顶部y坐标
    :param y_bottom: 多边形的底部y坐标
    :param x_coords: 多边形的x坐标列表
    :param y_coords: 多边形的y坐标列表
    :return: 插值后的y坐标 (top_y, bottom_y)
    """
    n = len(x_coords)
    if n < 2:
        return y_top, y_bottom

    def interpolate(x, x_coords, y_coords):
        for i in range(n - 1):
            if x_coords[i] <= x <= x_coords[i + 1]:
                t = (x - x_coords[i]) / (x_coords[i + 1] - x_coords[i])
                return y_coords[i] + t * (y_coords[(i + 1) % n] - y_coords[i])
        # 如果x不在任何区间内，使用最近的点
        return y_coords[-1]

    # 计算顶部的y坐标
    top_y_start = interpolate(x_start, x_coords, y_coords)
    top_y_end = interpolate(x_end, x_coords, y_coords)
    top_y = (top_y_start + top_y_end) / 2

    # 计算底部的y坐标
    bottom_y_start = interpolate(x_start, x_coords, [y_bottom] * n)
    bottom_y_end = interpolate(x_end, x_coords, [y_bottom] * n)
    bottom_y = (bottom_y_start + bottom_y_end) / 2

    return top_y, bottom_y


def match_chinese_string(text):
    # 匹配姓名后面的名字
    # name_pattern = r"姓名：(\w+)"
    # 匹配年龄后面的数字
    age_pattern = r"(\d+) 岁"

    # 查找所有匹配项
    # name_match = re.search(name_pattern, text)
    age_match = re.search(age_pattern, text)

    # if name_match and age_match:
    if age_match:
        # name = name_match.group(1)
        age = age_match.group(1)
        # return {"name": name, "age": age}
        return age
    else:
        return None


def process_row(task_id):
    print('开始处理下面的row了！！！')
    task = AeTrackerTask.objects.filter(id=task_id, category=AeTrackerTask.PIC_MASK, delete_flag=0).first()
    subject_item = task.subject_item
    if not task:
        print(f"没有发现任务")
        return
    subject_id = task.subject_id
    subject_item_id = task.subject_item_id
    sql = f"""
        select 
        file_id,file_masked_id 
        from subject_medical_info
        where delete_flag = 0 and file_masked_id is null 
        and subject_id = '{subject_id}'
        and subject_item_id = '{subject_item_id}'
    """
    #
    # df1 = pd.read_sql_query(sql, db_erp)
    df1 = sql_to_df('default', sql)
    id_set = df1['file_id'].tolist()
    if not id_set:
        print("未找到符合条件的 file_id，无法继续查询。")
        df2 = pd.DataFrame()
    else:
        id_str = ', '.join(map(str, id_set))
        sql2 = f"""
            SELECT
                *
            FROM
                subject_medical_file
            WHERE
                id IN ({id_str})
        """
        # df2 = pd.read_sql_query(sql2, db_erp)
        df2 = sql_to_df('default', sql2)

    params = {
        'which_need_update_id': task_id,
        'mask_status': 'IN_PROGRESS'
    }
    sql = text(
        f"""update ae_tracker_task set status=:mask_status where id=:which_need_update_id""")
    # print(sql)q
    with db_erp.begin() as conn:
        conn.execute(sql, params)
    conn.close()
    print(task_id)

    minio_client = get_minio_client()
    list_params = []
    for index, row in df2.iterrows():
        try:
            bucket_name = row['bucket_name']
            object_name = row['object_name']
            original_filename = row['original_filename']
            # 从 MinIO 中获取对象
            response = minio_client.get_object(bucket_name, object_name)
            # 读取对象内容到内存中
            data = response.read()
            both_conditions_false = True
            if original_filename[-3:].lower() == 'pdf' and int(subject_item.item_type) != 1:
                print('pdf!!!')
                # 将数据加载到 PDF 文档对象中
                pdf_doc = fitz.open(stream=io.BytesIO(data), filetype="pdf")
                # 遍历 PDF 的每一页
                image_list = []
                matrix = fitz.Matrix(2, 2)
                # model_invocation_log_instances = []  # Disabled: HIPAA mode doesn't use AI model logging
                for page_num in range(pdf_doc.page_count):
                    page = pdf_doc.load_page(page_num)
                    # 将页面转换为图像
                    pix = page.get_pixmap(matrix=matrix)
                    # 创建图像字节流
                    img_bytes = pix.tobytes()
                    try:
                        # start_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')  # Disabled: not needed for HIPAA mode
                        # 调用HIPAA模式专用的ocr_desensitive函数，返回3元组：(image, input_text, masked_text)
                        # 注释掉的原5元组调用：
                        # image, inputText, outputText, think, generated_tokens = ocr_desensitive(img_bytes, 6)
                        image, _, _ = ocr_desensitive(img_bytes, project_no=task.project.project_id)  # Only need image for PDF processing

                        # 注释掉AI模型日志入库操作，因为HIPAA模式不使用AI模型
                        # Disabled: HIPAA mode doesn't use AI models, so no need to save AI-related logs
                        # outputText = None         # AI响应（HIPAA模式为None）
                        # think = f"HIPAA模式处理完成"  # 思考内容（简化）
                        # generated_tokens = {"prompt_tokens": 0, "completion_tokens": 0}  # token统计（默认值）
                        # result = {}
                        # result['task_id'] = task_id
                        # result['create_user'] = task.create_user
                        # result['create_name'] = task.create_name
                        # result['category'] = 'SENSITIVE_INFORMATION_RECOGNITION'
                        # result['model_name'] = 'DeepSeek-R1-Distill-Qwen-32B'  # No longer applicable for HIPAA mode
                        # result['start_time'] = start_time
                        # result['end_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        # result['input_text'] = inputText
                        # result['think_text'] = think
                        # result['output_text'] = outputText
                        # result['prompt_tokens'] = generated_tokens['prompt_tokens']
                        # result['completion_tokens'] = generated_tokens['completion_tokens']
                        # result['business_id'] = task.patient_id
                        # model_invocation_log_instances.append(ModelInvocationLog(**result))
                        image_list.append(image)
                    except:
                        pass
                # ModelInvocationLog.objects.bulk_create(model_invocation_log_instances)  # Disabled: HIPAA mode doesn't use AI models
                file_bytes = io.BytesIO()

                if image_list:
                    # 取列表中的第一个 Image 对象作为基础
                    first_image = image_list[0]
                    # 使用 save 方法将 PDF 内容保存到 BytesIO 对象中
                    first_image.save(file_bytes, save_all=True, append_images=image_list[1:], format='PDF')
                    # 将文件指针移动到流的开头，以便后续读取
                    file_bytes.seek(0)

                    print('PDF 文件已成功保存到流中。')
                else:
                    print('没有可用的图片，无法创建 PDF 文件。')
                pdf_doc.close()
                both_conditions_false = False
            if original_filename.lower().endswith('.jpg') or original_filename.lower().endswith('.jpeg') or original_filename.lower().endswith('.png') or original_filename.lower().endswith('.webp'):
                print('图片！！！')
                file_bytesX = data
                # start_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')  # Disabled: not needed for HIPAA mode
                # 调用HIPAA模式专用的ocr_desensitive函数，返回3元组：(image, input_text, masked_text)
                # 注释掉的原5元组调用：
                # image, inputText, outputText, think, generated_tokens = ocr_desensitive(file_bytesX, 6)
                image, _, _ = ocr_desensitive(file_bytesX, project_no=task.project.project_id)  # Only need image for image processing

                # 注释掉AI模型日志入库操作，因为HIPAA模式不使用AI模型
                # Disabled: HIPAA mode doesn't use AI models, so no need to save AI-related logs
                # outputText = None         # AI响应（HIPAA模式为None）
                # think = f"HIPAA模式处理完成"  # 思考内容（简化）
                # generated_tokens = {"prompt_tokens": 0, "completion_tokens": 0}  # token统计（默认值）
                # result = {}
                # result['task_id'] = task_id
                # result['create_user'] = task.create_user
                # result['create_name'] = task.create_name
                # result['category'] = 'SENSITIVE_INFORMATION_RECOGNITION'
                # result['model_name'] = 'DeepSeek-R1-Distill-Qwen-32B'  # No longer applicable for HIPAA mode
                # result['start_time'] = start_time
                # result['end_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                # result['input_text'] = inputText
                # result['think_text'] = think
                # result['output_text'] = outputText
                # result['prompt_tokens'] = generated_tokens['prompt_tokens']
                # result['completion_tokens'] = generated_tokens['completion_tokens']
                # result['business_id'] = task.patient_id
                # ModelInvocationLog.objects.create(**result)
                format_mapping = {
                    'jpg': 'JPEG',
                    'jpeg': 'JPEG',
                    'png': 'PNG',

                }
                # # 获取对应的格式
                image_format = format_mapping.get(original_filename.split('.')[1].lower(), 'PNG')
                #
                if image.mode == 'RGBA':
                    image = image.convert('RGB')
                file_bytes = io.BytesIO()
                # 将 Image 对象保存到 BytesIO 对象中，这里以 PNG 格式为例，可根据实际需求修改
                image.save(file_bytes, format=image_format)
                file_bytes.seek(0)
                # # 关闭 BytesIO 对象
                both_conditions_false = False
            if both_conditions_false:
                print(row)
                print('文件类型不属于处理范畴！！！')
                continue

            class FileStreamWithName:
                def __init__(self, stream, filename):
                    self.stream = stream
                    self.filename = filename
                    self.size = len(stream.getvalue())
                    self.content_type = row['content_type']

                @property
                def name(self):
                    return self.filename
            # 创建包含文件流和文件名的对象
            file = FileStreamWithName(file_bytes, original_filename)
            import uuid
            _, ext = os.path.splitext(file.name)
            object_name = f"{uuid.uuid4().hex}{ext}"
            # 保存文件
            try:
                minio_client = get_minio_client()
                # Ensure bucket exists
                if not minio_client.bucket_exists(bucket_name):
                    minio_client.make_bucket(bucket_name)
                # Upload file to MinIO
                minio_client.put_object(
                    bucket_name=bucket_name,
                    object_name=object_name,
                    data=file_bytes,  # 直接使用 output 作为 data 参数
                    length=file.size,
                    part_size=1024 * 1024 * 5,
                    content_type=row['content_type']
                )
            except Exception as e:
                # logger = logging.getLogger(__name__)
                # logger.error(e)
                raise Exception(f"文件上传失败：{e}")

            # 数据入库
            output = file_bytes

            with transaction.atomic():
                # 计算 hash
                hash_object = hashlib.sha256(output.getvalue())
                hash = hash_object.hexdigest()
                # now = datetime.now()  # Disabled: not needed
                # formatted_time = now.strftime("%Y%m%d%H%M%S")  # Disabled: not used
                material_file = {
                    'original_filename': file.name,
                    'bucket_name': bucket_name,
                    # 'version': 'v' + str(formatted_time),  # Disabled: version not used
                    'object_name': object_name,
                    'content_type': row['content_type'],
                    'size': file.size,
                    'hash': hash,

                }
            task_info = SubjectMedicalFileMasked.objects.create(**material_file)
            id_value = task_info.id
            which_need_update_id = row['id']
            params = {'id_value': id_value,
                      'which_need_update_id': which_need_update_id,
                      # 'mask_status': 'COMPLETED'
                      }
            list_params.append(params)
            file_bytes.close()


        except Exception as e:
            print(e)
            print('文件处理错误！！！')
            print(row)

    for params in list_params:
        sql = text(
            f"""update subject_medical_info set file_masked_id =:id_value where file_id=:which_need_update_id""")
        # print(sql)q
        with db_erp.begin() as conn:
            conn.execute(sql, params)

    params = {
        'subject_id': subject_id,
        'subject_item_id': subject_item_id,
        'ae_ai_current_step': 1
    }
    sql = text(
        f"""update subject_item_info set ae_ai_current_step=:ae_ai_current_step where subject_id=:subject_id and subject_item_id=:subject_item_id and ae_ai_current_step <= 1""")
    # print(sql)q
    with db_erp.begin() as conn:
        conn.execute(sql, params)
    conn.close()

    params = {
        'which_need_update_id': task_id,
        'mask_status': 'COMPLETED',
        'end_time': datetime.now()
    }
    sql = text(
        f"""update ae_tracker_task set status=:mask_status,end_time=:end_time where id=:which_need_update_id""")
    # print(sql)q
    with db_erp.begin() as conn:
        conn.execute(sql, params)
    conn.close()
    print(task_id)
    return True


def main():
    parser = argparse.ArgumentParser(description="AE打码任务")
    parser.add_argument('--task_id', type=int, required=True, help="task_id")
    args = parser.parse_args()
    process_row(args.task_id)

if __name__ == "__main__":
    main()
    close_db_connection()
