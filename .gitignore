# Byte-compiled / optimized / DLL files
**/__pycache__/
**/*.py[cod]
**/*.pyc
*$py.class

**/.cursor
zcw_test

**/.cursorrules

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
*.log.*
.*.swp
etc/nginx/*
!etc/nginx/nginx.conf
local_settings.py
db.sqlite3

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/

# .idea
.idea/
.vscode/
.DS_Store

# Django static file
static/

# Nacos file
nacos-data/

# Notebook temp file
**/notebook/*.py

ch_PP-OCRv4_det_server_infer/
ch_PP-OCRv4_rec_server_infer/
ch_PP-OCRv4_det_infer/

nacos_databases.json
nacos_mail.json

tmp/