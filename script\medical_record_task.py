import os
import datetime

import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smo.settings.test')
django.setup()

import random
import time
import logging
from apps.medical_collection.models import MedicalCollectionTask, MedicalCollectionFile
from apps.medical.models import MedicalInfoLite, MedicalFile
from apps.subject.models import Subject
from apps.project.models import ProjectMaterialInfo, ProjectMaterialFile
from common.minio_client import get_minio_client, download_file_from_minio, upload_file_to_minio

from common.compare2llms_retrospective_study.config import CRF_excel_input, LLM_OUTPUT_PATH
from common.compare2llms_retrospective_study.main import main as llm_process_main  # 调用大模型

import mimetypes
from django.db import transaction, connection
import uuid
from django.conf import settings

import pymysql

import hashlib


def clc_file_hash(file_obj, hash_algorithm='sha256', chunk_size=8192):
    """
    分块计算文件的哈希值
    :param file_obj: 文件对象
    :param hash_algorithm: 哈希算法，默认为 sha256
    :param chunk_size: 每次读取的块大小，默认为 8KB
    :return: 文件的哈希值
    """
    hash_func = hashlib.new(hash_algorithm)

    # 以块大小读取文件并更新哈希值
    while chunk := file_obj.read(chunk_size):
        hash_func.update(chunk)

    # 将文件指针重置到文件开头（如果之后还需要读取文件）
    file_obj.seek(0)

    return hash_func.hexdigest()


# 初始化 MinIO 客户端
client = get_minio_client()

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 获取项目根目录路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
medical_file_folder = os.path.join(project_root, 'medical_crf_results', 'medical_files')
crf_file_folder = os.path.join(project_root, 'medical_crf_results', 'template_files')
retrospective_study_folder = os.path.join(project_root, 'medical_crf_results', 'retrospective_study_results', 'llm_output.xlsx')


# 定时扫描任务
def retrospective_study_medical_consumer():
    """
    回顾性研究 主要流程实现逻辑
    """
    while True:
        try:
            # 扫描 medical_collection_task 表，查找 status 为 TODO 或 IN_PROGRESS 的记录
            tasks = MedicalCollectionTask.objects.filter(status__in=['TODO', 'IN_PROGRESS'])

            for task in tasks:
                # 如果 category 是 MEDICAL_RECORD，则进行相关的处理
                if task.category == 'MEDICAL_RECORD':
                    if task.status == 'TODO':
                        print(f"当前状态为：{task.status}，准备下载文件...")
                        # 下载文件并更新状态
                        get_medical_file(task)
                        print("✅ 文件下载完成，准备处理文件...")
                    elif task.status == 'IN_PROGRESS':
                        # 任务正在进行，调用大模型进行处理
                        print(f"当前状态为：{task.status}，正在处理文件...")
                        process_with_llm(task)
                        print("✅ 【回顾性研究任务已经完成】！")

            # 每隔 5 秒钟执行一次
            time.sleep(5)
        except Exception as e:
            logger.error(f"Error occurred during task scanning: {e}")
            time.sleep(random.randint(5, 15))  # 随机回退，避免一直紧密循环


# 处理 CRF 类型的任务
def get_medical_file(task):
    """
    从minio拿到业务给的crf表头，并下载到本地临时存储位置
    同时根据 task.subject_id 查找相关的病历文件，并下载所有的病历文件
    """
    try:
        """===========================================下载病历文件================================================="""
        # 根据 task.subject_id 查找对应的 medical_info_lite 记录
        medical_info_lites = MedicalInfoLite.objects.filter(subject_id=task.subject_id)
        if not medical_info_lites:
            print("❌ 没有找到对应的 medical_info_lite 记录，跳过该任务")
            logger.warning(f'No medical_info_lite found for subject_id: {task.subject_id}')
            raise  # 如果没有找到对应的 medical_info_lite 记录，跳过该任务

        # 遍历 medical_info_lite 获取多个 file_id，下载相关的病历文件
        # all_files_downloaded = True
        medical_file_list = []
        for medical_info_lite in medical_info_lites:
            # 通过 file_id 查找对应的 MedicalFile
            medical_file = MedicalFile.objects.filter(id=medical_info_lite.file_id).first()
            if medical_file:
                file_original_name = medical_file.original_filename
                # 获取病历文件的 bucket_name 和 object_name
                file_bucket_name = medical_file.bucket_name
                file_object_name = medical_file.object_name

                # 定义病历文件的下载路径
                medical_file_path = f'{medical_file_folder}/{file_original_name}'
                # 下载病历文件
                try:
                    download_file_from_minio(
                        client=client,
                        bucket_name=file_bucket_name,
                        object_name=file_object_name,
                        local_file_path=medical_file_path
                    )
                    medical_file_list.append(medical_file_path)
                    print(f"✅ Medical file {file_object_name} downloaded successfully: {medical_file.original_filename}")
                except Exception as e:
                    logger.error(f"Failed to download medical file {medical_file.id}: {e}")
                    # all_files_downloaded = False
                    break  # 如果某个文件下载失败，则跳出循环，处理错误

        print(f"成功下载的病历：{medical_file_list}")
        # 更新 task 状态为 "进行中" 或 "已完成"，根据业务逻辑调整
        task.status = 'IN_PROGRESS'  # 表示在该处理中
        task.save()
        logger.info(f"Task {task.id} is being processed and file downloaded successfully.")
        print("✅ 文件下载成功，更新任务状态为进行中...")
        return medical_file_list

    except Exception as e:
        logger.error(f"Error processing task {task.id}: {e}")
        print("❌ 文件下载失败...")
        raise


def get_file_properties(file_path):
    """
    获取文件的属性信息
    :param file_path: 文件路径
    :return: 文件名、文件大小、文件 MIME 类型
    """
    # 获取文件名（包括扩展名）
    file_name = os.path.basename(file_path)

    # 获取文件大小（单位：字节）
    file_size = os.path.getsize(file_path)

    # 获取文件的 MIME 类型
    content_type, _ = mimetypes.guess_type(file_path)

    return {
        'file_name': file_name,
        'file_size': file_size,
        'content_type': content_type
    }


def save_task_data_with_pymysql(task_data):
    """
    使用 PyMySQL 执行 SQL 保存任务数据
    """
    try:
        # 建立数据库连接
        conn = pymysql.connect(
            host='**************',  # 数据库主机
            port=3309,
            user='aigc_test',  # 数据库用户名
            password='KZx6KmA6JNCQPypdpHtbUvphSiXn6AUM',  # 数据库密码
            database='aigc_test',  # 数据库名称
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor
        )

        with conn.cursor() as cursor:
            # SQL 插入语句
            sql = """
            INSERT INTO medical_collection_file (
                original_filename,
                bucket_name,
                object_name,
                content_type,
                size,
                hash,
                task_id,
                delete_flag,
                data_version,
                create_time,
                update_time
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            values = (
                task_data.get('original_filename'),
                task_data.get('bucket_name'),
                task_data.get('object_name'),
                task_data.get('content_type'),
                task_data.get('size'),
                task_data.get('hash'),
                task_data.get('task_id'),
                task_data.get('delete_flag'),
                task_data.get('data_version'),
                task_data.get('create_time'),
                task_data.get('update_time')
            )

            # 执行 SQL 语句
            cursor.execute(sql, values)

            # 提交事务
            conn.commit()

        print("✅ 数据库记录创建成功！")

        if not connection.is_usable():
            connection.close()
            connection.connect()
        # 获取 task_id
        task_id = task_data.get('task_id')

        # 使用 task_id 查找对应的 MedicalCollectionTask 对象并更新状态
        task_to_update = MedicalCollectionTask.objects.get(id=task_id)
        task_to_update.status = 'COMPLETED'
        task_to_update.save()

        print(f"✅ Task {task_to_update.id} status updated to COMPLETED.")


    except pymysql.MySQLError as e:
        print(f"❌ 数据库操作失败: {e}")
        raise
    except Exception as e:
        print(f"❌ 发生未知错误: {e}")
        raise  # Raise error to propagate it for higher-level handling
    finally:
        # 确保连接关闭
        if conn:
            conn.close()
            print("✅ 数据库连接已关闭")


# 处理 IN_PROGRESS 状态的任务，调用大模型
def process_with_llm(task):
    """
    调用大模型处理任务，并将结果上传到 MinIO 存储，更新数据库状态
    :param task: 任务对象
    :return: None
    """
    """ 这个大模型的接口要改变 """
    # try:
    #     logger.info(f"Start processing task {task.id} with the LLM model.")
    #     # 调用大模型，main() 处理结果会保存到 LLM_OUTPUT_PATH
    #     llm_process_main()  # main 函数调用
    # except Exception as llm_process_error:
    #     logger.error(f"Failed to process task {task.id} with the LLM model：{llm_process_error}.")
    #     print("❌ 大模型处理失败，未获取到处理结果，任务处理失败！")
    #     raise

    print("=======================模拟大模型处理===================================")
    # time.sleep(100)

    # 数据入库
    # file_info = get_file_properties(LLM_OUTPUT_PATH)  # 获取 文件扩展名、文件content_type、文件大小
    file_info = get_file_properties(retrospective_study_folder)  # 获取 文件扩展名、文件content_type、文件大小
    object_name = f"{uuid.uuid4()}{file_info.get('file_name')}"
    bucket_name = settings.MINIO_BUCKET_NAME

    # 保存数据到数据库
    try:
        # 计算文件的 hash
        # 打开文件并获取文件对象
        with open(LLM_OUTPUT_PATH, 'rb') as llm_output_file:
            # 你可以在这里直接将文件对象传递给 MinIO 上传函数
            hash_num = clc_file_hash(llm_output_file)

        medical_collection_file = {
            'original_filename': "aaaa.xlsx",
            'bucket_name': bucket_name,
            'object_name': object_name,
            'content_type': file_info.get('content_type'),
            'size': file_info.get('file_size'),
            'hash': hash_num,
            'task_id': task.id,
            'delete_flag': 0,
            'data_version': 0,
            'create_time': datetime.datetime.now(),
            'update_time': datetime.datetime.now(),
        }

        save_task_data_with_pymysql(medical_collection_file)

        logger.info(f"Database record created successfully for task {task.id}.")
    except Exception as db_error:
        logger.error(f"Error saving task data to database for task {task.id}: {db_error}")
        print("❌ 数据库记录创建失败！")
        raise  # 继续抛出异常，以便上层处理

    # 上传文件到 Minio
    try:

        upload_file_to_minio(
            client=client,
            local_file_path=LLM_OUTPUT_PATH,  # 传递文件对象
            bucket_name=bucket_name,
            object_name=object_name
        )
        logger.info(f"File uploaded to MinIO successfully for task {task.id}.")
        print("✅ 文件成功上传到 minio ！")
    except Exception as upload_error:
        logger.error(f"Error uploading file to MinIO for task {task.id}: {upload_error}")
        print("❌ 文件上传到 minio 失败！")
        return

    print("✅ 文件处理任务成功完成！")


if __name__ == '__main__':
    # 启动定时任务
    retrospective_study_medical_consumer()
    pass
