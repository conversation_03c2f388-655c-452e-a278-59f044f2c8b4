# -*- coding: utf-8 -*-
"""
统一的医疗OCR处理入口

本模块提供统一的入口函数，负责：
- 配置管理和模式选择（并行/串行）
- 调用相应的处理器
- 事务管理和结果返回
- 与现有代码的完全兼容
"""

import os
import sys
import logging
from typing import Dict, List, Optional, Any

# 获取项目根目录并添加到路径
current_script_path = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_script_path)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

logger = logging.getLogger(__name__)


def process_medical_file(ocr_text: str, 
                        task_info: Dict = None, 
                        ocr_blocks: List[Dict] = None,
                        file_metadata: Dict = None,
                        ae_tracker_task_item = None,
                        subject_id: str = None,
                        subject_item_id: str = None,
                        visit_data: List[Dict] = None,
                        task_id: int = None) -> Dict:
    """
    统一的医疗文件处理入口函数
    
    Args:
        ocr_text: OCR识别的文本
        task_info: 任务信息字典
        ocr_blocks: OCR文本块列表
        file_metadata: 文件元数据
        ae_tracker_task_item: AE跟踪任务项（用于事务处理）
        subject_id: 受试者ID
        subject_item_id: 受试者项目ID
        visit_data: 访问数据
        task_id: 任务ID
        
    Returns:
        Dict: 处理结果，兼容原有格式
    """
    
    print("🏥 开始统一医疗文件处理...")
    
    try:
        # 1. 获取配置并决定处理模式
        processing_mode = _determine_processing_mode()
        print(f"📋 处理模式: {processing_mode}")
        
        # 2. 构建完整的处理参数
        processing_params = _build_processing_params(
            ocr_text=ocr_text,
            task_info=task_info,
            ocr_blocks=ocr_blocks,
            file_metadata=file_metadata,
            subject_id=subject_id,
            subject_item_id=subject_item_id,
            visit_data=visit_data
        )
        
        # 3. 根据模式选择处理器
        if processing_mode == "parallel":
            result = _process_with_parallel_mode(processing_params)
        else:
            result = _process_with_serial_mode(processing_params)
        
        # 4. 执行事务处理（如果提供了必要参数）
        if ae_tracker_task_item and task_id:
            transaction_success = _execute_transaction(
                result=result,
                ae_tracker_task_item=ae_tracker_task_item,
                subject_id=subject_id,
                subject_item_id=subject_item_id,
                visit_data=visit_data or [],
                task_id=task_id
            )
            
            if not transaction_success:
                print("❌ 事务处理失败")
                # 注意：这里不抛出异常，让上层决定如何处理
        
        print(f"✅ 统一处理完成，识别到 {len(result.get('test_results', []))} 个检验项目")
        return result
        
    except Exception as e:
        print(f"❌ 统一处理失败: {e}")
        # 回退到最基础的处理方式
        return _fallback_processing(ocr_text, task_info, ocr_blocks)


def _determine_processing_mode() -> str:
    """确定处理模式"""
    try:
        from script.test_result_format_ae_ocr.simple_config import get_simple_config
        config = get_simple_config()
        
        if config.force_serial_processing:
            return "serial"
        elif config.enable_parallel:
            return "parallel"
        else:
            return "serial"
            
    except Exception as e:
        print(f"⚠️ 配置获取失败，使用串行模式: {e}")
        return "serial"


def _build_processing_params(ocr_text: str, task_info: Dict, ocr_blocks: List[Dict],
                           file_metadata: Dict, subject_id: str, subject_item_id: str,
                           visit_data: List[Dict]) -> Dict:
    """构建处理参数"""
    
    # 如果没有提供file_metadata，构建一个基础的
    if not file_metadata:
        file_metadata = {}
    
    # 确保file_metadata包含所有必要字段
    complete_metadata = {
        'subject_id': subject_id,
        'subject_item_id': subject_item_id,
        'task_info': task_info or {},
        'visit_data': visit_data or [],
        **file_metadata  # 合并现有的metadata
    }
    
    return {
        'ocr_text': ocr_text,
        'task_info': task_info,
        'ocr_blocks': ocr_blocks,
        'file_metadata': complete_metadata
    }


def _process_with_parallel_mode(params: Dict) -> Dict:
    """使用并行模式处理"""
    try:
        from script.test_result_format_ae_ocr.parallel_processor import ParallelIntegrationAdapter
        from script.test_result_format_ae_ocr.simple_config import get_simple_config
        
        config = get_simple_config()
        adapter = ParallelIntegrationAdapter(
            enable_parallel=True, 
            max_workers=config.max_workers
        )
        
        result = adapter.process_medical_ocr_enhanced(
            ocr_text=params['ocr_text'],
            task_info=params['task_info'],
            ocr_blocks=params['ocr_blocks'],
            file_metadata=params['file_metadata']
        )
        
        print(f"🚀 并行处理完成")
        return result
        
    except Exception as e:
        print(f"❌ 并行处理失败，回退到串行模式: {e}")
        return _process_with_serial_mode(params)


def _process_with_serial_mode(params: Dict) -> Dict:
    """使用串行模式处理"""
    try:
        from script.test_result_format_ae_ocr.main import process_medical_ocr
        
        test_items = process_medical_ocr(
            ocr_text=params['ocr_text'],
            task_info=params['task_info'],
            ocr_blocks=params['ocr_blocks']
        )
        
        # 转换为兼容格式
        test_results = []
        for item in test_items:
            item_dict = item.to_dict()
            # 过滤掉 TestResult 模型不支持的字段
            filtered_dict = {k: v for k, v in item_dict.items() if k != 'test_text'}
            test_results.append(filtered_dict)
        
        result = {"test_results": test_results}
        print(f"📝 串行处理完成")
        return result
        
    except Exception as e:
        print(f"❌ 串行处理失败: {e}")
        return {"test_results": []}


def _execute_transaction(result: Dict, ae_tracker_task_item, subject_id: str,
                        subject_item_id: str, visit_data: List[Dict], task_id: int) -> bool:
    """执行事务处理"""
    try:
        from script.test_result_format_ae_ocr.simple_transaction import get_simple_transaction_manager
        
        transaction_manager = get_simple_transaction_manager()
        
        success = transaction_manager.execute_file_transaction(
            file_result=result,
            task_id=task_id,
            ae_tracker_task_item=ae_tracker_task_item,
            subject_id=subject_id,
            subject_item_id=subject_item_id,
            data=visit_data
        )
        
        if success:
            print("✅ 事务处理成功")
        else:
            print("❌ 事务处理失败")
            
        return success
        
    except Exception as e:
        print(f"❌ 事务处理异常: {e}")
        return False


def _fallback_processing(ocr_text: str, task_info: Dict, ocr_blocks: List[Dict]) -> Dict:
    """最基础的回退处理"""
    try:
        # 尝试使用最基础的处理方式
        from script.test_result_format_ae_ocr.main import process_medical_ocr
        
        test_items = process_medical_ocr(ocr_text, task_info, ocr_blocks)
        
        test_results = []
        for item in test_items:
            try:
                item_dict = item.to_dict()
                filtered_dict = {k: v for k, v in item_dict.items() if k != 'test_text'}
                test_results.append(filtered_dict)
            except:
                # 如果单个项目处理失败，跳过
                continue
        
        return {"test_results": test_results}
        
    except Exception as e:
        print(f"❌ 回退处理也失败: {e}")
        return {"test_results": []}


# 为了保持兼容性，提供一个简化的接口
def process_medical_ocr_unified(ocr_text: str, task_info: Dict = None, 
                               ocr_blocks: List[Dict] = None, 
                               file_metadata: Dict = None) -> Dict:
    """
    简化的统一处理接口，仅处理OCR，不涉及事务
    
    Args:
        ocr_text: OCR识别的文本
        task_info: 任务信息字典
        ocr_blocks: OCR文本块列表
        file_metadata: 文件元数据
        
    Returns:
        Dict: 处理结果，兼容原有格式
    """
    return process_medical_file(
        ocr_text=ocr_text,
        task_info=task_info,
        ocr_blocks=ocr_blocks,
        file_metadata=file_metadata
    )


def process_medical_file_complete(ocr_text: str,
                                 task_info: Dict = None,
                                 ocr_blocks: List[Dict] = None,
                                 file_metadata: Dict = None,
                                 ae_tracker_task_item = None,
                                 subject_id: str = None,
                                 subject_item_id: str = None,
                                 visit_data: List[Dict] = None,
                                 task_id: int = None,
                                 enable_transaction: bool = True) -> Dict:
    """
    完整的医疗文件处理入口函数（包含事务处理）

    Args:
        ocr_text: OCR识别的文本
        task_info: 任务信息字典
        ocr_blocks: OCR文本块列表
        file_metadata: 文件元数据
        ae_tracker_task_item: AE跟踪任务项
        subject_id: 受试者ID
        subject_item_id: 受试者项目ID
        visit_data: 访问数据
        task_id: 任务ID
        enable_transaction: 是否启用事务处理

    Returns:
        Dict: 处理结果，包含事务处理状态
    """

    print("🏥 开始完整医疗文件处理（包含事务）...")

    try:
        # 1. 先进行OCR处理
        ocr_result = process_medical_file(
            ocr_text=ocr_text,
            task_info=task_info,
            ocr_blocks=ocr_blocks,
            file_metadata=file_metadata,
            subject_id=subject_id,
            subject_item_id=subject_item_id,
            visit_data=visit_data
        )

        # 2. 如果启用事务处理且提供了必要参数
        transaction_success = True
        if enable_transaction and ae_tracker_task_item and task_id:
            transaction_success = _execute_transaction(
                result=ocr_result,
                ae_tracker_task_item=ae_tracker_task_item,
                subject_id=subject_id,
                subject_item_id=subject_item_id,
                visit_data=visit_data or [],
                task_id=task_id
            )

        # 3. 返回包含事务状态的结果
        result = {
            **ocr_result,
            'transaction_success': transaction_success,
            'processing_mode': _determine_processing_mode()
        }

        print(f"✅ 完整处理完成，事务状态: {'成功' if transaction_success else '失败'}")
        return result

    except Exception as e:
        print(f"❌ 完整处理失败: {e}")
        return {
            "test_results": [],
            'transaction_success': False,
            'processing_mode': 'fallback',
            'error': str(e)
        }


# 导出主要函数
__all__ = ['process_medical_file', 'process_medical_ocr_unified', 'process_medical_file_complete']
