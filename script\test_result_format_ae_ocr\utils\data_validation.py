"""
数据验证工具：医院检验单OCR处理系统的数据验证和计算功能
"""
import re
import logging
from typing import Dict, Optional, Any
from datetime import datetime
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def parse_reference_range(reference_value: str) -> Dict[str, Optional[float]]:
    """
    解析参考范围字符串，提取最小值和最大值
    支持科学计数法，如 "<1E+03"、"1.23E+03-5.67E+04"等
    
    Args:
        reference_value: 参考范围字符串，如 "3.5-9.5"、"<1E+03"、"阴性"等
        
    Returns:
        包含min_value和max_value的字典
    """
    result = {'min_value': None, 'max_value': None}
    
    if not reference_value or not isinstance(reference_value, str):
        return result
    
    # 清理参考值字符串
    clean_ref = reference_value.strip()
    
    # 科学计数法数值模式：支持 1E+03, 1.23E-04, 2.5e+10, -3.5 等格式（包含负数）
    scientific_number_pattern = r'[+-]?[0-9]*\.?[0-9]+(?:[eE][+-]?[0-9]+)?'
    
    # 数值范围模式：如 "3.5-9.5"、"1E+03～2E+04"、"3.5 - 9.5"、"3--8"、"3/8"
    numeric_patterns = [
        rf'({scientific_number_pattern})\s*--\s*({scientific_number_pattern})',  # 双连字符
        rf'({scientific_number_pattern})\s*/\s*({scientific_number_pattern})',   # 斜杠分隔
        rf'({scientific_number_pattern})\s*[-～~]\s*({scientific_number_pattern})',
        rf'({scientific_number_pattern})\s*[至到]\s*({scientific_number_pattern})',
    ]
    
    for pattern in numeric_patterns:
        match = re.search(pattern, clean_ref, re.IGNORECASE)
        if match:
            try:
                min_val = float(match.group(1))
                max_val = float(match.group(2))
                result['min_value'] = min_val
                result['max_value'] = max_val
                # 对于数值范围（如"3-7"），边界值默认是包含的
                result['min_inclusive'] = True
                result['max_inclusive'] = True
                logger.debug(f"解析范围值: {clean_ref} -> min: {min_val}, max: {max_val} (包含边界)")
                return result
            except ValueError as e:
                logger.warning(f"无法解析范围值: {match.group(1)}, {match.group(2)}, 错误: {e}")
                continue
    
    # 单一边界值模式：区分严格不等号和非严格不等号
    boundary_patterns = [
        # 严格小于：< 和 ＜
        (rf'[<＜]\s*({scientific_number_pattern})', 'max_strict'),
        # 小于等于：≤
        (rf'[≤]\s*({scientific_number_pattern})', 'max_inclusive'),
        # 严格大于：> 和 ＞
        (rf'[>＞]\s*({scientific_number_pattern})', 'min_strict'),
        # 大于等于：≥
        (rf'[≥]\s*({scientific_number_pattern})', 'min_inclusive'),
    ]
    
    for pattern, boundary_type in boundary_patterns:
        match = re.search(pattern, clean_ref, re.IGNORECASE)
        if match:
            try:
                value = float(match.group(1))
                if boundary_type in ['max_strict', 'max_inclusive']:
                    result['max_value'] = value
                    result['max_inclusive'] = boundary_type == 'max_inclusive'
                    logger.debug(f"解析边界值: {clean_ref} -> max: {value}, inclusive: {result.get('max_inclusive', False)}")
                else:
                    result['min_value'] = value
                    result['min_inclusive'] = boundary_type == 'min_inclusive'
                    logger.debug(f"解析边界值: {clean_ref} -> min: {value}, inclusive: {result.get('min_inclusive', False)}")
                return result
            except ValueError as e:
                logger.warning(f"无法解析边界值: {match.group(1)}, 错误: {e}")
                continue
    
    logger.debug(f"未能解析参考范围: {clean_ref}")
    return result


def calculate_test_flag(test_value: str, reference_value: str) -> int:
    """
    计算检验结果标志（0正常/1偏高/2偏低）
    支持科学计数法，如 "1.23E+03"、"2.5e-04"等
    
    Args:
        test_value: 检验结果值
        reference_value: 参考范围

    Returns:
        0: 正常, 1: 偏高, 2: 偏低
    """
    # 处理特殊情况
    if not test_value or not isinstance(test_value, str):
        return 0
    
    test_value_clean = test_value.strip()

    # 尝试数值比较
    try:
        # 提取数值部分，支持科学计数法和负数
        scientific_number_pattern = r'([+-]?[0-9]*\.?[0-9]+(?:[eE][+-]?[0-9]+)?)'
        numeric_match = re.search(scientific_number_pattern, test_value_clean, re.IGNORECASE)
        if not numeric_match:
            return 0
        
        test_numeric = float(numeric_match.group(1))
        logger.debug(f"提取测试值: {test_value_clean} -> {test_numeric}")
        
        # 解析参考范围
        ref_range = parse_reference_range(reference_value)
        logger.debug(f"参考范围解析结果: {ref_range}")

        # 进行数值比较
        # 特殊处理：当min_value等于max_value时，表示精确值（如"0-0"、"1-1"）
        if (ref_range['min_value'] is not None and ref_range['max_value'] is not None and
            ref_range['min_value'] == ref_range['max_value']):
            exact_value = ref_range['min_value']
            if test_numeric == exact_value:
                logger.debug(f"测试值 {test_numeric} 等于精确值 {exact_value}, 标记为正常")
                return 0  # 正常
            elif test_numeric < exact_value:
                logger.debug(f"测试值 {test_numeric} < 精确值 {exact_value}, 标记为偏低")
                return 2  # 偏低
            else:
                logger.debug(f"测试值 {test_numeric} > 精确值 {exact_value}, 标记为偏高")
                return 1  # 偏高

        # 检查下边界
        if ref_range['min_value'] is not None:
            min_inclusive = ref_range.get('min_inclusive', False)
            if min_inclusive:
                # ≥ 符号：测试值小于边界值时偏低（等于时正常）
                if test_numeric < ref_range['min_value']:
                    logger.debug(f"测试值 {test_numeric} < 最小值 {ref_range['min_value']} (≥), 标记为偏低")
                    return 2  # 偏低
            else:
                # > 符号：测试值小于等于边界值时偏低
                if test_numeric <= ref_range['min_value']:
                    logger.debug(f"测试值 {test_numeric} <= 最小值 {ref_range['min_value']} (>), 标记为偏低")
                    return 2  # 偏低

        # 检查上边界
        if ref_range['max_value'] is not None:
            max_inclusive = ref_range.get('max_inclusive', False)
            if max_inclusive:
                # ≤ 符号：测试值大于边界值时偏高（等于时正常）
                if test_numeric > ref_range['max_value']:
                    logger.debug(f"测试值 {test_numeric} > 最大值 {ref_range['max_value']} (≤), 标记为偏高")
                    return 1  # 偏高
            else:
                # < 符号：测试值大于等于边界值时偏高
                if test_numeric >= ref_range['max_value']:
                    logger.debug(f"测试值 {test_numeric} >= 最大值 {ref_range['max_value']} (<), 标记为偏高")
                    return 1  # 偏高
        
        logger.debug(f"测试值 {test_numeric} 在正常范围内")
        return 0  # 正常
        
    except (ValueError, TypeError) as e:
        # 非数值类型，进行定性比较
        logger.debug(f"数值解析失败，转为定性比较: {e}")
        return calculate_qualitative_flag(test_value_clean, reference_value)


def calculate_qualitative_flag(test_value: str, reference_value: str) -> int:
    """
    计算定性检验结果的标志
    
    Args:
        test_value: 检验结果值
        reference_value: 参考范围
        
    Returns:
        0: 正常, 1: 异常
    """
    if not reference_value:
        return 0
    
    test_lower = test_value.lower().strip()
    ref_lower = reference_value.lower().strip()
    
    # 定义正常值集合
    normal_values = {'阴性', 'negative', '正常', 'normal', '无', 'none', '-'}
    abnormal_values = {'阳性', 'positive', '异常', 'abnormal', '+'}
    
    # 检查是否为明确的阳性结果
    for abnormal in abnormal_values:
        if abnormal in test_lower:
            return 1
    
    # 检查参考值中的正常值
    for normal in normal_values:
        if normal in ref_lower:
            # 如果参考值是阴性，检验值也是阴性则正常
            if normal in test_lower:
                return 0
            else:
                return 1  # 检验值不是阴性则异常
    
    return 0


def determine_test_type(test_value: str) -> str:
    """
    确定检验结果值类型（数值/定性）
    支持科学计数法识别
    
    Args:
        test_value: 检验结果值
        
    Returns:
        '数值' 或 '定性'
    """
    if not test_value or not isinstance(test_value, str):
        return '定性'
    
    # 清理测试值
    clean_value = test_value.strip()
    
    # 检查是否为科学计数法或普通数值
    scientific_number_pattern = r'[0-9]*\.?[0-9]+(?:[eE][+-]?[0-9]+)?'
    if re.search(scientific_number_pattern, clean_value, re.IGNORECASE):
        # 进一步验证是否能转换为数值
        numeric_match = re.search(scientific_number_pattern, clean_value, re.IGNORECASE)
        if numeric_match:
            try:
                float(numeric_match.group(0))
                logger.debug(f"识别为数值类型: {clean_value}")
                return '数值'
            except ValueError:
                pass
    
    # 定性值关键词
    qualitative_keywords = [
        '阴性', '阳性', 'positive', 'negative', 
        '正常', '异常', '无', '有',
        '+', '-', '++', '+++',
        '微量', '少量', '中量', '大量'
    ]
    
    for keyword in qualitative_keywords:
        if keyword in clean_value.lower():
            logger.debug(f"识别为定性类型: {clean_value} (关键词: {keyword})")
            return '定性'
    
    logger.debug(f"默认识别为定性类型: {clean_value}")
    return '定性'


def normalize_time(time_str: str) -> str:
    """
    标准化时间格式
    
    Args:
        time_str: 原始时间字符串
        
    Returns:
        标准化后的时间字符串
    """
    if not time_str:
        return ""
    
    # 清理时间字符串
    time_str = time_str.strip()
    
    # 替换中文日期分隔符
    time_str = re.sub(r'年|月', '-', time_str)
    time_str = re.sub(r'日', ' ', time_str)
    
    # 替换各种分隔符为标准格式
    time_str = re.sub(r'[/\.]', '-', time_str)
    
    # 处理空格
    time_str = re.sub(r'\s+', ' ', time_str)
    
    # 尝试解析并重新格式化，优先匹配带秒的格式
    time_formats = [
        '%Y-%m-%d %H:%M:%S',  # 优先匹配带秒的格式
        '%Y-%m-%d %H:%M',
        '%Y-%m-%d',
    ]
    
    for fmt in time_formats:
        try:
            dt = datetime.strptime(time_str, fmt)
            # 根据原始格式决定输出格式
            if fmt == '%Y-%m-%d %H:%M:%S':
                return dt.strftime('%Y-%m-%d %H:%M:%S')
            elif fmt == '%Y-%m-%d %H:%M':
                return dt.strftime('%Y-%m-%d %H:%M')
            else:  # 只有日期的情况
                return dt.strftime('%Y-%m-%d %H:%M')
        except ValueError:
            continue
    
    # 如果都失败了，返回原始字符串
    logger.warning(f"无法解析时间格式: {time_str}")
    return time_str


def validate_test_item(item_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    验证和标准化单个检验项目数据
    
    Args:
        item_data: 原始检验项目数据
        
    Returns:
        验证和标准化后的检验项目数据
    """
    # 确保必要字段存在
    required_fields = ['test_code', 'test_name', 'test_value', 'test_unit', 'reference_value','abnormal_symbol']
    # required_fields = ['test_code', 'test_name', 'test_value', 'test_unit', 'reference_value']

    for field in required_fields:
        if field not in item_data:
            item_data[field] = ''
    
    # 数据清理
    for field in required_fields:
        if item_data[field] is None:
            item_data[field] = ''
        else:
            item_data[field] = str(item_data[field]).strip()
    
    # 计算派生字段
    item_data['test_flag'] = calculate_test_flag(
        item_data['test_value'], 
        item_data['reference_value']
    )
    
    item_data['test_type'] = determine_test_type(item_data['test_value'])
    
    # 解析参考范围
    ref_range = parse_reference_range(item_data['reference_value'])
    item_data['reference_range_min'] = ref_range['min_value']
    item_data['reference_range_max'] = ref_range['max_value']
    
    return item_data