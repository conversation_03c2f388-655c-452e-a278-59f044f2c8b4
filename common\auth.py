import jwt
import time
import logging
from datetime import timedelta

from django.db import transaction, connections
from rest_framework.authentication import BaseAuthentication
from rest_framework.permissions import BasePermission, IsAuthenticated
from rest_framework import exceptions
from rest_framework.exceptions import PermissionDenied

from apps.users.models import User, SysUser, AuthProjectRoleACL
# from apps.project.models import ProjectSite


class ERPSysJWTAuthentication(BaseAuthentication):
    """
    ERP系统用户JWT验证
    """

    def authenticate(self, request):
        """
        Returns a two-tuple of `User` and token if a valid signature has been
        supplied using JWT-based authentication.  Otherwise returns `None`.
        """
        token = request.META.get('HTTP_X_ACCESS_TOKEN')
        if not token:
            return None

        # 不验证jwt获取payload
        try:
            payload = jwt.decode(token, options={"verify_signature": False, "verify_exp": False})
            username = payload.get('username')
        except jwt.ExpiredSignatureError:
            raise exceptions.AuthenticationFailed('Signature has expired.')
        except jwt.DecodeError:
            raise exceptions.AuthenticationFailed('Error decoding signature.')
        except jwt.InvalidTokenError:
            raise exceptions.AuthenticationFailed()

        if not username:
            raise exceptions.AuthenticationFailed('Invalid signature.')

        # 从sys_user获取secret(password)
        try:
            sys_user = SysUser.objects.using('master').get(username=username)
        except SysUser.DoesNotExist:
            raise exceptions.AuthenticationFailed('No such user')

        # 通过secret验证jwt
        try:
            payload = jwt.decode(token, sys_user.password, algorithms="HS256", options={"verify_exp": False})
        except jwt.ExpiredSignatureError:
            raise exceptions.AuthenticationFailed('Signature has expired.')
        except jwt.DecodeError:
            raise exceptions.AuthenticationFailed('Error decoding signature.')
        except jwt.InvalidTokenError:
            raise exceptions.AuthenticationFailed()

        if sys_user.status != 1:
            raise exceptions.AuthenticationFailed('User account is disabled.')

        # 额外延长2天过期时间
        exp = payload['exp'] + timedelta(days=2).total_seconds()
        if time.time() > exp:
            raise exceptions.AuthenticationFailed('Signature has expired.')

        username = payload.get('username')
        if not username:
            raise exceptions.AuthenticationFailed('Invalid payload.')

        # 插入更新用户信息
        user, created = User.objects.get_or_create(username=username)

        # if created:
        #     with transaction.atomic():
        #         user.post = sys_user.post
        #         user.save()

        if not user.is_active:
            raise exceptions.AuthenticationFailed('User account is disabled.')

        request.sys_user = sys_user
        return (user, token)

    def authenticate_header(self, request):
        return 'JWT'


class IsCRCUser(BasePermission):
    """
    Allows access only to crc users.
    """

    def has_permission(self, request, view):
        return bool(request.user and 'CRC' in request.sys_user.post)


def has_project_access_permission(user, project_id, access_scope=None):
    """
    判断用户在指定项目中是否有对应访问权限（如访问全部功能/部分功能）
    :param user: 当前用户
    :param project_id: 项目标识
    :param access_scope: 权限范围（可选：SCOPE_ALL, SCOPE_PARTIAL）
    :return: True/False
    """

    # 白名单用户
    if user.project_whitelist_flag:
        return True

    with connections['OTDB'].cursor() as cursor:
        sql = """
        SELECT t2.code
        FROM auth_user_role t1
        JOIN auth_role t2 ON t2.ID = t1.role_id
        JOIN org_user t3 ON t3.user_id = t1.user_id AND t3.org_id = t1.org_id
        WHERE t2.is_del = 0
          AND t3.org_id = 'e888888'
          AND t3.employee_no = %s
          AND t1.project_id = %s
        """
        cursor.execute(sql, [user.username, project_id])
        results = cursor.fetchall()

    role_codes = [i[0] for i in results if i[0]]

    if not role_codes:
        return False

    query = AuthProjectRoleACL.objects.filter(
        is_allowed=True,
        delete_flag=0,
        role_code__in=role_codes,
    )
    if access_scope is not None:
        query = query.filter(access_scope=access_scope)

    return query.exists()


def check_project_access_permission(user, project_id, access_scope=None):
    """
    检查用户是否具有指定项目的权限，没有权限则抛出异常
    :param user: 当前用户
    :param project_id: 项目标识
    :param access_scope: 权限范围（可选）
    """
    if not has_project_access_permission(user, project_id, access_scope):
        raise PermissionDenied("您没有权限访问该项目或功能")


def has_project_site_access_permission(user, project_id, project_site_id):
    """
    判断用户在指定项目中心中是否有对应访问权限
    """
    if user.project_whitelist_flag:
        return True

    with connections['OTDB'].cursor() as cursor:
        sql = """
        SELECT t2.code, t1.project_site_id
        FROM auth_user_role t1
        JOIN auth_role t2 ON t2.ID = t1.role_id
        JOIN org_user t3 ON t3.user_id = t1.user_id AND t3.org_id = t1.org_id
        WHERE t2.is_del = 0
          AND t3.org_id = 'e888888'
          AND t3.employee_no = %s
          AND t1.project_id = %s
        """
        cursor.execute(sql, [user.username, project_id])
        results = cursor.fetchall()

    role_codes = [i[0] for i in results if i[0]]
    project_site_ids = [i[1] for i in results if i[0]]

    if not role_codes:
        return False

    query = AuthProjectRoleACL.objects.filter(
        is_allowed=True,
        role_code__in=role_codes,
    )

    if not query.exists():
        return False

    if AuthProjectRoleACL.SCOPE_ALL in query.values_list('access_scope', flat=True):
        return True

    return project_site_id in project_site_ids


def check_project_site_access_permission(user, project_id, project_site_id):
    """
    检查用户是否具有指定项目中心的权限，没有权限则抛出异常
    """
    if not has_project_site_access_permission(user, project_id, project_site_id):
        raise PermissionDenied("您没有权限访问该项目中心功能")
