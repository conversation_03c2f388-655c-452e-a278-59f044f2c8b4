"""
AE等级识别系统的PocketFlow流程定义
"""
import logging
from pocketflow import AsyncFlow
from typing import List, Dict, Any

from script.ae_grade.nodes import (
    AbnormalItemFilterAndPreprocessNode,
    CTCAERuleMatcherNode,
    ConcurrencyControlledBatchNode,
    OutputNode,
    NoAbnormalItemsNode
)

logger = logging.getLogger(__name__)


def create_ae_recognition_flow(timeout=None) -> AsyncFlow:
    """
    创建AE等级识别处理流程

    流程逻辑：
    1. 数据预处理 - 转换原始数据库格式
    2. 异常项筛选 - 过滤需要处理的项目
        - 有异常项：进入CTCAE规则匹配
        - 无异常项：直接输出正常结果
    3. CTCAE规则匹配 - 使用rerank服务匹配相关规则
    4. 双模型并发推理 - 并行调用两个模型
    5. 简化输出 - 生成标准化结果

    Args:
        timeout: 模型调用超时时间（秒）

    Returns:
        AsyncFlow: 配置好的流程对象
    """
    # 创建各个节点
    filter_and_preprocess = AbnormalItemFilterAndPreprocessNode()
    rule_matcher = CTCAERuleMatcherNode()
    inference = ConcurrencyControlledBatchNode(max_retries=2, wait=5, timeout=timeout)
    output = OutputNode()
    no_abnormal = NoAbnormalItemsNode()

    # 定义流程连接
    # 1. 合并的筛选和预处理节点根据结果决定路径
    filter_and_preprocess - "has_abnormal_items" >> rule_matcher  # 有异常项，进入规则匹配
    filter_and_preprocess - "no_abnormal_items" >> no_abnormal    # 无异常项，直接输出

    # 2. 规则匹配 -> 模型推理 -> 输出
    rule_matcher >> inference >> output

    # 3. 无异常项也输出
    no_abnormal >> output

    # 创建流程
    flow = AsyncFlow(start=filter_and_preprocess)

    return flow


async def process_ae_analysis(test_results, timeout=None, prompt_templates=None, task_info=None):
    """
    处理AE分析的主入口函数

    Args:
        test_results: 原始数据库查询结果
        timeout: 模型调用超时时间（秒）
        prompt_templates: 预加载的提示词模板字典
        task_info: 任务信息字典，包含task_id, create_user, create_name, business_id等

    Returns:
        AE识别的最终输出结果
    """
    # 如果没有传入提示词模板，使用默认空模板
    if prompt_templates is None:
        logger.warning("未传入提示词模板，将使用SmartPromptBuilder的默认模板")
        prompt_templates = None  # 保持None，让SmartPromptBuilder使用默认模板

    # 创建流程
    flow = create_ae_recognition_flow(timeout=timeout)

    # 准备共享状态
    shared = {
        "raw_input_data": test_results,
        "test_results": [],
        "original_raw_data": [],
        "abnormal_items": [],
        "normal_items": [],
        "matched_rules_by_item": {},
        "items_with_ctcae_matches": [],
        "items_without_ctcae_matches": [],
        "model_results": [],
        "primary_parsed_results": [],
        "secondary_parsed_results": [],
        "final_compared_results": [],
        "comparison_stats": {},
        "output_result": {},
        "prompt_templates": prompt_templates,  # 添加预加载的提示词模板
        "task_info": task_info  # 直接传递完整的task_info对象
    }

    logger.info(f"开始运行AE识别流程，输入项目数: {len(test_results)}")

    # 运行流程
    await flow.run_async(shared)

    # 获取结果
    output_result = shared.get("output_result", {})

    # print("*"*50)
    # for a in output_result["dual_model_data"]["llm1_results"]:
    #     print(a)
    # print("*"*50)
    # for a in output_result["dual_model_data"]["llm2_results"]:
    #     print(a)
    # 添加元数据
    if "metadata" not in output_result:
        output_result["metadata"] = {}

    output_result["metadata"]["total_input_items"] = len(test_results)

    logger.info(f"AE识别流程完成，输出项目数: {len(output_result.get('results', []))}")
    return output_result
