import os
import json
import uuid
import logging
from datetime import timedelta
import pandas as pd
from django.db.models import Case, When, Value, IntegerField
from django.http import FileResponse
from django.conf import settings
from django.shortcuts import render
from django.db import transaction
from django.db.models import Subquery, OuterRef
from django.db.models import Count
from django.db import connections
from rest_framework.viewsets import GenericViewSet
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import serializers
from rest_framework.fields import CharField
from rest_framework import filters, viewsets
from rest_framework.mixins import ListModelMixin, CreateModelMixin, UpdateModelMixin, DestroyModelMixin
from rest_framework.viewsets import ReadOnlyModelViewSet
from rest_framework.filters import OrderingFilter
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.renderers import BaseRenderer
from rest_framework.exceptions import Validation<PERSON><PERSON>r, NotFound, APIException
from rest_framework.parsers import <PERSON>PartParser, FormParser
from rest_framework.permissions import IsAuthenticated
from rest_framework.exceptions import APIException

from django_filters.rest_framework import DjangoFilterBackend

from rest_pandas.views import PandasViewBase
from rest_pandas import PandasExcelRenderer
from openpyxl.styles import Font, Border
from openpyxl.styles import Alignment

from drf_spectacular.utils import extend_schema
from drf_spectacular.utils import extend_schema, OpenApiParameter

from apps.users.models import AuthProjectRoleACL
from common.auth import ERPSysJWTAuthentication, check_project_access_permission
from common.utils import calculate_file_hash
from common.minio_client import get_minio_client
from common.pagination import StandardResultsSetPagination
from common.views import deprecated_view

from . import serializers
from . import models
from . import filters


class FileAlreadyExistsError(APIException):
    status_code = status.HTTP_409_CONFLICT
    default_detail = "文件已存在！！！"
    default_code = "error"


logger = logging.getLogger('app')


class BaseAPIView(APIView):
    authentication_classes = [ERPSysJWTAuthentication]
    permission_classes = [IsAuthenticated]


class BaseViewSet(BaseAPIView, GenericViewSet):
    pass


class BaseListViewSet(BaseViewSet, ListModelMixin):
    filter_backends = (DjangoFilterBackend, OrderingFilter)


class ProjectListViewSet(BaseListViewSet):
    # queryset = models.Project.objects.annotate(site_count=Count('sites'), subject_count=Count('subjects'),)
    queryset = models.Project.objects.filter(delete_flag=0)
    serializer_class = serializers.ProjectSerializer
    filterset_class = filters.ProjectFilter
    pagination_class = StandardResultsSetPagination
    ordering_fields = '__all__'
    # ordering = ['project_no']

    def get_queryset(self):
        # 白名单用户
        if self.request.user.project_whitelist_flag:
            projects = self.queryset.all()
            for project in projects:
                project.has_file_flag = 0
                project.role_codes = []  # 添加空的角色代码列表
            with connections['ETMF'].cursor() as cursor:
                project_nos = [project.project_no for project in projects]
                placeholders = ','.join(['%s'] * len(project_nos))
                sql = f"""
                    SELECT project_no,count(1) > 0 AS has_file_flag  
                    FROM etmf_file_update_log 
                    WHERE file_id not IN(SELECT file_id  FROM etmf_file_update_log 
                    WHERE file_state = 'delete') and (project_no IN ({placeholders}) OR project_no = '') 
                    GROUP BY project_no
                    """
                cursor.execute(sql, project_nos)
                projects_with_files = {project_no for project_no, has_file_flag in cursor.fetchall()}
                projects = projects.annotate(
                    has_file_flag=Case(
                        When(project_no__in=projects_with_files, then=Value(1)),
                        default=Value(0),
                        output_field=IntegerField()
                    )
                )

            return projects

        # 可以访问项目的角色
        role_codes = AuthProjectRoleACL.objects.filter(is_allowed=1,delete_flag=0).values_list('role_code', flat=True)
        role_codes = tuple(role_codes)

        logging.info(f"role_codes: {role_codes}")

        # 通过OT项目成员获取项目号
        with connections['OTDB'].cursor() as cursor:
            sql = """
            SELECT t1.project_id, t2.code
            FROM
                auth_user_role t1
                JOIN auth_role t2 ON t2.ID = t1.role_id
                JOIN org_user t3 ON t3.user_id = t1.user_id
                AND t3.org_id = t1.org_id
            WHERE
                t2.is_del = 0
                -- AND t1.platform = 'O_CTMS_TRIAL'
                AND t3.org_id = 'e888888'
                AND t3.employee_no = %s
                AND t1.project_id != ''
                AND t2.code in %s
            """
            cursor.execute(sql, [self.request.user.username, role_codes])
            results = cursor.fetchall()

        # 按项目ID分组角色代码，并使用set去重
        project_roles = {}
        code_mapping = {
            "20101": "执行-PM",
            "20105": "执行-CoPM",
            "20107": "授权CRC",
            "20111": "执行-PL",
            "20116": "执行-监管人",
            "20117": "BU 总",
            "20119": "BU Head",
            "20121": "PMD",
            "212886296": "backup-CRC(参与项目执行CRC)",
            "23349789": "BU PA",
            "10306": "BU PA",
            "1819817163": "兼职PA",
            "20104": "执行-PA",
            "23364244": "大客户PA"
        }

        for project_id, role_code in results:
            if project_id not in project_roles:
                project_roles[project_id] = set()
            # 示例
            code_name = code_mapping.get(role_code, "未知名称")  # 结果为"执行-PM"
            project_roles[project_id].add(code_name)

        # 将set转换为list
        for project_id in project_roles:
            project_roles[project_id] = list(project_roles[project_id])

        project_ids = list(project_roles.keys())
        logging.info(f"project_ids: {len(project_ids)}")

        if project_ids:
            json_ids = json.dumps(project_ids)

            subquery = f"""
            SELECT project_id
            FROM JSON_TABLE(%s, '$[*]' COLUMNS(project_id VARCHAR(50) PATH '$')) AS jt
            """

            projects = models.Project.objects.extra(
                where=["project_id IN (" + subquery + ")"],
                params=[json_ids]
            ).filter(delete_flag=0)


            with connections['ETMF'].cursor() as cursor:
                project_nos = [project.project_no for project in projects]
                if project_nos:  # 只有当有项目时才执行查询
                    placeholders = ','.join(['%s'] * len(project_nos))
                    sql = f"""
                                            SELECT project_no,count(1) > 0 AS has_file_flag  
                                            FROM etmf_file_update_log 
                                            WHERE file_id not IN(SELECT file_id  FROM etmf_file_update_log 
                                            WHERE file_state = 'delete') and (project_no IN ({placeholders}) OR project_no = '') 
                                            GROUP BY project_no
                                        """
                    cursor.execute(sql, project_nos)
                    projects_with_files = {project_no for project_no, has_file_flag in cursor.fetchall()}

                    # 使用Case When添加has_file_flag注解
                    projects = projects.annotate(
                        has_file_flag=Case(
                            When(project_no__in=projects_with_files, then=Value(1)),
                            default=Value(0),
                            output_field=IntegerField()
                        )
                    )
                else:
                    # 如果没有项目，添加默认的has_file_flag注解
                    projects = projects.annotate(
                        has_file_flag=Value(0, output_field=IntegerField())
                    )
            # 为了role_codes，我们需要自定义一个序列化方法
            # 将角色代码信息存储在request中供序列化器使用
            self.request.project_roles = project_roles
            return projects
        # 返回空的QuerySet而不是空列表
        return self.queryset.none()

    @extend_schema(summary='项目信息列表', tags=['项目中心受试者'])
    def list(self, request, *args, **kwargs):
        # queryset = self.filter_queryset(self.get_queryset())

        # page = self.paginate_queryset(queryset)
        # if page is not None:
        #     project_ids = [project.id for project in page]
        #     projects = queryset.filter(id__in=project_ids).annotate(
        #         site_count=Count('sites'), subject_count=Count('subjects'),)

        #     serializer = self.get_serializer(projects, many=True)
        #     return self.get_paginated_response(serializer.data)

        # serializer = self.get_serializer(queryset, many=True)
        # return Response(serializer.data)
        return super().list(request, *args, **kwargs)


class ProjectSiteListViewSetDeprecated(BaseListViewSet):
    queryset = models.ProjectSite.objects.filter(delete_flag=0)
    serializer_class = serializers.ProjectSiteSerializer
    filterset_class = filters.ProjectSiteFilter
    pagination_class = StandardResultsSetPagination
    ordering_fields = '__all__'
    # ordering = ['hosp_department_no']

    @extend_schema(summary='项目中心信息列表', tags=['项目中心受试者'], deprecated=True)
    @deprecated_view
    def list(self, request, project_id, format=None):
        queryset = self.get_queryset()
        queryset = queryset.filter(project_id=project_id)
        queryset = self.filter_queryset(queryset)

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)


class ProjectSiteListViewSet(BaseListViewSet):
    queryset = models.ProjectSite.objects.filter(delete_flag=0)
    serializer_class = serializers.ProjectSiteSerializer
    filterset_class = filters.ProjectSiteFilter
    pagination_class = StandardResultsSetPagination
    ordering_fields = '__all__'
    # ordering = ['hosp_department_no']

    # def get_queryset(self):
    #     return models.ProjectSite.objects.filter(delete_flag=0).select_related('project')

    def get_queryset(self):
        project_id = self.request.query_params.get('project_id')
        project_no = self.request.query_params.get('project_no')

        if not project_id and not project_no:
            raise ValidationError("请填写project_id或project_no，二者必填其一。")

        if not project_id:
            project_site = self.queryset.select_related('project').filter(project__project_no=project_no).first()
            if not project_site:
                return self.queryset.none()

            project_id = project_site.project_id

        logging.info(f"project_id: {project_id}")
        
        # 白名单用户
        if self.request.user.project_whitelist_flag:
            return self.queryset.filter(project_id=project_id).select_related('project')

        # 通过OT项目成员获取当前项目当前用户的角色
        with connections['OTDB'].cursor() as cursor:
            sql = """
            SELECT 
                t2.code,
                t1.project_site_id
            FROM
                auth_user_role t1
                JOIN auth_role t2 ON t2.ID = t1.role_id
                JOIN org_user t3 ON t3.user_id = t1.user_id 
                AND t3.org_id = t1.org_id 
            WHERE
                t2.is_del = 0 
                -- AND t1.platform = 'O_CTMS_TRIAL' 
                AND t3.org_id = 'e888888' 
                AND t3.employee_no = %s
                AND t1.project_id = %s
            """
            cursor.execute(sql, [self.request.user.username, project_id])
            results = cursor.fetchall()

        role_codes = [i[0] for i in results if i[0]]
        logging.info(f"role_codes: {role_codes}")
        
        project_site_ids = [i[1] for i in results if i[1]]
        logging.info(f"project_site_ids: {project_site_ids}")

        if role_codes:
            # 可以访问项目的范围
            access_scopes = AuthProjectRoleACL.objects.filter(
                is_allowed=1, role_code__in=role_codes).values_list('access_scope', flat=True)
            access_scopes = tuple(access_scopes)
            
            if AuthProjectRoleACL.SCOPE_ALL in access_scopes:
                return self.queryset.filter(project_id=project_id).select_related('project')

            if AuthProjectRoleACL.SCOPE_PARTIAL in access_scopes:
                return self.queryset.filter(project_id=project_id, project_site_id__in=project_site_ids).select_related('project')

        return self.queryset.none()

    @extend_schema(summary='项目中心信息列表', tags=['项目中心受试者'])
    def list(self, request, format=None):
        return super().list(request, format)


# class ProjectMaterialInfoListViewSetDeprecated(BaseListViewSet, CreateModelMixin):
#     parser_classes = (MultiPartParser, )  # 支持文件上传
#     queryset = models.ProjectMaterialInfo.objects.filter(delete_flag=0)
#     serializer_class = serializers.ProjectMaterialInfoSerializer
#     filterset_class = filters.ProjectMaterialInfoFilter

#     # def get_serializer_class(self):
#     #     if self.action == 'list':
#     #         return serializers.ProjectMaterialInfoSerializer
#     #     elif self.action == 'create':
#     #         return serializers.ProjectMaterialFileSerializer
#     #     return super().get_serializer_class()

#     @extend_schema(
#         summary='项目素材信息列表',
#         tags=['项目'],
#         # request=serializers.ProjectMaterialLatestRequestSerializer,
#         responses=serializers.ProjectMaterialInfoSerializer,
#         deprecated=True
#     )
#     def list(self, request, project_id, format=None):
#         queryset = self.get_queryset()
#         queryset = queryset.filter(project_id=project_id)
#         queryset = self.filter_queryset(queryset)

#         page = self.paginate_queryset(queryset)
#         if page is not None:
#             serializer = self.get_serializer(page, many=True)
#             return self.get_paginated_response(serializer.data)

#         serializer = self.get_serializer(queryset, many=True)
#         return Response(serializer.data)

#     @extend_schema(
#         summary='获取最新的项目素材信息',
#         tags=['项目'],
#         parameters=[serializers.ProjectMaterialLatestRequestSerializer],
#         responses=serializers.ProjectMaterialInfoSerializer,
#         deprecated=True
#     )
#     @action(detail=False, methods=['get'])
#     def latest(self, request, project_id, format=None):
#         category = request.query_params.get('category')
#         queryset = self.queryset
#         if category:
#             queryset = queryset.filter(project_id=project_id, category=category)
#         latest_info = queryset.order_by('-create_time').first()
#         if not latest_info:
#             raise NotFound("还没有上传素材文件")
#         serializer = self.get_serializer(latest_info)
#         return Response(serializer.data)

#     @extend_schema(
#         summary='创建项目素材信息',
#         tags=['项目'],
#         request=serializers.ProjectMaterialCreateRequestSerializer,
#         responses=serializers.ProjectMaterialInfoSerializer,
#         deprecated=True
#     )
#     def create(self, request, project_id, format=None):
#         file = request.FILES.get('file')
#         category = request.POST.get('category') or models.ProjectMaterialInfo.CRF_TEMPLATE
#         if not file:
#             raise ValidationError({'file': ['未提供文件']})

#         # 检查项目是否存在
#         try:
#             project = models.Project.objects.get(project_id=project_id)
#         except models.Project.DoesNotExist:
#             raise NotFound({'project_id': ['项目不存在']})

#         # 计算文件的 SHA-256 哈希值
#         hash = calculate_file_hash(file)
#         if models.ProjectMaterialInfo.objects.filter(
#             project_id=project_id, category=category, file__hash=hash
#         ).exists():
#             raise FileAlreadyExistsError("文件已存在")

#         # 生成唯一的对象名称
#         _, ext = os.path.splitext(file.name)
#         object_name = f"{uuid.uuid4()}{ext}"
#         bucket_name = settings.MINIO_BUCKET_NAME

#         # 保存文件
#         try:
#             minio_client = get_minio_client()
#             # Ensure bucket exists
#             if not minio_client.bucket_exists(bucket_name):
#                 minio_client.make_bucket(bucket_name)
#             # Upload file to MinIO
#             minio_client.put_object(
#                 bucket_name=bucket_name,
#                 object_name=object_name,
#                 data=file,
#                 length=file.size,
#                 part_size=1024*1024*5,
#                 content_type=file.content_type
#             )
#         except Exception as e:
#             logger.error(e)
#             raise APIException(f"文件上传失败：{e}")

#         # 数据入库
#         with transaction.atomic():
#             material_file = {
#                 'original_filename': file.name,
#                 'bucket_name': bucket_name,
#                 'object_name': object_name,
#                 'content_type': file.content_type,
#                 'size': file.size,
#                 'hash': hash,
#             }
#             material_file = models.ProjectMaterialFile.objects.create(**material_file)

#             material_info = {
#                 'category': category,
#                 'file': material_file,
#                 'project': project,
#                 'create_user': request.sys_user.username,
#                 'create_name': request.sys_user.realname,
#             }
#             material_info = models.ProjectMaterialInfo.objects.create(**material_info)

#             serializer = serializers.ProjectMaterialInfoSerializer(material_info)
#             # if not serializer.is_valid():
#             #     raise APIException(serializer.errors)

#         return Response(serializer.data)


class ProjectMaterialInfoListViewSet(BaseViewSet, CreateModelMixin):
    parser_classes = (MultiPartParser, )  # 支持文件上传
    queryset = models.ProjectMaterialFile.objects.filter(delete_flag=0)
    serializer_class = serializers.ProjectMaterialFileSerializer
    filterset_class = filters.ProjectMaterialFileFilter
    pagination_class = StandardResultsSetPagination

    # def get_serializer_class(self):
    #     if self.action == 'list':
    #         return serializers.ProjectMaterialInfoSerializer
    #     elif self.action == 'create':
    #         return serializers.ProjectMaterialFileSerializer
    #     return super().get_serializer_class()

    # @extend_schema(
    #     summary='项目素材文件列表',
    #     tags=['项目'],
    #     # request=serializers.ProjectMaterialLatestRequestSerializer,
    #     responses=serializers.ProjectMaterialInfoSerializer
    # )
    # def list(self, request, format=None):
    #     return super().list(request, format)

    @extend_schema(
        summary='获取最新的项目素材文件',
        tags=['病历归集'],
        parameters=[serializers.ProjectMaterialLatestRequestSerializer],
        responses=serializers.ProjectMaterialFileSerializer
    )
    @action(detail=False, methods=['get'])
    def latest(self, request, format=None):
        project_id = request.query_params.get('project_id') or ''
        category = request.query_params.get('category') or ''
        epoch_id = request.query_params.get('epoch_id') or ''
        queryset = self.queryset
        material_file = queryset.select_related('material_info').filter(
            material_info__project_id=project_id,
            material_info__category=category,
            material_info__epoch_id = epoch_id
        ).order_by('-create_time').first()
        if material_file:
            serializer = self.get_serializer(material_file)
            return Response(serializer.data)
        return Response({})

    @extend_schema(
        summary='上传项目素材文件',
        tags=['病历归集'],
        request=serializers.ProjectMaterialCreateRequestSerializer,
        responses=serializers.ProjectMaterialFileSerializer
    )
    def create(self, request, format=None):
        file = request.FILES.get('file')
        project_id = request.POST.get('project_id')
        epoch_id = request.POST.get('epoch_id')
        category = request.POST.get('category') or models.ProjectMaterialInfo.CRF_TEMPLATE

        if not file:
            raise ValidationError({'file': ['未提供文件']})
        
        # 检查项目是否存在
        try:
            project = models.Project.objects.filter(delete_flag=0).get(project_id=project_id)
        except models.Project.DoesNotExist:
            raise NotFound({'project_id': ['项目不存在']})
        
        # 检查项目权限
        check_project_access_permission(request.user, project_id, access_scope=AuthProjectRoleACL.SCOPE_ALL)
        
        # 计算文件的 SHA-256 哈希值
        hash = calculate_file_hash(file)

        # if models.ProjectMaterialFile.objects.filter(delete_flag=0).filter(
        #     material_info__delete_flag=0,
        #     material_info__project_id=project_id,
        #     material_info__category=category,
        #     hash=hash
        # ).exists():
        #     raise FileAlreadyExistsError("文件已存在")

        # 生成唯一的对象名称
        _, ext = os.path.splitext(file.name)
        object_name = f"{uuid.uuid4().hex}{ext}"
        bucket_name = settings.MINIO_BUCKET_NAME

        # 保存文件
        try:
            minio_client = get_minio_client()
            # Ensure bucket exists
            if not minio_client.bucket_exists(bucket_name):
                minio_client.make_bucket(bucket_name)
            # Upload file to MinIO
            minio_client.put_object(
                bucket_name=bucket_name,
                object_name=object_name,
                data=file,
                length=file.size,
                part_size=1024 * 1024 * 5,
                content_type=file.content_type
            )
        except Exception as e:
            logger.error(e)
            raise APIException(f"文件上传失败：{e}")

        # 数据入库
        with transaction.atomic():
            # 删除历史上传的素材文件
            models.ProjectMaterialFile.objects.filter(
                delete_flag=0,
                material_info__epoch_id=epoch_id,
                material_info__project_id=project_id,
                material_info__category=category
            ).update(
                delete_flag=1,
                update_user=request.sys_user.username,
                update_name=request.sys_user.realname
            )

            try:
                material_info = models.ProjectMaterialInfo.objects.filter(delete_flag=0).get(
                    epoch_id=epoch_id,
                    project=project,
                    category=category
                )
            except models.ProjectMaterialInfo.DoesNotExist:
                # 如果不存在，创建新记录
                material_info = models.ProjectMaterialInfo.objects.create(
                    category=category,
                    project=project,
                    epoch_id=epoch_id,
                    create_user=request.sys_user.username,
                    create_name=request.sys_user.realname,
                )

            material_file = {
                'original_filename': file.name,
                'bucket_name': bucket_name,
                'object_name': object_name,
                'content_type': file.content_type,
                'size': file.size,
                'hash': hash,
                'create_user': request.sys_user.username,
                'create_name': request.sys_user.realname,
                'material_info': material_info
            }
            material_file = models.ProjectMaterialFile.objects.create(**material_file)

            serializer = serializers.ProjectMaterialFileSerializer(material_file)
            # if not serializer.is_valid():
            #     raise APIException(serializer.errors)
        return Response(serializer.data)


class ProjectMaterialFileDetailViewSet(BaseViewSet, UpdateModelMixin, DestroyModelMixin):
    queryset = models.ProjectMaterialFile.objects.filter(delete_flag=0)
    serializer_class = serializers.ProjectMaterialFileSerializer
    http_method_names = ['patch', 'delete']

    @extend_schema(
        summary='更新项目素材文件名',
        tags=['病历归集'],
        request=serializers.ProjectMaterialFileUpdateSerializer,
        responses=serializers.ProjectMaterialFileSerializer
    )
    def partial_update(self, request, *args, **kwargs):
        data = request.data.copy()
        data['update_user'] = request.sys_user.username
        data['update_name'] = request.sys_user.realname

        instance = self.get_object()
        project_id = instance.material_info.project_id
        
        # 检查项目权限
        check_project_access_permission(request.user, project_id, access_scope=AuthProjectRoleACL.SCOPE_ALL)
        
        partial = kwargs.pop('partial', True)
        serializer = self.get_serializer(instance, data=data, partial=partial)
        if serializer.is_valid():
            self.perform_update(serializer)
            return Response(serializer.data)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @extend_schema(
        summary='删除项目素材文件',
        tags=['病历归集'],
        request=serializers.ProjectMaterialFileUpdateSerializer,
        responses=serializers.ProjectMaterialFileSerializer
    )
    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        project_id = instance.material_info.project_id
        
        # 检查项目权限
        check_project_access_permission(request.user, project_id, access_scope=AuthProjectRoleACL.SCOPE_ALL)

        instance.delete_flag = 1
        instance.update_user = request.sys_user.username
        instance.update_name = request.sys_user.realname
        instance.save()
        return Response(status=status.HTTP_204_NO_CONTENT)
