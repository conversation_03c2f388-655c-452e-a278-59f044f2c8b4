# -*- coding: utf-8 -*-
"""
CTCAE规则智能匹配器
使用rerank模型实现精准的规则匹配，大幅减少提示词长度
"""
import time
import concurrent.futures
from typing import List, Dict, Any
import logging

from common.clients.rerank_client import call_rerank

logger = logging.getLogger(__name__)

class CTCAERuleMatcher:
    """CTCAE规则智能匹配器 - 使用通用rerank客户端的默认配置"""

    def __init__(self, max_concurrent_rerank=3):
        """
        初始化匹配器
        采用"约定优于配置"原则，使用通用rerank客户端的默认配置
        
        Args:
            max_concurrent_rerank: 最大并发rerank调用数，默认3
        """
        # 缓存
        self._ctcae_rules = None
        self._rule_search_texts = None
        # 时间统计
        self.total_rerank_time = 0.0
        self.rerank_call_count = 0
        # 并发配置
        self.max_concurrent_rerank = max_concurrent_rerank


    def _load_ctcae_rules(self) -> List[Dict[str, Any]]:
        """从新的数据结构中加载CTCAE规则"""
        if self._ctcae_rules is not None:
            return self._ctcae_rules

        try:
            # from script.ae_grade.data.ctcae_rules import get_ctcae_rules
            from script.ae_grade.data.ctcae_rules_2 import get_ctcae_rules

            # 直接从结构化数据中获取规则
            raw_rules = get_ctcae_rules()

            # 转换为匹配器需要的格式
            rules = []
            for rule in raw_rules:
                # 优化搜索文本构建：只包含检查项和AE名称，移除ae_definition以提高匹配精度
                check_items_text = ' '.join(rule['check_items'])
                # 构建简洁高效的搜索文本：检查项在前，AE名称在后，检查项重复增加权重
                search_text = f"{check_items_text} {rule['ae_name']} {check_items_text}"

                processed_rule = {
                    'id': rule['id'],
                    'ae_name': rule['ae_name'],
                    'ae_definition': rule['ae_definition'],
                    'check_items': rule['check_items'],
                    'grade_rules': rule['grade_rules'],
                    'search_text': search_text
                }
                rules.append(processed_rule)

            self._ctcae_rules = rules
            print(f"成功加载{len(rules)}条CTCAE规则")
            return rules

        except Exception as e:
            logger.error(f"加载CTCAE规则失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    def _rerank_with_service(self, query: str, candidates: List[str], silent=False) -> List[Dict[str, Any]]:
        """
        使用rerank服务进行重排序，返回分数大于0的结果

        Args:
            query: 查询文本
            candidates: 候选文本列表
            silent: 是否静默模式（不输出日志）

        Returns:
            List[Dict]: 包含index和score的结果列表，只返回分数>0的结果
        """
        try:
            if not silent:
                print(f">> Rerank请求 - 查询: '{query}'")
            
            # 记录开始时间
            start_time = time.time()

            results = call_rerank(
                query=query,
                texts=candidates,
                raw_scores=True,
                return_text=False,
                truncate=True,
                truncation_direction="Right"
            )
            
            # 记录结束时间并累计统计
            end_time = time.time()
            call_duration = end_time - start_time
            self.total_rerank_time += call_duration
            self.rerank_call_count += 1

            # 筛选分数大于0的结果
            filtered_results = []
            for result in results:
                if result['score'] > 0:
                    filtered_results.append({
                        'index': result['index'],
                        'score': result['score']
                    })

            if not silent:
                print(f">> Rerank结果 - 总候选数: {len(results)}, 分数>0的数量: {len(filtered_results)}, 耗时: {call_duration:.2f}秒")

            return filtered_results

        except Exception as e:
            logger.error(f"Rerank服务调用失败: {e}")
            raise
    #
    # def _rerank_single_item(self, item_data: Dict[str, Any]) -> Dict[str, Any]:
    #     """
    #     单个项目的rerank调用（用于并发执行）
    #
    #     Args:
    #         item_data: 包含item_id, test_name, candidates的字典
    #
    #     Returns:
    #         包含item_id和rerank结果的字典
    #     """
    #     item_id = item_data['item_id']
    #     test_name = item_data['test_name']
    #     candidates = item_data['candidates']
    #
    #     try:
    #         print(f"🔄 并发Rerank - 项目{item_id}: '{test_name}'")
    #         rerank_results = self._rerank_with_service(test_name, candidates)
    #         return {
    #             'item_id': item_id,
    #             'success': True,
    #             'rerank_results': rerank_results,
    #             'error': None
    #         }
    #     except Exception as e:
    #         logger.error(f"项目{item_id}的rerank调用失败: {e}")
    #         return {
    #             'item_id': item_id,
    #             'success': False,
    #             'rerank_results': [],
    #             'error': str(e)
    #         }

    def match_relevant_rules_concurrent(self, test_items: List[Dict[str, Any]]) -> Dict[int, List[Dict[str, Any]]]:
        """
        并发版本的规则匹配方法 - 使用整洁的表格化日志输出
        
        Args:
            test_items: 检验项列表
            
        Returns:
            Dict[item_id, List[matched_rules]] - 返回所有相关规则
        """
        rules = self._load_ctcae_rules()
        if not rules:
            logger.warning("没有可用的CTCAE规则")
            return {}

        # 缓存搜索文本，避免重复构建
        if self._rule_search_texts is None:
            self._rule_search_texts = [rule['search_text'] for rule in rules]

        # 收集日志信息的列表
        match_logs = []
        
        # 1. 先进行精确匹配（不需要并发）
        exact_matches_by_item = {}
        rerank_tasks = []
        
        print("🔍 开始CTCAE规则匹配...")
        
        for item in test_items:
            item_id = item.get('序号', item.get('id', 0))
            test_name = item.get('检查项目', item.get('test_name', ''))

            if not test_name:
                logger.warning(f"检验项目 {item_id} 缺少名称，跳过")
                exact_matches_by_item[item_id] = []
                continue

            # 精确匹配
            exact_matches = []
            for rule in rules:
                for check_item in rule['check_items']:
                    if len(test_name) == 1:
                        if test_name == check_item:
                            exact_matches.append(rule)
                            break
                    else:
                        if test_name in check_item or check_item in test_name:
                            exact_matches.append(rule)
                            break

            exact_matches_by_item[item_id] = exact_matches

            # 准备rerank任务数据
            rerank_tasks.append({
                'item_id': item_id,
                'test_name': test_name,
                'candidates': self._rule_search_texts
            })

        # 2. 并发执行rerank调用（静默模式）
        print(f"🚀 开始并发Rerank调用 - 任务数: {len(rerank_tasks)}, 最大并发数: {self.max_concurrent_rerank}")
        
        concurrent_start_time = time.time()
        rerank_results_by_item = {}
        
        if rerank_tasks:
            with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_concurrent_rerank) as executor:
                # 提交所有任务（使用静默模式的rerank调用）
                future_to_item = {
                    executor.submit(self._rerank_single_item_silent, task_data): task_data['item_id'] 
                    for task_data in rerank_tasks
                }
                
                # 收集结果
                for future in concurrent.futures.as_completed(future_to_item):
                    result = future.result()
                    item_id = result['item_id']
                    if result['success']:
                        rerank_results_by_item[item_id] = result['rerank_results']
                    else:
                        logger.warning(f"项目{item_id}的rerank调用失败: {result['error']}")
                        rerank_results_by_item[item_id] = []

        concurrent_end_time = time.time()
        concurrent_duration = concurrent_end_time - concurrent_start_time
        
        print(f"✅ 并发Rerank完成 - 总耗时: {concurrent_duration:.2f}秒, 平均每项: {concurrent_duration/len(rerank_tasks):.2f}秒")

        # 3. 合并精确匹配和rerank结果，并收集日志信息
        matched_rules = {}
        
        for item in test_items:
            item_id = item.get('序号', item.get('id', 0))
            test_name = item.get('检查项目', item.get('test_name', ''))
            
            if not test_name:
                continue
                
            exact_matches = exact_matches_by_item.get(item_id, [])
            rerank_results = rerank_results_by_item.get(item_id, [])
            
            # 合并结果逻辑
            exact_rule_ids = {rule['id'] for rule in exact_matches}
            
            valid_rerank_rules = [
                rules[result['index']] for result in rerank_results
                if 0 <= result['index'] < len(rules) and rules[result['index']]['id'] not in exact_rule_ids
            ]
            
            final_rules = exact_matches + valid_rerank_rules
            matched_rules[item_id] = final_rules
            
            # 收集日志信息
            exact_ae_names = [rule['ae_name'] for rule in exact_matches]
            rerank_matches_info = []
            for result in rerank_results:
                if 0 <= result['index'] < len(rules):
                    rule = rules[result['index']]
                    if rule['id'] not in exact_rule_ids:
                        rerank_matches_info.append(f"{rule['ae_name']}({result['score']:.3f})")
            
            match_logs.append([
                item_id,
                test_name[:20] + "..." if len(test_name) > 20 else test_name,
                f"{len(exact_matches)}条: {', '.join(exact_ae_names[:2])}" + ("..." if len(exact_ae_names) > 2 else "") if exact_matches else "无",
                f"{len(valid_rerank_rules)}条: {', '.join(rerank_matches_info[:2])}" + ("..." if len(rerank_matches_info) > 2 else "") if valid_rerank_rules else "无",
                len(final_rules)
            ])

        # 4. 使用优化的分组展示匹配结果
        if match_logs:
            self._print_match_results_summary(match_logs, "并发模式")
        
        return matched_rules

    def _rerank_single_item_silent(self, item_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        单个项目的rerank调用（静默模式，用于并发执行）
        
        Args:
            item_data: 包含item_id, test_name, candidates的字典
            
        Returns:
            包含item_id和rerank结果的字典
        """
        item_id = item_data['item_id']
        test_name = item_data['test_name']
        candidates = item_data['candidates']
        
        try:
            # 使用静默模式调用rerank服务
            rerank_results = self._rerank_with_service(test_name, candidates, silent=True)
            return {
                'item_id': item_id,
                'success': True,
                'rerank_results': rerank_results,
                'error': None
            }
        except Exception as e:
            logger.error(f"项目{item_id}的rerank调用失败: {e}")
            return {
                'item_id': item_id,
                'success': False,
                'rerank_results': [],
                'error': str(e)
            }

    def _print_match_results_summary(self, match_logs: List[List], mode: str = ""):
        """
        优化的匹配结果展示方法 - 使用清晰的分组展示而非表格
        
        Args:
            match_logs: 匹配结果日志列表
            mode: 模式标识（如"并发模式"、"串行模式"）
        """
        if not match_logs:
            return
            
        print(f"\n📊 CTCAE规则匹配结果汇总{f'（{mode}）' if mode else ''}:")
        print("=" * 80)
        
        # 统计信息
        total_items = len(match_logs)
        items_with_exact = sum(1 for log in match_logs if not log[2].startswith("无") and not log[2].startswith("错误"))
        items_with_rerank = sum(1 for log in match_logs if not log[3].startswith("无") and not log[3].startswith("错误"))
        items_with_matches = sum(1 for log in match_logs if log[4] > 0)
        
        print(f"📈 总体统计:")
        print(f"   • 总检验项目: {total_items}")
        print(f"   • 有精确匹配: {items_with_exact} 项")
        print(f"   • 有Rerank匹配: {items_with_rerank} 项")
        print(f"   • 所有匹配: {items_with_matches} 项")
        print(f"   • 匹配覆盖率: {items_with_matches/total_items*100:.1f}%")
        print("=" * 80)
        
        # 只显示有匹配结果的项目，按匹配类型分组展示
        filtered_logs = [log for log in match_logs if log[4] > 0]
        
        if filtered_logs:
            # 按匹配类型分组
            exact_only = []  # 只有精确匹配
            rerank_only = []  # 只有rerank匹配
            both_matches = []  # 两种匹配都有
            
            for log in filtered_logs:
                has_exact = not log[2].startswith("无")
                has_rerank = not log[3].startswith("无")
                
                if has_exact and has_rerank:
                    both_matches.append(log)
                elif has_exact:
                    exact_only.append(log)
                elif has_rerank:
                    rerank_only.append(log)
            
            # 展示精确匹配项目
            if exact_only:
                print(f"\n🎯 仅精确匹配项目 ({len(exact_only)}项):")
                for log in exact_only:
                    item_id, test_name, exact_match, _, total = log
                    print(f"   [{item_id:>3}] {test_name:<20} → {exact_match}")
            
            # 展示rerank匹配项目
            if rerank_only:
                print(f"\n🔍 仅Rerank匹配项目 ({len(rerank_only)}项):")
                for log in rerank_only:
                    item_id, test_name, _, rerank_match, total = log
                    print(f"   [{item_id:>3}] {test_name:<20} → {rerank_match}")
            
            # 展示混合匹配项目
            if both_matches:
                print(f"\n🎯🔍 精确+Rerank匹配项目 ({len(both_matches)}项):")
                for log in both_matches:
                    item_id, test_name, exact_match, rerank_match, total = log
                    print(f"   [{item_id:>3}] {test_name:<20}")
                    print(f"        ├─ 精确匹配: {exact_match}")
                    print(f"        └─ Rerank匹配: {rerank_match}")
            
            # 显示隐藏项目统计
            if len(filtered_logs) < total_items:
                no_match_count = total_items - len(filtered_logs)
                print(f"\n💡 无匹配规则项目: {no_match_count} 项（已隐藏显示）")
        else:
            print("⚠️  所有项目均无匹配的CTCAE规则")
        
        print("=" * 80)





    def match_relevant_rules(self, test_items: List[Dict[str, Any]]) -> Dict[int, List[Dict[str, Any]]]:
        """
        为每个检验项匹配最相关的CTCAE规则 - 使用整洁的表格化日志输出

        Args:
            test_items: 检验项列表

        Returns:
            Dict[item_id, List[matched_rules]] - 返回所有相关规则（精确匹配+rerank分数>0）

        匹配策略：
        1. 精确匹配：单字符完全匹配，多字符包含匹配
        2. rerank匹配：只返回分数>0的规则
        3. 结果合并：精确匹配优先，与rerank结果混合排序
        """
        rules = self._load_ctcae_rules()
        if not rules:
            logger.warning("没有可用的CTCAE规则")
            return {}

        # 缓存搜索文本，避免重复构建
        if self._rule_search_texts is None:
            self._rule_search_texts = [rule['search_text'] for rule in rules]
        
        matched_rules = {}
        match_logs = []  # 收集日志信息
        
        print("🔍 开始CTCAE规则匹配（串行模式）...")

        for item in test_items:
            item_id = item.get('序号', item.get('id', 0))
            test_name = item.get('检查项目', item.get('test_name', ''))

            if not test_name:
                logger.warning(f"检验项目 {item_id} 缺少名称，跳过")
                matched_rules[item_id] = []
                continue

            try:
                # 首先尝试精确匹配 - 改进的匹配逻辑
                exact_matches = []
                for rule in rules:
                    for check_item in rule['check_items']:
                        # 单字符检验项目名称必须完全匹配，多字符使用包含匹配
                        if len(test_name) == 1:
                            if test_name == check_item:
                                exact_matches.append(rule)
                                break
                        else:
                            if test_name in check_item or check_item in test_name:
                                exact_matches.append(rule)
                                break

                # 获取rerank结果（分数>0的规则）
                rerank_results = self._rerank_with_service(test_name, self._rule_search_texts, silent=True)

                # 优化的结果合并逻辑：精确匹配优先 + 已排序的rerank结果
                exact_rule_ids = {rule['id'] for rule in exact_matches}

                # 1. 批量筛选有效的rerank结果（分数>0且不重复）
                valid_rerank_rules = [
                    rules[result['index']] for result in rerank_results
                    if 0 <= result['index'] < len(rules) and rules[result['index']]['id'] not in exact_rule_ids
                ]

                # 2. 直接合并：精确匹配 + rerank结果（无需排序，保持原有优先级）
                final_rules = exact_matches + valid_rerank_rules
                matched_rules[item_id] = final_rules

                # 3. 收集日志信息
                exact_ae_names = [rule['ae_name'] for rule in exact_matches]
                rerank_matches_info = []
                for result in rerank_results:
                    if 0 <= result['index'] < len(rules):
                        rule = rules[result['index']]
                        if rule['id'] not in exact_rule_ids:
                            rerank_matches_info.append(f"{rule['ae_name']}({result['score']:.3f})")
                
                match_logs.append([
                    item_id,
                    test_name[:20] + "..." if len(test_name) > 20 else test_name,
                    f"{len(exact_matches)}条: {', '.join(exact_ae_names[:2])}" + ("..." if len(exact_ae_names) > 2 else "") if exact_matches else "无",
                    f"{len(valid_rerank_rules)}条: {', '.join(rerank_matches_info[:2])}" + ("..." if len(rerank_matches_info) > 2 else "") if valid_rerank_rules else "无",
                    len(final_rules)
                ])

            except Exception as e:
                logger.error(f"匹配规则失败 (item_id={item_id}, test_name='{test_name}'): {e}")
                matched_rules[item_id] = []
                match_logs.append([
                    item_id,
                    test_name[:20] + "..." if len(test_name) > 20 else test_name,
                    "错误",
                    "错误",
                    0
                ])
                logger.warning(f"项目{item_id} - 使用降级方案，返回空列表")

        # 4. 使用优化的分组展示匹配结果
        if match_logs:
            self._print_match_results_summary(match_logs, "串行模式")

        return matched_rules
