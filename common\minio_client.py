import os
from datetime import timedelta

import django
from django.conf import settings
from minio import Minio


def get_minio_client() -> Minio:
    # 返回 MinIO 客户端实例
    return Minio(
        settings.MINIO_ENDPOINT,
        access_key=settings.MINIO_ACCESS_KEY,
        secret_key=settings.MINIO_SECRET_KEY,
        secure=settings.MINIO_SECURE
    )


def upload_file_to_minio(client: Minio, local_file_path: str, bucket_name: str, object_name: str) -> None:
    """
    上传本地文件到 MinIO

    :param local_file_path: 本地文件路径
    :param bucket_name: MinIO 存储桶名称
    :param object_name: 上传后的对象名称（存储路径）
    """
    try:
        # 打开本地文件并上传
        with open(local_file_path, 'rb') as file_data:
            client.put_object(
                bucket_name=bucket_name,       # 存储桶名称
                object_name=object_name,       # 目标路径和文件名
                data=file_data,         # 文件对象
                length=-1,         # 文件大小（-1 表示自动计算）
                part_size=10*1024*1024  # 分块上传大小（10MB）
            )
        print(f"文件 {local_file_path} 上传成功！")
    except Exception as e:
        print(f"上传文件 {local_file_path} 失败: {e}")


def download_file_from_minio(client: Minio, bucket_name: str, object_name: str, local_file_path: str) -> None:
    """
    从 MinIO 下载文件到本地

    该函数通过 MinIO 客户端的 `get_object` 方法获取 MinIO 存储桶中的对象，并将文件内容写入本地文件。

    :param client: Minio，MinIO 客户端实例。
    :param bucket_name: str，MinIO 存储桶名称。
    :param object_name: str，MinIO 存储桶中对象的名称。
    :param local_file_path: str，本地文件保存路径。
    """
    try:
        # 获取文件对象
        response = client.get_object(bucket_name, object_name)

        # 将下载的文件写入本地
        with open(local_file_path, 'wb') as local_file:
            for data in response.stream(32*1024):  # 分块写入数据，默认 32KB
                local_file.write(data)

        print(f"文件 {object_name} 下载成功到 {local_file_path}！")
    except Exception as e:
        print(f"下载文件 {object_name} 失败: {e}")


def get_presigned_url(client: Minio, bucket_name, object_name, expires=timedelta(days=1)) -> str:
    """
    获取 MinIO 对象的临时访问 URL。

    该函数生成一个预签名 URL，用于访问存储在 MinIO 上的文件。 
    该 URL 有效期默认为 7 天，可以根据需要设置有效期。

    :param bucket_name: str，MinIO 存储桶的名称。
    :param object_name: str，MinIO 存储桶中对象（文件）的名称。
    :param expires: timedelta，预签名 URL 的过期时间，默认为 7 天。 
                     设置为 timedelta 对象，表示 URL 的有效期。
    :return: str，返回生成的预签名 URL。

    示例:
    >>> url = get_presigned_url('my-bucket', 'uploads/file.txt', timedelta(days=1))
    >>> print(url)
    """
    # 返回预签名的 URL
    return client.presigned_get_object(
        bucket_name,
        object_name,
        expires=expires
    )


if __name__ == "__main__":
    # python -m common.minio_client
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smo.settings.test')
    django.setup()

    # 初始化 MinIO 客户端
    client = get_minio_client()
    bucket_name = settings.MINIO_BUCKET_NAME
    # 上传示例
    upload_file_to_minio(client, "test-file.txt", bucket_name, "test-file.txt")
    # 下载示例
    download_file_from_minio(client, bucket_name, "test-file.txt", "test-file.txt")
    # 获取临时下载URL
    print(get_presigned_url(client, bucket_name, "test-file.txt"))
