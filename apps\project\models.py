from django.db import models
from apps.subject.models import SubjectEpoch
from common.models import BaseModel, BaseFileModel


class Project(BaseModel):
    """项目信息"""
    project_id = models.CharField(max_length=255, unique=True, verbose_name="项目ID")
    project_no = models.CharField(max_length=255, verbose_name="项目编号", db_index=True)
    project_name = models.CharField(max_length=512, verbose_name="项目名称", db_index=True)
    indication = models.CharField(max_length=255, null=True, verbose_name="适应症")
    indication_text = models.CharField(max_length=255, null=True,  verbose_name="适应症描述")
    stage = models.CharField(max_length=255, null=True,  verbose_name="项目分期", db_index=True)
    stage_text = models.CharField(max_length=255, null=True,  verbose_name="项目分期描述")
    status = models.Char<PERSON>ield(max_length=255, null=True,  verbose_name="项目阶段", db_index=True)
    status_text = models.Char<PERSON>ield(max_length=255, null=True,  verbose_name="项目阶段描述")
    pm_text = models.CharField(max_length=255, null=True,  verbose_name="PM描述")
    pm_group_text = models.CharField(max_length=255, null=True,  verbose_name="PM分组描述")
    bu_text = models.CharField(max_length=255, null=True,  verbose_name="BU描述")
    site_count = models.IntegerField(default=0, verbose_name='中心数量')
    subject_count = models.IntegerField(default=0, verbose_name='受试者数量')
    
    class Meta:
        verbose_name = "项目信息"
        verbose_name_plural = "项目信息"
        db_table = 'project_info'


class ProjectMaterialFile(BaseModel, BaseFileModel):
    """项目素材文件"""

    material_info = models.ForeignKey(
        'project.ProjectMaterialInfo',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='files',
        to_field='id',
        db_column='material_info_id',
        verbose_name="项目素材信息ID",
        db_index=True
    )

    class Meta:
        verbose_name = "项目素材文件"
        verbose_name_plural = "项目素材文件"
        db_table = 'project_material_file'


class ProjectMaterialInfo(BaseModel,):
    """项目素材信息"""
    CRF_TEMPLATE = 'CRF_TEMPLATE'
    MEDICAL_RECORD_TEMPLATE = 'MEDICAL_RECORD_TEMPLATE'
    CATEGORY_CHOICES = [(CRF_TEMPLATE, 'CRF表头'), (MEDICAL_RECORD_TEMPLATE, '病历模版')]
    category = models.CharField(
        max_length=50, choices=CATEGORY_CHOICES,
        default=CRF_TEMPLATE, db_index=True, verbose_name="素材分类")

    # 一（项目信息）对（项目素材信息）多
    project = models.ForeignKey(
        Project,
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='material_infos',
        to_field='project_id',
        db_column='project_id',
        verbose_name="项目ID",
        db_index=True
    )
    epoch_id = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        db_index=False,
        verbose_name="阶段ID"
    )

    epoch_name = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        db_index=False,
        verbose_name="周期名称"
    )

    class Meta:
        verbose_name = "项目素材信息"
        verbose_name_plural = "项目素材信息"
        db_table = 'project_material_info'


class ProjectSite(BaseModel):
    """项目研究中心信息"""
    project_site_id = models.CharField(max_length=255, unique=True, verbose_name="项目研究中心ID")
    hosp_id = models.CharField(max_length=255, verbose_name="医院ID", db_index=True)
    hosp_name = models.CharField(max_length=255, verbose_name="中心名称", db_index=True)
    hosp_department_no = models.CharField(max_length=255, verbose_name="中心编号", db_index=True)
    status = models.CharField(max_length=255, null=True, verbose_name="中心状态", db_index=True)
    status_text = models.CharField(max_length=255, null=True, verbose_name="中心状态描述")
    accredit_crc_text = models.TextField(null=True, verbose_name="授权CRC描述")
    backup_crc_text = models.TextField(null=True, verbose_name="Backup CRC描述")

    # 一（项目信息）对（项目研究中心信息）多
    project = models.ForeignKey(
        Project,
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='sites',
        to_field='project_id',
        db_column='project_id',
        verbose_name="项目ID",
        db_index=True
    )

    class Meta:
        verbose_name = "项目研究中心信息"
        verbose_name_plural = "项目研究中心信息"
        db_table = 'project_site_info'
