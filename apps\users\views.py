import re
import logging
import datetime
from datetime import timezone

from django.conf import settings
import jwt
from django.shortcuts import render
from django.contrib.auth.mixins import LoginRequiredMixin
from django.core.exceptions import PermissionDenied
from django.core.cache import cache

from rest_framework.exceptions import ValidationError, NotFound
from rest_framework.authentication import SessionAuthentication, BasicAuthentication
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.decorators import permission_classes, authentication_classes
from rest_framework.viewsets import GenericViewSet
from rest_framework.mixins import CreateModelMixin
from drf_spectacular.utils import extend_schema, OpenApiParameter, OpenApiExample, OpenApiResponse
from drf_spectacular.types import OpenApiTypes

from common.auth import ERPSysJWTAuthentication
from common.dingtalk import get_access_token, get_user_info
from common.renderers import TextRenderer
from . import serializers, models

logger = logging.getLogger('app')


class BaseAPIView(APIView):
    pass


class ObtainTokenView(LoginRequiredMixin, BaseAPIView):

    authentication_classes = []

    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_authenticated or not request.user.is_staff:
            raise PermissionDenied(self.get_permission_denied_message())
        return super().dispatch(request, *args, **kwargs)

    @extend_schema(
        summary='ERP系统测试JWT生成-仅供测试使用',
        tags=['用户'],
        request={'multipart/form-data': {
            'type': 'object',
            'properties': {
                'username': {'type': 'string', 'description': '用户名'},
                'superpwd': {'type': 'string', 'description': '超级密码'}
            },
            'required': ['username', 'superpwd']
        }},
        responses={200: {
            'type': 'object',
            'properties': {
                'username': {'type': 'string', 'title': '用户名'},
                'realname': {'type': 'string', 'title': '姓名'},
                'fg_depart': {'type': 'string', 'title': '分管部门'},
                'hr_post': {'type': 'string', 'title': '职位'},
                'token': {'type': 'string', 'title': '接口鉴权token'}
            }
        }}
    )
    def post(self, request, format=None):
        username = request.data.get('username')
        superpwd = request.data.get('superpwd')

        if not username or not isinstance(username, str):
            raise ValidationError({'username': ['Invalid username.']})

        if superpwd != '123':
            raise ValidationError({'superpwd': ['Invalid superpwd.']})

        # 从sys_user获取secret(password)
        try:
            sys_user = models.SysUser.objects.using('master').get(username=username)
        except models.SysUser.DoesNotExist:
            raise ValidationError({'username': ['No such user.']})

        # 通过secret生成jwt

        payload = {
            'username': sys_user.username,
            "exp": datetime.datetime.now(tz=timezone.utc) + datetime.timedelta(days=1)
        }
        token = jwt.encode(payload, sys_user.password, algorithm="HS256")

        content = {
            'username': str(sys_user.username),
            'realname': str(sys_user.realname),
            'fg_depart': str(sys_user.fg_depart),
            'hr_post': str(sys_user.hr_post),
            'token': token,
        }
        return Response(content)


class DingtalkObtainTokenView(BaseAPIView):

    authentication_classes = []

    @extend_schema(
        summary='钉钉登录',
        tags=['用户'],
        request={'multipart/form-data': {
            'type': 'object',
            'properties': {
                'code': {'type': 'string', 'description': '钉钉授权码，用于获取用户信息的临时凭证。'}
            },
            'required': ['code']
        }},
        responses={200: {
            'type': 'object',
            'properties': {
                'username': {'type': 'string', 'title': '用户名'},
                'realname': {'type': 'string', 'title': '姓名'},
                'fg_depart': {'type': 'string', 'title': '分管部门'},
                'hr_post': {'type': 'string', 'title': '职位'},
                'token': {'type': 'string', 'title': '接口鉴权token'}
            }
        }}
    )
    def post(self, request, format=None):
        code = request.data.get('code')
        if not code or not isinstance(code, str):
            raise ValidationError({'code': ['Invalid code.']})

        # 获取缓存
        dingtalk_access_token = cache.get('dingtalk_access_token')
        if dingtalk_access_token is None:
            dingtalk_access_token = get_access_token(settings.DINGTALK_APP_KEY, settings.DINGTALK_APP_SECRET)
            dingtalk_access_token = dingtalk_access_token['accessToken']

            # 设置缓存
            cache.set('dingtalk_access_token', dingtalk_access_token, timeout=60 * 120)  # 缓存120分钟

        dingtalk_user_info = get_user_info(dingtalk_access_token, code)
        if 'errcode' in dingtalk_user_info and dingtalk_user_info['errcode'] != 0:
            raise ValidationError({'code': [dingtalk_user_info['errcode'], dingtalk_user_info['errmsg']]})

        logger.debug(dingtalk_user_info)

        unionid = dingtalk_user_info['result']['unionid']
        # 从sys_user获取secret(password)
        try:
            sys_user = models.SysUser.objects.using('master').get(dd_union_id=unionid)
        except models.SysUser.DoesNotExist:
            raise ValidationError({'username': ['No such user.']})

        # 通过secret生成jwt

        payload = {
            'username': sys_user.username,
            "exp": datetime.datetime.now(tz=timezone.utc) + datetime.timedelta(days=3)
        }
        token = jwt.encode(payload, sys_user.password, algorithm="HS256")

        content = {
            'username': str(sys_user.username),
            'realname': str(sys_user.realname),
            'fg_depart': str(sys_user.fg_depart),
            'hr_post': str(sys_user.hr_post),
            'token': token,
        }
        return Response(content)