from datetime import timedelta
from django.conf import settings
from rest_framework import serializers

from common.minio_client import get_minio_client


class FileUrlMixin:

    def get_url(self, obj):
        minio_client = get_minio_client()
        # 设置响应头，指定文件名
        response_headers = {
            'response-content-disposition': f'attachment; filename="{obj.original_filename}"'
        }
        presigned_url = minio_client.presigned_get_object(
            settings.MINIO_BUCKET_NAME,
            obj.object_name,
            expires=timedelta(hours=2),
            response_headers=response_headers
        )
        return presigned_url