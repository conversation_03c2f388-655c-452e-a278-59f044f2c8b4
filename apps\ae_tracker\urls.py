from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SimpleRouter

from . import views

router = DefaultRouter(trailing_slash=False)

router.register(r'/ocr-text', views.OcrResultTextViewSet)
router.register(r'/ocr-text', views.OcrResultTextUpdateViewSet)
router.register(r'/test-results', views.OcrResultListViewSet)
router.register(r'/test-results/single-ad', views.OcrResultAddDeleteViewSet)
router.register(r'/test-results/single-meds-update', views.MedsUpdateViewSet)
router.register(r'/test-results', views.OcrResultBatchUpdateViewSet)
router.register(r'/operation-logs', views.OperationLogViewSet)
router.register(r'/ae-tracker-task', views.AeTrackerTasksView)
# router.register(r'/data', views.DeleteItemDataViewSet)
router.register(r'/back', views.BackStepTwoViewSet)
# router.register(r'/ae-tracker-task/item-step', views.ItemStepTaskStatusView)


urlpatterns = [
    # path('/downloads-maskfile', views.download_and_zip_view.as_view()),
]

urlpatterns = router.urls + urlpatterns
