import os
import io
import logging
import datetime
from datetime import timed<PERSON>ta

from django.http import FileResponse
from django.conf import settings
from django.shortcuts import render
from django.db.models import Subquery, OuterRef
from django.http import HttpResponse
from django.db import connections
from django.db.models import Count
from rest_framework.viewsets import GenericViewSet
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import serializers
from rest_framework.fields import <PERSON><PERSON><PERSON><PERSON>
from rest_framework import filters, viewsets
from rest_framework.mixins import ListModelMixin, CreateModelMixin, RetrieveModelMixin
from rest_framework.viewsets import ReadOnlyModelViewSet
from rest_framework.filters import OrderingFilter
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.renderers import BaseRenderer
from rest_framework.exceptions import ValidationError, NotFound, APIException
from rest_framework.parsers import MultiPartParser, FormParser
from rest_framework.permissions import Is<PERSON>uth<PERSON>icated
from rest_framework.exceptions import APIException
from rest_framework.renderers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from django_filters.rest_framework import DjangoFilterBackend
from rest_pandas.views import PandasViewBase
from rest_pandas import PandasExcelRenderer

import numpy as np
import pandas as pd
from openpyxl.styles import Font, Border
from openpyxl.styles import Alignment
from drf_spectacular.utils import extend_schema, OpenApiResponse
from openpyxl import Workbook
from openpyxl.styles import Alignment, Border, Side
from openpyxl.utils import get_column_letter
from drf_spectacular.utils import extend_schema
from minio import Minio

from apps.ae_tracker.models import TestResult
from common.auth import ERPSysJWTAuthentication
from common.utils import calculate_file_hash
from common.minio_client import get_minio_client
from common.pagination import StandardResultsSetPagination
from common.renderers import ExcelRenderer
from common.views import deprecated_view

from apps.medical_collection.models import MedicalCollectionTask
from apps.ae_tracker.models import AeTrackerTask, TestResult, TestOcrResult
from apps.subject_medical.models import SubjectMedicalInfo
from apps.subject.models import SubjectItem
from apps.system.models import OperationLog
from apps.users.models import AuthProjectRoleACL
from common.auth import check_project_site_access_permission
from common.def_tools import generate_numeric_unique_id
from . import serializers
from . import models
from . import filters

logger = logging.getLogger('app')


class TaskExistsStatusIN_PROGRESS(APIException):
    status_code = status.HTTP_409_CONFLICT
    default_detail = "先前任务流程还未完全结束,正在处理中！！！"
    default_code = "error"


class BaseAPIView(APIView):
    authentication_classes = [ERPSysJWTAuthentication]
    permission_classes = [IsAuthenticated]


class BaseListViewSet(BaseAPIView, GenericViewSet, ListModelMixin):
    filter_backends = (DjangoFilterBackend, OrderingFilter)


class ProjectSiteSubjectListViewSetDeprecated(BaseListViewSet):
    queryset = models.Subject.objects.filter(delete_flag=0)
    serializer_class = serializers.ProjectSiteSubjectSerializer
    filterset_class = filters.ProjectSiteSubjectFilter
    pagination_class = StandardResultsSetPagination
    ordering_fields = '__all__'
    # ordering = ['code']

    def get_queryset(self):
        return models.Subject.objects.select_related('project', 'project_site')

    @extend_schema(summary='受试者信息列表', tags=['项目中心受试者'], deprecated=True)
    @deprecated_view
    def list(self, request, project_id, project_site_id, format=None):
        queryset = self.get_queryset()
        queryset = queryset.filter(project_id=project_id, project_site_id=project_site_id)
        queryset = self.filter_queryset(queryset)

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)


class ProjectSiteSubjectListViewSet(BaseListViewSet, RetrieveModelMixin):
    queryset = models.Subject.objects.filter(delete_flag=0)
    serializer_class = serializers.ProjectSiteSubjectSerializer
    filterset_class = filters.ProjectSiteSubjectFilter
    pagination_class = StandardResultsSetPagination
    ordering_fields = '__all__'
    # ordering = ['code']
    lookup_field = 'subject_id'

    # def get_queryset(self):
    #     return models.Subject.objects.select_related('project', 'project_site')

    def get_queryset(self):
        if self.action != 'list':
            return models.Subject.objects.select_related('project', 'project_site')

        project_id = self.request.query_params.get('project_id')
        project_no = self.request.query_params.get('project_no')

        if not project_id and not project_no:
            raise ValidationError("请填写project_id或project_no，二者必填其一。")

        if not project_id:
            project_site = self.queryset.select_related('project').filter(project__project_no=project_no).first()
            if not project_site:
                return self.queryset.none()

            project_id = project_site.project_id

        logging.info(f"project_id: {project_id}")

        # 白名单用户
        if self.request.user.project_whitelist_flag:
            return self.queryset.filter(project_id=project_id).select_related('project', 'project_site')

        # 通过OT项目成员获取当前项目当前用户的角色
        with connections['OTDB'].cursor() as cursor:
            sql = """
            SELECT 
                t2.code,
                t1.project_site_id
            FROM
                auth_user_role t1
                JOIN auth_role t2 ON t2.ID = t1.role_id
                JOIN org_user t3 ON t3.user_id = t1.user_id 
                AND t3.org_id = t1.org_id 
            WHERE
                t2.is_del = 0 
                -- AND t1.platform = 'O_CTMS_TRIAL' 
                AND t3.org_id = 'e888888' 
                AND t3.employee_no = %s
                AND t1.project_id = %s
            """
            cursor.execute(sql, [self.request.user.username, project_id])
            results = cursor.fetchall()

        role_codes = [i[0] for i in results if i[0]]
        logging.info(f"role_codes: {role_codes}")

        project_site_ids = [i[1] for i in results if i[1]]
        logging.info(f"project_site_ids: {project_site_ids}")

        # 可以访问项目的范围
        if role_codes:
            access_scopes = AuthProjectRoleACL.objects.filter(
                is_allowed=1, role_code__in=role_codes,delete_flag=0).values_list('access_scope', flat=True)
            access_scopes = tuple(access_scopes)

            if AuthProjectRoleACL.SCOPE_ALL in access_scopes:
                return self.queryset.filter(project_id=project_id).select_related('project', 'project_site')

            if AuthProjectRoleACL.SCOPE_PARTIAL in access_scopes:
                return self.queryset.filter(project_id=project_id, project_site_id__in=project_site_ids).select_related('project', 'project_site')

        return self.queryset.none()

    @extend_schema(summary='受试者信息列表', tags=['项目中心受试者'])
    def list(self, request, format=None):
        return super().list(request, format)

    @extend_schema(summary='获取受试者信息详情', tags=['项目中心受试者'])
    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        project_id = instance.project_site.project_id
        project_site_id = instance.project_site.project_site_id
        
        check_project_site_access_permission(request.user, project_id, project_site_id)
        
        serializer = self.get_serializer(instance)
        data = serializer.data

        # 获取前端传递的 subject_visit_id 参数
        subject_visit_id = request.query_params.get('subject_visit_id')

        if subject_visit_id:
            try:
                # 根据 subject_visit_id 查找对应的 subject_visit 对象
                subject_visit = models.SubjectVisit.objects.select_related('subject_epoch').get(
                    subject_visit_id=subject_visit_id,
                    subject_id=instance.subject_id
                )

                # 从 subject_visit 获取关联的 subject_epoch 信息
                if subject_visit.subject_epoch:
                    data['subject_epoch_id'] = subject_visit.subject_epoch.subject_epoch_id
                    data['subject_epoch_label'] = subject_visit.subject_epoch.label
                else:
                    data['subject_epoch_id'] = None
                    data['subject_epoch_label'] = None
            except models.SubjectVisit.DoesNotExist:
                # 如果没有找到对应的 subject_visit，设置为 None
                data['subject_epoch_id'] = None
                data['subject_epoch_label'] = None
        # else:
        #     # 如果没有传递 subject_visit_id，使用默认逻辑获取第一个 subject_epoch
        #     subject_epoch = instance.subject_epochs.first()
        #     if subject_epoch:
        #         data['subject_epoch_id'] = subject_epoch.subject_epoch_id
        #         data['subject_epoch_label'] = subject_epoch.label
        #     else:
        #         data['subject_epoch_id'] = None
        #         data['subject_epoch_label'] = None

        return Response(data)
        # return Response(serializer.data)
        # return super().retrieve(self, request, *args, **kwargs)


class SubjectEpochListViewSet(BaseListViewSet):
    queryset = models.SubjectEpoch.objects.filter(delete_flag=0)
    serializer_class = serializers.SubjectEpochSerializer
    filterset_class = filters.SubjectEpochFilter
    # pagination_class = StandardResultsSetPagination
    ordering_fields = '__all__'
    ordering = ['seq']

    @extend_schema(summary='受试者访视阶段列表', tags=['访视详情'])
    def list(self, request, format=None):
        return super().list(request, format)


class SubjectVisitListViewSet(BaseListViewSet):
    queryset = models.SubjectVisit.objects.filter(delete_flag=0)
    serializer_class = serializers.SubjectVisitSerializer
    filterset_class = filters.SubjectVisitFilter
    # pagination_class = StandardResultsSetPagination
    ordering_fields = '__all__'
    ordering = ['ot_create_time']

    @extend_schema(summary='受试者访视信息列表', tags=['访视详情'])
    def list(self, request, format=None):
        return super().list(request, format)


class SubjectItemListViewSet(BaseListViewSet):
    queryset = models.SubjectItem.objects.select_related('subject_visit', 'subject', 'subject_visit__subject_epoch').filter(delete_flag=0)
    serializer_class = serializers.SubjectItemSerializer
    filterset_class = filters.SubjectItemFilter
    # pagination_class = StandardResultsSetPagination
    ordering_fields = '__all__'
    ordering = ['seq']

    @extend_schema(summary='受试者访视操作项列表', tags=['访视详情'])
    def list(self, request, format=None):
        # 从请求中获取参数
        subject_id = request.query_params.get('subject_id')
        subject_visit_id = request.query_params.get('subject_visit_id')

        # 检查必要参数
        if not all([subject_id, subject_visit_id]):
            return Response({"error": "缺少必要参数"}, status=400)

        # 使用原生SQL查询数据
        with connections['default'].cursor() as cursor:
            sql = """
                    SELECT
                      t1.project_id,
                      t1.project_site_id,
                      t1.subject_id,
                      t1.subject_visit_id,
                      t1.visit_date,
                      t1.label as visit_label,
                      t4.file_type as item_type,
                      COALESCE(t3.id, 'None') as id,
                      COALESCE(t3.ae_ai_current_step, '0') as ae_ai_current_step,
                      COALESCE(t3.ae_ai_task_id, '0') as ae_ai_task_id,
                      COALESCE(t3.source_id, 'None') as source_id,
                      COALESCE(t3.subject_item_id, 'None') as subject_item_id,
                      t4.category_name as label,
                      LPAD(t4.sort_no, 3, '0') as seq,
                      t2.category_code as item_id
                    FROM
                      (
                        SELECT
                          s.*,
                          sei.label as epoch_label  -- 从subject_epoch_info获取label
                        FROM
                          subject_visit_info AS s
                        -- 关联subject_epoch_info表（多对一）
                        INNER JOIN subject_epoch_info AS sei 
                          ON s.subject_epoch_id = sei.subject_epoch_id
                          AND sei.delete_flag = 0  -- 过滤已删除的记录
                        WHERE
                          s.subject_id = %s
                          AND s.subject_visit_id = %s
                          AND s.delete_flag = 0
                      ) t1
                      INNER JOIN project_visit_epoch_category_rlt t2 
                        ON t1.project_id = t2.project_id
                        AND t1.epoch_label = t2.label
                        AND t2.delete_flag = 0
                      INNER JOIN project_visit_file_category t4
                        ON t1.project_id = t4.project_id
                        AND t2.category_code = t4.category_code
                        AND t4.delete_flag = 0
                      LEFT JOIN subject_item_info t3
                        ON t1.project_id = t3.project_id
                        AND t2.category_code = t3.item_id
                        AND t1.subject_id = t3.subject_id
                        AND t1.subject_visit_id = t3.subject_visit_id
                        AND t3.delete_flag = 0
                    """
            cursor.execute(sql, [subject_id, subject_visit_id])
            columns = [col[0] for col in cursor.description]
            data = cursor.fetchall()

        # 将查询结果转换为字典列表
        result_data = [dict(zip(columns, row)) for row in data]
        # 收集需要创建的SubjectItem对象
        new_subject_items = []
        # 获取所有 subject_item_id
        subject_item_ids = [item['subject_item_id'] for item in result_data if item['subject_item_id'] != 'None']

        # 批量查询文件数量
        file_counts = SubjectMedicalInfo.objects.filter(
            subject_item_id__in=subject_item_ids,
            delete_flag=0
        ).values('subject_item_id').annotate(
            count=Count('id')
        )

        # 转换为字典便于查找
        file_count_dict = {item['subject_item_id']: item['count'] for item in file_counts}

        # 为每个对象添加 file_count
        for item in result_data:
            # 如果source_id为'None'，则生成新的唯一ID
            if item['source_id'] == 'None':
                unique_id = generate_numeric_unique_id()
                item['source_id'] = str(unique_id)
                item['subject_item_id'] = f"{item['source_id']}-{subject_id}"
                new_subject_items.append(SubjectItem(
                    subject_item_id=item['subject_item_id'],
                    subject_visit_id=item['subject_visit_id'],
                    subject_id=item['subject_id'],
                    project_id=item['project_id'],
                    project_site_id=item['project_site_id'],
                    item_id=item['item_id'],
                    item_type=item['item_type'],
                    ae_ai_current_step=item['ae_ai_current_step'],
                    ae_ai_task_id=item['ae_ai_task_id'],
                    source_id=item['source_id'],
                    label=item['label'],
                    seq=item['seq']
                ))
            else:
                item['subject_item_id'] = item['subject_item_id']

            if item['subject_item_id'] != 'None':
                item['file_count'] = file_count_dict.get(item['subject_item_id'], 0)
            else:
                item['file_count'] = 0

        if new_subject_items:
            # 使用 bulk_create 创建对象
            created_subject_items = SubjectItem.objects.bulk_create(new_subject_items)

            # 创建一个映射字典，将 source_id 映射到新创建的对象
            source_id_map = {item.source_id: item for item in created_subject_items}

            # 更新 result_data 中对应项的 ID 和 subject_item_id
            for item in result_data:
                if item['source_id'] != 'None' and item['source_id'] in source_id_map:
                    created_item = source_id_map[item['source_id']]
                    item['id'] = created_item.id
                    # item['subject_item_id'] 已经在创建对象时设置，这里不需要再更新
        for item in result_data:
            if item['id'] != 'None' and item['id'] is not None:
                try:
                    item['id'] = int(item['id'])
                except (ValueError, TypeError):
                    # 如果转换失败，保持原值
                    pass
            else:
                # 将 'None' 或 None 设置为 None 或者 0，根据你的需求决定
                item['id'] = None  # 或者 item['id'] = 0
        return Response(result_data)


class SubjectItemDetailViewSet(BaseAPIView, GenericViewSet, RetrieveModelMixin):
    queryset = models.SubjectItem.objects.select_related('subject_visit', 'subject', 'subject_visit__subject_epoch').filter(delete_flag=0)
    serializer_class = serializers.SubjectItemSerializer
    # filterset_class = filters.SubjectItemFilter
    # pagination_class = StandardResultsSetPagination
    ordering_fields = '__all__'
    ordering = ['seq']
    lookup_field = 'subject_item_id'

    @extend_schema(summary='受试者访视操作项详情', tags=['访视详情'])
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, format)

    @extend_schema(
        summary='操作项类型更新',
        tags=['访视详情'],
        request=serializers.UpdateItemTypeRequestSerializer,
        responses=serializers.UpdateItemTypeRequestSerializer
    )
    @action(url_path='item-type', detail=False, methods=['post'])
    def item_type(self, request, format=None):
        data = request.data.copy()
        # 为数据添加更新时间
        # 检查数据是否包含 id
        subject_item_id = data.get('subject_item_id')
        subject_id = data.get('subject_id')
        instance_id = subject_item_id

        task = AeTrackerTask.objects.filter(
            subject_id=subject_id,
            subject_item_id=subject_item_id,
            status__in=[AeTrackerTask.IN_PROGRESS, AeTrackerTask.TODO],
            delete_flag=0
        ).order_by('-create_time').first()
        if task:
            raise TaskExistsStatusIN_PROGRESS()

        # 软删除 AeTrackerTask 模型的相关对象
        AeTrackerTask.objects.filter(
            subject_id=subject_id,
            subject_item_id=subject_item_id,
            delete_flag=0
        ).update(delete_flag=1)

        test_result_ids = list(
            TestResult.objects.filter(
                subject_id=subject_id,
                subject_item_id=subject_item_id,
                delete_flag=0
            ).values_list("id", flat=True)
        )
        TestResult.objects.filter(id__in=test_result_ids).update(delete_flag=1)
        OperationLog.objects.filter(
            target_id__in=test_result_ids
        ).update(delete_flag=1)
        OperationLog.objects.filter(
            target_id=subject_item_id,
            delete_flag=0
        ).update(delete_flag=1)

        TestOcrResult.objects.filter(
            subject_id=subject_id,
            subject_item_id=subject_item_id,
            delete_flag=0
        ).update(delete_flag=1)

        # SubjectMedicalInfo.objects.filter(
        #     subject_id=subject_id,
        #     subject_item_id=subject_item_id,
        #     delete_flag=0
        # ).update(file_masked_id=None, ocr_time=None)
        # 软删除 OperationLog 模型的相关对象
        # a = SubjectItem.objects.filter(subject_id=subject_id, subject_item_id=subject_item_id).first()

        SubjectItem.objects.filter(subject_id=subject_id, subject_item_id=subject_item_id).update(
            ae_ai_current_step=0, ae_ai_task_id=0)

        if not instance_id:
            return Response({"detail": "数据必须包含 'id' 字段"}, status=status.HTTP_400_BAD_REQUEST)

        fields_to_update = ['item_type']  # 这里替换为你实际要更新的字段名
        try:
            # 根据 id 获取要更新的实例
            instance = models.SubjectItem.objects.get(subject_item_id=instance_id)
            filtered_data = {key: value for key, value in data.items() if key in fields_to_update}
            # 创建序列化器并验证数据
            serializer = self.get_serializer(instance, data=filtered_data, partial=True)
            if serializer.is_valid():
                # 保存更新后的实例
                updated_instance = serializer.save()
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except models.SubjectItem.DoesNotExist:
            return Response({"detail": f"未找到 id 为 {instance_id} 的记录"}, status=status.HTTP_404_NOT_FOUND)
        # 序列化更新后的实例
        updated_serializer = self.get_serializer(updated_instance)
        return Response(updated_serializer.data, status=status.HTTP_200_OK)


class AETrackerViewSet(BaseListViewSet):
    queryset = TestResult.objects.select_related(
        'subject_item', 'subject_visit').filter(delete_flag=0, ae_tracker_flag=1)
    serializer_class = serializers.AETrackerSerializer
    filterset_class = filters.AETrackerFilter
    # pagination_class = StandardResultsSetPagination
    ordering_fields = '__all__'
    # ordering = ['seq']

    @extend_schema(summary='获取AE Tracker列表信息', tags=['AE Tracker'])
    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        # subject_id = request.query_params.get('subject_id')
        # subject_item_id = request.query_params.get('subject_item_id')
        # seq = SubjectItem.objects.get(subject_id=subject_id, subject_item_id=subject_item_id).seq
        data = list(queryset.values(
            'id', 'subject_id', 'subject_item_id', 'subject_item_id', 'subject_item__label',
            'test_name', 'test_value', 'test_unit', 'test_flag', 'reference_value',
            'ae_name', 'ae_grade', 'ae_desc', 'subject_visit__visit_date',
            'medical_history_flag', 'ae_edit_flag', 'ae_ai_result_list', 'ae_ai_result_flag',
            'create_time', 'subject_item__item_type', 'subject_item__item_id', 'collect_time', 'report_time'
        ))
        print(data)

        for i in data:
            i['subject_item_label'] = i['subject_item__label']
            i.pop('subject_item__label')

            if i['collect_time']:
                date_value = i['collect_time']
            elif i['report_time']:
                date_value = i['report_time']
            elif i['subject_visit__visit_date']:
                date_value = i['subject_visit__visit_date'].get('cmpl') or \
                             i['subject_visit__visit_date'].get('st') or \
                             i['subject_visit__visit_date'].get('winB')
            else:
                date_value = ''

            if i['subject_item__item_id'] in ['3', '4'] and date_value:
                # 精确到分钟
                if isinstance(date_value, str):
                    if ' ' in date_value:
                        # 包含时间部分，可能为 YYYY-MM-DD HH:MM 或 YYYY-MM-DD HH:MM:SS
                        date_part, time_part = date_value.split(' ', 1)
                        if time_part.count(':') >= 2:
                            # 格式为 YYYY-MM-DD HH:MM:SS，只取到分钟
                            hour_minute = ':'.join(time_part.split(':')[:2])
                            i['date'] = f"{date_part} {hour_minute}"
                        else:
                            # 格式已经是 YYYY-MM-DD HH:MM
                            i['date'] = date_value
                    else:
                        # 只有日期部分 YYYY-MM-DD
                        i['date'] = date_value
                else:
                    # 如果是日期对象，格式化到分钟
                    i['date'] = date_value.strftime('%Y-%m-%d %H:%M')
            else:
                # 精确到天
                if isinstance(date_value, str):
                    # 如果是字符串，只取日期部分
                    i['date'] = date_value.split(' ')[0] if ' ' in date_value else date_value
                else:
                    # 如果是日期对象，格式化到天
                    i['date'] = date_value.strftime('%Y-%m-%d') if date_value else ''


            # i['date'] = i['subject_visit__visit_date'].get('cmpl') if i['subject_visit__visit_date'] else ''
            i.pop('subject_visit__visit_date')
            i['subject_item_item_type'] = i['subject_item__item_type']
            i.pop('subject_item__item_type')
            i.pop('subject_item__item_id')
            i.pop('collect_time')
            i.pop('report_time')

        # print(data)

        # 转换成DataFrame
        df = pd.DataFrame(data)
        if df.empty:
            return Response([])

        df = df.replace([np.inf, -np.inf, np.nan], '')
        df = df.sort_values(by='create_time', ascending=False)
        df = df.drop_duplicates(subset=['subject_id', 'date', 'subject_item_label', 'test_name'], keep='first')

        # 处理日期格式
        # df['date'] = pd.to_datetime(df['date']).dt.strftime('%Y/%m/%d')
        df = df[~df['date'].isna()]
        dates_set = set(df['date'].tolist())

        # 定义分组字段 - 用于聚合相同检测项的不同日期数据
        groupby_columns = [
            'subject_id', 'subject_item_label', 'test_name'
        ]

        # 创建结果数据结构
        result = []

        # 按分组字段分组
        for name, group in df.groupby(groupby_columns):
            # print(name, '=====')
            # 创建基本数据结构
            record = {
                'subject_id': name[0],
                'subject_item_label': name[1],
                'test_name': name[2],
                'subject_item_item_type': '',
                'test_unit': '',
                'reference_value': '',
                'dates': []
            }
            if 'subject_item_item_type' in group.columns:
                record['subject_item_item_type'] = '1'
                if any(group['subject_item_item_type'] is not None and group['subject_item_item_type'] != '1'):
                    record['subject_item_item_type'] = '3'
            record_dates = set()

            # 添加每个日期的数据
            for _, row in group.iterrows():
                if row['date'] in record_dates:
                    continue
                date_record = {
                    'id': row['id'],
                    'test_value': row['test_value'],
                    'test_flag': row['test_flag'],
                    'medical_history_flag': row['medical_history_flag'],
                    'ae_edit_flag': row['ae_edit_flag'],
                    'ae_ai_result_list': row['ae_ai_result_list'],
                    'ae_ai_result_flag': row['ae_ai_result_flag'],
                    'ae_name': row['ae_name'] if pd.notna(row['ae_name']) else None,
                    'ae_grade': row['ae_grade'] if pd.notna(row['ae_grade']) else None,
                    'date': row['date'],
                    'ae_desc': row['ae_desc']
                }
                if row['test_unit']:
                    record['test_unit'] = row['test_unit']
                if row['reference_value']:
                    record['reference_value'] = row['reference_value']
                record['dates'].append(date_record)
                record_dates.add(row['date'])

            for date_str in dates_set:
                if date_str not in record_dates:
                    date_record = {
                        'id': None,
                        'test_value': None,
                        'test_flag': None,
                        'medical_history_flag': None,
                        'ae_edit_flag': None,
                        'ae_ai_result_list': None,
                        'ae_ai_result_flag': None,
                        'ae_name': None,
                        'ae_grade': None,
                        'ae_desc': None,
                        'date': date_str
                    }
                    record['dates'].append(date_record)

            record['dates'] = sorted(record['dates'], key=lambda x: x['date'])
            result.append(record)
        # print(result)
        for item in result:
            if item.get('subject_item_item_type') != '1':
                item['test_unit'] = '/'
                item['reference_value'] = '/'
                for date_info in item.get('dates', []):
                    if date_info['id'] is not None:
                        date_info['test_value'] = '/'
                    date_info['test_flag'] = '/'
        return Response(result)

    @extend_schema(
        summary='导出AE Tracker数据为Excel文件',
        tags=['AE Tracker'],
        parameters=[serializers.AETrackerQueryParamsSerializer],
        responses={
            200: {
                "description": "导出的AE Tracker数据Excel文件",
                "content": {
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": {
                        "schema": {
                            "type": "file"
                        }
                    }
                }
            }
        }
    )
    @action(detail=False, methods=['get'], url_path='export-excel', renderer_classes=[ExcelRenderer])
    def export_excel(self, request, *args, **kwargs):
        # 使用与list相同的查询和过滤逻辑
        queryset = self.filter_queryset(self.get_queryset())

        # 获取数据并处理
        data = list(queryset.values(
            'id', 'subject_id', 'subject_item_id', 'subject_item__label',
            'test_name', 'test_value', 'test_unit', 'test_flag', 'reference_value',
            'ae_name', 'ae_grade', 'subject_visit__visit_date',
            'create_time', 'subject_item__item_type', 'collect_time', 'subject_item__item_id'

        ))

        # 处理数据，与list接口相同
        processed_data = []
        for i in data:
            item = i.copy()
            item['subject_item_label'] = item['subject_item__label']
            item.pop('subject_item__label')
            item['subject_item_item_type'] = item['subject_item__item_type']
            item.pop('subject_item__item_type')

            # 标准化日期格式
            # if item['subject_visit__visit_date'] and 'cmpl' in item['subject_visit__visit_date']:
            #     date_str = item['subject_visit__visit_date'].get('cmpl')
            #     # 确保日期格式一致
            #     try:
            #         item['date'] = pd.to_datetime(date_str).strftime('%Y/%m/%d')
            #     except:
            #         item['date'] = ''
            # else:
            #     item['date'] = ''
            item['date'] = item['collect_time']
            item.pop('collect_time')
            date_value = item['date']
            if item['subject_item__item_id'] in ['3', '4'] and date_value:
                # 精确到分钟
                if isinstance(date_value, str):
                    if ' ' in date_value:
                        # 包含时间部分，可能为 YYYY-MM-DD HH:MM 或 YYYY-MM-DD HH:MM:SS
                        date_part, time_part = date_value.split(' ', 1)
                        if time_part.count(':') >= 2:
                            # 格式为 YYYY-MM-DD HH:MM:SS，只取到分钟
                            hour_minute = ':'.join(time_part.split(':')[:2])
                            item['date'] = f"{date_part} {hour_minute}"
                        else:
                            # 格式已经是 YYYY-MM-DD HH:MM
                            item['date'] = date_value
                    else:
                        # 只有日期部分 YYYY-MM-DD
                        item['date'] = date_value
                else:
                    # 如果是日期对象，格式化到分钟
                    item['date'] = date_value.strftime('%Y-%m-%d %H:%M')
            else:
                # 精确到天
                if isinstance(date_value, str):
                    # 如果是字符串，只取日期部分
                    item['date'] = date_value.split(' ')[0] if ' ' in date_value else date_value
                else:
                    # 如果是日期对象，格式化到天
                    item['date'] = date_value.strftime('%Y-%m-%d') if date_value else ''
            item.pop('subject_item__item_id')
            item.pop('subject_visit__visit_date')
            processed_data.append(item)

        # 使用处理后的数据
        data = processed_data

        # 创建一个Workbook
        wb = Workbook()
        ws = wb.active
        ws.title = "AE Tracker"

        # 转换为DataFrame以便处理
        df = pd.DataFrame(data)
        if df.empty:
            # 创建文件流
            excel_file = io.BytesIO()
            wb.save(excel_file)
            excel_file.seek(0)

            # 设置文件名（包含当前日期）
            filename = f"AE-Tracker-{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"

            # 返回文件流
            response = HttpResponse(
                excel_file.read(),
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            response['Content-Disposition'] = f'attachment; filename="{filename}"'
            return response

        df = df.replace([np.inf, -np.inf, np.nan], '')
        df = df.sort_values(by='create_time', ascending=False)
        df = df.drop_duplicates(subset=['subject_id', 'date', 'subject_item_label', 'test_name'], keep='first')
        # print(df)

        # 设置基本表头
        headers = ["检查项目名称", "检验项目", "单位", "正常值范围"]
        for col_idx, header in enumerate(headers, 1):
            ws.cell(row=2, column=col_idx, value=header)

        # 获取唯一日期列表并排序
        unique_dates = sorted(df['date'].unique()) if not df.empty and 'date' in df.columns else []
        # 移除空日期
        unique_dates = [date for date in unique_dates if date]

        # 填充日期表头（每个日期跨2列）
        date_col_start = len(headers) + 1
        for idx, date in enumerate(unique_dates):
            col_idx = date_col_start + idx * 2

            # 合并3列作为日期表头
            cell = ws.cell(row=1, column=col_idx, value=date)
            ws.merge_cells(start_row=1, start_column=col_idx, end_row=1, end_column=col_idx + 1)
            cell.alignment = Alignment(horizontal='center')

            # 在第二行添加子表头
            ws.cell(row=2, column=col_idx, value="结果")
            ws.cell(row=2, column=col_idx + 1, value="CTCAE等级")

        # 获取所有唯一的检测项目
        unique_tests = {}
        for _, item in df.iterrows():
            key = (item['subject_id'], item['subject_item_label'], item['test_name'])

            if key not in unique_tests:
                filtered_df = df[(df['subject_id'] == item['subject_id']) &
                     (df['subject_item_label'] == item['subject_item_label'])]
                # 判断筛选后的数据中 subject_item_item_type 列是否存在不等于 '1' 的值
                subject_item_item_type = '1'
                test_unit = item['test_unit']
                reference_value = item['reference_value']
                if any(filtered_df['subject_item_item_type'] is not None and filtered_df['subject_item_item_type'] != '1'):
                    subject_item_item_type = '3'
                    test_unit = '/'
                    reference_value = '/'
                unique_tests[key] = {
                    'subject_id': item['subject_id'],
                    'subject_item_label': item['subject_item_label'],
                    'test_name': item['test_name'],
                    'test_unit': test_unit,
                    'reference_value': reference_value,
                    'subject_item_item_type': subject_item_item_type
                }
            else:
                if item['test_unit']:
                    unique_tests[key]['test_unit'] = item['test_unit']
                if item['reference_value']:
                    unique_tests[key]['reference_value'] = item['reference_value']

        # 填充数据行
        for row_idx, (key, test_info) in enumerate(unique_tests.items(), 3):
            _, subject_item_label, test_name = key
            test_unit = test_info['test_unit']
            reference_value = test_info['reference_value']
            if test_info['subject_item_item_type'] == '3':
                test_unit = '/'
                reference_value = '/'
            # 填充基本信息
            ws.cell(row=row_idx, column=1, value=subject_item_label)
            ws.cell(row=row_idx, column=2, value=test_name)
            ws.cell(row=row_idx, column=3, value=test_unit)
            ws.cell(row=row_idx, column=4, value=reference_value)
            # ws.cell(row=row_idx, column=5, value=test_info['subject_item_item_type'])

            # 填充每个日期的数据
            for date_idx, date in enumerate(unique_dates):
                col_idx = date_col_start + date_idx * 2

                # 查找当前检测项目在当前日期的数据
                filtered_data = [
                    item for _, item in df.iterrows() if
                    item['subject_item_label'] == subject_item_label and
                    item['test_name'] == test_name and
                    item['date'] == date
                ]

                # 如果找到数据，填充相应单元格
                if filtered_data:
                    record = filtered_data[0]  # 如果有多条记录，只取第一条
                    test_value = record['test_value']
                    if test_info['subject_item_item_type'] == '3' and record['id'] is not None:
                        test_value = '/'
                    ws.cell(row=row_idx, column=col_idx, value=test_value)
                    ae_grade_value = {
                        '0': '正常', '1': '1级', '2': '2级', '3': '3级',
                        '4': '4级', '5': '5级', '6': "NCS"}.get(str(record['ae_grade']), '')
                    ws.cell(row=row_idx, column=col_idx + 1, value=ae_grade_value)

        # 设置列宽
        for i in range(1, ws.max_column + 1):
            column_letter = get_column_letter(i)
            if i <= len(headers):
                ws.column_dimensions[column_letter].width = 15
            else:
                ws.column_dimensions[column_letter].width = 12

        # 添加边框样式
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        for row in ws.iter_rows(min_row=1, max_row=ws.max_row, min_col=1, max_col=ws.max_column):
            for cell in row:
                cell.border = thin_border
                cell.alignment = Alignment(horizontal='center', vertical='center')

        # 创建文件流
        excel_file = io.BytesIO()
        wb.save(excel_file)
        excel_file.seek(0)
        # df = pd.read_excel(excel_file)
        # df.loc[df.iloc[:, 4] == '3', ['单位', '正常值范围', '结果']] = '/'
        # excel_file = io.BytesIO()
        # with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
        #     df.to_excel(writer, index=False)
        # # 将文件指针重置到流的起始位置，以便后续使用
        # excel_file.seek(0)

        # 设置文件名（包含当前日期）
        filename = f"AE-Tracker-{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"

        # 返回文件流
        response = HttpResponse(
            excel_file.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        return response


class TestResultsViewSet(BaseAPIView, GenericViewSet):
    queryset = TestResult.objects.filter(delete_flag=0)
    # serializer_class = serializers.AETrackerSerializer
    filterset_class = filters.TestResultsFilter
    # pagination_class = StandardResultsSetPagination
    ordering_fields = '__all__'
    # ordering = ['seq']

    @extend_schema(
        summary='导出化验单数据为Excel文件',
        tags=['AE Tracker'],
        parameters=[serializers.TestResultsQueryParamsSerializer],
        responses={
            200: {
                "description": "导出化验单数据为Excel文件",
                "content": {
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": {
                        "schema": {
                            "type": "file"
                        }
                    }
                }
            }
        }
    )
    @action(detail=False, methods=['get'], url_path='export-excel', renderer_classes=[ExcelRenderer])
    def export_excel(self, request, *args, **kwargs):
        # 使用与list相同的查询和过滤逻辑
        queryset = self.filter_queryset(self.get_queryset())

        # 获取数据并处理
        data = list(queryset.values(
            'test_code', 'test_name', 'test_value', 'test_unit', 'reference_value', 'ae_grade', 'ae_name'
        ))

        output = io.BytesIO()
        # 设置文件名（包含当前日期）
        filename = f"Test-Results-{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"

        # 转换为 DataFrame 以便处理
        df = pd.DataFrame(data)
        if df.empty:
            wb = Workbook()
            output = io.BytesIO()
            wb.save(output)
            output.seek(0)
            response = HttpResponse(
                output.read(),
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            response['Content-Disposition'] = f'attachment; filename="{filename}"'
            return response

        df['ae_grade'] = df['ae_grade'].apply(
            lambda x: {'0': '正常', '1': '1级', '2': '2级', '3': '3级', '4': '4级', '5': '5级', '6': 'NCS'}.get(x, ''))
        df = df.rename(columns={'test_code': '#', 'test_name': '检验项目', 'test_value': '结果',
                       'test_unit': '单位', 'reference_value': '参考结果', 'ae_grade': 'AI判定CTCAE等级', 'ae_name': 'AE名称'})

        # 创建文件流
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, index=False)

        # 返回文件流
        output.seek(0)
        response = HttpResponse(
            output.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        return response


class TestResultItemTypeOneViewSet(BaseAPIView, GenericViewSet):
    queryset = TestResult.objects.filter(delete_flag=0)
    # serializer_class = serializers.AETrackerSerializer
    filterset_class = filters.TestResultsFilter
    # pagination_class = StandardResultsSetPagination
    ordering_fields = '__all__'
    # ordering = ['seq']

    @extend_schema(
        summary='导出化验单双模型数据为Excel文件',
        tags=['AE Tracker'],
        parameters=[serializers.TestResultsQueryParamsSerializer],
        responses={
            200: {
                "description": "导出化验单双模型数据为Excel文件",
                "content": {
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": {
                        "schema": {
                            "type": "file"
                        }
                    }
                }
            }
        }
    )
    @action(detail=False, methods=['get'], url_path='export-excel', renderer_classes=[ExcelRenderer])
    def export_excel(self, request, *args, **kwargs):
        # 使用与list相同的查询和过滤逻辑
        queryset = self.filter_queryset(self.get_queryset())

        # 获取数据并处理
        data = list(queryset.values(
            'test_code', 'test_name', 'test_value', 'test_unit', 'reference_value', 'ae_ai_result_list', 'ae_grade', 'ae_name'
        ))
        for item in data:
            try:
                ae_ai_result_list = item.pop('ae_ai_result_list', [])
                for i, result in enumerate(ae_ai_result_list[:2], start=1):
                    try:
                        item[f'ae_grade{i}'] = str(int(result['ae_grade']))
                    except:
                        item[f'ae_grade{i}'] = result['ae_grade']
                    # item[f'ae_grade{i}'] = result.get('ae_grade')
                    item[f'ae_name{i}'] = result.get('ae_name')
            except:
                item['ae_grade1'] = ''
                item['ae_name1'] = ''
                item['ae_grade2'] = ''
                item['ae_name2'] = ''
        output = io.BytesIO()
        # 设置文件名（包含当前日期）
        filename = f"Test-Results-{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"

        # 转换为 DataFrame 以便处理
        df = pd.DataFrame(data)
        df = df.fillna('')
        if df.empty:
            wb = Workbook()
            output = io.BytesIO()
            wb.save(output)
            output.seek(0)
            response = HttpResponse(
                output.read(),
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            response['Content-Disposition'] = f'attachment; filename="{filename}"'
            return response

        df['ae_grade'] = df['ae_grade'].apply(
            lambda x: {'0': '正常', '1': '1级', '2': '2级', '3': '3级', '4': '4级', '5': '5级', '6': 'NCS'}.get(x, ''))
        df['ae_grade1'] = df['ae_grade1'].apply(
            lambda x: {'0': '正常', '1': '1级', '2': '2级', '3': '3级', '4': '4级', '5': '5级', '6': 'NCS'}.get(x, ''))
        df['ae_grade2'] = df['ae_grade2'].apply(
            lambda x: {'0': '正常', '1': '1级', '2': '2级', '3': '3级', '4': '4级', '5': '5级', '6': 'NCS'}.get(x, ''))
        new_column_order = [
            'test_code',
            'test_name',
            'test_value',
            'test_unit',
            'reference_value',
            'ae_name',
            'ae_name1',
            'ae_name2',
            'ae_grade',
            'ae_grade1',
            'ae_grade2'
        ]
        df = df[new_column_order]
        df = df.rename(columns={'test_code': '#', 'test_name': '检验项目', 'test_value': '结果',
                       'test_unit': '单位', 'reference_value': '参考结果', 'ae_grade1': 'DeepseekAI判定CTCAE等级', 'ae_name1': 'DeepseekAE名称', 'ae_grade2': 'qwen3_32bAI判定CTCAE等级', 'ae_name2': 'qwen3_32bAE名称', 'ae_grade': '页面CTCAE等级', 'ae_name': '页面AE名称'})
        # 创建文件流
        df = df.fillna('')
        print(df)

        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, index=False)

        # 返回文件流
        output.seek(0)
        response = HttpResponse(
            output.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        return response


class TestResultItemTypeThreeViewSet(BaseAPIView, GenericViewSet):
    queryset = TestResult.objects.filter(delete_flag=0)
    # serializer_class = serializers.AETrackerSerializer
    filterset_class = filters.TestResultsFilter
    # pagination_class = StandardResultsSetPagination
    ordering_fields = '__all__'
    # ordering = ['seq']

    @extend_schema(
        summary='导出病史类双模型数据为Excel文件',
        tags=['AE Tracker'],
        parameters=[serializers.TestResultsQueryParamsSerializer],
        responses={
            200: {
                "description": "导出病史类双模型数据为Excel文件",
                "content": {
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": {
                        "schema": {
                            "type": "file"
                        }
                    }
                }
            }
        }
    )
    @action(detail=False, methods=['get'], url_path='export-excel', renderer_classes=[ExcelRenderer])
    def export_excel(self, request, *args, **kwargs):
        # 使用与list相同的查询和过滤逻辑
        queryset = self.filter_queryset(self.get_queryset())

        # 获取数据并处理
        data = list(queryset.values(
            'test_name', 'ai_test_result_list', 'ae_grade', 'ae_name', 'ae_desc', 'ae_ai_result_list'
        ))
        for item in data:
            try:
                ae_ai_result_list = item.pop('ae_ai_result_list', [])
                for i, result in enumerate(ae_ai_result_list[:2], start=1):
                    try:
                        item[f'ae_grade{i}'] = str(int(result['ae_grade']))
                    except:
                        item[f'ae_grade{i}'] = result['ae_grade']
                    item[f'ae_name{i}'] = result.get('ae_name')
                    item[f'ae_desc{i}'] = result.get('ae_desc')
            except:
                item['ae_grade1'] = ''
                item['ae_name1'] = ''
                item['ae_desc1'] = ''
                item['ae_grade2'] = ''
                item['ae_name2'] = ''
                item['ae_desc2'] = ''
            try:
                values_list = []
                # 遍历列表中的每个字典
                for i in item['ai_test_result_list']:
                    values_list.append(i["model"])
                item['ai_test_result_list'] = '，'.join(values_list)
            except:
                item['ai_test_result_list'] = ''
        output = io.BytesIO()
        # 设置文件名（包含当前日期）
        filename = f"Test-Results-{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"

        # 转换为 DataFrame 以便处理
        df = pd.DataFrame(data)
        df = df.fillna('')
        if df.empty:
            wb = Workbook()
            output = io.BytesIO()
            wb.save(output)
            output.seek(0)
            response = HttpResponse(
                output.read(),
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            response['Content-Disposition'] = f'attachment; filename="{filename}"'
            return response
        df['ae_grade'] = df['ae_grade'].apply(
            lambda x: {'0': '正常', '1': '1级', '2': '2级', '3': '3级', '4': '4级', '5': '5级', '6': 'NCS'}.get(x, ''))
        df['ae_grade1'] = df['ae_grade1'].apply(
            lambda x: {'0': '正常', '1': '1级', '2': '2级', '3': '3级', '4': '4级', '5': '5级', '6': 'NCS'}.get(x, ''))
        df['ae_grade2'] = df['ae_grade2'].apply(
            lambda x: {'0': '正常', '1': '1级', '2': '2级', '3': '3级', '4': '4级', '5': '5级', '6': 'NCS'}.get(x, ''))
        new_column_order = [
            'test_name',
            'ai_test_result_list',
            'ae_name',
            'ae_name1',
            'ae_name2',
            'ae_grade',
            'ae_grade1',
            'ae_grade2',
            'ae_desc',
            'ae_desc1',
            'ae_desc2'
        ]
        df = df[new_column_order]
        df = df.rename(columns={'test_name': 'AI的推断依据（OCR结果）', 'ai_test_result_list': 'AI的推断依据（来源）', 'ae_grade1': 'DeepseekAI判定CTCAE等级', 'ae_name1': 'DeepseekAE名称', 'ae_desc1': 'DeepseekCTCAEAE定义', 'ae_grade2': 'qwen3_32bAI判定CTCAE等级', 'ae_name2': 'qwen3_32bAE名称', 'ae_desc2': 'qwen3_32bCTCAEAE定义', 'ae_grade': '页面CTCAE等级', 'ae_name': '页面AE名称', 'ae_desc': '页面CTCAEAE定义'})
        df = df.fillna('')
        # 创建文件流
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, index=False)
        # print(df)
        # 返回文件流
        output.seek(0)
        response = HttpResponse(
            output.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        return response

