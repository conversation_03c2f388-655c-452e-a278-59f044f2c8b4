from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>, SimpleRouter

from . import views

router = DefaultRouter(trailing_slash=False)
router.register(r'/projects/(?P<project_id>\w+)/project-sites/(?P<project_site_id>\w+)/subjects',
                views.ProjectSiteSubjectListViewSetDeprecated)
router.register(r'/subjects', views.ProjectSiteSubjectListViewSet)
router.register(r'/subject-epochs', views.SubjectEpochListViewSet)
router.register(r'/subject-visits', views.SubjectVisitListViewSet)
router.register(r'/subject-items', views.SubjectItemListViewSet)
router.register(r'/subject-items', views.SubjectItemDetailViewSet)
router.register(r'/ae-trackers', views.AETrackerViewSet)
router.register(r'/test-results', views.TestResultsViewSet)
router.register(r'/test-results/item-type-one', views.TestResultItemTypeOneViewSet)
router.register(r'/test-results/item-type-three', views.TestResultItemTypeThreeViewSet)

urlpatterns = []

urlpatterns = router.urls + urlpatterns
