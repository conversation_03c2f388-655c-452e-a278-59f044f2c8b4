"""
文本处理工具：医院检验单OCR处理系统的文本解析功能
"""
import re
from typing import Dict, Optional
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
def extract_times(text: str) -> Dict[str, Optional[str]]:
    """
    TODO 有风险

    从OCR文本中提取各种时间信息

    Args:
        text: OCR文本

    Returns:
        时间信息字典
    """
    times = {
        'collect_time': None,
        'report_time': None
    }

    # 时间字段名称定义
    collect_time_fields = ['采集时间', '采样时间', '标本时间', '收样时间', '取样时间']
    # 报告时间、打印时间、检验时间、完成时间、审核时间
    report_time_fields = ['报告时间', '检验时间', '接收时间', '完成时间', '审核时间', '打印时间']

    # 时间格式正则表达式模式
    time_patterns = [
        # 完整日期时间格式（年月日时分秒）
        r'(\d{4}[/\-\.年]\d{1,2}[/\-\.月]\d{1,2}[/\-\.日]*\s*\d{1,2}:\d{2}:\d{2})',
        # 日期时间格式（年月日时分）
        r'(\d{4}[/\-\.年]\d{1,2}[/\-\.月]\d{1,2}[/\-\.日]*\s*\d{1,2}:\d{2})',
        # 仅日期格式（年月日）
        r'(\d{4}[/\-\.年]\d{1,2}[/\-\.月]\d{1,2}[/\-\.日]*)',
        # 简化格式（YYYY-MM-DD HH:MM:SS）- 有空格
        r'(\d{4}[/\-\.]\d{1,2}[/\-\.]\d{1,2}\s+\d{1,2}:\d{2}:\d{2})',
        # 简化格式（YYYY-MM-DD HH:MM）- 有空格
        r'(\d{4}[/\-\.]\d{1,2}[/\-\.]\d{1,2}\s+\d{1,2}:\d{2})',
        # 简化格式（YYYY-MM-DDHH:MM:SS）- 无空格
        r'(\d{4}[/\-\.]\d{1,2}[/\-\.]\d{1,2}\d{1,2}:\d{2}:\d{2})',
        # 简化格式（YYYY-MM-DDHH:MM）- 无空格
        r'(\d{4}[/\-\.]\d{1,2}[/\-\.]\d{1,2}\d{1,2}:\d{2})',
        # 简化格式（YYYY-MM-DD）
        r'(\d{4}[/\-\.]\d{1,2}[/\-\.]\d{1,2})',
        # 仅时间格式（HH:MM:SS）
        r'(\d{1,2}:\d{2}:\d{2})',
        # 仅时间格式（HH:MM）
        r'(\d{1,2}:\d{2})',
    ]
    
    # 动态生成采集时间模式
    collect_patterns = []
    for field in collect_time_fields:
        for pattern in time_patterns:
            collect_patterns.append(rf'{field}[：:\s]*{pattern}')

    # 动态生成报告时间模式
    report_patterns = []
    for field in report_time_fields:
        for pattern in time_patterns:
            report_patterns.append(rf'{field}[：:\s]*{pattern}')

    # 提取采集时间
    for pattern in collect_patterns:
        match = re.search(pattern, text)
        if match:
            times['collect_time'] = normalize_time(match.group(1))
            break

    # 提取报告时间
    for pattern in report_patterns:
        match = re.search(pattern, text)
        if match:
            times['report_time'] = normalize_time(match.group(1))
            break

    return times


def normalize_time(time_str: str) -> str:
    """
    标准化时间格式为 YYYY-MM-DD HH:MM

    Args:
        time_str: 原始时间字符串

    Returns:
        标准化后的时间字符串
    """
    # 避免循环导入，在这里使用data_validation模块的normalize_time
    from .data_validation import normalize_time as norm_time
    return norm_time(time_str)

# def extract_patient_info(text: str) -> Dict:
#     """
#     TODO 有些数据没有必要识别
#
#     从OCR文本中提取患者信息
#
#     Args:
#         text: OCR识别的原始文本
#
#     Returns:
#         患者信息字典
#     """
#     patient_info = {
#         'name': None,
#         'id_number': None,
#         'card_number': None,
#         'gender': None,
#         'age': None,
#         'department': None,
#         'bed_number': None,
#         'sample_number': None,
#         'doctor': None
#     }
#
#     # 姓名提取
#     name_pattern = r'姓名[：:\s]*([^\s]+)'
#     name_match = re.search(name_pattern, text)
#     if name_match:
#         patient_info['name'] = name_match.group(1).strip()
#
#     # 卡号/病案号提取
#     card_pattern = r'卡号[/病案号]*[：:\s]*([^\s]+)'
#     card_match = re.search(card_pattern, text)
#     if card_match:
#         patient_info['card_number'] = card_match.group(1).strip()
#
#     # 性别提取
#     gender_pattern = r'性别[：:\s]*([男女])'
#     gender_match = re.search(gender_pattern, text)
#     if gender_match:
#         patient_info['gender'] = gender_match.group(1)
#
#     # 年龄提取
#     age_pattern = r'年龄[：:\s]*(\d+[岁年]*)'
#     age_match = re.search(age_pattern, text)
#     if age_match:
#         patient_info['age'] = age_match.group(1)
#
#     # 科别提取
#     dept_pattern = r'科别[：:\s]*([^\s]+)'
#     dept_match = re.search(dept_pattern, text)
#     if dept_match:
#         patient_info['department'] = dept_match.group(1).strip()
#
#     # 床号提取
#     bed_pattern = r'床号[：:\s]*([^\s]+)'
#     bed_match = re.search(bed_pattern, text)
#     if bed_match:
#         patient_info['bed_number'] = bed_match.group(1).strip()
#
#     # 标本编号提取
#     sample_pattern = r'标本编号[：:\s]*([^\s]+)'
#     sample_match = re.search(sample_pattern, text)
#     if sample_match:
#         patient_info['sample_number'] = sample_match.group(1).strip()
#
#     # 申请医生提取
#     doctor_pattern = r'申请医生[：:\s]*([^\s]+)'
#     doctor_match = re.search(doctor_pattern, text)
#     if doctor_match:
#         patient_info['doctor'] = doctor_match.group(1).strip()
#
#     return patient_info
#
#
# def extract_metadata(text: str) -> Dict:
#     """
#     从OCR文本中提取报告元数据
#
#     Args:
#         text: OCR识别的原始文本
#
#     Returns:
#         报告元数据字典
#     """
#     metadata = {
#         'hospital_name': None,
#         'report_type': None,
#         'sample_time': None,
#         'report_time': None,
#         'lab_department': None,
#         'technician': None,
#         'reviewer': None,
#         'page_info': None
#     }
#
#     # 医院名称提取
#     hospital_patterns = [
#         r'([^第\n]*医院)',
#         r'([^第\n]*Hospital)',
#     ]
#
#     for pattern in hospital_patterns:
#         match = re.search(pattern, text)
#         if match:
#             metadata['hospital_name'] = match.group(1).strip()
#             break
#
#     # 报告类型提取
#     report_type_pattern = r'(.*检验报告单|.*分析.*|.*检查.*)'
#     type_match = re.search(report_type_pattern, text)
#     if type_match:
#         metadata['report_type'] = type_match.group(1).strip()
#
#     # 时间提取
#     time_patterns = [
#         r'采样时间[：:\s]*(\d{4}-\d{1,2}-\d{1,2}\d{2}:\d{2})',
#         r'报告时间[：:\s]*(\d{4}-\d{1,2}-\d{1,2}\d{2}:\d{2})',
#         r'接收时间[：:\s]*(\d{4}-\d{1,2}-\d{1,2}\d{2}:\d{2})',
#     ]
#
#     for pattern in time_patterns:
#         match = re.search(pattern, text)
#         if match:
#             try:
#                 time_str = match.group(1)
#                 if '采样时间' in pattern:
#                     metadata['sample_time'] = datetime.strptime(time_str, '%Y-%m-%d%H:%M')
#                 elif '报告时间' in pattern:
#                     metadata['report_time'] = datetime.strptime(time_str, '%Y-%m-%d%H:%M')
#             except ValueError:
#                 logger.warning(f"时间格式解析失败: {time_str}")
#
#     # 检验科室提取
#     lab_pattern = r'执行科室[：:\s]*([^\s]+)'
#     lab_match = re.search(lab_pattern, text)
#     if lab_match:
#         metadata['lab_department'] = lab_match.group(1).strip()
#
#     # 检验者和审核者提取
#     tech_pattern = r'检验者[：:\s]*([^\s]+)'
#     tech_match = re.search(tech_pattern, text)
#     if tech_match:
#         metadata['technician'] = tech_match.group(1).strip()
#
#     reviewer_pattern = r'审核者[：:\s]*([^\s]+)'
#     reviewer_match = re.search(reviewer_pattern, text)
#     if reviewer_match:
#         metadata['reviewer'] = reviewer_match.group(1).strip()
#
#     # 页数信息提取
#     page_pattern = r'第(\d+)页[/共]*(\d+)页'
#     page_match = re.search(page_pattern, text)
#     if page_match:
#         metadata['page_info'] = f"{page_match.group(1)}/{page_match.group(2)}"
#
#     return metadata

