# Generated by Django 4.1.5 on 2025-04-23 13:36

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="ConversationFeedback",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "original_filename",
                    models.Char<PERSON>ield(max_length=255, verbose_name="原始文件名"),
                ),
                ("bucket_name", models.Char<PERSON>ield(max_length=100, verbose_name="存储桶名称")),
                ("object_name", models.Char<PERSON>ield(max_length=500, verbose_name="对象名称")),
                ("content_type", models.Char<PERSON>ield(max_length=100, verbose_name="文件类型")),
                ("size", models.BigIntegerField(verbose_name="文件大小（字节）")),
                (
                    "hash",
                    models.Char<PERSON><PERSON>(
                        db_index=True, max_length=255, verbose_name="文件哈希（sha256）"
                    ),
                ),
                (
                    "knowledge_base_name",
                    models.Char<PERSON>ield(max_length=100, verbose_name="知识库名称"),
                ),
                (
                    "conversation_id",
                    models.CharField(max_length=100, verbose_name="对话ID"),
                ),
                ("message_id", models.CharField(max_length=100, verbose_name="消息ID")),
                ("question", models.TextField(verbose_name="对话问题")),
                ("answer", models.TextField(verbose_name="对话答案")),
                ("realname", models.CharField(max_length=50, verbose_name="用户姓名")),
                ("username", models.CharField(max_length=50, verbose_name="用户工号")),
                ("feedback_type", models.CharField(max_length=50, verbose_name="反馈类型")),
                (
                    "error_type",
                    models.CharField(
                        blank=True, max_length=100, null=True, verbose_name="错误类型"
                    ),
                ),
                ("feedback_content", models.TextField(verbose_name="反馈的内容")),
                (
                    "submitted_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="反馈提交时间"),
                ),
                (
                    "review_status",
                    models.CharField(default="未审核", max_length=20, verbose_name="审核状态"),
                ),
                (
                    "reviewer_name",
                    models.CharField(
                        blank=True, max_length=50, null=True, verbose_name="审核人姓名"
                    ),
                ),
                (
                    "reviewer_id",
                    models.CharField(
                        blank=True, max_length=50, null=True, verbose_name="审核人工号"
                    ),
                ),
                (
                    "reviewed_time",
                    models.DateTimeField(blank=True, null=True, verbose_name="审核时间"),
                ),
            ],
            options={
                "verbose_name": "对话问题反馈",
                "verbose_name_plural": "对话问题反馈",
                "db_table": "conversation_feedback",
            },
        ),
    ]
