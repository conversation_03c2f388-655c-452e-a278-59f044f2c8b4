"""
医疗检验单识别系统的PocketFlow流程定义
"""
from pocketflow import Flow

from script.test_result_format_ae_ocr.nodes import (
    TextPreprocessingNode,
    LLMTableFormatNode,
    ParseResponseNode,
    ValidateItemsNode,
    GenerateResultNode,
    ErrorHandlingNode,
    LLMTimeExtractionNode,
    CoordinateExtractionNode
)


def create_flow(enable_coordinate_extraction: bool = False):
    """
    创建医疗检验单处理流程

    新的流程逻辑：
    1. 预处理（包含正则时间提取和验证）
        - 如果时间提取完整，直接进入表格化
        - 如果时间提取不完整，先进行LLM时间提取
    2. LLM时间提取（仅在需要时执行）
    3. LLM表格化
    4. 解析响应
    5. 坐标提取（可选，在验证之前执行以确保数据匹配度）
    6. 验证数据（包含OCR错误修复）
    7. 生成最终结果

    Args:
        enable_coordinate_extraction: 是否启用坐标提取功能

    Returns:
        Flow: 配置好的优化流程对象
    """
    # 创建各个节点
    preprocess = TextPreprocessingNode()
    llm_time_extract = LLMTimeExtractionNode(max_retries=3, wait=5)
    llm_format = LLMTableFormatNode(max_retries=3, wait=5)
    parse_response = ParseResponseNode()
    validate_items = ValidateItemsNode()  # 默认启用OCR修复
    generate_result = GenerateResultNode()
    error_handler = ErrorHandlingNode()

    # 可选的坐标提取节点
    coordinate_extract = CoordinateExtractionNode()

    # 定义流程连接
    # 1. 预处理 -> 根据时间提取情况决定路径
    preprocess >> llm_format  # 时间提取完整，直接表格化
    preprocess - "need_time_extraction" >> llm_time_extract  # 时间不完整，LLM提取
    
    # 2. LLM时间提取 -> 表格化
    llm_time_extract - "continue_processing" >> llm_format
    
    # 3. 表格化 -> 解析 -> 验证 -> 坐标提取（可选）-> 生成结果
    if enable_coordinate_extraction:
         llm_format >> parse_response >> coordinate_extract >> validate_items >> generate_result
    else:
        llm_format >> parse_response >> validate_items >> generate_result

    # 错误处理路径
    llm_format - "error" >> error_handler

    # 创建流程
    flow = Flow(start=preprocess)

    return flow
