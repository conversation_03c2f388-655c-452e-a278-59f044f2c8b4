# Generated by Django 4.1.5 on 2025-03-03 12:32

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("project", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Subject",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "delete_flag",
                    models.SmallIntegerField(
                        choices=[(0, "未删除"), (1, "已删除")],
                        db_index=True,
                        default=0,
                        verbose_name="删除标志（0：未删除；1：已删除）",
                    ),
                ),
                (
                    "data_version",
                    models.PositiveIntegerField(default=0, verbose_name="数据版本"),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True, db_index=True, verbose_name="创建时间"
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "create_user",
                    models.CharField(max_length=255, null=True, verbose_name="创建人工号"),
                ),
                (
                    "create_name",
                    models.CharField(max_length=255, null=True, verbose_name="创建人姓名"),
                ),
                (
                    "update_user",
                    models.CharField(max_length=255, null=True, verbose_name="更新人工号"),
                ),
                (
                    "update_name",
                    models.CharField(max_length=255, null=True, verbose_name="更新人姓名"),
                ),
                (
                    "subject_id",
                    models.CharField(max_length=50, unique=True, verbose_name="受试者ID"),
                ),
                (
                    "code",
                    models.CharField(
                        db_index=True, max_length=100, verbose_name="受试者编号"
                    ),
                ),
                (
                    "real_name",
                    models.CharField(
                        db_index=True, max_length=100, null=True, verbose_name="受试者姓名"
                    ),
                ),
                (
                    "short_name",
                    models.CharField(
                        db_index=True, max_length=100, null=True, verbose_name="受试者姓名缩写"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        db_index=True, max_length=255, null=True, verbose_name="最新状态"
                    ),
                ),
                (
                    "status_text",
                    models.CharField(max_length=255, null=True, verbose_name="最新状态描述"),
                ),
                (
                    "project",
                    models.ForeignKey(
                        db_column="project_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subjects",
                        to="project.project",
                        to_field="project_id",
                        verbose_name="项目ID",
                    ),
                ),
                (
                    "project_site",
                    models.ForeignKey(
                        db_column="project_site_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subjects",
                        to="project.projectsite",
                        to_field="project_site_id",
                        verbose_name="项目中心ID",
                    ),
                ),
            ],
            options={
                "verbose_name": "受试者信息",
                "verbose_name_plural": "受试者信息",
                "db_table": "subject_info",
            },
        ),
    ]
