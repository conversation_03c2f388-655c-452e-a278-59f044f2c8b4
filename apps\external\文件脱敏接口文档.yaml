openapi: 3.0.3
info:
  title: 普蕊斯文件脱敏功能接口文档
  version: 1.0.0
  description: |+
    该接口用于 A 系统（铨融）调用 B 系统（普蕊斯）实现图片打码功能，并在处理完成后由 B 系统回调 A 系统。

    所有接口均要求使用 `token` 认证，并添加统一前缀 `/api/v1/external`，以标识为外部接口。

    ## 一、B 系统接口文档（由 A 系统调用）

    ### 1. 创建文件脱敏任务

    - **接口地址**：`POST /api/v1/external/masking/tasks`
    - **请求类型**：`multipart/form-data`
    - **认证方式**：在请求头中添加 `Authorization: Bearer <token>`


    ### 2. 查询文件脱敏任务详情

    - **接口地址**：`GET /api/v1/external/masking/tasks/{task_id}`
    - **认证方式**：在请求头中添加 `Authorization: Bearer <token>`


    ## 二、A 系统接口文档（供 B 系统回调）

    ### 1. 文件脱敏完成或者失败回调通知接口

    - **接口地址**：`POST URL由 A 系统自定义，在创建文件脱敏任务时，传递给B系统`
    - **请求类型**：`multipart/form-data`
    - **认证方式**：在请求头中添加 `Authorization: Bearer <token>`
    - **请求参数**：
      ```
      success：必传，识别脱敏状态：成功=1；失败=-1
      file：必传，脱敏后的文件，若识别失败，应回传原始文件以避免用户页面一直显示“待脱敏”
      meta：必传，附加的业务参数，JSON字符串
      ```

    ## 三、Token 认证说明

    所有接口必须在请求头中携带有效的 token：

    ```
    Authorization: Bearer <token>
    ```

    B 系统和 A 系统需分别校验对方提供的 token 是否在白名单中。

    ---
servers:
  - url: https://ddapp-test.smo-clinplus.com:18082
paths:
  /api/v1/external/masking/tasks:
    post:
      operationId: api_v1_external_masking_tasks_create
      summary: 创建文件脱敏任务
      tags:
        - 文件脱敏
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/CreateMaskingTaskRequest'
        required: true
      security:
        - BearerAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MaskingTask'
          description: ''
        '401':
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: string
                    title: 错误代码
                    example: NOT_AUTHENTICATED
                  message:
                    type: string
                    title: 错误描述
                  detail:
                    type: object
                    title: 错误详情
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: string
                    title: 错误代码
                    example: PERMISSION_DENIED
                  message:
                    type: string
                    title: 错误描述
                  detail:
                    type: object
                    title: 错误详情
          description: Forbidden
  /api/v1/external/masking/tasks/{task_id}:
    get:
      operationId: api_v1_external_masking_tasks_retrieve
      summary: 查询文件脱敏任务详情
      parameters:
        - in: path
          name: task_id
          schema:
            type: string
          required: true
      tags:
        - 文件脱敏
      security:
        - BearerAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MaskingTask'
          description: ''
        '401':
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: string
                    title: 错误代码
                    example: NOT_AUTHENTICATED
                  message:
                    type: string
                    title: 错误描述
                  detail:
                    type: object
                    title: 错误详情
          description: Unauthorized
        '403':
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: string
                    title: 错误代码
                    example: PERMISSION_DENIED
                  message:
                    type: string
                    title: 错误描述
                  detail:
                    type: object
                    title: 错误详情
          description: Forbidden
components:
  schemas:
    CreateMaskingTaskRequest:
      type: object
      properties:
        meta:
          type: string
          minLength: 1
          description: 附加的业务参数，JSON字符串
        file:
          type: string
          format: binary
          writeOnly: true
          description: 上传的文件，支持图片，Word，PDF。
        callback_url:
          type: string
          minLength: 1
          description: 任务执行成功或者失败回调的url
      required:
        - file
    DeleteFlagEnum:
      enum:
        - 0
        - 1
      type: integer
      description: |-
        * `0` - 未删除
        * `1` - 已删除
    MaskingFileObject:
      type: object
      description: 文件对象
      properties:
        id:
          type: integer
          readOnly: true
        url:
          type: string
          readOnly: true
          title: 文件下载url，有效期2小时
        delete_flag:
          allOf:
            - $ref: '#/components/schemas/DeleteFlagEnum'
          title: 删除标志（0：未删除；1：已删除）
          minimum: -32768
          maximum: 32767
        data_version:
          type: integer
          maximum: 4294967295
          minimum: 0
          format: int64
          title: 数据版本
        create_time:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
        update_time:
          type: string
          format: date-time
          readOnly: true
          title: 更新时间
        original_filename:
          type: string
          title: 原始文件名
          maxLength: 255
        bucket_name:
          type: string
          title: 存储桶名称
          maxLength: 100
        object_name:
          type: string
          title: 对象名称
          maxLength: 500
        content_type:
          type: string
          title: 文件类型
          maxLength: 100
        size:
          type: integer
          maximum: 9223372036854776000
          minimum: -9223372036854776000
          format: int64
          title: 文件大小（字节）
        hash:
          type: string
          title: 文件哈希（sha256）
          maxLength: 255
      required:
        - bucket_name
        - content_type
        - create_time
        - hash
        - id
        - object_name
        - original_filename
        - size
        - update_time
        - url
    MaskingTask:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        original_file:
          $ref: '#/components/schemas/MaskingFileObject'
        masked_file:
          $ref: '#/components/schemas/MaskingFileObject'
        delete_flag:
          allOf:
            - $ref: '#/components/schemas/DeleteFlagEnum'
          title: 删除标志（0：未删除；1：已删除）
          minimum: -32768
          maximum: 32767
        data_version:
          type: integer
          maximum: 4294967295
          minimum: 0
          format: int64
          title: 数据版本
        create_time:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
        update_time:
          type: string
          format: date-time
          readOnly: true
          title: 更新时间
        task_id:
          type: string
          title: 任务ID
          maxLength: 255
        status:
          allOf:
            - $ref: '#/components/schemas/StatusEnum'
          title: 任务状态
        note:
          type: string
          nullable: true
          title: 备注
          maxLength: 255
        start_time:
          type: string
          format: date-time
          readOnly: true
          title: 开始时间
        end_time:
          type: string
          format: date-time
          nullable: true
          title: 结束时间
        meta:
          nullable: true
          title: 附加的业务参数
        callback_url:
          type: string
          nullable: true
          title: 回调URL
          maxLength: 512
        callback_time:
          type: string
          nullable: true
          title: 回调时间
          maxLength: 512
        callback_status:
          type: string
          nullable: true
          title: 回调状态
          maxLength: 20
      required:
        - create_time
        - id
        - masked_file
        - original_file
        - start_time
        - task_id
        - update_time
    StatusEnum:
      enum:
        - TODO
        - IN_PROGRESS
        - COMPLETED
        - CANCELLED
        - ERROR
      type: string
      description: |-
        * `TODO` - 待办
        * `IN_PROGRESS` - 进行中
        * `COMPLETED` - 已完成
        * `CANCELLED` - 已取消
        * `ERROR` - 执行错误
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
