from django.db import models
from common.models import BaseModel


class SysDict(BaseModel):
    """
    数据字典表
    """
    dict_code = models.CharField(max_length=50, default='', verbose_name="字典编码", unique=True)
    dict_name = models.CharField(max_length=50, default='', verbose_name="字典名称")
    dict_desc = models.CharField(max_length=255, default='', verbose_name="字典描述")

    class Meta:
        db_table = 'sys_dict'
        verbose_name = "数据字典"
        verbose_name_plural = verbose_name


class SysDictItem(BaseModel):
    """
    数据字典明细表
    """
    dict_code = models.CharField(max_length=50, default='', verbose_name="字典编码")
    item_code = models.CharField(max_length=50, default='', verbose_name="字典明细编码")
    item_name = models.CharField(max_length=50, default='', verbose_name="字典明细名称")
    item_desc = models.CharField(max_length=255, default='', verbose_name="字典明细描述")

    class Meta:
        db_table = 'sys_dict_item'
        verbose_name = "数据字典明细"
        verbose_name_plural = verbose_name
        constraints = [
            models.UniqueConstraint(fields=['dict_code', 'item_code'], name="unique_dict_item")
        ]


class OperationLog(BaseModel):
    """
    操作日志表
    """

    CONFIRM_OCR_LOG = 'CONFIRM_OCR_LOG'
    CONFIRM_AE_LOG = 'CONFIRM_AE_LOG'
    EDIT_AE_LOG = 'EDIT_AE_LOG'
    CREATE_AE_TRACKER_LOG = 'CREATE_AE_TRACKER_LOG'
    EDIT_AE_MEDICATION_MEASURES = 'EDIT_AE_MEDICATION_MEASURES'
    CONFIRM_AE_MEDICATION_MEASURES = 'CONFIRM_AE_MEDICATION_MEASURES'
    TARGET_TYPE_CHOICES = [(CONFIRM_OCR_LOG, 'ocr确认操作痕迹'), (CONFIRM_AE_LOG, 'ae等级确认操作痕迹'),
                           (CREATE_AE_TRACKER_LOG, '创建ae-tracker确认操作痕迹'), (EDIT_AE_LOG, '编辑单条ae等级操作痕迹'),
                           (CONFIRM_AE_MEDICATION_MEASURES, '确认AE用药操作痕迹'), (EDIT_AE_MEDICATION_MEASURES, '单条编辑用药操作痕迹')]

    target_type = models.CharField(max_length=50, choices=TARGET_TYPE_CHOICES, default=CONFIRM_OCR_LOG,
                                   verbose_name="目标类型", db_index=True)
    target_id = models.CharField(max_length=100, default=0, verbose_name="操作目标id", db_index=True)
    operate_content = models.CharField(max_length=1100, default='', verbose_name="操作内容")

    class Meta:
        db_table = 'operation_log'  # 保持原表名
        verbose_name = "操作日志"
        verbose_name_plural = verbose_name


class ModelInvocationLog(models.Model):
    """
    模型调用记录表
    """

    class DeleteStatus(models.IntegerChoices):
        ACTIVE = 0, '未删除'
        DELETED = 1, '已删除'

    delete_flag = models.SmallIntegerField(choices=DeleteStatus.choices, default=DeleteStatus.ACTIVE,
                                           verbose_name="删除标志（0：未删除；1：已删除）", db_index=True)
    task_id = models.CharField(max_length=50, verbose_name="任务ID")
    create_user = models.CharField(max_length=255, null=True, verbose_name="创建人工号")
    create_name = models.CharField(max_length=255, null=True, verbose_name="创建人姓名")
    create_time = models.DateTimeField(auto_now_add=True, verbose_name="创建时间", db_index=True)
    category = models.CharField(max_length=50, null=True, blank=True, verbose_name="任务名称")
    model_name = models.CharField(max_length=100, null=True, blank=True, verbose_name="模型名称")
    start_time = models.DateTimeField(null=True, blank=True, verbose_name="模型开始时间")
    end_time = models.DateTimeField(null=True, blank=True, verbose_name="模型结束时间")
    input_text = models.TextField(null=True, blank=True, verbose_name="输入文本")
    think_text = models.TextField(null=True, blank=True, verbose_name="think文本")
    output_text = models.TextField(null=True, blank=True, verbose_name="输出文本")
    prompt_tokens = models.CharField(max_length=50, verbose_name="输入token长度")
    completion_tokens = models.CharField(max_length=50, verbose_name="输出token长度")
    business_id = models.CharField(max_length=100, null=True, blank=True, verbose_name="业务ID")
    page_count = models.IntegerField(null=True, blank=True, verbose_name="页数")
    # subject_epoch_id = models.CharField(max_length=100, null=True, blank=True, verbose_name="受试者阶段ID")
    # subject_visit_id = models.CharField(max_length=100, null=True, blank=True, verbose_name="受试者访视ID")
    # subject_item_id = models.CharField(max_length=100, null=True, blank=True, verbose_name="受试者操作项ID")

    # project = models.ForeignKey(
    #     'project.Project',
    #     on_delete=models.CASCADE,
    #     db_constraint=False,
    #     related_name='model_invocation_log',
    #     to_field='project_id',
    #     db_column='project_id',
    #     verbose_name="项目ID",
    #     db_index=True
    # )
    #
    # project_site = models.ForeignKey(
    #     'project.ProjectSite',
    #     on_delete=models.CASCADE,
    #     db_constraint=False,
    #     related_name='model_invocation_log',
    #     to_field='project_site_id',
    #     db_column='project_site_id',
    #     verbose_name="项目中心ID",
    #     db_index=True
    # )
    #
    # subject = models.ForeignKey(
    #     'subject.Subject',
    #     on_delete=models.CASCADE,
    #     db_constraint=False,
    #     related_name='model_invocation_log',
    #     to_field='subject_id',
    #     db_column='subject_id',
    #     verbose_name="受试者ID",
    #     db_index=True
    # )
    #
    # patient = models.ForeignKey(
    #     'patient.Patient',
    #     on_delete=models.CASCADE,
    #     db_constraint=False,
    #     related_name='model_invocation_log',
    #     to_field='patient_id',
    #     db_column='patient_id',
    #     verbose_name="患者ID",
    #     db_index=True
    # )

    class Meta:
        db_table = 'model_invocation_log'
        verbose_name = "模型调用记录表"
        verbose_name_plural = verbose_name


class PrivacyRuleConfig(models.Model):
    """
    打码字段配置
    """
    class DeleteStatus(models.IntegerChoices):
        ACTIVE = 0, '未删除'
        DELETED = 1, '已删除'

    STATUS_IND_OPEN = 1
    STATUS_IND_CLOSE = 0
    STATUS_IND_CHOICES = [(STATUS_IND_OPEN, '打码字段开关状态开'), (STATUS_IND_CLOSE, '打码字段开关状态关')]

    delete_flag = models.SmallIntegerField(choices=DeleteStatus.choices, default=DeleteStatus.ACTIVE, verbose_name="删除标志（0：未删除；1：已删除）", db_index=True)
    field_name = models.CharField(max_length=255, default='', verbose_name="隐私字段名称")
    status_ind = models.CharField(max_length=50, choices=STATUS_IND_CHOICES, default=STATUS_IND_OPEN, verbose_name="字段状态", db_index=True)
    create_user = models.CharField(max_length=255, null=True, verbose_name="创建人工号")
    create_name = models.CharField(max_length=255, null=True, verbose_name="创建人姓名")
    create_time = models.DateTimeField(auto_now_add=True, verbose_name="创建时间", db_index=True)
    update_user = models.CharField(max_length=255, null=True, verbose_name="更新人工号")
    update_name = models.CharField(max_length=255, null=True, verbose_name="更新人姓名")
    update_time = models.DateTimeField(auto_now=True, verbose_name="更新时间", db_index=True)

    class Meta:
        db_table = 'privacy_rule_config'
        verbose_name = "打码字段配置"
        verbose_name_plural = verbose_name