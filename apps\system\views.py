from django.shortcuts import render

# Create your views here.
import os
import logging
from datetime import timed<PERSON><PERSON>, datetime

from minio import Minio
from django.db import transaction
from django.db.models import Q
from django.http import FileResponse
from django.conf import settings
from django.shortcuts import render
from django.db.models import Subquery, OuterRef
from rest_framework.viewsets import GenericViewSet
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import serializers
from rest_framework.fields import <PERSON>r<PERSON>ield
from rest_framework import filters, viewsets
from rest_framework.mixins import ListModelMixin, CreateModelMixin, UpdateModelMixin, RetrieveModelMixin
from rest_framework.viewsets import ReadOnlyModelViewSet
from rest_framework.filters import OrderingFilter
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.renderers import BaseRenderer
from rest_framework.exceptions import ValidationError, NotFound, APIException
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser
from rest_framework.permissions import IsAuthenticated
from rest_framework.exceptions import APIException

from django_filters.rest_framework import DjangoFilterBackend

from rest_pandas.views import PandasViewBase
from rest_pandas import PandasExcelRenderer
from openpyxl.styles import Font, Border
from openpyxl.styles import Alignment

from drf_spectacular.utils import extend_schema
from drf_spectacular.utils import extend_schema, OpenApiParameter

from common.auth import ERPSysJWTAuthentication
from common.utils import calculate_file_hash
from common.minio_client import get_minio_client
from common.pagination import StandardResultsSetPagination

from apps.medical_collection.models import MedicalCollectionTask
from . import serializers
from . import models
from . import filters
from apps.subject.models import SubjectItem
from apps.subject_medical.models import SubjectMedicalInfo
from apps.subject_medical.serializers import SubjectMedicalInfoSerializer
from apps.subject.models import SubjectItem, Subject
from apps.system.models import OperationLog
import re
from common.tools import trigger_dag
import io
import requests
from django.http import HttpResponse
import zipfile
from common.renderers import ExcelRenderer
import pandas as pd
from openpyxl import Workbook

logger = logging.getLogger('app')


class BaseAPIView(APIView):
    authentication_classes = [ERPSysJWTAuthentication]
    permission_classes = [IsAuthenticated]


class BaseListViewSet(BaseAPIView, GenericViewSet, ListModelMixin):
    filter_backends = (DjangoFilterBackend, OrderingFilter)


class PrivacyRuleConfigViewSet(BaseListViewSet, UpdateModelMixin):
    queryset = models.PrivacyRuleConfig.objects.filter(delete_flag=0)
    serializer_class = serializers.PrivacyRuleConfigResponseSerializer
    filterset_class = filters.PrivacyRuleConfigListFilter

    http_method_names = ['patch', 'delete', 'post']

    @extend_schema(
        summary='打码字段配置添加',
        tags=['配置系统打码关键字'],
        request=serializers.PrivacyRuleConfigAddRequestSerializer,
        responses=serializers.PrivacyRuleConfigResponseSerializer
    )
    def create(self, request, format=None):
        field_name = request.data.get('field_name')
        result = {}
        result['field_name'] = field_name
        result['create_user'] = request.sys_user.username
        result['create_name'] = request.sys_user.realname
        result['update_user'] = request.sys_user.username
        result['update_name'] = request.sys_user.realname
        PrivacyRuleConfigAdd = models.PrivacyRuleConfig.objects.create(**result)
        serializer = serializers.PrivacyRuleConfigResponseSerializer(PrivacyRuleConfigAdd)
        return Response(serializer.data)

    @extend_schema(
        summary='删除打码字段',
        tags=['配置系统打码关键字'],
    )
    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.delete_flag = 1

        instance.update_user = request.sys_user.username
        instance.update_name = request.sys_user.realname
        instance.save()

        return Response(status=status.HTTP_204_NO_CONTENT)

    @extend_schema(
        summary='更新打码字段及状态',
        tags=['配置系统打码关键字'],
        request=serializers.PrivacyRuleConfigAddRequestSerializer,
        responses=serializers.PrivacyRuleConfigResponseSerializer
    )
    def partial_update(self, request, *args, **kwargs):
        data = request.data.copy()
        data['update_user'] = request.sys_user.username
        data['update_name'] = request.sys_user.realname
        test_result = self.get_object()
        partial = kwargs.pop('partial', True)
        serializer = serializers.PrivacyRuleConfigResponseSerializer(test_result, data=data, partial=partial)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        self.perform_update(serializer)
        serializer = serializers.PrivacyRuleConfigResponseSerializer(test_result)
        return Response(serializer.data)


class PrivacyRuleConfigListViewSet(BaseListViewSet):
    queryset = models.PrivacyRuleConfig.objects.filter(delete_flag=0)
    serializer_class = serializers.PrivacyRuleConfigResponseSerializer
    filterset_class = filters.PrivacyRuleConfigListFilter
    pagination_class = StandardResultsSetPagination
    # ordering_fields = '-id'
    @extend_schema(
        summary='获取打码关键词列表',
        tags=['配置系统打码关键字'],
        responses=serializers.PrivacyRuleConfigResponseSerializer(many=True)
    )
    def list(self, request, format=None):
        res = super().list(request, format)
        return res


class PrivacyRuleImportExportViewSet(BaseListViewSet):
    parser_classes = (MultiPartParser,)  # 支持文件上传
    queryset = models.PrivacyRuleConfig.objects.filter(delete_flag=0)
    # serializer_class = serializers.PrivacyRuleConfigResponseSerializer
    # filterset_class = filters.TestResultsFilter
    # pagination_class = StandardResultsSetPagination
    # ordering_fields = '__all__'
    # ordering = ['seq']

    @extend_schema(
        summary='配置系统打码关键字导出模版',
        tags=['配置系统打码关键字'],
        # parameters=[serializers.TestResultsQueryParamsSerializer],
        responses={
            200: {
                "description": "配置系统打码关键字导出模版",
                "content": {
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": {
                        "schema": {
                            "type": "file"
                        }
                    }
                }
            }
        }
    )
    # def get_extra_actions(self):
    #     """手动实现获取自定义动作的方法"""
    #     return [
    #         method for method in dir(self)
    #         if hasattr(getattr(self, method), 'action')
    #     ]

    @action(detail=False, methods=['get'], url_path='export-excel', renderer_classes=[ExcelRenderer])
    def export_excel(self, request, *args, **kwargs):
        # 设置文件名（包含当前日期）
        filename = f"File-Masks-Template-{datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"

        # 构造数据
        data = [
            {'打码字段': '姓名', '是否打码': '是'},
            {'打码字段': '年龄', '是否打码': '否'}
        ]

        # 转换为 DataFrame
        df = pd.DataFrame(data)

        # 创建文件流
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, index=False)

        # 返回文件流
        output.seek(0)
        response = HttpResponse(
            output.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        return response

    @extend_schema(
        summary='配置系统打码关键字导出',
        tags=['配置系统打码关键字'],
        # parameters=[serializers.TestResultsQueryParamsSerializer],
        responses={
            200: {
                "description": "配置系统打码关键字导出",
                "content": {
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": {
                        "schema": {
                            "type": "file"
                        }
                    }
                }
            }
        }
    )
    @action(detail=False, methods=['get'], url_path='export-excel-data', renderer_classes=[ExcelRenderer])
    def export_excel_data(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())

        # 获取数据并处理
        data = list(queryset.values(
            'field_name', 'status_ind', 'create_user', 'create_name', 'create_time', 'update_user', 'update_name', 'update_time'
        ))

        output = io.BytesIO()
        # 设置文件名（包含当前日期）
        filename = f"File-Masks-Data-{datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"

        # 转换为 DataFrame 以便处理
        df = pd.DataFrame(data)
        if df.empty:
            wb = Workbook()
            output = io.BytesIO()
            wb.save(output)
            output.seek(0)
            response = HttpResponse(
                output.read(),
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            response['Content-Disposition'] = f'attachment; filename="{filename}"'
            return response

        df['status_ind'] = df['status_ind'].apply(
            lambda x: {'1': '是', '0': '否'}.get(x, ''))
        df = df.rename(columns={'field_name': '打码字段', 'status_ind': '是否打码', 'create_user': '创建人',
                                'create_name': '创建人姓名', 'create_time': '创建时间', 'update_user': '更新人',
                                'update_name': '更新人姓名',
                                'update_time': '更新时间'})

        # 创建文件流
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, index=False)

        # 返回文件流
        output.seek(0)
        response = HttpResponse(
            output.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        return response


    @extend_schema(
        summary='导入打码关键字',
        tags=['配置系统打码关键字'],
        request=serializers.PrivacyRuleImportExcelRequestSerializer,
        responses={200: '导入成功', 400: '导入失败'}
    )
    @action(detail=False, methods=['post'], url_path='import-excel')
    def import_excel(self, request, format=None):
        # 检查是否上传了文件
        if 'file' not in request.FILES:
            return Response({'error': '未上传文件'}, status=status.HTTP_400_BAD_REQUEST)

        file = request.FILES['file']

        # 检查文件类型
        if not file.name.endswith(('.xlsx', '.xls')):
            return Response({'error': '请上传Excel文件'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            # 读取Excel文件
            df = pd.read_excel(file)

            # 检查必要的列是否存在
            required_columns = ['打码字段', '是否打码']
            for col in required_columns:
                if col not in df.columns:
                    return Response({'error': f'Excel文件缺少必要的列: {col}'}, status=status.HTTP_400_BAD_REQUEST)

            # 处理数据并更新到数据库
            update_user = request.sys_user.username
            update_name = request.sys_user.realname
            for index, row in df.iterrows():
                field_name = row['打码字段']
                should_mask = row['是否打码']

                # 转换"是否打码"的值为0或1
                status_ind = 0
                if isinstance(should_mask, str):
                    if should_mask.lower() in ['是', '1']:
                        status_ind = 1
                elif isinstance(should_mask, int):
                    if should_mask == 1:
                        status_ind = 1

                # 更新或创建记录
                # 先检查记录是否存在
                try:
                    obj = models.PrivacyRuleConfig.objects.get(field_name=field_name, delete_flag=0)
                    # 记录存在，只更新需要更新的字段
                    obj.status_ind = status_ind
                    obj.update_user = update_user
                    obj.update_name = update_name
                    obj.update_time = datetime.now()
                    obj.save()
                    created = False
                except models.PrivacyRuleConfig.DoesNotExist:
                    # 记录不存在，创建新记录并包含所有字段
                    obj = models.PrivacyRuleConfig.objects.create(
                        field_name=field_name,
                        status_ind=status_ind,
                        update_user=update_user,
                        update_name=update_name,
                        create_user=update_user,  # 新增字段（仅创建时需要）
                        create_name=update_name  # 新增字段（仅创建时需要）
                    )
                    created = True

            return Response({'message': '导入成功'}, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({'error': f'导入失败: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
