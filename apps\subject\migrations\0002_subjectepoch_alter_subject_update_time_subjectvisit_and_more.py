# Generated by Django 4.1.5 on 2025-03-26 22:00

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("project", "0002_alter_project_update_time_and_more"),
        ("subject", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="SubjectEpoch",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "delete_flag",
                    models.SmallIntegerField(
                        choices=[(0, "未删除"), (1, "已删除")],
                        db_index=True,
                        default=0,
                        verbose_name="删除标志（0：未删除；1：已删除）",
                    ),
                ),
                (
                    "data_version",
                    models.PositiveIntegerField(default=0, verbose_name="数据版本"),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True, db_index=True, verbose_name="创建时间"
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name="更新时间"
                    ),
                ),
                (
                    "create_user",
                    models.CharField(max_length=255, null=True, verbose_name="创建人工号"),
                ),
                (
                    "create_name",
                    models.CharField(max_length=255, null=True, verbose_name="创建人姓名"),
                ),
                (
                    "update_user",
                    models.CharField(max_length=255, null=True, verbose_name="更新人工号"),
                ),
                (
                    "update_name",
                    models.CharField(max_length=255, null=True, verbose_name="更新人姓名"),
                ),
                (
                    "subject_epoch_id",
                    models.CharField(
                        db_index=True,
                        max_length=100,
                        unique=True,
                        verbose_name="受试者阶段ID（OT受试者阶段数据源ID + 受试者ID）",
                    ),
                ),
                (
                    "source_id",
                    models.CharField(
                        db_index=True, max_length=50, verbose_name="OT受试者阶段数据源ID"
                    ),
                ),
                (
                    "epoch_id",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        max_length=50,
                        null=True,
                        verbose_name="阶段ID",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        blank=True, max_length=50, null=True, verbose_name="阶段状态"
                    ),
                ),
                (
                    "status_text",
                    models.CharField(
                        blank=True, max_length=50, null=True, verbose_name="阶段状态描述"
                    ),
                ),
                (
                    "label",
                    models.CharField(
                        blank=True, max_length=50, null=True, verbose_name="阶段名称"
                    ),
                ),
                ("seq", models.IntegerField(null=True, verbose_name="阶段序号")),
                (
                    "project",
                    models.ForeignKey(
                        db_column="project_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subject_epochs",
                        to="project.project",
                        to_field="project_id",
                        verbose_name="项目ID",
                    ),
                ),
                (
                    "project_site",
                    models.ForeignKey(
                        db_column="project_site_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subject_epochs",
                        to="project.projectsite",
                        to_field="project_site_id",
                        verbose_name="项目中心ID",
                    ),
                ),
                (
                    "subject",
                    models.ForeignKey(
                        db_column="subject_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subject_epochs",
                        to="subject.subject",
                        to_field="subject_id",
                        verbose_name="受试者ID",
                    ),
                ),
            ],
            options={
                "verbose_name": "受试者阶段信息",
                "verbose_name_plural": "受试者阶段信息",
                "db_table": "subject_epoch_info",
            },
        ),
        migrations.AlterField(
            model_name="subject",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True, db_index=True, verbose_name="更新时间"
            ),
        ),
        migrations.CreateModel(
            name="SubjectVisit",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "delete_flag",
                    models.SmallIntegerField(
                        choices=[(0, "未删除"), (1, "已删除")],
                        db_index=True,
                        default=0,
                        verbose_name="删除标志（0：未删除；1：已删除）",
                    ),
                ),
                (
                    "data_version",
                    models.PositiveIntegerField(default=0, verbose_name="数据版本"),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True, db_index=True, verbose_name="创建时间"
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name="更新时间"
                    ),
                ),
                (
                    "create_user",
                    models.CharField(max_length=255, null=True, verbose_name="创建人工号"),
                ),
                (
                    "create_name",
                    models.CharField(max_length=255, null=True, verbose_name="创建人姓名"),
                ),
                (
                    "update_user",
                    models.CharField(max_length=255, null=True, verbose_name="更新人工号"),
                ),
                (
                    "update_name",
                    models.CharField(max_length=255, null=True, verbose_name="更新人姓名"),
                ),
                (
                    "subject_visit_id",
                    models.CharField(
                        db_index=True,
                        max_length=100,
                        unique=True,
                        verbose_name="受试者访视ID（OT受试者访访数据源ID + 受试者ID）",
                    ),
                ),
                (
                    "source_id",
                    models.CharField(
                        db_index=True, max_length=50, verbose_name="OT受试者访视数据源ID"
                    ),
                ),
                (
                    "visit_id",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        max_length=50,
                        null=True,
                        verbose_name="访视ID",
                    ),
                ),
                (
                    "type",
                    models.CharField(
                        blank=True, max_length=50, null=True, verbose_name="访视类型"
                    ),
                ),
                (
                    "type_text",
                    models.CharField(
                        blank=True, max_length=50, null=True, verbose_name="访视类型描述"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        blank=True, max_length=50, null=True, verbose_name="访视状态"
                    ),
                ),
                (
                    "status_text",
                    models.CharField(
                        blank=True, max_length=50, null=True, verbose_name="访视状态描述"
                    ),
                ),
                (
                    "label",
                    models.CharField(
                        blank=True, max_length=300, null=True, verbose_name="访视名称"
                    ),
                ),
                ("visit_date", models.JSONField(null=True, verbose_name="访视日期")),
                (
                    "skip_reason",
                    models.CharField(
                        blank=True, max_length=500, null=True, verbose_name="跳过原因"
                    ),
                ),
                ("prepare_date", models.DateField(null=True, verbose_name="访视准备时间")),
                (
                    "ot_create_time",
                    models.DateTimeField(
                        db_index=True, null=True, verbose_name="OT创建时间"
                    ),
                ),
                (
                    "ot_update_time",
                    models.DateTimeField(
                        db_index=True, null=True, verbose_name="OT更新时间"
                    ),
                ),
                (
                    "project",
                    models.ForeignKey(
                        db_column="project_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subject_visits",
                        to="project.project",
                        to_field="project_id",
                        verbose_name="项目ID",
                    ),
                ),
                (
                    "project_site",
                    models.ForeignKey(
                        db_column="project_site_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subject_visits",
                        to="project.projectsite",
                        to_field="project_site_id",
                        verbose_name="项目中心ID",
                    ),
                ),
                (
                    "subject",
                    models.ForeignKey(
                        db_column="subject_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subject_visits",
                        to="subject.subject",
                        to_field="subject_id",
                        verbose_name="受试者ID",
                    ),
                ),
                (
                    "subject_epoch",
                    models.ForeignKey(
                        db_column="subject_epoch_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subject_visits",
                        to="subject.subjectepoch",
                        to_field="subject_epoch_id",
                        verbose_name="受试者阶段ID（OT受试者阶段数据源ID + 受试者ID）",
                    ),
                ),
            ],
            options={
                "verbose_name": "受试者访视信息",
                "verbose_name_plural": "受试者访视信息",
                "db_table": "subject_visit_info",
            },
        ),
        migrations.CreateModel(
            name="SubjectItem",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "delete_flag",
                    models.SmallIntegerField(
                        choices=[(0, "未删除"), (1, "已删除")],
                        db_index=True,
                        default=0,
                        verbose_name="删除标志（0：未删除；1：已删除）",
                    ),
                ),
                (
                    "data_version",
                    models.PositiveIntegerField(default=0, verbose_name="数据版本"),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True, db_index=True, verbose_name="创建时间"
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name="更新时间"
                    ),
                ),
                (
                    "create_user",
                    models.CharField(max_length=255, null=True, verbose_name="创建人工号"),
                ),
                (
                    "create_name",
                    models.CharField(max_length=255, null=True, verbose_name="创建人姓名"),
                ),
                (
                    "update_user",
                    models.CharField(max_length=255, null=True, verbose_name="更新人工号"),
                ),
                (
                    "update_name",
                    models.CharField(max_length=255, null=True, verbose_name="更新人姓名"),
                ),
                (
                    "subject_item_id",
                    models.CharField(
                        db_index=True,
                        max_length=100,
                        unique=True,
                        verbose_name="受试者操作项唯一标识（OT受试者访视数据源ID + 受试者ID）",
                    ),
                ),
                (
                    "source_id",
                    models.CharField(
                        db_index=True, max_length=50, verbose_name="OT受试者访视数据源ID"
                    ),
                ),
                (
                    "item_id",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        max_length=50,
                        null=True,
                        verbose_name="操作项ID",
                    ),
                ),
                (
                    "label",
                    models.CharField(
                        blank=True, max_length=50, null=True, verbose_name="操作项名称"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        blank=True, max_length=50, null=True, verbose_name="操作项状态"
                    ),
                ),
                (
                    "status_text",
                    models.CharField(
                        blank=True, max_length=50, null=True, verbose_name="操作项状态描述"
                    ),
                ),
                (
                    "seq",
                    models.CharField(
                        blank=True, max_length=50, null=True, verbose_name="操作项序号"
                    ),
                ),
                (
                    "ae_ai_current_step",
                    models.CharField(
                        default=0, max_length=50, verbose_name="AE AI识别功能当前步骤：0,1,2,3,4"
                    ),
                ),
                (
                    "ae_ai_task_id",
                    models.CharField(default=0, max_length=50, verbose_name="任务id"),
                ),
                (
                    "project",
                    models.ForeignKey(
                        db_column="project_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subject_items",
                        to="project.project",
                        to_field="project_id",
                        verbose_name="项目ID",
                    ),
                ),
                (
                    "project_site",
                    models.ForeignKey(
                        db_column="project_site_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subject_items",
                        to="project.projectsite",
                        to_field="project_site_id",
                        verbose_name="项目中心ID",
                    ),
                ),
                (
                    "subject",
                    models.ForeignKey(
                        db_column="subject_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subject_items",
                        to="subject.subject",
                        to_field="subject_id",
                        verbose_name="受试者ID",
                    ),
                ),
                (
                    "subject_visit",
                    models.ForeignKey(
                        db_column="subject_visit_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subject_items",
                        to="subject.subjectvisit",
                        to_field="subject_visit_id",
                        verbose_name="受试者访视ID（OT受试者访视数据源ID + 受试者ID）",
                    ),
                ),
            ],
            options={
                "verbose_name": "受试者访视操作项信息",
                "verbose_name_plural": "受试者访视操作项信息",
                "db_table": "subject_item_info",
            },
        ),
    ]
