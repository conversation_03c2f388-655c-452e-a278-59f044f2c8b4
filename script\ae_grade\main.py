"""
AE等级识别主入口
基于PocketFlow的高性能AE识别系统
"""
import time
import logging
import asyncio
from typing import List, Dict, Any, Optional
from concurrent.futures import ThreadPoolExecutor

from script.ae_grade.flow import process_ae_analysis as flow_process_ae_analysis

logger = logging.getLogger(__name__)


def process_ae_analysis(test_results: List[Dict[str, Any]],
                       timeout: Optional[int] = None,
                       prompt_templates: Optional[Dict[str, str]] = None,
                       task_info: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    AE等级识别主入口函数

    Args:
        test_results: 从数据库直接查询的原始检验结果列表
                     包含字段：id, test_name, test_value, test_flag, test_unit等
        timeout: 模型调用超时时间（秒）
        prompt_templates: 预加载的提示词模板字典
        task_info: 任务信息字典，包含task_id, create_user, create_name, business_id等

    Returns:
        Dict: AE分析结果，格式与原系统兼容
        {
            "results": [
                {
                    "序号": int,            # 对应原始数据的id字段
                    "不良事件名称": str,      # AE名称，空字符串表示无AE
                    "CTCAE分级": int,       # CTCAE等级 (0-5)，0表示无AE
                    "不良事件定义": str,      # AE定义描述
                    "状态": str             # 异常状态符号 ("-", "↑", "↓")
                }
            ],
            "summary": {
                "total_processed": int,     # 总处理项目数
                "abnormal_detected": int,   # 检出异常项目数
                "processing_time": float,   # 处理耗时(秒)
                "status": str              # 处理状态
            }
        }
    """

    start_time = time.time()

    try:
        # 使用asyncio.run()调用异步流程处理函数，建立同步/异步边界
        result = asyncio.run(flow_process_ae_analysis(
            test_results, 
            timeout=timeout, 
            prompt_templates=prompt_templates,
            task_info=task_info
        ))

        # 添加处理时间
        processing_time = time.time() - start_time
        if "summary" in result:
            result["summary"]["processing_time"] = processing_time

        logger.info(f"AE分析完成，耗时: {processing_time:.2f}秒，处理了 {len(result.get('results', []))} 个项目")
        return result

    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(f"AE分析失败，耗时: {processing_time:.2f}秒，错误: {e}")
        raise
