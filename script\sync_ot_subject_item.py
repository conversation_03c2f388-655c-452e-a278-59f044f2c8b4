import os
import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smo.settings.test')
django.setup()

from apps.subject.models import SubjectItem
from django.db import transaction
import psycopg2


# 'postgresql://postgres:Qr%4077.88@**************:5432/oceanus_trial_clinplus'
pg_conn_params = {
    'host': '**************',
    'database': 'oceanus_trial_clinplus',
    'user': 'postgres',
    'password': 'Qr@77.88',
    'port': 5432
}

PAGE_SIZE = 100  # 每次查询的记录数，可以根据实际情况调整

with open('./script/test_project_no.txt') as f:
    PROJECT_NO_LIST = f.readlines()

PROJECT_NO_LIST = list(set([i.strip() for i in PROJECT_NO_LIST if i.strip()]))
print(PROJECT_NO_LIST)


def migrate_subject():
    # Connect to PostgreSQL
    pg_conn = psycopg2.connect(**pg_conn_params)
    pg_cursor = pg_conn.cursor()

    try:
        # 获取所有项目ID
        project_query = f"SELECT project_id FROM project WHERE project_no in {tuple(PROJECT_NO_LIST)}"
        pg_cursor.execute(project_query)
        project_ids = [row[0] for row in pg_cursor.fetchall()]
        
        print(f"找到 {len(project_ids)} 个项目需要处理")
        
        # 遍历每个项目
        for project_id in project_ids:
            print(f"正在处理项目 ID: {project_id}")
            
            # 获取项目下的所有患者ID
            subject_query = f"SELECT id FROM subject WHERE project_id = '{project_id}'"
            pg_cursor.execute(subject_query)
            subject_ids = [row[0] for row in pg_cursor.fetchall()]
            
            if not subject_ids:
                print(f"项目 {project_id} 没有患者数据，跳过")
                continue
                
            print(f"项目 {project_id} 有 {len(subject_ids)} 个患者")
            
            for sub_id in subject_ids:
                # 查询这批患者的 subject_item 数据
                pg_query = f"""
                SELECT
                    t1.id as source_id,
                    t1.visit->>'subVstId' as subject_visit_source_id,
                    t1.item->>'itemId' as item_id,
                    t1.status,
                    dictconstantname('e888888', t1.status) as status_text,
                    t1.item->>'itemLabel' as label,
                    t1.seq,
                    t1.project_id,
                    t1.project_site_id,
                    t1.sub_id as subject_id
                FROM
                    subject_item AS t1
                WHERE
                    t1.sub_id = '{sub_id}'
                """
                pg_cursor.execute(pg_query)
                subject_data = pg_cursor.fetchall()
                
                if not subject_data:
                    print(f"项目 {project_id} 的这批患者没有 subject_item 数据")
                    continue
                    
                print(f"获取到 {len(subject_data)} 条 subject_item 记录")

                # 获取列名
                column_names = [desc[0] for desc in pg_cursor.description]

                # 处理每一行并插入/更新到 MySQL
                for row in subject_data:
                    with transaction.atomic():
                        # 创建 subject_item 数据字典
                        subject_item_dict = dict(zip(column_names, row))
                        subject_item_dict['subject_item_id'] = subject_item_dict['source_id'] + '-' + subject_item_dict['subject_id']
                        subject_item_dict['subject_visit_id'] = subject_item_dict['subject_visit_source_id'] + '-' + subject_item_dict['subject_id']
                        subject_item_dict.pop('subject_visit_source_id')
                        
                        # print(f"处理 subject_item: {subject_item_dict['source_id']}, subject_id: {subject_item_dict['subject_id']}")

                        try:
                            # 尝试获取现有的 subject_item
                            subject_item = SubjectItem.objects.get(subject_id=subject_item_dict['subject_id'], source_id=subject_item_dict['source_id'])

                            # 更新现有的 subject_item
                            for key, value in subject_item_dict.items():
                                setattr(subject_item, key, value)
                            subject_item.save()

                            # print(f"更新了 subject_item: {subject_item.source_id}")

                        except SubjectItem.DoesNotExist:
                            # 创建新的 subject_item
                            subject_item = SubjectItem.objects.create(**subject_item_dict)

                            # print(f"创建了新的 subject_item: {subject_item_dict['source_id']}")

        print("迁移成功完成。")

    except Exception as e:
        print(f"迁移过程中出错: {str(e)}")
        # 添加堆栈跟踪以便调试
        import traceback
        traceback.print_exc()
    finally:
        pg_cursor.close()
        pg_conn.close()


if __name__ == "__main__":
    # python -m script.sync_ot_subject_item
    migrate_subject()
