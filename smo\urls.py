"""
URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

import os
from django.contrib import admin
from django.urls import path, include, re_path
from django.contrib.auth.mixins import LoginRequiredMixin
from django.core.exceptions import PermissionDenied
from rest_framework import permissions
from rest_framework.views import APIView
from rest_framework.response import Response
from drf_spectacular.views import SpectacularAPIView, SpectacularRedocView, SpectacularSwaggerView
from drf_spectacular.openapi import OpenApiAuthenticationExtension
from drf_spectacular.utils import extend_schema

from common.renderers import TextRenderer
from apps.users.views import ObtainTokenView, DingtalkObtainTokenView
from common.auth import ERPSysJWTAuthentication, IsAuthenticated


class ERPSysJWTAuthenticationScheme(OpenApiAuthenticationExtension):
    target_class = 'common.auth.ERPSysJWTAuthentication'
    name = 'ERP系统JWT验证'

    def get_security_definition(self, auto_schema):
        return {
            'type': 'apiKey',
            'in': 'header',
            'name': 'X-Access-Token',
        }


class BearerTokenAuthenticationScheme(OpenApiAuthenticationExtension):
    target_class = 'apps.external.views.BearerTokenAuthentication'  # 这里写你的 BearerTokenAuthentication 的全路径
    name = 'BearerAuth'  # OpenAPI中显示的 scheme 名字

    def get_security_definition(self, auto_schema):
        return {
            'type': 'http',
            'scheme': 'bearer',
            # 'bearerFormat': 'JWT',  # 如果你用的不是JWT，也可以写成 'API Key' 或直接删掉
        }


class StatusView(APIView):
    renderer_classes = [TextRenderer, ]

    @extend_schema(
        summary='接口状态检查',
        tags=['Status'],
    )
    def get(self, request, format=None):
        return Response('ok')


@extend_schema(exclude=True)
class LoginRequiredSpectacularAPIView(LoginRequiredMixin, SpectacularAPIView):

    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_authenticated:
            raise PermissionDenied(self.get_permission_denied_message())
        return super().dispatch(request, *args, **kwargs)


@extend_schema(exclude=True)
class LoginRequiredSpectacularSwaggerView(LoginRequiredMixin, SpectacularSwaggerView):
    login_url = '/admin/login/'


urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/status/', StatusView.as_view()),
    path('api/v1/auth/dingtalk', DingtalkObtainTokenView.as_view()),
    # path('api/v1/users/', include('apps.users.urls')),
    path('api/v1', include('apps.project.urls')),
    path('api/v1', include('apps.subject.urls')),
    path('api/v1', include('apps.medical.urls')),
    path('api/v1', include('apps.medical_collection.urls')),
    path('api/v1', include('apps.ae_tracker.urls')),
    path('api/v1', include('apps.subject_medical.urls')),
    path('api/v1', include('apps.users.urls')),
    path('api/v1/ruixing-chat', include('apps.ruixing_chat.urls')),
    path('api/v1/external', include('apps.external.urls')),
    path('api/v1', include('apps.system.urls'))
]

if os.environ.get('DJANGO_SETTINGS_MODULE') != 'smo.settings.prod':
    urlpatterns.append(path('api/v1/auth/test', ObtainTokenView.as_view()))

api_schema_urlpatterns = [
    path('api/schema/', LoginRequiredSpectacularAPIView.as_view(), name='schema'),
    path('api/schema/swagger-ui/', LoginRequiredSpectacularSwaggerView.as_view(url_name='schema'), name='swagger-ui'),
    # path('api/schema/redoc/', SpectacularRedocView.as_view(url_name='schema'), name='redoc'),
]

urlpatterns += api_schema_urlpatterns
