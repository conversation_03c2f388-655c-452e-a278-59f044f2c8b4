from datetime import timedelta
from django.conf import settings
from rest_framework import serializers

from common.minio_client import get_minio_client
from common.models import BaseFileModel
from common.serializers import FileUrlMixin

from . import models
from .models import ConversationFeedback


class ConversationFeedbackSerializer(serializers.ModelSerializer):
    class Meta:
        model = ConversationFeedback
        fields = "__all__"


class ChatMessageSerializer(serializers.Serializer):
    query = serializers.CharField(help_text="用户输入的对话内容")
    inputs = serializers.DictField(
        required=False, default=dict,
        help_text="可选参数，例如上下文设置"
    )
    response_mode = serializers.ChoiceField(
        choices=['streaming', 'blocking'],
        default='blocking',
        help_text="返回模式：streaming 为流式，blocking 为普通响应"
    )
    conversation_id = serializers.CharField(required=False, allow_null=True, help_text="对话ID，可选")
    auto_generate_name = serializers.BooleanField(default=True, help_text="是否自动生成对话标题")
    files = serializers.ListField(
        child=serializers.FileField(),
        required=False,
        allow_empty=True,
        help_text="上传的文件列表"
    )


class FilesUploadSerializer(serializers.Serializer):
    file = serializers.FileField(
        help_text="图片文件，支持格式：['png', 'jpeg', 'jpg', 'webp', 'gif']，最大15MB"
    )

    def validate_file(self, value):
        # 校验 MIME 类型
        allowed_mime_types = [
            'image/png',
            'image/jpeg',
            'image/webp',
            'image/gif'
        ]
        if value.content_type not in allowed_mime_types:
            raise serializers.ValidationError("不支持的图片格式")

        # 校验大小
        if value.size > 15 * 1024 * 1024:
            raise serializers.ValidationError("文件大小不能超过15MB")

        return value


class FeedbacksSerializer(serializers.Serializer):
    RATING_CHOICES = (
        ('like', 'like'),
        ('dislike', 'dislike'),
        ('neutral', 'null'),
    )

    rating = serializers.ChoiceField(choices=RATING_CHOICES, help_text="反馈评分：like / dislike / null")
    user = serializers.CharField(max_length=64, help_text="用户唯一标识")
    content = serializers.CharField(help_text="反馈内容", allow_blank=True)


class RenameConversationsSerializer(serializers.Serializer):

    user = serializers.CharField(max_length=64, help_text="用户唯一标识")
    name = serializers.CharField(help_text="名称", allow_blank=True)
    auto_generate = serializers.BooleanField(help_text="自动生成标题，默认 false")


class AudioToTextSerializer(serializers.Serializer):
    file = serializers.FileField(
        help_text="语音文件，支持格式：['mp3', 'mp4', 'mpeg', 'mpga', 'm4a', 'wav', 'webm']，最大15MB"
    )


class TextToAudioSerializer(serializers.Serializer):
    text = serializers.CharField(
        max_length=1000,
        help_text="待转换的文本内容",
        required=True
    )
    user = serializers.CharField(
        max_length=64,
        help_text="用户唯一标识",
        required=True
    )
    message_id = serializers.UUIDField(
        help_text="对应的消息 ID",
        required=True
    )


