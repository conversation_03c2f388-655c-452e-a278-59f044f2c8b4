from django.contrib import admin
from .models import User, AuthProjectRoleACL


class UserAdmin(admin.ModelAdmin):
    list_filter = ('project_whitelist_flag', 'is_staff', 'is_superuser')
    search_fields = ('username',)


class AuthProjectRoleACLAdmin(admin.ModelAdmin):
    list_filter = ('is_allowed', 'access_scope')
    search_fields = ('role_code', 'role_name', 'role_name_en')


admin.site.register(User, UserAdmin)
admin.site.register(AuthProjectRoleACL, AuthProjectRoleACLAdmin)
