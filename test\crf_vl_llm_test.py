import os
import time
import base64
import argparse
import fitz  # PyMuPDF
import requests


def pdf_to_base64_images(pdf_path):
    """
    将 PDF 每一页转换为 base64 编码的 PNG 图片
    """
    doc = fitz.open(pdf_path)
    images_base64 = []
    import random
    a = random.choice(list(range(1,100)))
    for page_num in range(len(doc)):
        page = doc.load_page(page_num)
        pix = page.get_pixmap()
        img_bytes = pix.tobytes("png")
        
        image_path = os.path.join('aaa', f"{a}_page_{page_num + 1}.png")
        pix.save(image_path)
            
        base64_str = base64.b64encode(img_bytes).decode("utf-8")
        images_base64.append(base64_str)

    return images_base64


def collect_all_images(folder_path):
    """
    收集文件夹中所有 PDF 的所有页（转为 base64 图片）
    """
    all_images = []
    for filename in sorted(os.listdir(folder_path)):
        if filename.lower().endswith(".pdf"):
            pdf_path = os.path.join(folder_path, filename)
            print(f"Extracting images from {filename}...")
            images = pdf_to_base64_images(pdf_path)
            all_images.extend(images)
    return all_images


def send_request_with_images(image_base64_list):
    """
    向大模型 API 发送请求，包含多个 base64 图片
    """
    url = 'https://192.168.230.105:30334/v1/infer/44e446dc-4dac-463a-bb20-a3dd65436b08/v1/chat/completions'

    messages_content = [{"type": "image_url", "image_url": img} for img in image_base64_list]

    messages_content.append({
        "type": "text",
        "text": "你是一个专业的临床试验数据分析专家，现在需要你从上边的病历图片中提取关键信息，并填入CRF表格的JSON结构中。你需要以最高的准确性和严谨性完成这项任务"
    })

    body = {
        "model": "InternVL2_5-78B",
        "messages": [
            {
                "role": "user",
                "content": messages_content
            }
        ],
        "max_tokens": 4096,
        "do_sample": True,
        "repetition_penalty": 1.00,
        "temperature": 0.01,
        "top_p": 0.001,
        "top_k": 1
    }

    response = requests.post(url, json=body, verify=False)
    return response


def main():
    parser = argparse.ArgumentParser(description="Send all PDFs in a folder as one request to the vision model.")
    parser.add_argument('folder_path', type=str, help="Folder containing PDF files.")
    parser.add_argument('output_file', type=str, help="Path to save the model output text.")
    args = parser.parse_args()

    print("Collecting all PDF pages as images...")
    all_images = collect_all_images(args.folder_path)

    print(f"Sending {len(all_images)} images to model...")
    response = send_request_with_images(all_images)

    with open(args.output_file, "w", encoding="utf-8") as f:
        if response.status_code == 200:
            result = response.json()
            content = result["choices"][0]["message"]["content"]
            f.write(content)
            print("Result saved to:", args.output_file)
        else:
            f.write(f"Request failed, status code: {response.status_code}")
            print("Failed with status code:", response.status_code)


if __name__ == '__main__':
    main()
