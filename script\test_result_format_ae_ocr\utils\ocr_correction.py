"""
OCR常见错误修复工具
支持针对特定字段进行可配置的精确修复
"""
import re
import json
from typing import Dict, List, Any, Optional, Union
import logging

logger = logging.getLogger(__name__)


class OCRCorrectionConfig:
    """OCR修复配置类"""
    
    def __init__(self):
        """初始化默认修复规则"""
        self.field_rules = {
            # 检验单位字段的修复规则
            'test_unit': {
                'exact_mapping': {
                    # 精确匹配替换（不区分大小写）
                    '/1omm' : 'mmol/L',
                    'umol/L': 'μmol/L',
                    'umo1/l': 'μmol/L',
                    'umoI/l': 'μmol/L',
                    'umo1/L': 'μmol/L',

                    'mmo1/l': 'mmol/L',
                    'mmol/l': 'mmol/L',
                    'mmo1/L': 'mmol/L',
                    'mmoi/l': 'mmol/L',
                    'mmoi/L': 'mmol/L',
                    'ol/l': 'mmol/L',
                    'ol/L': 'mmol/L',
                    'o1/l': 'mmol/L',
                    'o1/L': 'mmol/L',
                    'mo1/l': 'mmol/L',
                    'mol/l': 'mmol/L',
                    'mo1/L': 'mmol/L',
                    'g/1': 'g/L',
                    'g/l': 'g/L',
                    'mg/1': 'mg/L',
                    'mg/l': 'mg/L',
                    'ng/1': 'ng/L',
                    'ng/l': 'ng/L',
                    'pg/1': 'pg/L',
                    'pg/l': 'pg/L',
                    'ug/1': 'ug/L',
                    'ug/l': 'ug/L',
                    'μg/1': 'μg/L',
                    'μg/l': 'μg/L',
                    'u/1': 'U/L',
                    'u/l': 'U/L',
                    'iu/1': 'IU/L',
                    'iu/l': 'IU/L',
                    'IU/l': 'IU/L',
                    'iu/mL': 'IU/mL',
                    'iu/ml': 'IU/mL',
                    'IU/ml': 'IU/mL',
                    '/ul.': '/uL',
                    '/u1.': '/uL',
                    '/ul': '/uL',
                    '/u1': '/uL',
                    '1n/': '/μL',
                    'In/': '/μL',
                    'ln/': '/μL',
                    '1N/': '/μL',
                    'IN/': '/μL',
                    'LN/': '/μL',
                    'copies/m1': 'copies/mL',
                    'copies/ml': 'copies/mL',
                    'copies/mL': 'copies/mL',
                    '1og copies/mL': 'log copies/mL',
                    'log copies/m1': 'log copies/mL',
                    'log copies/ml': 'log copies/mL',
                    '％': '%',
                    's/00':"S/CO",
                    'S/00':"S/CO",
                    's/C0': "S/CO",
                    'S/C0': "S/CO",
                    'mmol/': 'mmol/L',
                    'umol/': 'μmol/L',
                    'μmol/': 'μmol/L',
                    'umol': 'μmol/L',
                    'μmol': 'μmol/L',
                    'f1': 'fL',
                    # 科学计数法修复
                    '109/L': '10^9/L',
                    '×10^9/L': 'x10^9/L',
                    '×10^6/L': 'x10^6/L',
                    '×10^12/L': 'x10^12/L',
                },
                'regex_rules': [
                    # 正则表达式规则：(pattern, replacement, description)
                    (r'(\d+)\s*x\s*1O\^(\d+)', r'\1×10^\2', '科学计数法修复：1O->10'),
                    (r'(\d+)\s*x\s*10\^(\d+)', r'\1×10^\2', '科学计数法修复：x->×'),
                    (r'(\d+)\s*X\s*10\^(\d+)', r'\1×10^\2', '科学计数法修复：X->×'),
                    (r'(\w+)/1\b', r'\1/L', '单位修复：/1->/L'),
                    (r'\bmmo1\b', 'mmol', '字符修复：mmo1->mmol'),
                    (r'\bumo1\b', 'μmol', '字符修复：umo1->μmol'),
                    (r'\bmo1\b', 'mol', '字符修复：mo1->mol'),
                    # L的修复
                    (r'I\.', 'L', 'I. 实际是 L'),
                    (r'l\.', 'L', 'l. 实际是 L'),
                    (r'1\.', 'L', '1. 实际是 L'),
                    # 10°后数值 转为 10^数值
                    (r'(10)°(\d+.*)', r'\1^\2', '10°后数值 转为 10^数值'),
                    # 修复OCR识别错误：1n/ In/ ln/ 等应该是 /μL
                    (r'^[1IlL][nN]/$', '/μL', '单位修复：1n/、In/、ln/等 -> /μL'),
                    (r'^[1IlL][nN]$', '/μL', '单位修复：1n、In、ln等 -> /μL'),
                    # 处理更广泛的模式：数字或字母+n/
                    (r'^[1IlL0Oo][nN]/$', '/μL', '单位修复：类似1n/、In/、On/等 -> /μL'),
                ]
            },
            
            # 检验值字段的修复规则
            'test_value': {
                'exact_mapping': {
                    'O': '0',  # 大写O误识别为0
                    'o': '0',  # 小写o误识别为0
                    'l': '1',  # 小写l误识别为1
                    'I': '1',  # 大写I误识别为1
                    'S': '5',  # 大写S误识别为5
                    'B': '8',  # 大写B误识别为8
                  
                 
                },
                'regex_rules': [
                    # 数值中的常见OCR错误
                    (r'\bo\b', '0', '字符修复：独立的o->0'),
                    (r'\bO\b', '0', '字符修复：独立的O->0'),
                    (r'\bl\b', '1', '字符修复：独立的l->1'),
                    (r'\bI\b', '1', '字符修复：独立的I->1'),
                    (r'(\d+)\.(\d+)o(\d*)', r'\1.\2\3', '小数点后o->0'),
                    (r'(\d+)\.(\d+)O(\d*)', r'\1.\2\3', '小数点后O->0'),
                    (r'(\d+)l(\d+)', r'\1\2', '数字中间的l去除'),
                    (r'(\d+)I(\d+)', r'\1\2', '数字中间的I去除'),
                    
                ]
            },
            
            # 检验名称字段的修复规则
            'test_name': {
                'exact_mapping': {
                    # 精确匹配适用于整个字符串的替换
                },
                'regex_rules': [
                    # 中文括号转英文括号
                    (r'（', '(', '中文左括号转英文'),
                    (r'）', ')', '中文右括号转英文'),
                    (r'％', '%', '中文百分号转英文百分号'),
                    # 中文逗号转英文逗号
                    (r'，', ',', '中文逗号转英文逗号'),
                    # I. 实际是 L
                    (r'I\.', 'L', 'I. 实际是 L'),
                    # l. 实际是 L
                    (r'l\.', 'L', 'l. 实际是 L'),
                    # 1. 实际是 L
                    # (r'1\.', 'L', '1. 实际是 L'),
                    # a- 修改为 α-
                    (r'a-', 'α-', 'a- 修改为 α-'),
                    # Y- γ-
                    (r'Y-', 'γ-', 'Y- 修改为 γ-'),

                ]
            },
            'test_code': {
                'exact_mapping': {
                    'C1': 'Cl',
                    'T-C02': 'T-CO2',
                    'I': '',
                    'f1':"fl"
                },
                'regex_rules': [
                    (r'（', '(', '中文左括号转英文'),
                    (r'）', ')', '中文右括号转英文'),
                    (r'％', '%', '中文百分号转英文百分号'),
                    # 中文逗号转英文逗号
                    (r'，', ',', '中文逗号转英文逗号'),
                    # I. 实际是 L
                    (r'I\.', 'L', 'I. 实际是 L'),
                    # l. 实际是 L
                    (r'l\.', 'L', 'l. 实际是 L'),
                    # 1. 实际是 L
                    (r'1\.', 'L', '1. 实际是 L'),

                    # M0 实际是 MO
                    (r'M0', 'MO', 'M0 实际是 MO'),
                    # E0 实际是 EO
                    (r'E0', 'EO', 'E0 实际是 EO'),
                    
                    # MON0 修复为 MONO
                    (r'MON0', 'MONO', 'MON0 修复为 MONO'),

                    # 如果数值空格开头，那么删除数值和空格 `2 CRE` 实际是 `CRE`
                    (r'^\s*\d+\s+', '', '数值空格开头，删除数值和空格'),

                    #如果只有数值，那么直接删除
                    (r'^\s*(\d+)\s*$', '', '只有数值，直接删除'),

                ]
            },
            
            # 异常符号字段的修复规则
            'abnormal_symbol': {
                'exact_mapping': {

                },
                'regex_rules': []
            },
            
            # 参考值字段的修复规则
            'reference_value': {
                'exact_mapping': {
                    # 精确匹配适用于整个字符串的替换
                },
                'regex_rules': [
                    # 中文标点符号转英文
                    (r'（', '(', '中文左括号转英文'),
                    (r'）', ')', '中文右括号转英文'),
                    (r'％', '%', '中文百分号转英文百分号'),
                    (r'，', ',', '中文逗号转英文逗号'),
                    (r'：', ':', '中文冒号转英文冒号'),
                    (r'；', ';', '中文分号转英文分号'),
                    
                    # 特殊符号修复
                    (r'士', '±', '士符号修复为±'),
                    
                    # 删除末尾的左括号
                    (r'\($', '', '删除末尾的左括号'),
                    
                    # 数值范围常见错误
                    (r'(\d+)\s*一\s*(\d+)', r'\1-\2', '中文"一"转连字符'),
                    (r'(\d+)\s*—\s*(\d+)', r'\1-\2', '长连字符转短连字符'),
                    (r'(\d+)\s*～\s*(\d+)', r'\1-\2', '波浪号转连字符'),
                    (r'(\d+)\s*~\s*(\d+)', r'\1-\2', '全角波浪号转连字符'),
                    
                    # 小于号修复
                    (r'＜', '<', '全角小于号转半角'),
                    (r'〈', '<', '书名号转小于号'),
                    (r'‹', '<', '单书名号转小于号'),
                    
                    # 大于号修复  
                    (r'＞', '>', '全角大于号转半角'),
                    (r'〉', '>', '书名号转大于号'),
                    (r'›', '>', '单书名号转大于号'),

                    (r'L', '1.', 'L 实际是 1.'),
                ]
            }
        }
    
    def add_field_rule(self, field_name: str, mapping_type: str, key: str, value: str, description: str = ""):
        """添加字段修复规则"""
        if field_name not in self.field_rules:
            self.field_rules[field_name] = {'exact_mapping': {}, 'regex_rules': []}
        
        if mapping_type == 'exact':
            self.field_rules[field_name]['exact_mapping'][key] = value
        elif mapping_type == 'regex':
            self.field_rules[field_name]['regex_rules'].append((key, value, description))
    
    def remove_field_rule(self, field_name: str, mapping_type: str, key: str):
        """删除字段修复规则"""
        if field_name not in self.field_rules:
            return
        
        if mapping_type == 'exact' and key in self.field_rules[field_name]['exact_mapping']:
            del self.field_rules[field_name]['exact_mapping'][key]
        elif mapping_type == 'regex':
            self.field_rules[field_name]['regex_rules'] = [
                rule for rule in self.field_rules[field_name]['regex_rules'] 
                if rule[0] != key
            ]
    
    def get_field_rules(self, field_name: str) -> Dict[str, Any]:
        """获取指定字段的修复规则"""
        return self.field_rules.get(field_name, {'exact_mapping': {}, 'regex_rules': []})
    
    def load_from_json(self, json_file: str):
        """从JSON文件加载配置"""
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                self.field_rules.update(config_data.get('field_rules', {}))
            logger.info(f"成功从 {json_file} 加载OCR修复配置")
        except Exception as e:
            logger.warning(f"加载OCR修复配置失败: {e}")
    
    def save_to_json(self, json_file: str):
        """将配置保存到JSON文件"""
        try:
            config_data = {
                'field_rules': self.field_rules,
                'version': '1.0',
                'description': 'OCR错误修复配置文件'
            }
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
            logger.info(f"成功保存OCR修复配置到 {json_file}")
        except Exception as e:
            logger.error(f"保存OCR修复配置失败: {e}")


class OCRCorrector:
    """OCR错误修复器"""
    
    def __init__(self, config: Optional[OCRCorrectionConfig] = None):
        """初始化修复器"""
        self.config = config or OCRCorrectionConfig()
        self.correction_stats = {
            'total_corrections': 0,
            'field_corrections': {},
            'rule_usage': {}
        }
    
    def correct_field(self, field_name: str, field_value: str) -> str:
        """修复指定字段的值"""
        if not field_value or not isinstance(field_value, str):
            return field_value
        
        original_value = field_value
        corrected_value = field_value.strip()
        corrections_made = []  # 记录本次修复的详细信息
        
        # 获取字段的修复规则
        field_rules = self.config.get_field_rules(field_name)
        
        # 应用精确匹配规则
        exact_mapping = field_rules.get('exact_mapping', {})
        for original, replacement in exact_mapping.items():
            if corrected_value.lower() == original.lower():
                corrections_made.append({
                    'rule_type': '精确匹配',
                    'pattern': original,
                    'before': corrected_value,
                    'after': replacement,
                    'description': f'整体替换: {original} → {replacement}'
                })
                corrected_value = replacement
                self._record_correction(field_name, 'exact_mapping', original, replacement)
                break
        
        # 应用正则表达式规则
        regex_rules = field_rules.get('regex_rules', [])
        for pattern, replacement, description in regex_rules:
            try:
                before_regex = corrected_value
                new_value = re.sub(pattern, replacement, corrected_value, flags=re.IGNORECASE)
                if new_value != corrected_value:
                    corrections_made.append({
                        'rule_type': '正则替换',
                        'pattern': pattern,
                        'before': before_regex,
                        'after': new_value,
                        'description': description
                    })
                    self._record_correction(field_name, 'regex', pattern, f"{corrected_value} -> {new_value}")
                    corrected_value = new_value
            except re.error as e:
                logger.warning(f"正则表达式 '{pattern}' 执行失败: {e}")
        
        # 输出详细的修复日志（表格形式）
        # if corrections_made:
        #     self._log_corrections_table(field_name, corrections_made)
        
        return corrected_value
    
    def correct_item(self, item_data: Dict[str, Any]) -> Dict[str, Any]:
        """修复整个检验项目的所有字段"""
        corrected_item = item_data.copy()
        
        # 确保location信息被保留
        location = item_data.get('location')
        if location:
            logger.debug(f"保留location信息: {location}")
        
        # 修复所有配置的字段
        for field_name in self.config.field_rules.keys():
            if field_name in corrected_item:
                original_value = corrected_item[field_name]
                corrected_value = self.correct_field(field_name, str(original_value) if original_value else "")
                corrected_item[field_name] = corrected_value
        
        # 如果test_code有值而test_name为空，将test_code值赋给test_name并清空test_code
        test_code = corrected_item.get('test_code', '')
        test_name = corrected_item.get('test_name', '')
        
        if test_code and not test_name:
            corrected_item['test_name'] = test_code
            corrected_item['test_code'] = ''
            logger.info(f"字段交换: test_code '{test_code}' -> test_name, test_code已清空")
        
        # 如果test_code和test_name相同，只保留test_name，清空test_code
        elif test_code and test_name and test_code.strip() == test_name.strip():
            corrected_item['test_code'] = ''
            logger.info(f"重复清理: test_code与test_name相同 '{test_code}' -> 只保留test_name，test_code已清空")
        
        
        return corrected_item
    
    def correct_items(self, items_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """批量修复检验项目"""
        corrected_items = []
        
        # 统计带有location的项目数量
        items_with_location = sum(1 for item in items_data if item.get('location'))
        if items_with_location > 0:
            logger.info(f"开始批量修复，发现 {items_with_location}/{len(items_data)} 个项目带有location信息")
        
        for i, item in enumerate(items_data):
            has_location_before = 'location' in item and item['location']
            corrected_item = self.correct_item(item)
            has_location_after = 'location' in corrected_item and corrected_item['location']
            
            if has_location_before and not has_location_after:
                logger.error(f"第 {i+1} 个项目location丢失！修复前: {item.get('location')}, 修复后: {corrected_item.get('location')}")
            elif has_location_before and has_location_after:
                logger.debug(f"第 {i+1} 个项目location保留成功")
            
            corrected_items.append(corrected_item)
        
        # 统计修复后的location数量
        items_with_location_after = sum(1 for item in corrected_items if item.get('location'))
        if items_with_location > 0:
            logger.info(f"批量修复完成，保留 {items_with_location_after}/{items_with_location} 个项目的location信息")
        
        return corrected_items
    
    def _log_corrections_table(self, field_name: str, corrections: List[Dict[str, Any]]):
        """以表格形式输出修复日志"""
        print(f"\n{'='*80}")
        print(f"OCR修复详情 - 字段: {field_name}")
        print(f"{'='*80}")
        
        # 表头
        header = f"{'规则类型':<12} {'模式/原值':<20} {'修复前':<25} {'修复后':<25}"
        print(header)
        print('-' * 80)
        
        # 表格内容
        for correction in corrections:
            rule_type = correction['rule_type']
            pattern = correction['pattern'][:18] + '..' if len(correction['pattern']) > 20 else correction['pattern']
            before = correction['before'][:23] + '..' if len(correction['before']) > 25 else correction['before']
            after = correction['after'][:23] + '..' if len(correction['after']) > 25 else correction['after']
            
            row = f"{rule_type:<12} {pattern:<20} {before:<25} {after:<25}"
            print(row)
            
            # 如果有描述，添加描述行
            if correction.get('description'):
                desc_line = f"{'描述:':<12} {correction['description']}"
                print(desc_line)
                print('-' * 80)
        
        print(f"{'='*80}\n")
    
    def _record_correction(self, field_name: str, rule_type: str, rule_key: str, correction_info: str):
        """记录修复统计"""
        self.correction_stats['total_corrections'] += 1
        
        if field_name not in self.correction_stats['field_corrections']:
            self.correction_stats['field_corrections'][field_name] = 0
        self.correction_stats['field_corrections'][field_name] += 1
        
        rule_id = f"{field_name}.{rule_type}.{rule_key}"
        if rule_id not in self.correction_stats['rule_usage']:
            self.correction_stats['rule_usage'][rule_id] = []
        self.correction_stats['rule_usage'][rule_id].append(correction_info)
    
    def get_correction_stats(self) -> Dict[str, Any]:
        """获取修复统计信息"""
        return self.correction_stats.copy()
    
    def reset_stats(self):
        """重置修复统计"""
        self.correction_stats = {
            'total_corrections': 0,
            'field_corrections': {},
            'rule_usage': {}
        }


# 创建默认的全局修复器实例
default_corrector = OCRCorrector()


def correct_ocr_errors(item_data: Dict[str, Any], corrector: Optional[OCRCorrector] = None) -> Dict[str, Any]:
    """便捷函数：修复单个检验项目的OCR错误"""
    if corrector is None:
        corrector = default_corrector
    
    return corrector.correct_item(item_data)


def batch_correct_ocr_errors(items_data: List[Dict[str, Any]], corrector: Optional[OCRCorrector] = None) -> List[Dict[str, Any]]:
    """便捷函数：批量修复检验项目的OCR错误"""
    if corrector is None:
        corrector = default_corrector
    
    return corrector.correct_items(items_data)


if __name__ == "__main__":
    # 测试代码
    test_items = [
        {
            'test_code': '2 CRE',
            'test_name': 'C111',
            'test_value': '5.6O',
            'test_unit': '10°9/L',
        },
        {
            'test_code': '血红蛋白',
            'test_name': '',  # test_name为空
            'test_value': '1l.5',
            'test_unit': 'g/1',
        }
    ]
    
    print("原始数据:")
    for item in test_items:
        print(f"  {item}")
    
    corrected_items = batch_correct_ocr_errors(test_items)
    
    print("\n修复后数据:")
    for item in corrected_items:
        print(f"  {item}")
    
    print(f"\n修复统计: {default_corrector.get_correction_stats()}") 