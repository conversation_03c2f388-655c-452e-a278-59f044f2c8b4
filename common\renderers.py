import io
import urllib.parse

import pandas as pd
from rest_framework.response import Response
from rest_framework.renderers import BaseRender<PERSON>
from rest_pandas.views import PandasViewBase
from rest_pandas import PandasExcelRenderer, PandasOldExcelRenderer
from openpyxl.styles import Font, Border
from openpyxl.styles import Alignment


class ExcelRenderer(BaseRenderer):
    media_type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    format = 'xlsx'

    def render(self, data, accepted_media_type=None, renderer_context=None):
        return data


class TextRenderer(BaseRenderer):
    media_type = 'text/plain'
    format = 'txt'

    def render(self, data, media_type=None, renderer_context=None):
        return str(data)
