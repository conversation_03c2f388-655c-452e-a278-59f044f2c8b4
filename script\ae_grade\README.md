

## 🏗️ 项目架构总结

### 核心流程图

```
原始数据库数据
       ↓
[数据预处理和异常项筛选]
       ↓
   有异常项? ——————————————— 无异常项
       ↓                      ↓
[CTCAE规则匹配]          [无异常项处理]
       ↓                      ↓
[双模型并发推理+AE描述丰富化]    ↓
       ↓                      ↓
[结果解析和输出] ←——————————————┘
       ↓
   最终结果输出
```

**功能优化说明**：
- **AE描述丰富化集成**：在双模型推理的后处理阶段，根据识别出的ae_name和ae_grade，从CTCAE规则数据中查找对应等级的具体描述
- **ae_desc字段优化**：从"不良事件定义（CTCAE原文，或空字符串）"改为具体的等级判断规则
- **架构优化**：避免额外节点开销，在现有节点中集成丰富化功能
- **示例**："2000>白细胞结果≥1000mm³或者 2.0>白细胞结果≥1.0 x10⁹/L"

### 📊 项目结构分析

```
ae_grade/
├── main.py                    # 系统入口，AERecognitionSystem主类
├── flow.py                   # PocketFlow流程编排
├── nodes.py                  # 核心业务节点实现(876行)
├── config.py                 # 模型和服务配置(1147行)
├── optimization_config.py    # 优化参数配置
├── utils/                    # 工具函数包
│   ├── rerank_client.py     # Rerank服务客户端
│   ├── rerank_matcher.py    # CTCAE规则匹配器
│   ├── prompt_builder.py    # 智能提示词构建器
│   ├── result_parser.py     # 结果解析器
│   └── performance_monitor.py # 性能监控器
├── test/                     # 测试文件
└── doc/                      # 项目文档
```

### 🔧 关键组件说明

#### 1. **PocketFlow核心应用模式**

**节点设计模式**：
- **三阶段执行**：`prep() → exec() → post()`
- **异步节点**：使用`AsyncNode`支持并发处理
- **条件分支**：通过`- "condition" >>` 语法实现流程分支
- **共享状态**：通过`shared`字典在节点间传递数据

**流程连接语法**：
```python
# 顺序连接
preprocess_node >> filter_node

# 条件分支
filter_node - "has_abnormal_items" >> prompt_node
filter_node - "no_abnormal_items" >> no_abnormal_node

# 异步流程
flow = AsyncFlow(start=preprocess_node)
```

#### 2. **核心业务节点**

1. **DataPreprocessNode** (同步节点)
   - 将数据库格式转换为内部处理格式
   - 英文key → 中文key映射

2. **AbnormalItemFilterNode** (同步节点)
   - 筛选异常项：`test_flag != 0 OR abnormal_symbol != ''`
   - 决定流程走向：有异常项 vs 无异常项

3. **CTCAERuleMatcherNode** (同步节点)
   - 使用rerank服务匹配相关CTCAE规则
   - 大幅减少提示词长度，提升处理效率

4. **ConcurrencyControlledBatchNode** (异步节点) **[优化]**
   - 双模型并发推理：DeepSeek-R1-Distill-Qwen-32B + qwen3-32b
   - 批处理优化：每批20项，最大2个并发批次
   - **集成AE描述丰富化**：在后处理阶段根据ae_name和ae_grade从CTCAE规则中提取具体等级描述
   - 将ae_desc字段从大模型生成改为规则查找，示例："2000>白细胞结果≥1000mm³或者 2.0>白细胞结果≥1.0 x10⁹/L"

5. **OutputNode** (同步节点)
   - 解析模型结果，生成标准化输出
   - 合并正常项和异常项结果

7. **SmartPromptBuilderNode** (同步节点)
   - 核心优化组件：使用Rerank服务精准匹配CTCAE规则
   - 将720行提示词优化为50-100行
   - 性能提升80%的关键节点

4. **DualModelInferenceNode** (异步节点)
   - 并发调用两个LLM模型
   - 使用`asyncio.gather()`实现真正的并发
   - 超时和错误处理机制

5. **ResultParserNode** (同步节点)
   - 分别解析两个模型的输出
   - 结构化数据验证

6. **ResultComparisonNode** (同步节点)
   - 对比两个模型结果而非聚合
   - 冲突检测和优先级处理

#### 3. **技术架构特色**

**异步并发设计**：
```python
class DualModelInferenceNode(AsyncNode):
    async def exec_async(self, prompt: str):
        task1 = asyncio.to_thread(self._call_model_safely, 
                                 primary_model_func, prompt, TIMEOUT_SECONDS)
        task2 = asyncio.to_thread(self._call_model_safely, 
                                 secondary_model_func, prompt, TIMEOUT_SECONDS)
        results = await asyncio.gather(task1, task2, return_exceptions=True)
```

**智能规则匹配**：
- 集成专用Rerank服务（`http://192.168.230.3:8081`）
- 从大量CTCAE规则中精准匹配相关条目
- 大幅减少LLM处理的上下文长度

**多层降级机制**：
1. 智能提示词失败 → 降级到基础提示词
2. 双模型推理失败 → 降级到单模型
3. 主流程失败 → 降级到简化流程
4. PocketFlow失败 → 降级到原始系统逻辑

### 🚀 系统主要特色和优势

#### 1. **性能突破**
- **处理时间**：60秒 → 8秒（87%提升）
- **提示词优化**：720行 → 50-100行（80%减少）
- **并发处理**：真正的异步并发而非串行

#### 2. **PocketFlow框架应用优势**
```python
# 简洁的流程定义
preprocess_node >> filter_node
filter_node - "has_abnormal_items" >> prompt_node
prompt_node >> inference_node >> parser_node >> comparison_node
```

- **声明式编程**：流程逻辑清晰易读
- **模块化设计**：每个节点职责单一，易于测试和维护  
- **异步原生支持**：`AsyncFlow`和`AsyncNode`无缝集成
- **错误处理内置**：节点级别的重试和降级机制

#### 3. **医疗领域适配**
- **CTCAE标准集成**：完整的不良事件分级标准支持
- **双模型验证**：通过对比提高诊断可靠性
- **智能筛选**：只处理真正异常的检验项目
- **结果可追溯**：完整的处理链路和元数据记录

#### 4. **工程化程度**
- **配置化管理**：支持环境变量和配置文件
- **性能监控**：实时监控处理性能和瓶颈
- **完善测试**：单元测试和集成测试覆盖
- **文档齐全**：中英文文档和架构说明

### 🎯 PocketFlow在项目中的应用模式

这个项目展示了PocketFlow框架在复杂业务场景中的**最佳实践模式**：

1. **业务逻辑完全在节点中**：系统主类只负责协调，不含业务逻辑
2. **异步+同步混合**：根据节点特性选择合适的执行模式
3. **条件分支流程**：根据业务状态动态选择处理路径
4. **共享状态管理**：通过结构化的`shared`字典管理复杂数据流
5. **降级和容错**：多层降级确保系统稳定性

这是一个将PocketFlow框架应用于实际生产环境的优秀范例，特别是在医疗AI领域的成功实践。