import pandas as pd
import re
import ast
import io
import fitz
from sqlalchemy import text
import hashlib
import argparse
from datetime import datetime, timedelta
import base64
import os
import django
from django.db import connections
from django.conf import settings

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smo.settings.test')
os.environ["DJANGO_ALLOW_ASYNC_UNSAFE"] = "true"

django.setup()
# mails = settings.MAILS
# DATABASES = settings.DATABASES
from sqlalchemy import create_engine
from sqlalchemy.pool import NullPool
from common.tools import get_db_engin_url
from django.db import transaction
from apps.medical.models import MedicalFileMasked
from apps.subject_medical.models import SubjectMedicalFileMasked
# 创建数据库引擎对象
from apps.ae_tracker.models import AeTrackerTask, TestResult
from common.tools import sql_to_df
from common.ocr_tools import ocr_desensitive
from common.minio_client import get_minio_client
from apps.system.models import ModelInvocationLog
from common.tools import activate_conn, get_use_meds_results_json_v7, get_ae_name_change_json

db_erp = create_engine(get_db_engin_url('default'), poolclass=NullPool)


def close_db_connection():
    # 关闭数据库连接
    db_erp.dispose()
    print("Database connection closed")


def find_all_indices(s, target_char):
    """
    找到所有字符串中指定字符的下标
    s：输入字符串
    target_char：要查找的字符
    return：所有下标的列表
    """
    if len(target_char) != 1:
        raise ValueError("target_char 必须是单个字符")
    return [i for i, char in enumerate(s) if char == target_char]


def mask_image_by_location(draw, image_path, ocrResult, keywords):
    """
    根据给定的位置信息对图像中的特定字符进行打码。

    :param image_path: 原始图像路径
    :param ocr_result: OCR识别结果
    :param keywords: 需要打码的关键字列表
    """

    # 提取位置信息
    for block in ast.literal_eval(ocrResult)["result"]["words_block_list"]:
        words = block["words"]
        locations = block["location"]
        if not locations:
            continue
        num_chars = len(words)

        # 计算每个字符的起始和结束位置
        x_coords = [point[0] for point in locations]
        y_coords = [point[1] for point in locations]

        left = min(x_coords)
        right = max(x_coords)
        top = min(y_coords)
        bottom = max(y_coords)

        # 使用线性插值计算每个字符的边界
        char_width = (right - left) / num_chars

        if keywords in words:
            previous_index = -1
            for keyword in keywords:
                indices = find_all_indices(words, keyword)
                if len(indices) == 1:
                    start_index = words.find(keyword)
                    if start_index != -1:
                        end_index = start_index + len(keyword)

                        for i in range(start_index, end_index):
                            if previous_index == -1 or i > previous_index:
                                char_left = left + i * char_width
                                char_right = left + (i + 1) * char_width

                                top_y, bottom_y = linear_interpolate(char_left, char_right, top, bottom, x_coords,
                                                                     y_coords)

                                draw.rectangle([char_left, top_y, char_right, bottom_y], fill='black')

                                previous_index = start_index
                else:
                    start_index = previous_index + 1
                    if start_index != -1:
                        end_index = start_index + len(keyword)

                        for i in range(start_index, end_index):
                            if previous_index == -1 or i > previous_index:
                                char_left = left + i * char_width
                                char_right = left + (i + 1) * char_width

                                top_y, bottom_y = linear_interpolate(char_left, char_right, top, bottom, x_coords,
                                                                     y_coords)

                                draw.rectangle([char_left, top_y, char_right, bottom_y], fill='black')

                                previous_index = start_index


def linear_interpolate(x_start, x_end, y_top, y_bottom, x_coords, y_coords):
    """
    使用线性插值计算顶部和底部的y坐标。

    :param x_start: 字符的左边界
    :param x_end: 字符的右边界
    :param y_top: 多边形的顶部y坐标
    :param y_bottom: 多边形的底部y坐标
    :param x_coords: 多边形的x坐标列表
    :param y_coords: 多边形的y坐标列表
    :return: 插值后的y坐标 (top_y, bottom_y)
    """
    n = len(x_coords)
    if n < 2:
        return y_top, y_bottom

    def interpolate(x, x_coords, y_coords):
        for i in range(n - 1):
            if x_coords[i] <= x <= x_coords[i + 1]:
                t = (x - x_coords[i]) / (x_coords[i + 1] - x_coords[i])
                return y_coords[i] + t * (y_coords[(i + 1) % n] - y_coords[i])
        # 如果x不在任何区间内，使用最近的点
        return y_coords[-1]

    # 计算顶部的y坐标
    top_y_start = interpolate(x_start, x_coords, y_coords)
    top_y_end = interpolate(x_end, x_coords, y_coords)
    top_y = (top_y_start + top_y_end) / 2

    # 计算底部的y坐标
    bottom_y_start = interpolate(x_start, x_coords, [y_bottom] * n)
    bottom_y_end = interpolate(x_end, x_coords, [y_bottom] * n)
    bottom_y = (bottom_y_start + bottom_y_end) / 2

    return top_y, bottom_y


def match_chinese_string(text):
    # 匹配姓名后面的名字
    # name_pattern = r"姓名：(\w+)"
    # 匹配年龄后面的数字
    age_pattern = r"(\d+) 岁"

    # 查找所有匹配项
    # name_match = re.search(name_pattern, text)
    age_match = re.search(age_pattern, text)

    # if name_match and age_match:
    if age_match:
        # name = name_match.group(1)
        age = age_match.group(1)
        # return {"name": name, "age": age}
        return age
    else:
        return None


def process_row(task_id):
    print('开始处理下面的row了！！！')
    task = AeTrackerTask.objects.select_related('project').filter(id=task_id,
                                                                  category=AeTrackerTask.AE_MEDICATION_MEASURES,
                                                                  status=AeTrackerTask.TODO, delete_flag=0).first()
    subject_item = task.subject_item
    subject_id = task.subject_id
    subject_item_id = task.subject_item_id
    try:

        if not task:
            print(f"没有发现任务")
            return
        params = {
            'which_need_update_id': task_id,
            'mask_status': 'IN_PROGRESS'
        }
        sql = text(
            f"""update ae_tracker_task set status=:mask_status where id=:which_need_update_id""")
        with db_erp.begin() as conn:
            conn.execute(sql, params)
        conn.close()
        print(task_id)

        print('用药RAG中，操作项id:{}'.format(subject_item_id))
        project_no = task.project.project_no
        # 从数据库获取所有符合条件的记录（不筛选ae_grade）
        test_results_all = TestResult.objects.filter(
            subject_id=subject_id,
            subject_item_id=subject_item_id,
            delete_flag=0
        )

        # 在内存中筛选ae_grade符合条件的记录，并处理空值情况
        valid_grades = ['1', '2', '3', '4', '5']
        filtered_results_wait = []
        filtered_results = []

        for result in list(test_results_all):
            grade = result.ae_grade
            if grade is None:
                filtered_results_wait.append(result)  # 处理空值情况
            elif str(grade) in valid_grades:
                filtered_results.append(result)  # 符合条件的记录
            else:
                filtered_results_wait.append(result)  # 不符合条件的记录

        extracted_data_wait = [
            {
                'id': item.id,
                'ae_name': item.ae_name,
                'ae_grade': item.ae_grade,
                'ae_meds': '/',
                'ae_medication_measures_list': None
            }
            for item in filtered_results_wait
        ]

        extracted_data = [
            {
                'id': item.id,
                'ae_name': item.ae_name,
                'ae_grade': item.ae_grade
            }
            for item in filtered_results
        ]
        test_results_list = extracted_data  # get_ae_name_change_json(extracted_data)
        print(f"filtered_results: {filtered_results}")
        # ae_names_list = list({result['ae_name'] for result in test_results_list})
        if test_results_list or len(filtered_results_wait) > 0:
            # 使用参数化查询避免SQL注入
            # query = """
            #     SELECT adverse_reactions, ae_grade, handling_measures
            #     FROM config_suggested_medication_info
            #     WHERE project_no = %s
            #     AND delete_flag = 0
            #     AND adverse_reactions IN ({})
            # """.format(', '.join(['%s'] * len(ae_names_list)))
            # # 构建查询参数
            # params = [project_no] + ae_names_list
            # # 使用正确的数据库连接和游标
            # with connections['default'].cursor() as cursor:  # 如果使用默认数据库
            #     cursor.execute(query, params)
            #     columns = [col[0] for col in cursor.description]
            #     suggested_medication_list = [
            #         dict(zip(columns, row))
            #         for row in cursor.fetchall()
            #     ]
            # meds_results_json = get_use_meds_results_json(test_results_list, suggested_medication_list)
            # print(meds_results_json)
            meds_results_json = get_use_meds_results_json_v7(test_results_list, project_no)
            meds_results_json = meds_results_json + extracted_data_wait
            update_instances = []
            for item in meds_results_json:
                try:
                    # 根据 id 获取对应的模型实例
                    instance = TestResult.objects.get(id=item["id"])
                    # 更新 ae_meds 字段
                    instance.ae_meds = item["ae_meds"]
                    instance.ae_medication_measures_list = item["ae_medication_measures_list"]
                    # 添加到待更新列表
                    update_instances.append(instance)
                except TestResult.DoesNotExist:
                    # 处理不存在的 id（可选）
                    print(f"ID {item['id']} 对应的记录不存在，跳过更新")

            # 执行批量更新
            with transaction.atomic():
                if update_instances:
                    TestResult.objects.bulk_update(update_instances, ["ae_meds", "ae_medication_measures_list"])
                    print(f"成功更新 {len(update_instances)} 条记录")
                else:
                    print("没有可更新的记录")

        params = {
            'subject_id': subject_id,
            'subject_item_id': subject_item_id,
            'ae_ai_current_step': 4
        }
        sql = text(
            f"""update subject_item_info set ae_ai_current_step=:ae_ai_current_step where subject_id=:subject_id and subject_item_id=:subject_item_id and ae_ai_current_step <= 4""")
        # print(sql)q
        with db_erp.begin() as conn:
            conn.execute(sql, params)
        conn.close()

        params = {
            'which_need_update_id': task_id,
            'mask_status': 'COMPLETED',
            'end_time': datetime.now()
        }
        sql = text(
            f"""update ae_tracker_task set status=:mask_status,end_time=:end_time where id=:which_need_update_id""")
        # print(sql)q
        with db_erp.begin() as conn:
            conn.execute(sql, params)
        conn.close()
        print(task_id)
        return True
    except:
        # print('用药处理失败，操作项id:{}'.format(subject_item_id))
        params = {
            'which_need_update_id': task_id,
            'mask_status': 'ERROR'
        }
        sql = text(
            f"""update ae_tracker_task set status=:mask_status where id=:which_need_update_id""")
        with db_erp.begin() as conn:
            conn.execute(sql, params)
        conn.close()

        params = {
            'subject_id': subject_id,
            'subject_item_id': subject_item_id,
            'ae_ai_current_step': 3
        }
        sql = text(
            f"""update subject_item_info set ae_ai_current_step=:ae_ai_current_step where subject_id=:subject_id and subject_item_id=:subject_item_id and ae_ai_current_step <= 4""")
        # print(sql)q
        with db_erp.begin() as conn:
            conn.execute(sql, params)
        conn.close()

        print('用药处理失败，task_id:{}'.format(task_id))


def main():
    parser = argparse.ArgumentParser(description="AE用药任务")
    parser.add_argument('--task_id', type=int, required=True, help="task_id")
    args = parser.parse_args()
    process_row(args.task_id)


if __name__ == "__main__":
    main()
    close_db_connection()
