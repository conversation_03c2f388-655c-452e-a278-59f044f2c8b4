# 极简Rerank客户端

## 概述

这是一个极简的文本重排序（rerank）客户端，提供通用的rerank API调用功能。从原有的CTCAE专用客户端中提取核心功能，实现了代码复用和模块化。

## 特点

- **极简设计**: 专注于核心rerank功能
- **统一接口**: 提供简洁的函数调用方式
- **错误处理**: 完善的异常处理和日志记录
- **批量处理**: 支持批量rerank请求
- **配置灵活**: 支持自定义服务配置

## 核心功能

### 1. 基础rerank调用

```python
from common.clients.rerank_client import call_rerank

# 基础调用 - 直接获取排序后的结果
results = call_rerank(
    query="血小板计数异常",
    texts=[
        "血小板计数降低：血小板计数低于正常范围",
        "白细胞计数异常：白细胞数量异常",
        "血红蛋白降低：血红蛋白水平低于正常值"
    ]
)

# 返回格式：按相关性分数从高到低排序的字典列表
# [
#   {"text": "血小板计数降低...", "score": 0.9872, "index": 0},
#   {"text": "血红蛋白降低...", "score": 0.0081, "index": 2},
#   {"text": "白细胞计数异常...", "score": 0.0099, "index": 1}
# ]

```

### 2. 自定义配置

```python
# 使用自定义服务配置
scores = call_rerank(
    query="查询文本",
    texts=["文本1", "文本2"],
    base_url="http://your-server:8081",
    token="your-token",
    timeout=30
)
```

### 3. 批量处理

```python
from common.clients.rerank_client import batch_rerank

queries = ["血小板计数", "白细胞计数"]
texts_lists = [
    ["血小板计数降低", "血小板聚集异常"],
    ["白细胞数降低", "白细胞数增多"]
]

results = batch_rerank(queries, texts_lists)
# 返回: List[List[Dict]] - 每个查询对应的排序结果列表
```


## API参考

### call_rerank()

主要的rerank调用函数。

**参数:**
- `query` (str): 查询文本
- `texts` (List[str]): 待排序的文本列表
- `base_url` (str, 可选): 服务基础URL
- `token` (str, 可选): 认证token
- `timeout` (int, 可选): 请求超时时间（秒）
- `raw_scores` (bool, 可选): 是否返回原始分数，默认True
- `return_text` (bool, 可选): 是否返回文本内容，默认False
- `truncate` (bool, 可选): 是否截断长文本，默认True
- `truncation_direction` (str, 可选): 截断方向，默认"Right"

**返回:**
- `List[Dict]`: 按相关性分数从高到低排序的结果列表
- 每个字典包含：`{"text": "文本内容", "score": 0.9, "index": 原始位置}`


### batch_rerank()

批量处理多个查询。

**参数:**
- `queries` (List[str]): 查询文本列表
- `texts_lists` (List[List[str]]): 对应的文本列表的列表
- `**kwargs`: 其他参数传递给call_rerank

**返回:**
- `List[List[Dict]]`: 每个查询对应的排序结果列表


## 使用示例

### 医学文本排序

```python
query = "血液系统异常"
texts = [
    "白细胞数降低 白细胞",
    "中性粒细胞计数降低 中性粒细胞计数",
    "血小板计数降低 血小板计数",
    "血红蛋白降低 血红蛋白"
]

results = call_rerank(query, texts)

# 直接使用排序后的结果
for i, result in enumerate(results):
    print(f"{i+1}. {result['score']:.4f} | {result['text']}")

# 获取最相关的前3个
top_3 = results[:3]
for result in top_3:
    print(f"{result['score']:.4f} | {result['text']}")
```
