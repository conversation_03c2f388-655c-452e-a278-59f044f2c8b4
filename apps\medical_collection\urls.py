from django.urls import path, include
from rest_framework.routers import DefaultRouter, SimpleRouter

from . import views

router = DefaultRouter(trailing_slash=False)
# 创建 crf，病历 任务
router.register(r'/medical-collection-tasks', views.MedicalCollectionTasksView)

# 获取最新的 病历 （文件）最新的 crf（文件）
router.register(r'/medical-collection-files/latest', views.MedicalCollectionTasksLatestMedicalRecordView)

# 获取最新的 crf（json）
router.register(r'/medical-collection-files/latest-crf-json', views.MedicalCollectionTasksLatestMedicalRecordJsonView)

# 修改 crf（json）
router.register(r'/medical-collection-files/crf-json', views.MedicalCollectionTasksLatestMedicalRecordChangeJsonView)

# 历史生成文件
router.register(r'/medical-collection-files', views.MedicalCollectionFileHistoryView)

# 删除 crf（json）
router.register(r'/medical-collection-files/delete', views.MedicalCollectionFileDeleteView)
# 支持批量删除
router.register(r'/medical-collection-files', views.MedicalCollectionFileDeletesView)
router.register(r'/medical-collection-files', views.MedicalCollectionManualUploadView)

urlpatterns = []

urlpatterns = router.urls + urlpatterns
