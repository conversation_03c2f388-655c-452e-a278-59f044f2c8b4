from datetime import timedelta
from django.conf import settings
from rest_framework import serializers

from common.minio_client import get_minio_client
from apps.ae_tracker.models import TestResult

from . import models


class ProjectSiteSubjectSerializer(serializers.ModelSerializer):
    project_id = serializers.CharField(source='project.project_id', read_only=True, label="项目ID")
    project_no = serializers.CharField(source='project.project_no', read_only=True, label="项目编号")
    project_site_id = serializers.CharField(source='project_site.project_site_id', read_only=True, label="项目中心ID")
    hosp_name = serializers.CharField(source='project_site.hosp_name', read_only=True, label="项目中心名称")
    hosp_department_no = serializers.CharField(source='project_site.hosp_department_no', read_only=True, label="项目中心编号")
    accredit_crc_text = serializers.<PERSON><PERSON><PERSON><PERSON>(source='project_site.accredit_crc_text', read_only=True, label="授权CRC描述")
    backup_crc_text = serializers.Char<PERSON>ield(source='project_site.backup_crc_text', read_only=True, label="Backup CRC描述")
    # operate_name = serializers.CharField(read_only=True, allow_null=True, label="操作人")
    # operate_time = serializers.DateTimeField(read_only=True, allow_null=True, label="操作时间")

    class Meta:
        model = models.Subject
        exclude = ['project', 'project_site']


class VisitDateSerializer(serializers.Serializer):
    st = serializers.DateTimeField(label="访视实际开始日期（CRC填写的）", required=False, allow_null=True, default=None)
    # edc = serializers.DateTimeField(label="")
    cmpl = serializers.DateTimeField(label="访视实际完成日期（CRC填写的）", required=False, allow_null=True, default=None)
    winB = serializers.DateTimeField(label="访视计划日期", required=False, allow_null=True, default=None)
    winL = serializers.DateTimeField(label="左窗口", required=False, allow_null=True, default=None)
    winR = serializers.DateTimeField(label="右窗口", required=False, allow_null=True, default=None)
    # stAct = serializers.DateField(label="录入系统时的访视实际开始日期（CRC开始第1个操作项时的系统时间）")
    # cmplAct = serializers.DateField(label="录入系统时的访视实际完成日期（CRC点击完成按钮时的系统时间）")
    # stUserId = serializers.CharField(label="")
    # cmplUserId = serializers.CharField(label="")
    # isSpecifyDate = serializers.IntegerField(label="")
    # updateCmplReason = serializers.CharField(allow_blank=True, label="")
    # outOfWindowReason = serializers.CharField(label="")
    # outOfWindowStatus = serializers.CharField(label="")
    # cmplOutOfWindowStatus = serializers.CharField(label="")
    # startOutOfWindowStatus = serializers.CharField(label="")


class SubjectEpochSerializer(serializers.ModelSerializer):
    subject_id = serializers.CharField(source='subject.subject_id', read_only=True, label="受试者ID")

    class Meta:
        model = models.SubjectEpoch
        exclude = ['project', 'project_site', 'subject']


class SubjectVisitSerializer(serializers.ModelSerializer):
    subject_id = serializers.CharField(source='subject.subject_id', read_only=True, label="受试者ID")
    visit_date = VisitDateSerializer(many=False)

    class Meta:
        model = models.SubjectVisit
        exclude = ['project', 'project_site', 'subject']


class SubjectItemSerializer(serializers.ModelSerializer):
    subject_id = serializers.CharField(source='subject.subject_id', read_only=True, label="受试者ID")
    # visit_date = serializers.CharField(source='subject_visit.visit_date', read_only=True, label="访视日期")
    visit_date = VisitDateSerializer(source='subject_visit.visit_date', read_only=True, label="访视日期")
    subject_visit_id = serializers.CharField(label="访视id")
    visit_label = serializers.CharField(source='subject_visit.label', read_only=True, label="访视名称")

    class Meta:
        model = models.SubjectItem
        exclude = ['project', 'project_site', 'subject', 'subject_visit']


class AETrackerTestResultSerializer(serializers.ModelSerializer):
    date = serializers.CharField(read_only=True, label="访视日期")

    class Meta:
        model = TestResult
        fields = ['id', 'test_value', 'test_flag', 'ae_name', 'ae_grade', 'date', 'medical_history_flag', 'ae_edit_flag', 'ae_ai_result_list', 'ae_ai_result_flag']


class AETrackerSerializer(serializers.Serializer):
    subject_id = serializers.CharField(label='受试者ID')
    subject_item_id = serializers.CharField(label='受试者操作项ID')
    subject_item_label = serializers.CharField(label='操作项名称')
    test_name = serializers.CharField(label='检查名称')
    test_unit = serializers.CharField(label='检查单位')
    reference_value = serializers.CharField(label='参考值')
    ae_desc = serializers.CharField(label='检查结果/AE事件描述')
    dates = AETrackerTestResultSerializer(many=True)


class AETrackerQueryParamsSerializer(serializers.Serializer):
    subject_id = serializers.CharField(label='受试者ID', required=True)
    abnormal_flag = serializers.CharField(label='是否为异常值；0：正常， 1：异常', required=False)
    subject_item_label = serializers.CharField(label='操作项名称（模糊匹配）', required=False)
    test_name = serializers.CharField(label='检查名称（模糊匹配）', required=False)


class TestResultsQueryParamsSerializer(serializers.Serializer):
    subject_id = serializers.CharField(label='受试者ID', required=True)
    subject_item_id = serializers.CharField(label='操作项ID', required=False)


class UpdateItemTypeRequestSerializer(serializers.Serializer):
    subject_id = serializers.CharField(label="受试者ID", required=True)
    subject_item_id = serializers.CharField(label="受试者操作项ID", required=True)
    item_type = serializers.CharField(label="擦作项类型", help_text="操作项类型:1检验项目,2检查项目,3病史,0NA")