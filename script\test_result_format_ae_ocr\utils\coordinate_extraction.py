# -*- coding: utf-8 -*-
"""
坐标提取工具：从结构化检验报告中提取行坐标信息

该模块提供了将结构化的检验项目数据映射回原始OCR坐标的功能，
支持提取整行的坐标信息，用于后续的定位和标注需求。

主要功能：
1. 文本匹配：将结构化项目文本与OCR文本块进行匹配
2. 行坐标提取：从匹配的文本块中提取行级别的坐标信息
3. 边界框计算：计算每行的完整边界框（bounding box）

使用方法：
    from script.test_result_format_ae_ocr.utils.coordinate_extraction import extract_row_coordinates
    
    # 提取行坐标
    row_coordinates = extract_row_coordinates(test_items, words_block_list)
"""

import re
import logging
from typing import List, Dict, Tuple, Any
from dataclasses import dataclass
from difflib import SequenceMatcher

logger = logging.getLogger(__name__)


def find_nearest_valid_location(line_words_block_list: List[Dict], target_index: int, search_direction: str = 'right') -> List:
    """
    找到最近的有坐标的words块
    
    Args:
        line_words_block_list: 行内words块列表
        target_index: 目标索引
        search_direction: 搜索方向 ('right' 或 'left')
    
    Returns:
        有效的location坐标
    """
    if search_direction == 'right':
        # 向右搜索
        for i in range(target_index, len(line_words_block_list)):
            if 'location' in line_words_block_list[i]:
                return line_words_block_list[i]['location']
        # 如果向右没找到，向左搜索
        for i in range(target_index - 1, -1, -1):
            if 'location' in line_words_block_list[i]:
                return line_words_block_list[i]['location']
    else:
        # 向左搜索
        for i in range(target_index, -1, -1):
            if 'location' in line_words_block_list[i]:
                return line_words_block_list[i]['location']
        # 如果向左没找到，向右搜索
        for i in range(target_index + 1, len(line_words_block_list)):
            if 'location' in line_words_block_list[i]:
                return line_words_block_list[i]['location']
    
    # 如果都没找到，返回行首的坐标（假设行首一定有坐标）
    return line_words_block_list[0]['location']


def get_valid_location(line_words_block_list: List[Dict], index: int, position_type: str = 'middle') -> List:
    """
    获取有效的location坐标，处理没有location的情况
    
    Args:
        line_words_block_list: 行内words块列表
        index: 目标索引
        position_type: 位置类型 ('start', 'end', 'middle')
    
    Returns:
        有效的location坐标
    """
    # 检查目标索引是否有location
    if index < len(line_words_block_list) and 'location' in line_words_block_list[index]:
        return line_words_block_list[index]['location']
    
    if position_type == 'start':
        # 行首：向右找
        return find_nearest_valid_location(line_words_block_list, index, 'right')
    elif position_type == 'end':
        # 行尾：向左找
        return find_nearest_valid_location(line_words_block_list, index, 'left')
    else:
        # 中间：使用行首坐标
        return get_valid_location(line_words_block_list, 0, 'start')


@dataclass
class RowCoordinate:
    """行坐标数据模型"""
    # 行左上角坐标 （x，y）
    point_1: Tuple[float, float]
    # 行右上角坐标 （x，y）
    point_2: Tuple[float, float]
    # 行左下角坐标 （x，y）
    point_3: Tuple[float, float]
    # 行右下角坐标 （x，y）
    point_4: Tuple[float, float]
    # 匹配的检验项索引 (检验项索引，line_words_block_list中的words索引)
    test_item_indices: List[Tuple[int, int]]
    # 原始坐标文本
    line_words_block_list: List[Dict[str, Any]]

    def __init__(self, line_words_block_list:List[Dict[str, Any]]):
        self.line_words_block_list = line_words_block_list
        """
        point_1 ：行首元素左上角坐标
        point_2 ：行尾元素右上角坐标
        point_3 ：行尾元素左下角坐标
        point_4 ：行首元素右下角坐标
        test_item_indices ：匹配的检验项索引
        line_words_block_list ：原始坐标文本
        """
        start_location = get_valid_location(line_words_block_list, 0, 'start')
        end_location = get_valid_location(line_words_block_list, len(line_words_block_list) - 1, 'end')
        
        point_1 = start_location[0]
        point_2 = end_location[1]
        point_3 = end_location[2]
        point_4 = start_location[3]
        self.point_1 = point_1
        self.point_2 = point_2
        self.point_3 = point_3
        self.point_4 = point_4
        self.test_item_indices = []



def calculate_text_similarity(text1: str, text2: str) -> float:
    """
    计算两个文本的相似度
    
    Args:
        text1: 第一个文本
        text2: 第二个文本
        
    Returns:
        相似度分数 (0.0-1.0)
    """
    if not text1 or not text2:
        return 0.0
    
    # 清理文本：移除空格和特殊字符
    clean_text1 = re.sub(r'\s+', '', text1.lower())
    clean_text2 = re.sub(r'\s+', '', text2.lower())
    
    # 使用序列匹配器计算相似度
    return SequenceMatcher(None, clean_text1, clean_text2).ratio()

def extract_row_coordinates(test_items: List[Dict[str, Any]], words_block_list: List[Dict[str, Any]], similarity_threshold: float = 0.5) -> List[Dict[str, Any]]:
    print("*****************************************************")
    print(test_items)
    print("*****************************************************")
    print(words_block_list)
    print("*****************************************************")
  
    """
    提取行坐标
    
    Args:
        test_items: 检验项列表
        words_block_list: OCR文本块列表
        similarity_threshold: 相似度阈值
        
    Returns:
        带有坐标信息的检验项列表
        
    Raises:
        ValueError: 当输入参数无效时
        IndexError: 当索引访问越界时
        KeyError: 当缺少必要字段时
    """
    try:
        # 创建RowCoordinate数组
        row_coordinates = []
        line_words_block_list = []
        
        # 重组words_block_list，将line_break为true的元素合并为一行
        for word_block in words_block_list:
            if word_block.get('words', '') == '':
                continue
            line_words_block_list.append(word_block)
            # 如果有元素line_break并为true
            if word_block.get('line_break') == True:
                # 创建RowCoordinate对象
                row_coordinate = RowCoordinate(line_words_block_list)
                row_coordinates.append(row_coordinate)
                line_words_block_list = []

        # 迭代test_items，将test_items的test_name与row_coordinates的line_words_block_list的words进行匹配
        for index, item in enumerate(test_items):
            similarity_list = [] # （相似度，row_coordinate，line_words_block_list中的words索引）
            for row_coordinate in row_coordinates:
                # 遍历line_words_block_list中的每个words，寻找与test_name最相似的
                for word_block_index, word_block in enumerate(row_coordinate.line_words_block_list):
                    similarity = calculate_text_similarity(item['test_name'], word_block.get('words', ''))
                    if similarity > similarity_threshold:
                        similarity_list.append((similarity, row_coordinate, word_block_index))
            
            # 检查是否找到匹配项
            if not similarity_list:
                logger.warning(f"未找到与 '{item.get('test_name', '')}' 相似度超过 {similarity_threshold} 的文本块")
                continue
                
            # 按相似度排序，相似度相同时按索引排序
            similarity_list.sort(key=lambda x: (x[0], -x[2]), reverse=True)
            
            # 尝试找到可用的匹配
            matched = False
            for similarity, row_coordinate, word_block_index in similarity_list:
                if not row_coordinate.test_item_indices:
                    # 该行未被匹配，直接使用
                    max_similarity_index = row_coordinate
                    max_similarity_word_block_index = word_block_index
                    matched = True
                    break
                else:
                    # 该行已被匹配，检查是否为双栏情况
                    existing_index, _ = row_coordinate.test_item_indices[0]
                    existing_item = test_items[existing_index]
                    
                    # 对比两个item的test_name相似度
                    name_similarity = calculate_text_similarity(
                        item['test_name'], 
                        existing_item['test_name']
                    )
                    
                    if name_similarity < 0.8:
                        # 双栏情况，允许匹配
                        max_similarity_index = row_coordinate
                        max_similarity_word_block_index = word_block_index
                        matched = True
                        break
                    else:
                        # 重复项情况，跳过这个匹配，尝试下一个
                        continue
            
            if not matched:
                logger.warning(f"item '{item.get('test_name', '')}' 所有匹配行都被相同test_name占用")
                continue
            
            # 为当前item分配坐标
            if max_similarity_index.test_item_indices:
                # 该行已被匹配，处理双栏坐标分割
                existing_index, existing_word_block_index = max_similarity_index.test_item_indices[0]
                existing_word_block = max_similarity_index.line_words_block_list[existing_word_block_index]
                new_word_block = max_similarity_index.line_words_block_list[max_similarity_word_block_index]
                
                # 获取x坐标，处理无location情况
                existing_x = get_valid_location(max_similarity_index.line_words_block_list, existing_word_block_index, 'middle')[0][0]
                new_x = get_valid_location(max_similarity_index.line_words_block_list, max_similarity_word_block_index, 'middle')[0][0]
                
                if new_x < existing_x:
                    # 新匹配的在左侧，原有匹配在右侧
                    left_item = item
                    right_item = test_items[existing_index]
                    left_word_index = max_similarity_word_block_index
                    right_word_index = existing_word_block_index
                else:
                    # 新匹配的在右侧，原有匹配在左侧
                    left_item = test_items[existing_index]
                    right_item = item
                    left_word_index = existing_word_block_index
                    right_word_index = max_similarity_word_block_index
                
                # 检查右侧检验项前一个words块是否与test_code匹配
                right_prev_index = right_word_index - 1
                if right_prev_index >= 0:
                    prev_word_block = max_similarity_index.line_words_block_list[right_prev_index]
                    code_similarity = calculate_text_similarity(
                        right_item.get('test_code', ''), 
                        prev_word_block.get('words', '')
                    )
                    
                    if code_similarity > similarity_threshold:
                        # 如果匹配，右侧检验项从该words块开始
                        right_start_index = right_prev_index
                        left_split_index = right_prev_index - 1
                    else:
                        # 不匹配，右侧检验项从当前words块开始
                        right_start_index = right_word_index
                        left_split_index = right_word_index - 1
                else:
                    # 右侧检验项是第一个words块
                    right_start_index = right_word_index
                    left_split_index = 0
                
                # 确保索引不越界
                if left_split_index < 0:
                    left_split_index = 0
                
                # 计算左侧区域坐标
                left_start_location = get_valid_location(max_similarity_index.line_words_block_list, 0, 'start')
                if left_split_index == 0:
                    # 如果分割点是第一个words，使用行首words的右边界作为分割点
                    split_location = get_valid_location(max_similarity_index.line_words_block_list, 0, 'start')
                    left_point_2 = split_location[1]
                    left_point_3 = split_location[2]
                else:
                    split_location = get_valid_location(max_similarity_index.line_words_block_list, left_split_index, 'middle')
                    left_point_2 = split_location[1]
                    left_point_3 = split_location[2]
                
                # 计算右侧区域坐标
                right_start_location = get_valid_location(max_similarity_index.line_words_block_list, right_start_index, 'start')
                end_location = get_valid_location(max_similarity_index.line_words_block_list, len(max_similarity_index.line_words_block_list) - 1, 'end')
                
                # 分配坐标
                if new_x < existing_x:
                    # 新匹配的在左侧
                    item['location'] = [left_start_location[0], left_point_2, left_point_3, left_start_location[3]]
                    test_items[existing_index]['location'] = [right_start_location[0], end_location[1], end_location[2], right_start_location[3]]
                else:
                    # 新匹配的在右侧
                    test_items[existing_index]['location'] = [left_start_location[0], left_point_2, left_point_3, left_start_location[3]]
                    item['location'] = [right_start_location[0], end_location[1], end_location[2], right_start_location[3]]
            else:
                # 第一次匹配，使用整行坐标
                item['location'] = [max_similarity_index.point_1, max_similarity_index.point_2, max_similarity_index.point_3, max_similarity_index.point_4]

            max_similarity_index.test_item_indices.append((index, max_similarity_word_block_index))

        return test_items
        
    except IndexError as e:
        logger.error(f"索引访问越界: {e}")
        raise IndexError(f"坐标提取过程中索引访问越界: {e}")
    except KeyError as e:
        logger.error(f"缺少必要字段: {e}")
        raise KeyError(f"坐标提取过程中缺少必要字段: {e}")
    except Exception as e:
        logger.error(f"坐标提取过程中发生未预期错误: {e}")
        raise ValueError(f"坐标提取失败: {e}")
    



if __name__ == '__main__':
    print(calculate_text_similarity('浊度','浊度(TURB)'))