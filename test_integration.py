# -*- coding: utf-8 -*-
"""
集成测试脚本

测试并行处理集成是否正确工作
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smo.settings.test')
os.environ["DJANGO_ALLOW_ASYNC_UNSAFE"] = "true"

try:
    django.setup()
    print("✅ Django环境初始化成功")
except Exception as e:
    print(f"❌ Django环境初始化失败: {e}")
    sys.exit(1)

def test_config():
    """测试配置模块"""
    try:
        from script.test_result_format_ae_ocr.simple_config import get_simple_config, update_simple_config
        
        print("\n🔧 测试配置模块...")
        config = get_simple_config()
        
        print("当前配置:")
        print(f"  - 启用并行处理: {'是' if config.enable_parallel else '否'}")
        print(f"  - 最大并发数: {config.max_workers}")
        print(f"  - 总超时时间: {config.timeout}秒")
        print(f"  - 成功率阈值: {config.success_rate_threshold:.1%}")
        
        # 测试配置更新
        print("\n测试配置更新...")
        update_simple_config(max_workers=2, debug_mode=True)
        updated_config = get_simple_config()
        print(f"  - 更新后并发数: {updated_config.max_workers}")
        print(f"  - 调试模式: {'是' if updated_config.debug_mode else '否'}")
        
        print("✅ 配置模块测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置模块测试失败: {e}")
        return False

def test_parallel_processor():
    """测试并行处理器"""
    try:
        from script.test_result_format_ae_ocr.parallel_processor import ParallelIntegrationAdapter
        
        print("\n🚀 测试并行处理器...")
        
        # 创建适配器
        adapter = ParallelIntegrationAdapter(enable_parallel=True, max_workers=2)
        
        # 测试数据
        test_ocr_text = """
        [Page 1]
        血常规检查报告
        
        检查项目    结果    单位    参考范围
        白细胞计数  6.5     10^9/L  3.5-9.5
        红细胞计数  4.2     10^12/L 4.3-5.8
        """
        
        test_metadata = {
            'subject_medical_info_id': 'test_001',
            'subject_id': 'subject_001',
            'subject_item_id': 'item_001',
            'task_info': {'task_id': 'test_task'},
            'visit_data': []
        }
        
        # 调用处理函数
        result = adapter.process_medical_ocr_enhanced(
            ocr_text=test_ocr_text,
            task_info={'task_id': 'test_task'},
            ocr_blocks=[],
            file_metadata=test_metadata
        )
        
        print(f"  - 处理结果类型: {type(result)}")
        print(f"  - 识别项目数: {len(result.get('test_results', []))}")
        
        print("✅ 并行处理器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 并行处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_transaction_manager():
    """测试事务管理器"""
    try:
        from script.test_result_format_ae_ocr.simple_transaction import get_simple_transaction_manager
        
        print("\n💾 测试事务管理器...")
        
        transaction_manager = get_simple_transaction_manager()
        print(f"  - 事务管理器类型: {type(transaction_manager)}")
        
        print("✅ 事务管理器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 事务管理器测试失败: {e}")
        return False

def test_integration():
    """测试集成功能"""
    try:
        from script.test_result_format import request_llm_api1
        
        print("\n🔗 测试集成功能...")
        
        # 测试数据
        test_ocr_text = """
        血常规检查报告
        
        检查项目    结果    单位
        白细胞计数  6.5     10^9/L
        """
        
        test_metadata = {
            'subject_medical_info_id': 'integration_test_001',
            'subject_id': 'subject_001',
            'subject_item_id': 'item_001',
            'task_info': {'task_id': 'integration_test'},
            'visit_data': []
        }
        
        # 调用集成的函数
        result = request_llm_api1(
            ocr_text=test_ocr_text,
            task_info={'task_id': 'integration_test'},
            ocr_blocks=[],
            file_metadata=test_metadata
        )
        
        print(f"  - 集成结果类型: {type(result)}")
        print(f"  - 识别项目数: {len(result.get('test_results', []))}")
        
        print("✅ 集成功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 集成功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 开始集成测试")
    print("=" * 60)
    
    tests = [
        ("配置模块", test_config),
        ("并行处理器", test_parallel_processor),
        ("事务管理器", test_transaction_manager),
        ("集成功能", test_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"⚠️ {test_name}测试未通过")
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！并行处理集成成功")
    else:
        print("⚠️ 部分测试失败，请检查相关模块")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
