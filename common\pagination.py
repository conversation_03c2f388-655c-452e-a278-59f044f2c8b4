from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from rest_framework.exceptions import NotFound


class StandardResultsSetPagination(PageNumberPagination):
    page_size = 10
    page_query_param = 'page'
    page_size_query_param = 'size'
    max_page_size = 100

    def paginate_queryset(self, queryset, request, view=None):
        """
        Override to handle large page numbers gracefully without raising exceptions.
        """
        # Store the request for later use
        self.request = request

        # Get the page size
        page_size = self.get_page_size(request)
        if not page_size:
            return None

        try:
            return super().paginate_queryset(queryset, request, view)
        except NotFound:
            page_number = request.query_params.get(self.page_query_param, 1)
            try:
                page_number = int(page_number)
            except (ValueError, TypeError):
                page_number = 1

            # Create empty paginator with same count
            paginator = self.django_paginator_class(queryset, page_size)

            # Create an empty page with the appropriate properties
            from django.core.paginator import Page
            # Create an empty queryset of the same type
            empty_queryset = queryset.none()

            # Create an empty page that knows its position in the overall pagination
            self.page = Page(empty_queryset, page_number, paginator)

            return []

    def get_paginated_response(self, data):
        return Response({
            'count': self.page.paginator.count,
            'page': self.page.start_index() // self.page.paginator.per_page + 1,
            'size': self.page.paginator.per_page,
            'results': data
        })

    def get_paginated_response_schema(self, schema):
        return {
            'type': 'object',
            'properties': {
                'count': {
                    'type': 'integer',
                    'title': '符合条件的总记录数。',
                    'example': 100,
                },
                'page': {
                    'type': 'integer',
                    'title': '当前页码，起始为1。',
                    'example': 1,
                },
                'size': {
                    'type': 'integer',
                    'title': '每页返回的记录条数。',
                    'example': 10,
                },
                'results': schema
            },
        }
