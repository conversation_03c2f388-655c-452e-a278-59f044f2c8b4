"""
Nacos增强版 部分接口添加登陆token
"""
import sys
import os
import socket
import time
import json
import logging
import http.client
from http import HTTPStatus
from urllib.parse import urlencode
from urllib.error import HTTPError, URLError

import retry
import nacos
import django
from django.conf import settings
from nacos.exception import NacosException, NacosRequestException



class NacosClientPlus(nacos.NacosClient):
    """
    支持登陆验证的Nacos客户端
    """

    def __init__(self, server_addresses, endpoint=None, namespace=None, ak=None, sk=None, username=None, password=None):
        super().__init__(server_addresses, endpoint, namespace, ak, sk, username, password)
        self.accessToken = False
        if username and password:
            self.accessToken = self.auth_login()['accessToken']

    def auth_login(self):
        params = {'username': self.username, 'password': self.password}
        logging.info("auth-login] username:%s, password:%s" % (self.username, self.password))

        try:
            resp = self._do_sync_req("/nacos/v1/auth/login", None, None, params, self.default_timeout, "POST")
            return json.loads(resp.read())
        # except HTTPError as e:
        #     if e.code == HTTPStatus.FORBIDDEN:
        #         raise NacosException("Insufficient privilege.")
        #     else:
        #         raise NacosException("Request Error, code is %s" % e.code)
        except Exception as e:
            logging.exception("[auth-login] exception %s occur" % str(e))
            raise

    def _do_sync_req(self, *args, **kwargs):
        if args[0] in [
            # 需要添加登陆token的接口列表
            '/nacos/v1/ns/instance',
        ]:
            if self.accessToken and len(args) > 4:
                if isinstance(args[3], dict):
                    args[3].update({'accessToken': self.accessToken})
        return super()._do_sync_req(*args, **kwargs)

    """
    def modify_naming_instance(self, service_name, ip, port, cluster_name=None, weight=None, metadata=None,
                               enable=None, ephemeral=True, group_name=DEFAULT_GROUP_NAME, need_login=True):
        logging.info("[modify-naming-instance] ip:%s, port:%s, service_name:%s, namespace:%s" % (
            ip, port, service_name, self.namespace))

        params = {
            "ip": ip,
            "port": port,
            "serviceName": service_name,
            "ephemeral": ephemeral,
            "groupName": group_name
        }

        if cluster_name is not None:
            params["clusterName"] = cluster_name

        if enable is not None:
            params["enable"] = enable

        if weight is not None:
            params["weight"] = weight

        self._build_metadata(metadata, params)

        if self.namespace:
            params["namespaceId"] = self.namespace

        # 当nacos开启登录验证时，需登陆
        if need_login:
            login_data = self.auth_login()
            params.update({'accessToken':  login_data['accessToken']})

        try:
            resp = self._do_sync_req("/nacos/v1/ns/instance", None, None, params, self.default_timeout, "PUT", "naming")
            c = resp.read()
            logging.info("[modify-naming-instance] ip:%s, port:%s, service_name:%s, namespace:%s, server response:%s" % (
                ip, port, service_name, self.namespace, c))
            return c == b"ok"
        except HTTPError as e:
            if e.code == HTTPStatus.FORBIDDEN:
                raise NacosException("Insufficient privilege.")
            else:
                raise NacosException("Request Error, code is %s" % e.code)
        except Exception as e:
            logging.exception("[modify-naming-instance] exception %s occur" % str(e))
            raise
    """
