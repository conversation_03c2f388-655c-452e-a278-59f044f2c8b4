from jinja2 import Template
from common.constants import *
from common.utils import get_general_prompt_sql

CRF_RXTRACT_PROMPT = Template(get_general_prompt_sql(CRF_GENERAL_PROMPT))
CRF_RESULT_COMPARE_PROMPT = Template(get_general_prompt_sql(CRF_COMPARE_PROMPT))

# CRF_RXTRACT_PROMPT = Template("""
# 你是一个临床试验专家，你需要根据ocr解析的病历内容，通过CRF的字段描述提取到对应的字段值，并按照json模板输出：

                                                         
# OCR文本内容
# ```
# {{ocr_text}}
# ```

# ## 你的任务要求

# 1. 仔细分析OCR文本，准确识别与每个CRF字段相关的信息
# 2. 将提取的信息根据字段规则进行标准化后，精确填入JSON结构的对应""value""字段
# 3. 如果字段描述中规定了可选值（如只能为“正常”或“异常”），必须从规定的选项中选择最符合原始表述含义的标准答案
# 4. 为每个填写的信息记录来源，填入对应""source""字段，格式为""文件名,页码""
# 5. 保持JSON的原始结构完全不变，只更新""value""和""source""的值
# 6. 对于无法在OCR文本中找到的字段，将""value""保留为原始值，""source""留空
# 7. 当信息存在多种可能的解读时，选择最符合医学专业判断的解读             

# ## 数据处理原则

# 1. 严格遵循医学规范：所有提取和填写的数据必须符合医学记录的标准格式和单位

# 2. 高精度匹配：
#    - 日期格式统一为""YYYY-MM-DD""
#    - 数值保留原文档的精确度
#    - 如遇多个可能的匹配项，优先选择上下文最相关的信息

# 3. 处理不确定数据：
#    - 如果信息明确不存在，填写""无""或""否""（根据字段类型）
#    - 如果信息可能存在但未找到，填写""未提供""
#    - 如果有模糊或不完整的信息，在值前加""可能：""并填写已知部分

# 4. 保持原始表述：尽可能使用原文档中的原始表述，避免改写或解释

# 5. 记录可信度：
#    - 对于高确定性的提取信息：直接填写
#    - 对于中等确定性：在值前注明""[待确认]：""
#    - 对于低确定性但有线索的信息：在值前注明""[推测]：""
   
# 6. 字段标准值遵循：
#    · 字段填写必须同时参考OCR文本内容与CRF字段描述要求
#    - 若字段描述中明确限定可选值（如“正常”/“异常”），必须根据OCR原文含义归类为这些标准值之一
#    - 例如：“皮肤：全身皮肤粘膜无干燥，弹性好，无黄疸、出血点、瘀斑及皮下结节”应判断为“正常”；若描述为“皮肤干燥伴瘙痒”，则判断为“异常”

# ## 特殊字段处理指南

# 1. 人口学资料：
#    - 从身份证信息、首页病历等处优先提取
#    - 年龄可能有""岁""、""月""、""天""等单位，保留原单位

# 2. 诊断相关：
#    - 病理诊断需完整记录，包括分型、分级等
#    - TNM分期必须精确到具体的T、N、M分类

# 3. 既往病史：
#    - 区分""否认""和""未提及""
#    - 保留原文中的时间描述（如""10年前""）

# 4. 实验室检查：
#    - 包含检查值和单位
#    - 必要时注明参考范围

# 5. 治疗信息：
#    - 药物名称使用通用名，保留剂量和用法
#    - 手术和放疗保留具体部位和时间

# 6. 临床意义：
#    - 若无明确描述，则统一归类为以下四类之一：  
#      - 正常  
#      - 异常无临床意义  
#      - 异常有临床意义  
#      - 未做

# ## 输出要求

# - 不要添加额外的解释或注释，只返回填写完成的JSON数据
# - 确保JSON数据字段名和字符串值均使用双号包裹，整个结果应为一个有效的 JSON 字符串，符合 json.loads() 的解析要求
# - 确保最终JSON的结构完整且格式正确
# - 我需要通过正则提取“```json   ```”里边的内容来拿到结果，因此你输出的json应该是这种结构,一定要有“json”关键词
# - 你需要根据下面的字段和字段描述提取对应的字段取值，并按照json模板输出下面字段名称，: 后面的描述和json格式里的desc相同，均为提取字段的要求和要求的返回值，请严格按要求分析并提取字段

# CRF字段描述：
# ```
# {{crf_desc}}
# ```

# ## 返回格式：CRF JSON模板：
# ```
# {{crf_json}}
# ```
# """)

# CRF_RESULT_COMPARE_PROMPT = Template("""
# 你是一位临床数据比对专家，精通医疗数据的语义理解和等效性判断。现在需要你完成一项重要的数据一致性检查任务。

# ## 任务概述
# 你需要智能比对两个AI模型从同一份医疗文档中提取的CRF字段值，判断它们传达的实际含义是否一致，而不仅仅是检查文本是否完全相同。

# ## 输入数据
# 你将收到一个分类整理的JSON数组，每个元素代表一个数据分类，其中包含多个字段的比对数据：
# [
# {
# "title": "分类标题",
# "fields": [
# {
# "key": "字段名称",
# "value_a": "模型A的提取值",
# "value_b": "模型B的提取值"
# },
# // 更多字段...
# ]
# },
# // 更多分类...
# ]

# ## 你的任务要求
# 1. 保持原始JSON的嵌套结构不变
# 2. 对每个字段，比较value_a和value_b的实际含义
# 3. 为每个字段添加一个"check"字段，标注比对结果为"一致"或"不一致"
# 4. 应用智能判断规则，识别下列情况为"一致":
#    - 日期格式不同但表示同一天(如"1971-06-04"和"1971/06/04")
#    - 肯定/否定表达方式不同但意思相同(如"是"和"有"、"否"和"无")
#    - 数值相同但单位表示不同(如"70kg"和"70公斤")
#    - 空值与表示无数据的不同表达(如""、"无"、"N/A"等)
#    - 大小写、空格或标点符号差异
#    - 同义词或医学术语的不同表达(如"高血压"和"血压升高")
# 5. 对于无法确定是否一致的复杂情况，标注为"需人工复核"并说明原因

# ## 输出格式
# 返回与输入格式相同的JSON结构，但为每个字段添加"check"字段和可选的"note"字段：
# [
# {
# "title": "分类标题",
# "fields": [
# {
# "key": "字段名称",
# "value_a": "模型A的提取值",
# "value_b": "模型B的提取值",
# "check": "一致|不一致|需人工复核",
# "note": "可选的说明，特别是对于'需人工复核'的情况"
# },
# // 更多字段...
# ]
# },
# // 更多分类...
# ]

# ## 特别考虑因素
# 1. 临床意义一致性：关注值的临床解释是否一致，而非简单的文本匹配
# 2. 针对不同类型字段的特殊规则：
#    - 日期字段：忽略格式差异，关注年月日是否一致
#    - 是/否字段：识别各种表示肯定或否定的等效表达方式
#    - 数值字段：注意单位转换和有效数字
#    - 分类字段：识别同义分类术语
# 3. 空值处理：各种表示"无数据"的方式应视为一致
# 4. 明显错误检测：如果一个日期字段的值是"是"，这明显是错误的，应标记为"不一致"

# 请确保你的输出是有效的JSON格式，可以直接用于后续处理。只返回结构化的JSON结果，不要添加额外的解释或注释。

# 以下是需要比对的数据：
# ```
# {{crf_result}}
# ```
# """)

CRF_RESULT_COMPARE_PROMPT_2 = Template("""
你是一位临床数据一致性比对专家，擅长医学术语的语义理解与等效性判断。你当前的任务是：比对两个AI模型从同一份医疗文档中提取的CRF（病例报告表）字段值，判断它们传达的实际含义是否一致，而非简单文本比对。

【任务目标】

请完成以下数据一致性检查：

1. 保持输入JSON的嵌套结构不变
2. 对每个字段比较 value_a 与 value_b 的实际含义
3. 新增字段 check，取值为：
   - "一致"：表达意义相同
   - "不一致"：表达含义不同或存在明显错误
   - "需人工复核"：语义可能相关但无法自动判断
4. 如标注为"需人工复核"，请新增 note 字段，简要说明原因

【一致性判断规则】

请运用语义理解能力，识别以下情况为“一致”：

- 日期格式不同但表示同一天，如 "1971-06-04" vs "1971/06/04"
- 肯定/否定表达方式不同，如 "是" vs "有"，"否" vs "无"
- 数值一致但单位不同或换算后的表达，如 "70kg" vs "70公斤"，"170cm" vs "1.7米"
- 空值与各种无数据表达等效，如 ""、"无"、"N/A"、"未记录" 等
- 大小写差异、标点差异、前后空格等无实际影响的格式差异
- 同义医学表达，如 "高血压" vs "血压升高"，"糖尿病" vs "DM"

【不一致判断示例】

- 临床含义不同，如 "阴性" vs "阳性"，"否" vs "是"
- 明显值类型错误，如 日期字段值为 "是"，数值字段为 "无"
- 数值差异过大无法解释，如 "180cm" vs "150cm"

【需人工复核的场景】

- 两者表达可能相关但语义差异微妙
- 涉及缩写、上下文依赖解释等情形
- 模型输出可能截断、不完整等

请在 note 中说明复核理由。

【输入输出格式说明】

输入格式如下：

[
  {
    "title": "分类标题",
    "fields": [
      {
        "key": "字段名称",
        "value_a": "模型A的提取值",
        "value_b": "模型B的提取值"
      },
      ...
    ]
  },
  ...
]

请在每个字段对象中添加字段 check，并在必要时添加 note 字段，输出格式如下：

[
  {
    "title": "分类标题",
    "fields": [
      {
        "key": "字段名称",
        "value_a": "模型A的提取值",
        "value_b": "模型B的提取值",
        "check": "一致 | 不一致 | 需人工复核",
        "note": "如有必要，说明原因"
      },
      ...
    ]
  },
  ...
]

【输出要求】

- 输出必须是有效的 JSON 格式，可供系统自动解析
- 不要添加任何非结构化文本或解释说明
- 输出即为处理结果，格式与输入结构一致，仅增加字段

以下是需要比对的数据：
```
{{crf_result}}
```
""")
