# views.py

import json
import time
import logging
import re
import datetime

import requests
from django.conf import settings
from django.http import StreamingHttpResponse, HttpResponse
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import mixins, status
from rest_framework.exceptions import ValidationError
from rest_framework.filters import Ordering<PERSON>ilter
from rest_framework.mixins import ListModelMixin
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON>, MultiPartParser
from drf_spectacular.utils import OpenApiTypes, extend_schema_field, OpenApiResponse, extend_schema, OpenApiExample, \
    OpenApiRequest, OpenApiParameter
from rest_framework.renderers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.permissions import IsAuthenticated
from rest_framework.viewsets import GenericViewSet

from common.exceptions import DifyApiException
from common.pagination import StandardResultsSetPagination
from common.auth import ERPSysJWTAuthentication, check_project_access_permission
from . import models
from . import filters
from . import serializers
from ..project.models import Project

logger = logging.getLogger('app')
from apps.system.models import ModelInvocationLog


class BaseAPIView(APIView):
    authentication_classes = [ERPSysJWTAuthentication]
    permission_classes = [IsAuthenticated]

    # 🔧 默认关闭校验，子类可开启
    require_chat_name_validation = True

    def initial(self, request, *args, **kwargs):
        super().initial(request, *args, **kwargs)

        if self.require_chat_name_validation:
            self._validate_chat_name(request)

    @staticmethod
    def _validate_chat_name(request):
        chat_name = request.META.get('HTTP_X_CHAT_NAME')
        # chat_name = 'ruixing-chat'
        # chat_name = 'project-chat'
        if not chat_name or chat_name not in settings.DIFY_CHAT_API_KEYS:
            raise ValidationError({'X-Chat-Name': ['无效的X-Chat-Name']})

        request.chat_name = chat_name
        request.chat_api_key = settings.DIFY_CHAT_API_KEYS[chat_name]


class BaseViewSet(BaseAPIView, GenericViewSet):
    pass


class BaseListViewSet(BaseViewSet, ListModelMixin):
    filter_backends = (DjangoFilterBackend, OrderingFilter)


class EventStreamRenderer:
    media_type = 'text/event-stream'
    format = 'event-stream'

    def render(self, data, accepted_media_type=None, renderer_context=None):
        return data


class ChatMessagesView(BaseAPIView):
    parser_classes = [JSONParser, MultiPartParser]
    renderer_classes = [JSONRenderer, EventStreamRenderer]

    @extend_schema(
        tags=['蕊星chat'],
        request=serializers.ChatMessageSerializer,
        summary="发送对话消息",
        description="向模型发送对话请求，支持文本和文件上传，支持流式返回（SSE）和普通模式",
        responses={
            (200, 'application/json'): OpenApiResponse(
                response=serializers.ChatMessageSerializer,
                description="Blocking 模式返回结构",
                # examples=[...]
            ),
            (200, 'text/event-stream'): OpenApiResponse(
                response=OpenApiTypes.STR,
                description="Streaming 模式（SSE）返回结构",
                examples=[
                    OpenApiExample(
                        name="SSE 响应",
                        value="data: {\"role\": \"assistant\", \"content\": \"你好\"}\n\ndata: [DONE]\n\n",
                        media_type="text/event-stream",
                        response_only=True,
                    )
                ]
            )
        }
    )
    def post(self, request):
        file_types = {
        'document': ['TXT', 'MD', 'MARKDOWN', 'PDF', 'HTML', 'XLSX', 'XLS', 'DOCX', 'CSV', 'EML', 'MSG', 'PPTX', 'PPT', 'XML', 'EPUB'],
        'image': ['JPG', 'JPEG', 'PNG', 'GIF', 'WEBP', 'SVG'],
        'audio': ['MP3', 'M4A', 'WAV', 'WEBM', 'AMR'],
        'video': ['MP4', 'MOV', 'MPEG', 'MPGA'],
    }
        data = request.data
        if data['files'] != []:
            # 目前单个文件默认去第一个，后续会更改
            for idx,i in enumerate([data['files'][0]]):
                text = i['type'].upper()
                found_key = None
                for key, extensions in file_types.items():
                    if text.upper() in extensions:
                        found_key = key
                        break

                if found_key:
                    print(f"The file type '{text}' is in the key: '{found_key}'")
                else:
                    print(f"The file type '{text}' is not found in any key")
                
                i['type'] = found_key
        data['user'] = request.sys_user.username
        # print('user:',data['user'])
        # print("key为input的输入:",data['inputs'])
        # 将原本data['files']内容存入新的key，chat_file中
        if data['files'] != []:
            data['inputs']['chat_file'] = data['files'][0]
        data['files'] = list()
        # print(data['inputs']['chat_file'])
        # print(data['files'])
        project_no = data.get('inputs', {}).get('project_no')
        qa_name_1 = data.get('inputs', {}).get('qa_name_1')
        qa_name_2 = data.get('inputs', {}).get('qa_name_2')

        headers = {
            'Authorization': f'Bearer {request.chat_api_key}'
        }

        if not project_no and request.chat_name == 'project-chat':
            # 1. 项目号没传，且是 project-chat，需要强制校验
            raise ValidationError({'project_no': ['未提供项目号']})
        if not qa_name_1 and request.chat_name == 'department_chat':
            raise ValidationError({'qa_name_1': ['未提供部门号']})

        if project_no:
            # 2. 项目号有传，去查
            project = Project.objects.filter(project_no=project_no).first()
            if not project:
                raise ValidationError({'project_no': ['项目号不存在']})

            # 3. 有找到项目，拿到 project_id
            project_id = project.project_id

            # 4. ✅ 有 project_id 才去做权限校验
            check_project_access_permission(request.user, project_id)
        if qa_name_1:
            del data['inputs']['project_no']
        else:
            qa_name_1 = ''
            qa_name_2 = ''
        if not qa_name_2 and qa_name_1:
            qa_name_2 = ''
            data['inputs']['qa_name_2'] = "NULL"
        response_mode = data.get('response_mode', 'blocking')

        url = f'http://{settings.DIFY_RUIXING_CHAT_ENDPOINT}/v1/chat-messages'
        try:
            if response_mode == "streaming":

                def stream():
                    with requests.post(url, headers=headers, json=data, stream=True) as r:
                        r.raise_for_status()  # 确保抛出错误而不是悄悄失败
                        result = {}
                        for line in r.iter_lines(decode_unicode=True):
                            if line:
                                if 'workflow_finished' in line:
                                    data_chunk = line[6:]

                                    chunk_json = json.loads(data_chunk)
                                    result['task_id'] = chunk_json['task_id']
                                    result['category'] = 'QA_CHAT'
                                    result['model_name'] = 'DeepSeek-R1-Distill-Qwen-32B'
                                    result['input_text'] = request.data.get('query')
                                    result['output_text'] = chunk_json['data']['outputs']['answer']
                                    match = re.search(r'<think>(.*?)</think>', result['output_text'], re.DOTALL)  # re.DOTALL 允许匹配换行符
                                    result['think_text'] = match.group(1) if match else None
                                    result['business_id'] = project_no if project_no else qa_name_1 + '_' +qa_name_2
                                    result['create_user'] = request.sys_user.username
                                    result['create_name'] = request.sys_user.realname
                                    dt_start = datetime.datetime.fromtimestamp(chunk_json['data']['created_at'])
                                    # 格式化输出年月日时分秒
                                    result['start_time'] = dt_start.strftime('%Y-%m-%d %H:%M:%S')
                                    dt_end = datetime.datetime.fromtimestamp(chunk_json['data']['finished_at'])
                                    result['end_time'] = dt_end.strftime('%Y-%m-%d %H:%M:%S')
                                if 'message_end' in line:
                                    data_chunk = line[6:]
                                    chunk_json = json.loads(data_chunk)
                                    result['prompt_tokens'] = chunk_json['metadata']['usage'].get('prompt_tokens', 0)
                                    result['completion_tokens'] = chunk_json['metadata']['usage'].get('completion_tokens', 0)



                                yield f"{line}\n\n"  # SSE 规范前缀
                    # import requests
                        ModelInvocationLog.objects.create(**result)
                return StreamingHttpResponse(stream(), content_type='text/event-stream')

            resp = requests.post(url, headers=headers, json=data)
            resp.raise_for_status()
            data = resp.json()
            return Response(data)
        except:
            raise DifyApiException()


class FilesUploadView(BaseAPIView):
    parser_classes = (MultiPartParser,)

    @extend_schema(
        summary='上传文件',
        tags=['蕊星chat'],
        description='上传文件并在发送消息时使用，可实现图文多模态理解。 支持您的应用程序所支持的所有格式。 上传的文件仅供当前终端用户使用。',
        request=serializers.FilesUploadSerializer,
    )
    def post(self, request):
        headers = {
            'Authorization': f'Bearer {request.chat_api_key}'
        }
        file = request.FILES.get("file")
        user = request.sys_user.username  # 后端用户上下文注入

        if not file:
            return Response({"error": "文件不能为空"}, status=400)

        url = f'http://{settings.DIFY_RUIXING_CHAT_ENDPOINT}/v1/files/upload'

        files = {'file': (file.name, file, file.content_type)}
        data = {'user': user}
        try:
            resp = requests.post(url, headers=headers, data=data, files=files)
            # response = requests.request("POST", url, headers=headers, data=data, files=files)
            resp.raise_for_status()
            data = resp.json()
            return Response(data)
        except:
            raise DifyApiException()


class StopChatMessagesView(BaseAPIView):
    @extend_schema(
        summary='停止响应',
        tags=['蕊星chat'],
        description='仅支持流式模式',
        parameters=[
            OpenApiParameter(name='task_id', required=True, location=OpenApiParameter.PATH, description='任务 ID')
        ]
    )
    def post(self, request, task_id):
        headers = {
            'Authorization': f'Bearer {request.chat_api_key}'
        }
        params = request.data
        # taskid=params['task_id']
        url = f'http://{settings.DIFY_RUIXING_CHAT_ENDPOINT}/v1/chat-messages/{task_id}/stop'
        params['user'] = request.sys_user.username
        try:
            resp = requests.post(url, headers=headers, json=params, timeout=5)
            resp.raise_for_status()
            data = resp.json()

            return Response(data)
        except:
            raise DifyApiException()


class FeedbacksView(BaseAPIView):
    @extend_schema(
        summary='消息反馈（点赞）',
        tags=['蕊星chat'],
        description='消息终端用户反馈、点赞，方便应用开发者优化输出预期。',
        request=serializers.FeedbacksSerializer,
        parameters=[
            OpenApiParameter(name='message_id', required=True, location=OpenApiParameter.PATH, description='消息 ID')
        ]
    )
    def post(self, request, message_id):
        headers = {
            'Authorization': f'Bearer {request.chat_api_key}'
        }
        params = request.data
        # messageid=params['message_id']
        url = f'http://{settings.DIFY_RUIXING_CHAT_ENDPOINT}/v1/messages/{message_id}/feedbacks'
        params['user'] = request.sys_user.username
        try:
            resp = requests.post(url, headers=headers, json=params)
            resp.raise_for_status()
            data = resp.json()

            return Response(data)
        except:
            raise DifyApiException()


class SuggestedView(BaseAPIView):
    @extend_schema(
        summary='获取下一轮建议问题列表',
        tags=['蕊星chat'],
        description='获取下一轮建议问题列表。',
        parameters=[
            OpenApiParameter(name='message_id', required=True, location=OpenApiParameter.PATH, description='消息 ID')
        ]
    )
    def get(self, request, message_id):
        headers = {
            'Authorization': f'Bearer {request.chat_api_key}'
        }
        params = request.query_params
        user = request.sys_user.username
        url = f'http://{settings.DIFY_RUIXING_CHAT_ENDPOINT}/v1/messages/{message_id}/suggested?user={user}'
        # params['user'] = request.sys_user.username
        try:
            resp = requests.get(url, headers=headers, params=params, timeout=5)
            resp.raise_for_status()
            data = resp.json()

            return Response(data)
        except:
            raise DifyApiException()


class MessagesView(BaseAPIView):
    @extend_schema(
        summary='获取会话历史消息',
        tags=['蕊星chat'],
        description='滚动加载形式返回历史聊天记录，第一页返回最新 limit 条，即：倒序返回。',
    )
    def get(self, request):
        headers = {
            'Authorization': f'Bearer {request.chat_api_key}'
        }
        params = request.query_params.dict()
        url = f'http://{settings.DIFY_RUIXING_CHAT_ENDPOINT}/v1/messages'
        params['user'] = request.sys_user.username
        try:
            resp = requests.get(url, headers=headers, params=params)
            resp.raise_for_status()  # 抛出 HTTPError 会进 except
            data = resp.json()
            return Response(data)
        except:
            raise DifyApiException()


class ConversationsView(BaseAPIView):
    pagination_class = StandardResultsSetPagination

    @extend_schema(
        summary='获取会话列表',
        tags=['蕊星chat'],
        operation_id="conversation_list"
    )
    def get(self, request):
        headers = {
            'Authorization': f'Bearer {request.chat_api_key}'
        }
        # user = request.query_params.get('user', '')
        # last_id = request.query_params.get('last_id', '')
        # limit = request.query_params.get('limit', 100)
        # 'count': self.page.paginator.count,
        # 'page': self.page.start_index() // self.page.paginator.per_page + 1,
        # 'size': self.page.paginator.per_page,
        user = request.sys_user.username
        last_id = request.query_params.get('last_id', '')
        url = f'http://{settings.DIFY_RUIXING_CHAT_ENDPOINT}/v1/conversations'
        params = request.query_params.dict() or {}
        params['user'] = request.sys_user.username

        try:
            resp = requests.get(url, headers=headers, params=params)
            resp.raise_for_status()
            data = resp.json()
            return Response(data)
        except:
            raise DifyApiException()


class DeleteConversationsView(BaseAPIView):
    @extend_schema(
        summary='删除会话',
        tags=['蕊星chat'],
        description='删除会话',
        operation_id="conversation_detail",
        parameters=[
            OpenApiParameter(name='conversation_id', required=True, location=OpenApiParameter.PATH,
                             description='会话 ID')
        ]

    )
    def delete(self, request, conversation_id):
        headers = {
            'Authorization': f'Bearer {request.chat_api_key}'
        }
        params = request.data
        url = f'http://{settings.DIFY_RUIXING_CHAT_ENDPOINT}/v1/conversations/{conversation_id}'
        params['user'] = request.sys_user.username
        try:
            resp = requests.delete(url, headers=headers, json=params)
            resp.raise_for_status()
            data = {"result": "success"}
            return Response(data)
        except:
            raise DifyApiException()


class RenameConversationsView(BaseAPIView):
    @extend_schema(
        summary='会话重命名',
        tags=['蕊星chat'],
        description='对会话进行重命名，会话名称用于显示在支持多会话的客户端上。',
        request=serializers.RenameConversationsSerializer,
        parameters=[
            OpenApiParameter(name='conversation_id', required=True, location=OpenApiParameter.PATH,
                             description='会话 ID')
        ]
    )
    def post(self, request, conversation_id):
        headers = {
            'Authorization': f'Bearer {request.chat_api_key}'
        }
        params = request.data
        url = f'http://{settings.DIFY_RUIXING_CHAT_ENDPOINT}/v1/conversations/{conversation_id}/name'
        params['user'] = request.sys_user.username
        try:
            resp = requests.post(url, headers=headers, json=params, timeout=5)
            resp.raise_for_status()
            data = resp.json()

            return Response(data)
        except:
            raise DifyApiException()


class AudioToText(BaseAPIView):
    parser_classes = (MultiPartParser,)  # 支持文件上传

    @extend_schema(
        summary='语音转文字',
        tags=['蕊星chat'],
        description='语音转文字',
        request=serializers.AudioToTextSerializer,
        responses={200: OpenApiTypes.STR},
    )
    def post(self, request):
        headers = {
            'Authorization': f'Bearer {request.chat_api_key}'
        }
        file = request.FILES.get("file")
        user = request.sys_user.username  # 后端用户上下文注入

        if not file:
            return Response({"error": "文件不能为空"}, status=400)

        url = f'http://{settings.DIFY_RUIXING_CHAT_ENDPOINT}/v1/audio-to-text'

        files = {'file': (file.name, file, file.content_type)}
        data = {'user': user}
        try:
            resp = requests.post(url, headers=headers, data=data, files=files, timeout=10)
            resp.raise_for_status()

            return Response(data)

        except:
            raise DifyApiException()


class TextToAudio(BaseAPIView):
    parser_classes = [MultiPartParser]  # 接收 multipart/form-data

    @extend_schema(
        summary='文字转语音',
        tags=['蕊星chat'],
        description='文字转语音',
        request=serializers.TextToAudioSerializer
    )
    def post(self, request):
        headers = {
            'Authorization': f'Bearer {request.chat_api_key}'
        }
        serializer = serializers.TextToAudioSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        params = serializer.validated_data

        params['user'] = request.sys_user.username  # 自动注入用户

        # params = request.data
        url = f'http://{settings.DIFY_RUIXING_CHAT_ENDPOINT}/v1/text-to-audio'
        # 构造 multipart/form-data 请求（无文件时的规范方式）
        files = [
            ("text", (None, params["text"])),
            ("user", (None, params["user"])),
            ("message_id", (None, params.get("message_id", ""))),
        ]
        # resp = requests.post(url, headers=headers, data=params)
        try:
            resp = requests.post(url, headers=headers, files=files)
            resp.raise_for_status()

            return HttpResponse(resp.content, content_type="audio/wav")
        except:
            raise DifyApiException()


class InfoView(BaseAPIView):
    @extend_schema(
        summary='获取应用基本信息',
        tags=['蕊星chat'],
        description="获取应用基本信息"
    )
    def get(self, request):
        headers = {
            'Authorization': f'Bearer {request.chat_api_key}'
        }
        url = f'http://{settings.DIFY_RUIXING_CHAT_ENDPOINT}/v1/info'
        params = request.query_params or {}
        try:
            resp = requests.get(url, headers=headers, params=params, timeout=5)
            resp.raise_for_status()
            data = resp.json()

            return Response(data)
        except:
            raise DifyApiException()


class ParametersView(BaseAPIView):
    @extend_schema(
        summary='获取应用参数',
        tags=['蕊星chat'],
        description="用于进入页面一开始，获取功能开关、输入参数名称、类型及默认值等使用"
    )
    def get(self, request):
        headers = {
            'Authorization': f'Bearer {request.chat_api_key}'
        }
        url = f'http://{settings.DIFY_RUIXING_CHAT_ENDPOINT}/v1/parameters'
        params = request.query_params or {}
        try:
            resp = requests.get(url, headers=headers, params=params, timeout=5)
            resp.raise_for_status()
            data = resp.json()

            return Response(data)
        except:
            raise DifyApiException()


class MetaView(BaseAPIView):
    @extend_schema(
        summary='获取应用Meta信息',
        tags=['蕊星chat'],
        description="用于获取工具icon"
    )
    def get(self, request):
        headers = {
            'Authorization': f'Bearer {request.chat_api_key}'
        }
        url = f'http://{settings.DIFY_RUIXING_CHAT_ENDPOINT}/v1/meta'
        params = request.query_params or {}
        try:
            resp = requests.get(url, headers=headers, params=params, timeout=5)
            resp.raise_for_status()
            data = resp.json()

            return Response(data)
        except:
            raise DifyApiException()


class ConversationFeedbackListViewSet(BaseListViewSet, mixins.CreateModelMixin, ):
    # queryset = models.ConversationFeedback.objects.annotate(site_count=Count('sites'), subject_count=Count('subjects'),)
    queryset = models.ConversationFeedback.objects.filter(delete_flag=0)
    serializer_class = serializers.ConversationFeedbackSerializer
    filterset_class = filters.ConversationFeedbackFilter
    pagination_class = StandardResultsSetPagination
    ordering_fields = '__all__'
    require_chat_name_validation = False

    @extend_schema(
        summary="获取反馈列表",
        description="分页获取反馈记录，可按关键词过滤、排序。",
        tags=["蕊星chat"],
        responses={200: serializers.ConversationFeedbackSerializer(many=True)}
    )
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)

    @extend_schema(
        summary='新增反馈',
        tags=['蕊星chat'],
        request=serializers.ConversationFeedbackSerializer,  # ✅ 显式指定请求体
        responses={201: serializers.ConversationFeedbackSerializer}
    )
    def create(self, request, *args, **kwargs):
        """重载 create 方法，使 @extend_schema 生效"""
        return super().create(request, *args, **kwargs)

    def perform_create(self, serializer):
        # 从 request 拿你要注入的数据，比如用户信息
        user = self.request.user  # 如果你用了认证
        sys_user = getattr(self.request, "sys_user", None)  # 你项目中的自定义字段

        serializer.save(
            username=sys_user.username or {},
            realname=sys_user.realname or {}
        )
