
from django.db.models import Q
from django_filters import rest_framework as filters
from . import models

class ProjectFilter(filters.FilterSet):
    project_no = filters.CharFilter(field_name='project_no', lookup_expr='icontains', label='项目号（模糊匹配）')
    project_name = filters.CharFilter(field_name='project_name', lookup_expr='icontains', label='项目名称（模糊匹配）')
    query = filters.CharFilter(method='filter_by_query', label='综合查询（模糊匹配）')

    def filter_by_query(self, queryset, name, value):
        """
        综合查询，OR 关系
        """
        return queryset.filter(
            Q(project_no__icontains=value) | Q(project_name__icontains=value)
        )


class ProjectSiteFilter(filters.FilterSet):
    hosp_name = filters.CharFilter(field_name='hosp_name', lookup_expr='icontains', label='中心名称（模糊查询）')
    hosp_department_no = filters.Char<PERSON>ilter(field_name='hosp_department_no',
                                            lookup_expr='icontains', label='中心编号（模糊查询）')
    query = filters.CharFilter(method='filter_by_query', label='综合查询（模糊匹配）')

    def filter_by_query(self, queryset, name, value):
        """
        综合查询，OR 关系
        """
        return queryset.filter(
            Q(hosp_name__icontains=value) | Q(hosp_department_no__icontains=value)
        )


class ProjectMaterialInfoFilter(filters.FilterSet):
    category = filters.CharFilter(field_name='category', lookup_expr='exact', label='素材分类')


class WordTemplateInfoFilter(filters.FilterSet):
    project_no = filters.CharFilter(field_name='project_no', lookup_expr='icontains', label='项目号（模糊匹配）')


class MedicalCollectionFileHistoryFilter(filters.FilterSet):
    subject_id = filters.CharFilter(field_name='task__subject_id', label='受试者ID')
    category = filters.CharFilter(field_name='task__category', label='素材分类')
    subject_visit_id = filters.CharFilter(field_name='task__subject_visit_id', label='访视ID')

    class Meta:
        model = models.MedicalCollectionFile
        fields = ['subject_id', 'category','subject_visit_id']