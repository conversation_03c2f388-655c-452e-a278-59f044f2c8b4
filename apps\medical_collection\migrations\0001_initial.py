# Generated by Django 4.1.5 on 2025-03-03 12:32

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("subject", "0001_initial"),
        ("project", "0001_initial"),
        ("patient", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="MedicalCollectionTask",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "delete_flag",
                    models.SmallIntegerField(
                        choices=[(0, "未删除"), (1, "已删除")],
                        db_index=True,
                        default=0,
                        verbose_name="删除标志（0：未删除；1：已删除）",
                    ),
                ),
                (
                    "data_version",
                    models.PositiveIntegerField(default=0, verbose_name="数据版本"),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True, db_index=True, verbose_name="创建时间"
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "create_user",
                    models.CharField(max_length=255, null=True, verbose_name="创建人工号"),
                ),
                (
                    "create_name",
                    models.CharField(max_length=255, null=True, verbose_name="创建人姓名"),
                ),
                (
                    "update_user",
                    models.CharField(max_length=255, null=True, verbose_name="更新人工号"),
                ),
                (
                    "update_name",
                    models.CharField(max_length=255, null=True, verbose_name="更新人姓名"),
                ),
                ("name", models.CharField(max_length=255, verbose_name="任务名称")),
                (
                    "category",
                    models.CharField(
                        choices=[("CRF", "CRF"), ("MEDICAL_RECORD", "病历")],
                        default="CRF",
                        max_length=20,
                        verbose_name="任务分类",
                    ),
                ),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="任务描述"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("TODO", "待办"),
                            ("IN_PROGRESS", "进行中"),
                            ("COMPLETED", "已完成"),
                            ("CANCELLED", "已取消"),
                            ("ERROR", "执行错误"),
                        ],
                        default="TODO",
                        max_length=20,
                        verbose_name="任务状态",
                    ),
                ),
                (
                    "start_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="开始时间"),
                ),
                (
                    "end_time",
                    models.DateTimeField(blank=True, null=True, verbose_name="结束时间"),
                ),
                (
                    "patient",
                    models.ForeignKey(
                        db_column="patient_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="medical_collection_tasks",
                        to="patient.patient",
                        to_field="patient_id",
                        verbose_name="患者ID",
                    ),
                ),
                (
                    "project",
                    models.ForeignKey(
                        db_column="project_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="medical_collection_tasks",
                        to="project.project",
                        to_field="project_id",
                        verbose_name="项目ID",
                    ),
                ),
                (
                    "project_site",
                    models.ForeignKey(
                        db_column="project_site_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="medical_collection_tasks",
                        to="project.projectsite",
                        to_field="project_site_id",
                        verbose_name="项目中心ID",
                    ),
                ),
                (
                    "subject",
                    models.ForeignKey(
                        db_column="subject_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="medical_collection_tasks",
                        to="subject.subject",
                        to_field="subject_id",
                        verbose_name="受试者ID",
                    ),
                ),
            ],
            options={
                "verbose_name": "病历归集任务",
                "verbose_name_plural": "病历归集任务",
                "db_table": "medical_collection_task",
            },
        ),
        migrations.CreateModel(
            name="MedicalCollectionFile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "delete_flag",
                    models.SmallIntegerField(
                        choices=[(0, "未删除"), (1, "已删除")],
                        db_index=True,
                        default=0,
                        verbose_name="删除标志（0：未删除；1：已删除）",
                    ),
                ),
                (
                    "data_version",
                    models.PositiveIntegerField(default=0, verbose_name="数据版本"),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True, db_index=True, verbose_name="创建时间"
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "create_user",
                    models.CharField(max_length=255, null=True, verbose_name="创建人工号"),
                ),
                (
                    "create_name",
                    models.CharField(max_length=255, null=True, verbose_name="创建人姓名"),
                ),
                (
                    "update_user",
                    models.CharField(max_length=255, null=True, verbose_name="更新人工号"),
                ),
                (
                    "update_name",
                    models.CharField(max_length=255, null=True, verbose_name="更新人姓名"),
                ),
                (
                    "original_filename",
                    models.CharField(max_length=255, verbose_name="原始文件名"),
                ),
                ("bucket_name", models.CharField(max_length=100, verbose_name="存储桶名称")),
                ("object_name", models.CharField(max_length=500, verbose_name="对象名称")),
                ("content_type", models.CharField(max_length=100, verbose_name="文件类型")),
                ("size", models.BigIntegerField(verbose_name="文件大小（字节）")),
                (
                    "hash",
                    models.CharField(
                        db_index=True, max_length=255, verbose_name="文件哈希（sha256）"
                    ),
                ),
                (
                    "version",
                    models.CharField(max_length=255, null=True, verbose_name="版本号"),
                ),
                (
                    "task",
                    models.ForeignKey(
                        db_column="task_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="files",
                        to="medical_collection.medicalcollectiontask",
                        verbose_name="病历归集结果ID",
                    ),
                ),
            ],
            options={
                "verbose_name": "病历归集文件",
                "verbose_name_plural": "病历归集文件",
                "db_table": "medical_collection_file",
            },
        ),
    ]
