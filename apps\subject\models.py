from django.db import models
from common.models import BaseModel, BaseFileModel


class Subject(BaseModel):
    """受试者信息"""
    subject_id = models.CharField(max_length=50, unique=True, verbose_name="受试者ID")
    code = models.CharField(max_length=100, verbose_name="受试者编号", db_index=True)
    real_name = models.CharField(max_length=100, null=True, verbose_name="受试者姓名", db_index=True)
    short_name = models.CharField(max_length=100, null=True, verbose_name="受试者姓名缩写", db_index=True)
    status = models.CharField(max_length=255, null=True, verbose_name="最新状态", db_index=True)
    status_text = models.CharField(max_length=255, null=True, verbose_name="最新状态描述")

    # 一（项目信息）对（受试者信息）多
    project = models.ForeignKey(
        'project.Project',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='subjects',
        to_field='project_id',
        db_column='project_id',
        verbose_name="项目ID",
        db_index=True
    )

    # 一（项目中心信息）对（受试者信息）多
    project_site = models.ForeignKey(
        'project.ProjectSite',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='subjects',
        to_field='project_site_id',
        db_column='project_site_id',
        verbose_name="项目中心ID",
        db_index=True
    )

    class Meta:
        verbose_name = "受试者信息"
        verbose_name_plural = "受试者信息"
        db_table = 'subject_info'


class SubjectEpoch(BaseModel):
    """
    受试者阶段信息
    """
    subject_epoch_id = models.CharField(max_length=100, verbose_name="受试者阶段ID（OT受试者阶段数据源ID + 受试者ID）", db_index=True, unique=True)
    source_id = models.CharField(max_length=50, verbose_name="OT受试者阶段数据源ID", db_index=True)
    epoch_id = models.CharField(max_length=50, verbose_name="阶段ID", db_index=True, null=True, blank=True)
    status = models.CharField(max_length=50, verbose_name="阶段状态", null=True, blank=True)
    status_text = models.CharField(max_length=50, verbose_name="阶段状态描述", null=True, blank=True)
    label = models.CharField(max_length=50, verbose_name="阶段名称", null=True, blank=True)
    seq = models.IntegerField(verbose_name="阶段序号", null=True)

    project = models.ForeignKey(
        'project.Project',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='subject_epochs',
        to_field='project_id',
        db_column='project_id',
        verbose_name="项目ID",
        db_index=True
    )

    project_site = models.ForeignKey(
        'project.ProjectSite',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='subject_epochs',
        to_field='project_site_id',
        db_column='project_site_id',
        verbose_name="项目中心ID",
        db_index=True
    )

    subject = models.ForeignKey(
        'subject.Subject',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='subject_epochs',
        to_field='subject_id',
        db_column='subject_id',
        verbose_name="受试者ID",
        db_index=True
    )

    class Meta:
        db_table = 'subject_epoch_info'
        verbose_name = "受试者阶段信息"
        verbose_name_plural = verbose_name
        # constraints = [
        #     models.UniqueConstraint(fields=['subject_epoch_id', 'subject_id'], name="subject_epoch_info_subject_epoch_id_subject_id_unique")
        # ]


class SubjectVisit(BaseModel):
    """
    受试者访视信息
    """
    subject_visit_id = models.CharField(max_length=100, verbose_name="受试者访视ID（OT受试者访访数据源ID + 受试者ID）", db_index=True, unique=True)
    source_id = models.CharField(max_length=50, verbose_name="OT受试者访视数据源ID", db_index=True)
    visit_id = models.CharField(max_length=50, verbose_name="访视ID", db_index=True, null=True, blank=True)
    type = models.CharField(max_length=50, verbose_name="访视类型", null=True, blank=True)
    type_text = models.CharField(max_length=50, verbose_name="访视类型描述", null=True, blank=True)
    status = models.CharField(max_length=50, verbose_name="访视状态", null=True, blank=True)
    status_text = models.CharField(max_length=50, verbose_name="访视状态描述", null=True, blank=True)
    label = models.CharField(max_length=300, verbose_name="访视名称", null=True, blank=True)

    visit_date = models.JSONField(verbose_name="访视日期", null=True)

    skip_reason = models.CharField(max_length=500, verbose_name="跳过原因", null=True, blank=True)
    prepare_date = models.DateField(verbose_name="访视准备时间", null=True)
    ot_create_time = models.DateTimeField(verbose_name="OT创建时间", db_index=True, null=True)
    ot_update_time = models.DateTimeField(verbose_name="OT更新时间", db_index=True, null=True)

    subject_epoch = models.ForeignKey(
        'SubjectEpoch',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='subject_visits',
        to_field='subject_epoch_id',
        db_column='subject_epoch_id',
        verbose_name="受试者阶段ID（OT受试者阶段数据源ID + 受试者ID）",
        db_index=True
    )
    
    project = models.ForeignKey(
        'project.Project',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='subject_visits',
        to_field='project_id',
        db_column='project_id',
        verbose_name="项目ID",
        db_index=True
    )

    project_site = models.ForeignKey(
        'project.ProjectSite',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='subject_visits',
        to_field='project_site_id',
        db_column='project_site_id',
        verbose_name="项目中心ID",
        db_index=True
    )

    subject = models.ForeignKey(
        'subject.Subject',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='subject_visits',
        to_field='subject_id',
        db_column='subject_id',
        verbose_name="受试者ID",
        db_index=True
    )

    class Meta:
        db_table = 'subject_visit_info'
        verbose_name = "受试者访视信息"
        verbose_name_plural = verbose_name
        # constraints = [
        #     models.UniqueConstraint(fields=['subject_visit_id', 'subject_id'], name="subject_visit_info_subject_visit_id_subject_id_unique")
        # ]

class SubjectItem(BaseModel):
    """
    受试者访视操作项信息
    """
    subject_item_id = models.CharField(max_length=100, verbose_name="受试者操作项唯一标识（OT受试者访视数据源ID + 受试者ID）", db_index=True, unique=True)
    source_id = models.CharField(max_length=50, verbose_name="OT受试者访视数据源ID", db_index=True)
    item_id = models.CharField(max_length=50, verbose_name="操作项ID", db_index=True, null=True, blank=True)
    label = models.CharField(max_length=50, verbose_name="操作项名称", null=True, blank=True)
    status = models.CharField(max_length=50, verbose_name="操作项状态", null=True, blank=True)
    status_text = models.CharField(max_length=50, verbose_name="操作项状态描述", null=True, blank=True)
    seq = models.CharField(max_length=50, verbose_name="操作项序号", null=True, blank=True)
    item_type = models.CharField(max_length=50, verbose_name="操作项类型1检验,2检查,3病史,0NA", null=True, blank=True)

    ae_ai_current_step = models.CharField(max_length=50, default=0, verbose_name="AE AI识别功能当前步骤：0,1,2,3,4")
    ae_ai_task_id = models.CharField(max_length=50, default=0, verbose_name="任务id")

    subject_visit = models.ForeignKey(
        'SubjectVisit',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='subject_items',
        to_field='subject_visit_id',
        db_column='subject_visit_id',
        verbose_name="受试者访视ID（OT受试者访视数据源ID + 受试者ID）",
        db_index=True
    )

    project = models.ForeignKey(
        'project.Project',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='subject_items',
        to_field='project_id',
        db_column='project_id',
        verbose_name="项目ID",
        db_index=True
    )

    project_site = models.ForeignKey(
        'project.ProjectSite',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='subject_items',
        to_field='project_site_id',
        db_column='project_site_id',
        verbose_name="项目中心ID",
        db_index=True
    )

    subject = models.ForeignKey(
        'subject.Subject',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='subject_items',
        to_field='subject_id',
        db_column='subject_id',
        verbose_name="受试者ID",
        db_index=True
    )

    class Meta:
        db_table = 'subject_item_info'
        verbose_name = "受试者访视操作项信息"
        verbose_name_plural = verbose_name
        # constraints = [
        #     models.UniqueConstraint(fields=['subject_item_id', 'subject_id'], name="subject_item_info_subject_item_id_subject_id_unique")
        # ]