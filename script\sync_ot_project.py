
import os
import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smo.settings.test')
django.setup()

import psycopg2
from django.db import transaction
from apps.project.models import Project

# 'postgresql://postgres:Qr%4077.88@**************:5432/oceanus_trial_clinplus'
pg_conn_params = {
    'host': '**************',
    'database': 'oceanus_trial_clinplus',
    'user': 'postgres',
    'password': 'Qr@77.88',
    'port': 5432
}

PAGE_SIZE = 100  # 每次查询的记录数，可以根据实际情况调整

with open('./script/test_project_no.txt') as f:
    PROJECT_NO_LIST = f.readlines()

PROJECT_NO_LIST = list(set([i.strip() for i in PROJECT_NO_LIST if i.strip()]))
print(PROJECT_NO_LIST)


def migrate_projects():
    # Connect to PostgreSQL
    pg_conn = psycopg2.connect(**pg_conn_params)
    pg_cursor = pg_conn.cursor()

    try:
        # 先获取总记录数
        count_query = f"""
        SELECT COUNT(*) FROM project 
        --where project_no in {tuple(PROJECT_NO_LIST)}
        """
        print(count_query)
        pg_cursor.execute(count_query)
        total_count = pg_cursor.fetchone()[0]

        # 计算总页数
        total_pages = (total_count + PAGE_SIZE - 1) // PAGE_SIZE

        for page in range(total_pages):
            offset = page * PAGE_SIZE
            pg_query = f"""
                WITH filtered_projects AS (
                    SELECT project_id
                    FROM project
                    WHERE org_id = 'e888888'
                    ORDER BY project_id
                    LIMIT {PAGE_SIZE} OFFSET {offset}
                )
                SELECT 
                    t1.project_id,
                    t1.project_name,
                    t1.project_no,
                    t1.indication,
                    dictconstantname('e888888', t1.indication) as indication_text,
                    t1.stage,
                    dictconstantname('e888888', t1.stage) as stage_text,
                    t3.stage_of_project as status,
                    dictconstantname('e888888', t3.stage_of_project) as status_text,
                    projectroleusersandno(t1.project_id, '20101') as pm_text,
                    dictconstantname('e888888', t3.project_group2) as pm_group_text,
                    dictconstantname('e888888', t3.bu_group2) as bu_text,
                    t1.is_del as delete_flag
                FROM 
                    filtered_projects p
                JOIN 
                    project t1 ON p.project_id = t1.project_id
                JOIN 
                    project_extra t3 ON p.project_id = t3.project_id
                ORDER BY 
                    t1.project_id
            """
            print(pg_query)
            pg_cursor.execute(pg_query)
            projects_data = pg_cursor.fetchall()

            # Get column names
            column_names = [desc[0] for desc in pg_cursor.description]

            # Process each row and insert/update to MySQL

            for row in projects_data:
                with transaction.atomic():
                    # Create a dictionary of project data
                    project_dict = dict(zip(column_names, row))
                    
                    count_sql = f"""
                    select 
                        t1.project_id, count(distinct(t2.id)) as site_count, count(distinct(t3.id)) as subject_count
                    from project as t1
                    left join project_site as t2 on t1.project_id = t2.project_id 
                    left join subject as t3 on t1.project_id = t3.project_id
                    where t1.project_id = '{project_dict['project_id']}'
                    group by t1.project_id
                    """
                    pg_cursor.execute(count_sql)
                    count_data = pg_cursor.fetchone()
                    project_dict.update({'site_count': count_data[1], 'subject_count': count_data[2]})

                    # Method 1: Using Django ORM
                    try:
                        # Try to get the existing project
                        project = Project.objects.get(project_id=project_dict['project_id'])

                        # Update existing project
                        for key, value in project_dict.items():
                            setattr(project, key, value)
                        project.save()
                        print(f"Updated project: {project.project_id}")

                    except Project.DoesNotExist:
                        # Create new project
                        Project.objects.create(**project_dict)
                        print(f"Created new project: {project_dict['project_id']}")

            print(f"Migration completed successfully. Processed {len(projects_data)} projects.")

    except Exception as e:
        print(f"Error during migration: {str(e)}")
    finally:
        pg_cursor.close()
        pg_conn.close()


if __name__ == "__main__":
    # python -m script.sync_ot_project
    migrate_projects()
