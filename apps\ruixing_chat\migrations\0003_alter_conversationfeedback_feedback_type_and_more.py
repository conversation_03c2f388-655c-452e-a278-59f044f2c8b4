# Generated by Django 4.1.5 on 2025-04-24 11:05

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("ruixing_chat", "0002_remove_conversationfeedback_bucket_name_and_more"),
    ]

    operations = [
        migrations.Alter<PERSON><PERSON>(
            model_name="conversationfeedback",
            name="feedback_type",
            field=models.CharField(
                blank=True, max_length=50, null=True, verbose_name="反馈类型"
            ),
        ),
        migrations.AlterField(
            model_name="conversationfeedback",
            name="realname",
            field=models.Char<PERSON>ield(
                blank=True, max_length=50, null=True, verbose_name="用户姓名"
            ),
        ),
        migrations.AlterField(
            model_name="conversationfeedback",
            name="username",
            field=models.Char<PERSON>ield(
                blank=True, max_length=50, null=True, verbose_name="用户工号"
            ),
        ),
    ]
