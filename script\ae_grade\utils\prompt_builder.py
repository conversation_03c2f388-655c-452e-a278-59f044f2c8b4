# -*- coding: utf-8 -*-
"""
智能提示词构建器
基于rerank匹配结果构建精简的提示词
"""
import json
from typing import List, Dict, Any
import logging

logger = logging.getLogger(__name__)

from common.utils import get_general_prompt_sql
from common.constants import *


class SmartPromptBuilder:
    """智能提示词构建器"""

    def build_optimized_prompt(self, test_items: List[Dict[str, Any]],
                               matched_rules: Dict[int, List[Dict[str, Any]]],
                               prompt_templates: Dict[str, str] = None
                               ) -> str:
        """
        构建优化的CTCAE分级提示词，专注于医疗检验结果的不良事件分析

        Args:
            test_items: 检验项列表
            matched_rules: 匹配的规则字典

        Returns:
            优化后的提示词
        """
        try:
            # 收集所有相关规则，去重
            relevant_rules = {}
            for item_id, rules in matched_rules.items():
                for rule in rules:
                    rule_id = rule['id']
                    if rule_id not in relevant_rules:
                        relevant_rules[rule_id] = rule

            # 构建精简的CTCAE规则文本
            ctcae_rules_text = self._build_ctcae_rules_text(relevant_rules)

            # 构建检验数据JSON
            test_results_json = json.dumps(test_items, ensure_ascii=False, indent=2)

            # 构建高度优化的提示词模板
            optimized_template = f"""
你是一位精通CTCAE标准的医学数据分析专家，以严谨细致著称。请根据以下输入数据，对检验结果逐一进行不良事件（AE）分级分析，并严格输出YAML格式结果。
以下是输入数据：
<检验数据>
{test_results_json}
</检验数据>
<CTCAE_5.0匹配规则>
{ctcae_rules_text}
</CTCAE_5.0匹配规则>

**AE分级规则（按优先级执行）**：
**规则1**：**CTCAE规则中没有该检验项**：
   - ae_grade: ""
   - ae_name: "CTCAE未涉及，请研究者判定"  

**规则2**：**CTCAE规则中存在该检验项**：
   - **正常值处理**：若结果值在参考区间内（根据 status_flag 或值比较），ae_grade: "0"；ae_name。
   - **未达到AE等级**：若有匹配的CTCAE规则但没有达到最低等级，ae_grade: ""；ae_name: "请研究者判断"。
   - **异常值分级**：根据CTCAE规则匹配分级。

**规则3**：**OCR错误容错**：  
   - 忽略明显数据错误（如单位混乱、不现实数值），通过上下文推测合理值（如单位统一转换）。
   - 若无法容错（如关键数据缺失），按规则1处理。

**输出规范（YAML格式）**：
```yaml
results:
- id: "原始检验项ID"  # 必须与输入ID一致
  ae_grade: "值必须是'0'、'1'、'2'、'3'、'4'、'5'"
  ae_name: "不良事件名称（CTCAE原文，或'CTCAE未涉及，请研究者判定'，或'请研究者判断'，或空字符串）"
```

**关键要求**：
1. **精确性**：数值比较和单位转换必须绝对准确（如WBC单位需统一为/mm³）。
2. **完整性**：输出项数必须与输入检验项完全一致，不得遗漏或新增。
3. **原文引用**：ae_name 仅允许直接从CTCAE规则文本复制，禁止修改、缩写或填充。

输出样例：
```yaml
results:
- id: 1
  ae_grade: ""
  ae_name: "CTCAE未涉及，请研究者判定"
- id: 2
  ae_grade: "2"
  ae_name: "白细胞数降低"
- id: 3
  ae_grade: "0"
  ae_name: ""
- id: 4
  ae_grade: ""
  ae_name: "请研究者判断"
```
"""
            optimized_template_qwen = f"""
你是一位精通CTCAE标准的医学数据分析专家，以严谨细致著称。请根据以下输入数据，对检验结果逐一进行不良事件（AE）分级分析，并严格输出YAML格式结果。
以下是输入数据：
<检验数据>
{test_results_json}
</检验数据>
<CTCAE_5.0匹配规则>
{ctcae_rules_text}
</CTCAE_5.0匹配规则>

**AE分级规则（按优先级执行）**：
**规则1**：**CTCAE规则中没有该检验项**：
   - ae_grade: ""
   - ae_name: "CTCAE未涉及，请研究者判定"  

**规则2**：**CTCAE规则中存在该检验项**：
   - **正常值处理**：若结果值在参考区间内（根据 status_flag 或值比较），ae_grade: "0"；ae_name。
   - **未达到AE等级**：若有匹配的CTCAE规则但没有达到最低等级，ae_grade: ""；ae_name: "请研究者判断"。
   - **异常值分级**：根据CTCAE规则匹配分级。

**规则3**：**OCR错误容错**：  
   - 忽略明显数据错误（如单位混乱、不现实数值），通过上下文推测合理值（如单位统一转换）。
   - 若无法容错（如关键数据缺失），按规则1处理。

**输出规范（YAML格式）**：
```yaml
results:
- id: "原始检验项ID"  # 必须与输入ID一致
  ae_grade: "值必须是'0'、'1'、'2'、'3'、'4'、'5'"
  ae_name: "不良事件名称（CTCAE原文，或'CTCAE未涉及，请研究者判定'，或'请研究者判断'，或空字符串）"
```

**关键要求**：
1. **精确性**：数值比较和单位转换必须绝对准确（如WBC单位需统一为/mm³）。
2. **完整性**：输出项数必须与输入检验项完全一致，不得遗漏或新增。
3. **原文引用**：ae_name 仅允许直接从CTCAE规则文本复制，禁止修改、缩写或填充。

【输出要求】
在分析过程中，请逐步思考，但每个步骤的描述尽量简洁。一个检验项目只占用一步思考。
使用分隔符“####”来区分思考过程与最终答案。

**格式示例:**
思考1: Alb（白蛋白）：ID 24813，值33.1，参考范围40.0-55.0，异常↓。CTCAE中有“低白蛋白血症”（编号32）。规则中，3g/dL=30g/L，33.1g/L在30-40之间，属于1级。
思考2: ...
思考3: ...
####
```yaml
results:
- id: 1
  ae_grade: ""
  ae_name: "CTCAE未涉及，请研究者判定"
- id: 2
  ae_grade: "2"
  ae_name: "白细胞数降低"
- id: 3
  ae_grade: "0"
  ae_name: ""
- id: 4
  ae_grade: ""
  ae_name: "请研究者判断"
```
/no_think
"""
            # 检查是否有有效的预加载模板，如果有则使用，否则保持默认模板
            if prompt_templates and isinstance(prompt_templates, dict) and prompt_templates.get(
                    "DS") and prompt_templates.get("Qwen"):
                # 使用预加载的提示词模板
                optimized_template = prompt_templates.get("DS", "")
                optimized_template_qwen = prompt_templates.get("Qwen", "")
                logger.info("使用预加载的提示词模板")

                # 替换预加载模板中的占位符
                optimized_template = optimized_template.replace("{{test_results}}", test_results_json)
                optimized_template = optimized_template.replace("{{ctcae_rules}}", ctcae_rules_text)
                optimized_template_qwen = optimized_template_qwen.replace("{{test_results}}", test_results_json)
                optimized_template_qwen = optimized_template_qwen.replace("{{ctcae_rules}}", ctcae_rules_text)
            else:
                # 使用默认模板（optimized_template 和 optimized_template_qwen 已经有默认值）
                logger.warning("未提供有效的提示词模板，使用SmartPromptBuilder的默认模板")

            return optimized_template, optimized_template_qwen

        except Exception as e:
            logger.error(f"构建优化提示词失败: {e}")

    def _build_ctcae_rules_text(self, relevant_rules: Dict[int, Dict[str, Any]]) -> str:
        """构建CTCAE规则文本"""
        rules_text = ""

        # for rule_id in sorted(relevant_rules.keys()):
        #     rule = relevant_rules[rule_id]
        # rules_text += f"##{rule['id']}【不良事件名称】：{rule['ae_name']}；"
        # rules_text += f"【不良事件定义】：{rule['ae_definition']}，"
        # rules_text += f"判断依据的【检查项】：{', '.join(rule['check_items'])}，"
        # rules_text += f"【等级判定规则】：{rule['grade_rules']}\n\n"

        # 直接返回json转化的字符串
        rules_text = json.dumps(relevant_rules, ensure_ascii=False, indent=2)
        return rules_text
