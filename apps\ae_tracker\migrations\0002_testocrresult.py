# Generated by Django 4.1.5 on 2025-04-11 21:56

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("ae_tracker", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="TestOcrResult",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "delete_flag",
                    models.SmallIntegerField(
                        choices=[(0, "未删除"), (1, "已删除")],
                        db_index=True,
                        default=0,
                        verbose_name="删除标志（0：未删除；1：已删除）",
                    ),
                ),
                (
                    "data_version",
                    models.PositiveIntegerField(default=0, verbose_name="数据版本"),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True, db_index=True, verbose_name="创建时间"
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name="更新时间"
                    ),
                ),
                (
                    "create_user",
                    models.CharField(max_length=255, null=True, verbose_name="创建人工号"),
                ),
                (
                    "create_name",
                    models.CharField(max_length=255, null=True, verbose_name="创建人姓名"),
                ),
                (
                    "update_user",
                    models.CharField(max_length=255, null=True, verbose_name="更新人工号"),
                ),
                (
                    "update_name",
                    models.CharField(max_length=255, null=True, verbose_name="更新人姓名"),
                ),
                (
                    "subject_id",
                    models.CharField(max_length=50, unique=True, verbose_name="受试者ID"),
                ),
                (
                    "subject_item_id",
                    models.CharField(
                        db_index=True,
                        max_length=100,
                        unique=True,
                        verbose_name="受试者操作项唯一标识（OT受试者访视数据源ID + 受试者ID）",
                    ),
                ),
                (
                    "ocr_text",
                    models.TextField(blank=True, null=True, verbose_name="OCR 文本"),
                ),
            ],
            options={
                "verbose_name": "AE ocr文本",
                "verbose_name_plural": "AE ocr文本",
                "db_table": "test_ocr_result",
            },
        ),
    ]
