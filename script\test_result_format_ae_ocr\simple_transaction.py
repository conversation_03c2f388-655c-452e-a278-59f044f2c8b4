# -*- coding: utf-8 -*-
"""
简化的事务管理器

用于阶段一的最小侵入式改造，提供基础的事务管理功能
"""

import logging
from typing import List, Dict, Any
from datetime import datetime

logger = logging.getLogger(__name__)


class SimpleTransactionManager:
    """简化的事务管理器，处理页面级并行处理的事务一致性"""
    
    def __init__(self):
        self.logger = logger
    
    def execute_file_transaction(self, file_result, task_id: int, 
                                ae_tracker_task_item, subject_id: str, 
                                subject_item_id: str, data: List[Dict]) -> bool:
        """
        执行文件级事务处理（简化版本）
        
        Args:
            file_result: 文件处理结果（FileResult对象或兼容字典）
            task_id: 任务ID
            ae_tracker_task_item: AE跟踪任务项
            subject_id: 受试者ID
            subject_item_id: 受试者项目ID
            data: 访问数据
            
        Returns:
            bool: 处理是否成功
        """
        try:
            from django.db import transaction
            from apps.ae_tracker.models import TestResult
            from sqlalchemy import text
            from common.tools import get_db_engin_url, collect_time_choice
            from sqlalchemy import create_engine
            from sqlalchemy.pool import NullPool
            
            # 获取数据库引擎
            db_erp = create_engine(get_db_engin_url('default'), poolclass=NullPool)
            
            with transaction.atomic():
                # 1. 检查是否有失败的页面
                if hasattr(file_result, 'has_failures') and file_result.has_failures:
                    # 根据策略决定是否继续处理
                    if not self._should_continue_with_failures(file_result):
                        raise Exception(f"文件处理失败，失败页面: {file_result.failed_pages}")
                
                # 2. 获取测试项目列表
                if hasattr(file_result, 'test_items'):
                    test_items = file_result.test_items
                elif isinstance(file_result, dict) and 'test_results' in file_result:
                    # 兼容原有格式
                    test_items = file_result['test_results']
                else:
                    test_items = []
                
                # 3. 准备TestResult实例
                test_result_instances = self._prepare_test_result_instances(
                    test_items, task_id, ae_tracker_task_item, subject_id, subject_item_id, data
                )
                
                # 4. 批量创建TestResult记录
                if test_result_instances:
                    TestResult.objects.bulk_create(test_result_instances)
                    self.logger.info(f"成功创建 {len(test_result_instances)} 条TestResult记录")
                
                # 5. 更新相关状态
                self._update_related_status(subject_id, subject_item_id, task_id, db_erp)
                
                return True
                
        except Exception as e:
            self.logger.error(f"文件事务处理失败: {e}")
            # 执行错误状态更新
            self._handle_transaction_error(subject_id, subject_item_id, task_id)
            return False
    
    def _should_continue_with_failures(self, file_result) -> bool:
        """
        决定是否在有失败页面的情况下继续处理
        
        策略：
        1. 如果成功率 >= 80%，继续处理
        2. 如果成功率 < 80%，抛出异常
        """
        if hasattr(file_result, 'success_rate'):
            success_rate = file_result.success_rate
        else:
            # 兼容处理
            success_rate = 1.0
        
        threshold = 0.8  # 80%成功率阈值
        
        if success_rate >= threshold:
            self.logger.warning(f"部分页面处理失败，但成功率 {success_rate:.1%} >= {threshold:.1%}，继续处理")
            return True
        else:
            self.logger.error(f"页面处理成功率 {success_rate:.1%} < {threshold:.1%}，终止处理")
            return False
    
    def _prepare_test_result_instances(self, test_items, task_id: int, 
                                     ae_tracker_task_item, subject_id: str, 
                                     subject_item_id: str, data: List[Dict]) -> List:
        """准备TestResult实例列表"""
        from apps.ae_tracker.models import TestResult
        from common.tools import collect_time_choice
        
        # 获取当前最大seq值
        max_seq_value = TestResult.objects.filter(
            subject_id=subject_id,
            subject_item_id=subject_item_id,
            delete_flag=0
        ).values_list('seq', flat=True).order_by('-seq').first() or 0
        
        test_result_instances = []
        page_collect_times = {}
        
        # 首先为每个page_num确定collect_time
        for i, item in enumerate(test_items):
            if isinstance(item, dict):
                # 字典格式（原有格式）
                page_num = item.get('page_num', 0)
                collect_time = item.get('collect_time')
            else:
                # TestItem对象格式
                page_num = getattr(item, 'page_num', 0)
                collect_time = getattr(item, 'collect_time', None)
            
            if page_num not in page_collect_times:
                page_collect_times[page_num] = collect_time_choice(collect_time, data)
        
        # 处理每个测试项目
        for i, item in enumerate(test_items):
            if isinstance(item, dict):
                # 字典格式处理
                item_dict = item.copy()
            else:
                # TestItem对象格式处理
                item_dict = item.to_dict() if hasattr(item, 'to_dict') else item.__dict__.copy()
            
            # 添加必要的字段
            page_num = item_dict.get('page_num', 0)
            item_dict.update({
                'seq': max_seq_value + i + 1,
                'collect_time': page_collect_times.get(page_num),
                'abnormal_flag': 0,
                'subject_medical_info_id': getattr(file_result, 'file_id', '') if hasattr(file_result, 'file_id') else '',
                'project': ae_tracker_task_item.project,
                'project_site': ae_tracker_task_item.project_site,
                'subject_id': subject_id,
                'patient': ae_tracker_task_item.patient,
                'subject_item_id': subject_item_id,
                'subject_epoch': ae_tracker_task_item.subject_epoch,
                'subject_visit': ae_tracker_task_item.subject_visit,
            })
            
            # 处理空值
            if item_dict.get('report_time') == '':
                item_dict['report_time'] = None
            if item_dict.get('test_flag') == '':
                item_dict['test_flag'] = None
            
            # 过滤掉TestResult模型不支持的字段
            filtered_dict = {k: v for k, v in item_dict.items() if k != 'test_text'}
            
            test_result_instances.append(TestResult(**filtered_dict))
        
        return test_result_instances
    
    def _update_related_status(self, subject_id: str, subject_item_id: str, task_id: int, db_erp):
        """更新相关状态"""
        from sqlalchemy import text
        
        # 更新subject_item_info状态
        params = {
            'subject_id': subject_id,
            'subject_item_id': subject_item_id,
            'ae_ai_current_step': 2
        }
        sql = text("""
            UPDATE subject_item_info 
            SET ae_ai_current_step=:ae_ai_current_step 
            WHERE subject_id=:subject_id 
            AND subject_item_id=:subject_item_id 
            AND ae_ai_current_step <= 2
        """)
        
        with db_erp.begin() as conn:
            conn.execute(sql, params)
        
        # 更新任务状态为完成
        params = {
            'which_need_update_id': task_id,
            'mask_status': 'COMPLETED',
            'end_time': datetime.now()
        }
        sql = text("""
            UPDATE ae_tracker_task 
            SET status=:mask_status, end_time=:end_time 
            WHERE id=:which_need_update_id
        """)
        
        with db_erp.begin() as conn:
            conn.execute(sql, params)
    
    def _handle_transaction_error(self, subject_id: str, subject_item_id: str, task_id: int):
        """处理事务错误"""
        try:
            from sqlalchemy import text
            from common.tools import get_db_engin_url
            from sqlalchemy import create_engine
            from sqlalchemy.pool import NullPool
            
            db_erp = create_engine(get_db_engin_url('default'), poolclass=NullPool)
            
            with db_erp.begin() as conn:
                # 更新任务状态为错误
                params = {
                    'which_need_update_id': task_id,
                    'mask_status': 'ERROR',
                    'end_time': datetime.now()
                }
                sql = text("""
                    UPDATE ae_tracker_task 
                    SET status=:mask_status, end_time=:end_time 
                    WHERE id=:which_need_update_id
                """)
                conn.execute(sql, params)
                
                # 重置subject_item_info状态
                params = {
                    'subject_id': subject_id,
                    'subject_item_id': subject_item_id,
                    'ae_ai_current_step': 0
                }
                sql = text("""
                    UPDATE subject_item_info 
                    SET ae_ai_current_step=:ae_ai_current_step 
                    WHERE subject_id=:subject_id 
                    AND subject_item_id=:subject_item_id 
                    AND ae_ai_current_step <= 2
                """)
                conn.execute(sql, params)
                
        except Exception as e:
            self.logger.error(f"错误状态更新失败: {e}")


# 全局事务管理器实例
_transaction_manager = SimpleTransactionManager()


def get_simple_transaction_manager() -> SimpleTransactionManager:
    """获取简化事务管理器"""
    return _transaction_manager
