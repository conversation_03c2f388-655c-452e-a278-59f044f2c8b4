# -*- coding: utf-8 -*-
"""
并行处理配置管理

本模块提供并行处理的配置管理功能，包括：
- 环境变量配置
- 动态配置调整
- 配置验证和默认值
"""

import os
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class ParallelConfig:
    """并行处理配置类"""
    
    # 基础并行配置
    enable_parallel: bool = True                    # 是否启用并行处理
    max_workers: int = 3                           # 最大并发工作线程数
    timeout: int = 300                             # 总超时时间（秒）
    page_timeout: int = 60                         # 单页处理超时时间（秒）
    
    # 资源管理配置
    memory_threshold: float = 0.8                  # 内存使用率阈值
    cpu_threshold: float = 0.9                     # CPU使用率阈值
    auto_adjust_workers: bool = True               # 是否自动调整并发度
    
    # 错误处理配置
    success_rate_threshold: float = 0.8            # 成功率阈值
    continue_on_partial_failure: bool = True       # 部分失败时是否继续处理
    max_retries: int = 2                          # 最大重试次数
    retry_delay: int = 5                          # 重试延迟（秒）
    
    # 监控和日志配置
    enable_performance_monitoring: bool = True     # 是否启用性能监控
    log_level: str = "INFO"                       # 日志级别
    export_metrics: bool = False                  # 是否导出性能指标
    metrics_export_path: str = "/tmp/parallel_metrics.json"  # 指标导出路径
    
    # 调试配置
    debug_mode: bool = False                      # 调试模式
    force_serial_processing: bool = False         # 强制串行处理（用于对比测试）


class ConfigManager:
    """配置管理器"""
    
    def __init__(self):
        self.logger = logger
        self._config: Optional[ParallelConfig] = None
    
    def get_config(self) -> ParallelConfig:
        """获取当前配置"""
        if self._config is None:
            self._config = self._load_config()
        return self._config
    
    def reload_config(self) -> ParallelConfig:
        """重新加载配置"""
        self._config = self._load_config()
        return self._config
    
    def _load_config(self) -> ParallelConfig:
        """从环境变量加载配置"""
        config = ParallelConfig()
        
        # 基础并行配置
        config.enable_parallel = self._get_bool_env(
            'AE_PARALLEL_ENABLE', config.enable_parallel
        )
        config.max_workers = self._get_int_env(
            'AE_PARALLEL_MAX_WORKERS', config.max_workers, min_val=1, max_val=10
        )
        config.timeout = self._get_int_env(
            'AE_PARALLEL_TIMEOUT', config.timeout, min_val=60, max_val=1800
        )
        config.page_timeout = self._get_int_env(
            'AE_PARALLEL_PAGE_TIMEOUT', config.page_timeout, min_val=10, max_val=300
        )
        
        # 资源管理配置
        config.memory_threshold = self._get_float_env(
            'AE_PARALLEL_MEMORY_THRESHOLD', config.memory_threshold, min_val=0.5, max_val=0.95
        )
        config.cpu_threshold = self._get_float_env(
            'AE_PARALLEL_CPU_THRESHOLD', config.cpu_threshold, min_val=0.5, max_val=0.99
        )
        config.auto_adjust_workers = self._get_bool_env(
            'AE_PARALLEL_AUTO_ADJUST', config.auto_adjust_workers
        )
        
        # 错误处理配置
        config.success_rate_threshold = self._get_float_env(
            'AE_PARALLEL_SUCCESS_THRESHOLD', config.success_rate_threshold, min_val=0.1, max_val=1.0
        )
        config.continue_on_partial_failure = self._get_bool_env(
            'AE_PARALLEL_CONTINUE_ON_FAILURE', config.continue_on_partial_failure
        )
        config.max_retries = self._get_int_env(
            'AE_PARALLEL_MAX_RETRIES', config.max_retries, min_val=0, max_val=5
        )
        config.retry_delay = self._get_int_env(
            'AE_PARALLEL_RETRY_DELAY', config.retry_delay, min_val=1, max_val=60
        )
        
        # 监控和日志配置
        config.enable_performance_monitoring = self._get_bool_env(
            'AE_PARALLEL_ENABLE_MONITORING', config.enable_performance_monitoring
        )
        config.log_level = self._get_str_env(
            'AE_PARALLEL_LOG_LEVEL', config.log_level, 
            valid_values=['DEBUG', 'INFO', 'WARNING', 'ERROR']
        )
        config.export_metrics = self._get_bool_env(
            'AE_PARALLEL_EXPORT_METRICS', config.export_metrics
        )
        config.metrics_export_path = self._get_str_env(
            'AE_PARALLEL_METRICS_PATH', config.metrics_export_path
        )
        
        # 调试配置
        config.debug_mode = self._get_bool_env(
            'AE_PARALLEL_DEBUG', config.debug_mode
        )
        config.force_serial_processing = self._get_bool_env(
            'AE_PARALLEL_FORCE_SERIAL', config.force_serial_processing
        )
        
        # 验证配置
        self._validate_config(config)
        
        # 记录配置信息
        self._log_config(config)
        
        return config
    
    def _get_bool_env(self, key: str, default: bool) -> bool:
        """获取布尔型环境变量"""
        value = os.getenv(key, str(default)).lower()
        return value in ('true', '1', 'yes', 'on')
    
    def _get_int_env(self, key: str, default: int, min_val: int = None, max_val: int = None) -> int:
        """获取整型环境变量"""
        try:
            value = int(os.getenv(key, str(default)))
            if min_val is not None and value < min_val:
                self.logger.warning(f"配置 {key}={value} 小于最小值 {min_val}，使用最小值")
                return min_val
            if max_val is not None and value > max_val:
                self.logger.warning(f"配置 {key}={value} 大于最大值 {max_val}，使用最大值")
                return max_val
            return value
        except ValueError:
            self.logger.warning(f"配置 {key} 值无效，使用默认值 {default}")
            return default
    
    def _get_float_env(self, key: str, default: float, min_val: float = None, max_val: float = None) -> float:
        """获取浮点型环境变量"""
        try:
            value = float(os.getenv(key, str(default)))
            if min_val is not None and value < min_val:
                self.logger.warning(f"配置 {key}={value} 小于最小值 {min_val}，使用最小值")
                return min_val
            if max_val is not None and value > max_val:
                self.logger.warning(f"配置 {key}={value} 大于最大值 {max_val}，使用最大值")
                return max_val
            return value
        except ValueError:
            self.logger.warning(f"配置 {key} 值无效，使用默认值 {default}")
            return default
    
    def _get_str_env(self, key: str, default: str, valid_values: list = None) -> str:
        """获取字符串型环境变量"""
        value = os.getenv(key, default)
        if valid_values and value not in valid_values:
            self.logger.warning(f"配置 {key}={value} 不在有效值 {valid_values} 中，使用默认值 {default}")
            return default
        return value
    
    def _validate_config(self, config: ParallelConfig):
        """验证配置的合理性"""
        # 检查超时时间关系
        if config.page_timeout >= config.timeout:
            self.logger.warning("单页超时时间不应大于等于总超时时间，自动调整")
            config.page_timeout = min(config.page_timeout, config.timeout // 2)
        
        # 检查阈值合理性
        if config.success_rate_threshold > 1.0:
            config.success_rate_threshold = 1.0
        if config.success_rate_threshold < 0.0:
            config.success_rate_threshold = 0.0
        
        # 强制串行处理时禁用并行
        if config.force_serial_processing:
            config.enable_parallel = False
            self.logger.info("强制串行处理模式已启用")
    
    def _log_config(self, config: ParallelConfig):
        """记录配置信息"""
        self.logger.info("🔧 并行处理配置:")
        self.logger.info(f"  - 启用并行处理: {config.enable_parallel}")
        self.logger.info(f"  - 最大并发数: {config.max_workers}")
        self.logger.info(f"  - 总超时时间: {config.timeout}秒")
        self.logger.info(f"  - 单页超时时间: {config.page_timeout}秒")
        self.logger.info(f"  - 成功率阈值: {config.success_rate_threshold:.1%}")
        self.logger.info(f"  - 自动调整并发度: {config.auto_adjust_workers}")
        self.logger.info(f"  - 性能监控: {config.enable_performance_monitoring}")
        if config.debug_mode:
            self.logger.info("  - 调试模式: 已启用")
        if config.force_serial_processing:
            self.logger.info("  - 强制串行处理: 已启用")


# 全局配置管理器实例
config_manager = ConfigManager()


def get_parallel_config() -> ParallelConfig:
    """获取并行处理配置"""
    return config_manager.get_config()


def reload_parallel_config() -> ParallelConfig:
    """重新加载并行处理配置"""
    return config_manager.reload_config()


# 配置示例和说明
EXAMPLE_ENV_CONFIG = """
# AE并行处理配置示例
# 将以下环境变量添加到系统环境或.env文件中

# 基础配置
export AE_PARALLEL_ENABLE=true                    # 启用并行处理
export AE_PARALLEL_MAX_WORKERS=3                  # 最大并发工作线程数
export AE_PARALLEL_TIMEOUT=300                    # 总超时时间（秒）
export AE_PARALLEL_PAGE_TIMEOUT=60                # 单页处理超时时间（秒）

# 资源管理
export AE_PARALLEL_MEMORY_THRESHOLD=0.8           # 内存使用率阈值
export AE_PARALLEL_CPU_THRESHOLD=0.9              # CPU使用率阈值
export AE_PARALLEL_AUTO_ADJUST=true               # 自动调整并发度

# 错误处理
export AE_PARALLEL_SUCCESS_THRESHOLD=0.8          # 成功率阈值
export AE_PARALLEL_CONTINUE_ON_FAILURE=true       # 部分失败时继续处理
export AE_PARALLEL_MAX_RETRIES=2                  # 最大重试次数
export AE_PARALLEL_RETRY_DELAY=5                  # 重试延迟（秒）

# 监控和日志
export AE_PARALLEL_ENABLE_MONITORING=true         # 启用性能监控
export AE_PARALLEL_LOG_LEVEL=INFO                 # 日志级别
export AE_PARALLEL_EXPORT_METRICS=false           # 导出性能指标
export AE_PARALLEL_METRICS_PATH=/tmp/parallel_metrics.json

# 调试配置
export AE_PARALLEL_DEBUG=false                    # 调试模式
export AE_PARALLEL_FORCE_SERIAL=false             # 强制串行处理
"""


if __name__ == "__main__":
    # 测试配置加载
    config = get_parallel_config()
    print("当前配置:")
    print(f"  启用并行: {config.enable_parallel}")
    print(f"  最大并发数: {config.max_workers}")
    print(f"  超时时间: {config.timeout}秒")
    print(f"  成功率阈值: {config.success_rate_threshold:.1%}")
    
    print("\n环境变量配置示例:")
    print(EXAMPLE_ENV_CONFIG)
