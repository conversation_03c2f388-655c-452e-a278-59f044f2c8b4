from django.db.models import Q
from django_filters import rest_framework as filters
from . import models


class ConversationFeedbackFilter(filters.FilterSet):
    knowledge_base_name = filters.CharFilter(field_name='knowledge_base_name',lookup_expr='icontains', label='知识库名称')
    question = filters.CharFilter(field_name='question',lookup_expr='icontains', label='对话问题')
    # project_no = filters.CharFilter(field_name='project__project_no', lookup_expr='exact', label='项目号')
    # hosp_name = filters.CharFilter(field_name='hosp_name', lookup_expr='icontains', label='中心名称（模糊查询）')
    # hosp_department_no = filters.CharFilter(field_name='hosp_department_no',
    #                                         lookup_expr='icontains', label='中心编号（模糊查询）')
    # query = filters.CharFilter(method='filter_by_query', label='综合查询（模糊匹配）')

    # def filter_by_query(self, queryset, name, value):
    #     """
    #     综合查询，OR 关系
    #     """
    #     return queryset.filter(
    #         Q(hosp_name__icontains=value) | Q(hosp_department_no__icontains=value)
    #     )
