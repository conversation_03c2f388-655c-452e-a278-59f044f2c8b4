"""
图片处理相关工具函数
"""
import ast
from common.ocr_tools import sponsor_list


def calculate_keyword_precise_coordinates(text_block, keyword, location):
    """
    计算关键词在文本块中的精确坐标

    Args:
        text_block: OCR识别的完整文本
        keyword: 需要遮挡的敏感词
        location: 文本块的四个角坐标 [[x1,y1], [x2,y2], [x3,y3], [x4,y4]]

    Returns:
        list: 敏感词的坐标列表，每个匹配返回 [x1, y1, x2, y2]
    """
    if keyword not in text_block:
        return []

    # 获取文本块的边界坐标
    x1, y1 = location[0]  # 左上角
    x2, y2 = location[1]  # 右上角
    x3, y3 = location[2]  # 右下角
    x4, y4 = location[3]  # 左下角

    # 计算文本块的实际宽度和高度
    block_width = max(x2, x3) - min(x1, x4)
    block_height = max(y3, y4) - min(y1, y2)

    # 计算每个字符的平均宽度（考虑中英文混合）
    char_count = len(text_block)
    if char_count == 0:
        return []

    # 中文字符一般比英文字符宽，进行加权计算
    chinese_chars = sum(1 for char in text_block if '\u4e00' <= char <= '\u9fff')
    english_chars = char_count - chinese_chars

    effective_char_count = chinese_chars * 1.8 + english_chars
    avg_char_width = block_width / effective_char_count if effective_char_count > 0 else block_width / char_count

    keyword_coords = []
    start_index = 0

    # 查找所有匹配的关键词位置
    while True:
        index = text_block.find(keyword, start_index)
        if index == -1:
            break

        # 计算关键词前面字符的有效宽度
        prefix_text = text_block[:index]
        prefix_chinese = sum(1 for char in prefix_text if '\u4e00' <= char <= '\u9fff')
        prefix_english = len(prefix_text) - prefix_chinese
        prefix_width = (prefix_chinese * 1.8 + prefix_english) * avg_char_width

        # 计算关键词本身的宽度
        keyword_chinese = sum(1 for char in keyword if '\u4e00' <= char <= '\u9fff')
        keyword_english = len(keyword) - keyword_chinese
        keyword_width = (keyword_chinese * 1.8 + keyword_english) * avg_char_width

        # 计算关键词的实际坐标
        keyword_x1 = min(x1, x4) + prefix_width
        keyword_y1 = min(y1, y2)
        keyword_x2 = keyword_x1 + keyword_width
        keyword_y2 = max(y3, y4)

        # 添加一些边距确保完全覆盖
        margin_x = avg_char_width * 0.1  # 10%的边距
        margin_y = block_height * 0.05  # 5%的边距

        keyword_coords.append([
            max(0, int(keyword_x1 - margin_x)),
            max(0, int(keyword_y1 - margin_y)),
            int(keyword_x2 + margin_x),
            int(keyword_y2 + margin_y)
        ])

        start_index = index + 1

    return keyword_coords


def mask_keywords_precisely(draw, ocr_result, keywords_list, fill_color=(0, 0, 0)):
    """
    精确遮挡关键词，避免过度遮挡

    Args:
        draw: PIL.ImageDraw.Draw对象
        ocr_result: OCR识别结果
        keywords_list: 需要遮挡的关键词列表
        fill_color: 遮挡颜色，默认黑色
        
    Returns:
        tuple: (总遮挡数量, 精确遮挡数量, 降级遮挡数量)
    """
    words_blocks = ast.literal_eval(ocr_result)["result"]["words_block_list"]

    # 统计遮挡信息
    total_masks = 0
    precise_masks = 0
    fallback_masks = 0

    for keyword in keywords_list:
        if not keyword or keyword in ['None', '无', '未提及']:
            continue

        for block in words_blocks:
            text_block = block["words"]
            location = block["location"]

            if keyword in text_block:
                # 如果关键词等于整个文本块，直接遮挡整块
                if keyword.strip() == text_block.strip():
                    a, b, c, d = location[0][0], location[0][1], location[2][0], location[2][1]
                    draw.rectangle([min(a, c), min(b, d), max(a, c), max(b, d)], fill=fill_color)
                    fallback_masks += 1
                else:
                    # 精确计算关键词坐标
                    keyword_coords = calculate_keyword_precise_coordinates(text_block, keyword, location)

                    if keyword_coords:
                        for coord in keyword_coords:
                            draw.rectangle(coord, fill=fill_color)
                            precise_masks += 1
                    else:
                        # fallback: 如果计算失败，遮挡整个文本块
                        a, b, c, d = location[0][0], location[0][1], location[2][0], location[2][1]
                        draw.rectangle([min(a, c), min(b, d), max(a, c), max(b, d)], fill=fill_color)
                        fallback_masks += 1

                total_masks += 1

    return total_masks, precise_masks, fallback_masks


def prepare_mask_keywords(ai_extracted_keywords):
    """
    准备遮挡关键词列表，合并AI提取的敏感词和赞助商列表
    
    Args:
        ai_extracted_keywords: AI提取的敏感词列表
        
    Returns:
        list: 最终需要遮挡的关键词列表
    """
    # 使用AI提取的敏感词列表
    keywords = ai_extracted_keywords.copy() if ai_extracted_keywords else []

    # 合并赞助商列表
    keywords = keywords + sponsor_list
    
    # 去重并过滤无效关键词
    all_keywords = list(set([
        s for s in keywords 
        if s and s != 'None' and s != '无' and s != '未提及'
    ]))
    
    return all_keywords 