from datetime import timedelta
from django.conf import settings
from rest_framework import serializers
from common.minio_client import get_minio_client

from . import models


class AeAiResultListSerializers(serializers.Serializer):
    model = serializers.CharField(label="llm模型类型", required=False, allow_null=True, allow_blank=True, default=None)
    ae_grade = serializers.CharField(label="模型结果(不良事件结果)", required=False, allow_null=True, allow_blank=True, default=None)
    ae_name = serializers.CharField(label="ae名称", required=False, allow_null=True, allow_blank=True, default=None)
    ae_desc = serializers.CharField(label="描述", required=False, allow_null=True, allow_blank=True, default=None)


# class AiMedicationMeasuresListSerializers(serializers.Serializer):
#     model = serializers.CharField(label="llm模型类型", required=False, allow_null=True, allow_blank=True, default=None)
#     ae_meds = serializers.CharField(label="ae用药措施", required=False, allow_null=True, allow_blank=True, default=None)
class MeasureOptionSerializer(serializers.Serializer):
    id = serializers.CharField(label="措施id", required=False, allow_null=True, allow_blank=True, default=None)
    measure_name = serializers.CharField(label="用药措施", required=False, allow_null=True, allow_blank=True, default=None)


class DrugSerializer(serializers.Serializer):
    drug_name = serializers.CharField(label="药品名称", required=False, allow_null=True, allow_blank=True, default=None)
    measure_name_list = MeasureOptionSerializer(label="药品各等级用药措施列表", many=True, allow_null=True)  # 改为措施选项列表
    no_adjustment_needed = serializers.CharField(label="研究者判定无需调整是1否0勾选", required=False, allow_null=True, allow_blank=True, default=None)
    reason = serializers.CharField(label="研究者判定无需调整的理由", required=False, allow_null=True, allow_blank=True, default=None)
    id = serializers.CharField(label="被选中措施id", required=False, allow_null=True, allow_blank=True, default=None)

class AiMedicationMeasuresListSerializers(serializers.Serializer):
    ae_name = serializers.CharField(label="ae名称", required=False, allow_null=True, allow_blank=True, default=None)
    ae_grade = serializers.CharField(label="ae等级", required=False, allow_null=True, allow_blank=True, default=None)
    drug_type = serializers.CharField(label="页面类型(1、2默认勾选,34、56)", required=False, allow_null=True, allow_blank=True, default=None)
    drugs = DrugSerializer(many=True, allow_null=True)


# class AiMedicationMeasuresRequestSerializers(serializers.Serializer):
#     ae_medication_measures_list = AiMedicationMeasuresListSerializers(required=False, allow_null=True)


class OcrResultListSerializer(serializers.ModelSerializer):
    subject_item_id = serializers.CharField(source='subject_item.subject_item_id', label="受试者操作项ID", read_only=True)
    subject_id = serializers.CharField(source='subject.subject_id', label="受试者ID", read_only=True)
    ae_ai_result_list = AeAiResultListSerializers(many=True, allow_null=True)
    ae_medication_measures_list = AiMedicationMeasuresListSerializers(required=False, allow_null=True)
    visit_date_st = serializers.SerializerMethodField(label="访视开始日期", required=False)
    visit_date_cmpl = serializers.SerializerMethodField(label="访视结束日期", required=False)

    def get_visit_date_st(self, obj):
        if obj.subject_visit and obj.subject_visit.visit_date:
            visit_date_st = obj.subject_visit.visit_date.get('st')
            if visit_date_st:
                return obj.subject_visit.visit_date.get('st')[:10]
        return None

    def get_visit_date_cmpl(self, obj):
        if obj.subject_visit and obj.subject_visit.visit_date:
            visit_date_st = obj.subject_visit.visit_date.get('cmpl')
            if visit_date_st:
                return obj.subject_visit.visit_date.get('cmpl')[:10]
        return None

    class Meta:
        model = models.TestResult
        # fields = '__all__'  # ['test_type', 'test_code', 'test_name', 'test_unit', 'test_value', 'reference_value', 'reference_range_min', 'reference_range_max']
        exclude = ['project', 'project_site', 'subject', 'subject_epoch', 'patient', 'subject_visit', 'subject_item']


class OcrResultTextSerializer(serializers.ModelSerializer):
    id = serializers.CharField(label='数据id')
    class Meta:
        model = models.TestOcrResult
        # fields = '__all__'
        fields = ['id', 'subject_id', 'subject_item_id', 'ocr_text']


class OcrResultListRequestSerializer(serializers.Serializer):
    subject_id = serializers.CharField(label='受试者id')
    subject_epoch_id = serializers.CharField(label='阶段ID')
    subject_visit_id = serializers.CharField(label='访视ID')
    subject_item_id = serializers.CharField(label='访视项目ID')


class AeTrackerTaskSerializer(serializers.ModelSerializer):
    subject_item_id = serializers.CharField(source='subject_item.subject_item_id', label="受试者操作项ID")
    subject_id = serializers.CharField(source='subject.subject_id', label="受试者ID")
    class Meta:
        model = models.AeTrackerTask
        # fields = '__all__'
        exclude = ['project', 'project_site', 'subject', 'subject_epoch', 'patient', 'subject_visit', 'subject_item']



class AeTrackerTaskCreatRequestSerializer(serializers.ModelSerializer):
    subject_id = serializers.CharField(label="项目ID")
    subject_item_id = serializers.CharField(label="项目中心ID", required=False)
    subject_visit_id = serializers.CharField(source='subject_visit.subject_visit_id', label="受试者访视ID", required=False)

    class Meta:
        model = models.AeTrackerTask
        fields = ['subject_id', 'subject_visit_id', 'subject_item_id', 'category']
        # exclude = ['patient']  # , 'subject'


class AeTrackerTaskStatusRequestSerializer(serializers.Serializer):
    id = serializers.IntegerField(label='id')


from apps.subject.models import SubjectItem
class SubjectItemSerializer(serializers.ModelSerializer):
    # subject_id = serializers.CharField(source='subject.subject_id', read_only=True, label="受试者ID")
    # visit_date = serializers.CharField(source='subject_visit.visit_date', read_only=True, label="访视日期")
    # visit_label = serializers.CharField(source='subject_visit.label', read_only=True, label="访视名称")

    class Meta:
        model = SubjectItem
        exclude = ['project', 'project_site', 'subject']


class AeTrackerTaskListRequestSerializer(serializers.ModelSerializer):
    subject_id = serializers.CharField(label="项目ID")
    subject_item_id = serializers.CharField(label="项目中心ID")
    # id = serializers.CharField(label="ae被更新三角数据id", required=False)

    class Meta:
        model = models.AeTrackerTask
        fields = ['subject_id', 'subject_item_id']
        # exclude = ['patient']  # , 'subject'

from apps.system.models import OperationLog
class OperationLogListSerializer(serializers.ModelSerializer):

    class Meta:
        model = OperationLog
        fields = '__all__'#['subject_id', 'subject_item_id']
        # exclude = ['patient']  # , 'subject'


class AeTrackerOperationLogListRequestSerializer(serializers.ModelSerializer):
    subject_id = serializers.CharField(label="项目ID", required=False)
    subject_item_id = serializers.CharField(label="项目中心ID", required=False)
    id = serializers.CharField(label="ae被更新三角数据id", required=False)
    step = serializers.CharField(label="step步骤", required=False)

    class Meta:
        model = models.AeTrackerTask
        fields = ['subject_id', 'subject_item_id', 'id', 'step']
        # exclude = ['patient']  # , 'subject'


class ConfirmAiCtcaeSerializer(serializers.Serializer):
    subject_item_id = serializers.CharField(source='subject_item.subject_item_id', label="受试者操作项ID")
    subject_id = serializers.CharField(source='subject.subject_id', label="受试者ID")


class UpdateAeLevelSerializer(serializers.Serializer):
    # id = serializers.IntegerField(label="ID")
    test_name = serializers.CharField(label="修改项")
    ae_grade = serializers.CharField(label="AE修改值")
    medical_history_flag = serializers.CharField(label="是否病史标识：0-否 1-是")
    ae_name = serializers.CharField(label="AE名称")
    ae_desc = serializers.CharField(label="检查结果/AE事件描述")
    ae_meds = serializers.CharField(label="用药的结果")
    abnormal_symbol = serializers.CharField(label="检查结果标志文本")



class OcrResultSerializer(serializers.ModelSerializer):

    class Meta:
        model = models.TestResult
        # fields = '__all__'  # ['test_type', 'test_code', 'test_name', 'test_unit', 'test_value', 'reference_value', 'reference_range_min', 'reference_range_max']
        exclude = ['project', 'project_site', 'subject_epoch', 'patient', 'subject_visit', 'subject_medical_info']


class OperationLogSerializer(serializers.ModelSerializer):

    class Meta:
        model = OperationLog
        fields = '__all__'  # ['test_type', 'test_code', 'test_name', 'test_unit', 'test_value', 'reference_value', 'reference_range_min', 'reference_range_max']


class UpdateOCRHighLowRequestSerializer(serializers.Serializer):
    id = serializers.IntegerField(label="ID", required=False)
    # test_value = serializers.DecimalField(label="修改值", max_digits=10, decimal_places=2)
    # reference_range_min = serializers.CharField(label="参考值下限")
    # reference_range_max = serializers.CharField(label="参考值上限")
    test_value = serializers.CharField(label="修改值", required=False)
    reference_value = serializers.CharField(label="参考值", required=False)


class UpdateOCRHighLowResponseSerializer(serializers.Serializer):
    id = serializers.IntegerField(label="ID", required=False)
    # test_value = serializers.DecimalField(label="修改值", max_digits=10, decimal_places=2)
    test_flag = serializers.CharField(label="检查结果标志；0：正常，1：偏高，2：偏低", required=False)
    # reference_range_min = serializers.CharField(label="参考值下限")
    # reference_range_max = serializers.CharField(label="参考值上限")
    test_value = serializers.CharField(label="修改值", required=False)
    reference_value = serializers.CharField(label="参考值", required=False, allow_null=True, allow_blank=True)


class OcrResultAddSerializer(serializers.ModelSerializer):
    seq = serializers.FloatField(label="中间插入跟随排序", required=False)
    subject_id = serializers.CharField(label="项目ID", required=False)
    subject_item_id = serializers.CharField(label="项目中心ID", required=False)
    class Meta:
        model = models.TestResult
        fields = ['abnormal_symbol', 'ae_meds', 'seq', 'subject_id', 'subject_item_id', 'test_code', 'test_name', 'test_value', 'test_unit', 'reference_value', 'ae_grade', 'ae_name', 'ae_desc', 'medical_history_flag']  # '__all__'  #
        # exclude = ['project', 'project_site', 'subject', 'subject_epoch', 'patient', 'subject_visit', 'subject_item', 'subject_medical_info']

