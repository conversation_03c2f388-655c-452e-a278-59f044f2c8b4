# -*- coding: utf-8 -*-
"""
配置切换脚本

用于方便地切换并行/串行处理模式和调整相关参数
"""

import os
import sys
import logging

# 添加项目根目录到路径
current_script_path = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_script_path)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from script.test_result_format_ae_ocr.simple_config import (
    get_simple_config, update_simple_config, apply_preset_config, PRESET_CONFIGS
)

logger = logging.getLogger(__name__)


def show_current_config():
    """显示当前配置"""
    config = get_simple_config()
    
    print("🔧 当前并行处理配置:")
    print("=" * 50)
    print(f"启用并行处理: {'✅ 是' if config.enable_parallel else '❌ 否'}")
    print(f"最大并发数: {config.max_workers}")
    print(f"总超时时间: {config.timeout}秒")
    print(f"单页超时时间: {config.page_timeout}秒")
    print(f"成功率阈值: {config.success_rate_threshold:.1%}")
    print(f"部分失败时继续处理: {'✅ 是' if config.continue_on_partial_failure else '❌ 否'}")
    print(f"性能监控: {'✅ 启用' if config.enable_performance_monitoring else '❌ 禁用'}")
    print(f"详细日志: {'✅ 启用' if config.enable_detailed_logging else '❌ 禁用'}")
    print(f"调试模式: {'✅ 启用' if config.debug_mode else '❌ 禁用'}")
    print(f"强制串行处理: {'✅ 是' if config.force_serial_processing else '❌ 否'}")
    print("=" * 50)


def enable_parallel_mode(max_workers=3):
    """启用并行处理模式"""
    print(f"🚀 启用并行处理模式，并发数: {max_workers}")
    config = update_simple_config(
        enable_parallel=True,
        force_serial_processing=False,
        max_workers=max_workers
    )
    print("✅ 并行模式已启用")
    return config


def enable_serial_mode():
    """启用串行处理模式"""
    print("📝 启用串行处理模式")
    config = update_simple_config(
        enable_parallel=False,
        force_serial_processing=True
    )
    print("✅ 串行模式已启用")
    return config


def enable_debug_mode():
    """启用调试模式"""
    print("🐛 启用调试模式")
    config = update_simple_config(
        debug_mode=True,
        enable_detailed_logging=True,
        enable_performance_monitoring=True
    )
    print("✅ 调试模式已启用")
    return config


def disable_debug_mode():
    """禁用调试模式"""
    print("🔧 禁用调试模式")
    config = update_simple_config(
        debug_mode=False,
        enable_detailed_logging=False
    )
    print("✅ 调试模式已禁用")
    return config


def set_conservative_mode():
    """设置保守模式（低并发，高成功率要求）"""
    print("🛡️ 设置保守模式")
    config = apply_preset_config("conservative")
    print("✅ 保守模式已设置")
    return config


def set_balanced_mode():
    """设置平衡模式（中等并发，平衡的成功率要求）"""
    print("⚖️ 设置平衡模式")
    config = apply_preset_config("balanced")
    print("✅ 平衡模式已设置")
    return config


def set_aggressive_mode():
    """设置激进模式（高并发，较低成功率要求）"""
    print("🚀 设置激进模式")
    config = apply_preset_config("aggressive")
    print("✅ 激进模式已设置")
    return config


def interactive_config():
    """交互式配置"""
    print("🎛️ 交互式配置模式")
    print("=" * 50)
    
    while True:
        print("\n请选择操作:")
        print("1. 显示当前配置")
        print("2. 启用并行处理")
        print("3. 启用串行处理")
        print("4. 设置保守模式")
        print("5. 设置平衡模式")
        print("6. 设置激进模式")
        print("7. 启用调试模式")
        print("8. 禁用调试模式")
        print("9. 自定义配置")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-9): ").strip()
        
        if choice == "0":
            print("👋 退出配置")
            break
        elif choice == "1":
            show_current_config()
        elif choice == "2":
            workers = input("请输入并发数 (默认3): ").strip()
            workers = int(workers) if workers.isdigit() else 3
            enable_parallel_mode(workers)
        elif choice == "3":
            enable_serial_mode()
        elif choice == "4":
            set_conservative_mode()
        elif choice == "5":
            set_balanced_mode()
        elif choice == "6":
            set_aggressive_mode()
        elif choice == "7":
            enable_debug_mode()
        elif choice == "8":
            disable_debug_mode()
        elif choice == "9":
            custom_config()
        else:
            print("❌ 无效选择，请重新输入")


def custom_config():
    """自定义配置"""
    print("\n🎨 自定义配置")
    print("-" * 30)
    
    try:
        # 并行处理
        parallel = input("启用并行处理? (y/n, 默认y): ").strip().lower()
        enable_parallel = parallel != 'n'
        
        # 并发数
        if enable_parallel:
            workers = input("并发数 (1-10, 默认3): ").strip()
            max_workers = int(workers) if workers.isdigit() and 1 <= int(workers) <= 10 else 3
        else:
            max_workers = 1
        
        # 成功率阈值
        threshold = input("成功率阈值 (0.1-1.0, 默认0.8): ").strip()
        try:
            success_rate_threshold = float(threshold)
            if not (0.1 <= success_rate_threshold <= 1.0):
                success_rate_threshold = 0.8
        except ValueError:
            success_rate_threshold = 0.8
        
        # 部分失败时继续处理
        continue_on_failure = input("部分失败时继续处理? (y/n, 默认y): ").strip().lower()
        continue_on_partial_failure = continue_on_failure != 'n'
        
        # 性能监控
        monitoring = input("启用性能监控? (y/n, 默认y): ").strip().lower()
        enable_performance_monitoring = monitoring != 'n'
        
        # 应用配置
        config = update_simple_config(
            enable_parallel=enable_parallel,
            max_workers=max_workers,
            success_rate_threshold=success_rate_threshold,
            continue_on_partial_failure=continue_on_partial_failure,
            enable_performance_monitoring=enable_performance_monitoring,
            force_serial_processing=not enable_parallel
        )
        
        print("✅ 自定义配置已应用")
        show_current_config()
        
    except KeyboardInterrupt:
        print("\n❌ 配置已取消")
    except Exception as e:
        print(f"❌ 配置失败: {e}")


def show_preset_configs():
    """显示预设配置"""
    print("📋 可用的预设配置:")
    print("=" * 50)
    
    for name, config in PRESET_CONFIGS.items():
        print(f"\n🔧 {name.upper()}:")
        for key, value in config.items():
            print(f"  - {key}: {value}")


def main():
    """主函数"""
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "show":
            show_current_config()
        elif command == "parallel":
            workers = int(sys.argv[2]) if len(sys.argv) > 2 and sys.argv[2].isdigit() else 3
            enable_parallel_mode(workers)
        elif command == "serial":
            enable_serial_mode()
        elif command == "conservative":
            set_conservative_mode()
        elif command == "balanced":
            set_balanced_mode()
        elif command == "aggressive":
            set_aggressive_mode()
        elif command == "debug":
            enable_debug_mode()
        elif command == "presets":
            show_preset_configs()
        elif command == "interactive" or command == "i":
            interactive_config()
        else:
            print("❌ 未知命令")
            print_usage()
    else:
        interactive_config()


def print_usage():
    """打印使用说明"""
    print("📖 使用说明:")
    print("python config_switch.py [命令] [参数]")
    print("\n可用命令:")
    print("  show                 - 显示当前配置")
    print("  parallel [workers]   - 启用并行处理 (可选指定并发数)")
    print("  serial              - 启用串行处理")
    print("  conservative        - 设置保守模式")
    print("  balanced            - 设置平衡模式")
    print("  aggressive          - 设置激进模式")
    print("  debug               - 启用调试模式")
    print("  presets             - 显示预设配置")
    print("  interactive 或 i    - 交互式配置")
    print("\n示例:")
    print("  python config_switch.py show")
    print("  python config_switch.py parallel 5")
    print("  python config_switch.py balanced")


if __name__ == "__main__":
    main()
