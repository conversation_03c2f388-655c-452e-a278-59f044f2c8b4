"""
https://huggingface.co/OpenGVLab/InternVL2_5-78B
InternVL2_5-78B视觉大模型化验单内容提取测试
"""

import io
import os
import time
import json
import base64
import argparse
from functools import wraps
import requests, logging


def timing_decorator(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        print(f"## {list(args)} executed in {end_time - start_time:.2f} seconds")
        return result
    return wrapper


@timing_decorator
def process_image(image_path):
    """
    请求指定大模型 API，并返回结果
    """
    with open(image_path, "rb") as img_file:
        image_base64 = base64.b64encode(img_file.read()).decode("utf-8")

    url = 'http://192.168.230.1:1040/v1/chat/completions'

    body = {
        "model": "internvl",
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "image_url",
                        "image_url": image_base64
                    },
                    {
                        "type": "text",
                        "text": "1. 作为专业医生，你需要将用户提供的化验单内容整理成一个清晰的表格形式。2. 首先，仔细阅读化验单内容，识别出各项检测指标及其对应的结果。3. 将检测指标作为表格的行标题，包括但不限于以下内容：- 序号- 代号- 检测项目- 结果- 参考值- 单位4. 确保表格格式整齐，使用Markdown表格形式。5. 确保输出所有检测指标及其对应的结果。6. 结束后检查输出内容确保没有遗漏任何检测指标及其对应的结果。"
                    }
                ]
            }
        ],
        "max_tokens": 4096,
        "do_sample": True,
        "repetition_penalty": 1.00,
        "temperature": 0.01,
        "top_p": 0.001,
        "top_k": 1
    }

    response = requests.post(url, json=body)
    return response


def process_folder(folder_path, output_file):
    """
    遍历指定文件夹中的所有图片，调用大模型处理并将结果保存到一个txt文件。
    """
    with open(output_file, "w", encoding="utf-8") as output:
        for filename in os.listdir(folder_path):
            if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.webp')):  # Process image files
                image_path = os.path.join(folder_path, filename)
                print(f"Processing {filename}...")
                response = process_image(image_path)

                if response.status_code == 200:
                    result = response.json()
                    content = result["choices"][0]["message"]['content']
                    output.write(f"Result for {filename}:\n")
                    output.write(content + "\n\n")
                else:
                    output.write(f"Failed to process {filename}, status code: {response.status_code}\n")

                time.sleep(1)  # Optional: To avoid hitting API rate limits


def main():
    parser = argparse.ArgumentParser(description="Process images in a folder and save results to a text file.")
    parser.add_argument('folder_path', type=str, help="The path to the folder containing images.")
    parser.add_argument('output_file', type=str, help="The path to the output text file where results will be saved.")

    args = parser.parse_args()

    process_folder(args.folder_path, args.output_file)
    print("Processing completed, results saved to:", args.output_file)


if __name__ == '__main__':
    main()
