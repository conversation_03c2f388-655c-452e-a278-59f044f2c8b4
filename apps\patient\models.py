from django.db import models
from common.models import BaseModel, BaseFileModel


class Patient(BaseModel):
    """患者信息"""
    patient_id = models.CharField(max_length=255, unique=True, verbose_name="患者ID（项目号+中心编号+受试者编号）")

    # 一（患者信息）对（受试者信息）一
    subject = models.OneToOneField(
        'subject.Subject',
        on_delete=models.CASCADE,
        db_constraint=False,
        related_name='patient',
        to_field='subject_id',
        db_column='subject_id',
        verbose_name="受试者ID",
        db_index=True
    )

    class Meta:
        verbose_name = "患者信息"
        verbose_name_plural = "患者信息"
        db_table = 'patient_info'
