# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

SMO AI Backend is a Django-based medical AI system that focuses on clinical trial data processing, OCR (Optical Character Recognition), and adverse event tracking. The system integrates multiple AI services for medical document analysis, text processing, and data anonymization.

## Development Commands

### Django Management Commands
```bash
# Install dependencies
pip install -r requirements.txt

# Database operations
python manage.py makemigrations
python manage.py migrate
python manage.py createcachetable

# Development server
python manage.py runserver 0.0.0.0:8000

# Static files
python manage.py collectstatic

# Create superuser
python manage.py createsuperuser
```

### Scheduled Tasks
```bash
# Start cronicle for scheduled tasks
bash cronicle.sh

# Export cronicle configuration
/opt/cronicle/bin/control.sh export
```

### Docker Operations
```bash
# Build image
docker build -t smo-ai-backend .

# Run web service container
docker run --name smo-ai-backend-instance-test --env DJANGO_SETTINGS_MODULE=smo.settings.test --env APP_DEPLOY_HOST=************** --env APP_DEPLOY_PORT=31216 -p 8000:8000 -d smo-ai-backend

# Run cron container
docker run --name smo-ai-backend-cron-test --env DJANGO_SETTINGS_MODULE=smo.settings.test -p 3012:3012 -d smo-ai-backend /bin/bash -c "bash cronicle.sh && tail -f /dev/null"
```

## Architecture Overview

### Core Components

**Django Apps Structure:**
- `apps/ae_tracker/` - Adverse Event tracking and OCR processing
- `apps/medical_collection/` - Medical document collection and processing
- `apps/subject_medical/` - Subject medical information management
- `apps/users/` - User management and authentication
- `apps/project/` - Project and site management
- `apps/subject/` - Subject and visit management
- `apps/system/` - System logging and model invocation tracking

**Common Services (`common/`):**
- `clients/llm_client.py` - LLM API integration (DeepSeek-R1, Qwen3)
- `ocr_mask/` - OCR processing and HIPAA de-identification using PocketFlow
- `minio_client.py` - MinIO file storage client
- `tools.py` - Utility functions and NACOS configuration loading

**Scheduled Tasks (`dags/`):**
- Airflow DAGs for various automated processing tasks
- AE grade recognition, medication measures, CRF generation
- External masking tasks and medical file processing

### Key Technologies

- **Framework:** Django 4.1.5 with Django REST Framework
- **Database:** MySQL with NACOS for configuration management
- **AI/ML:** DeepSeek-R1, Qwen3 models, custom OCR processing
- **Task Processing:** PocketFlow for node-based workflows, Airflow for scheduled tasks
- **File Storage:** MinIO for medical document storage
- **Containerization:** Docker with Cronicle for task scheduling

### PocketFlow Integration

The system uses PocketFlow for complex OCR and de-identification workflows:

**Core Nodes (`common/ocr_mask/nodes.py`):**
- `OCRProcessingNode` - OCR recognition with image correction
- `HippaDeidentifyNode` - HIPAA compliant de-identification
- `MaskImageNode` - Image masking for sensitive content

**Configuration Files (.cursorrules):**
- `guide_for_pocketflow.mdc` - Guidelines for PocketFlow development
- `ae-grade-system.mdc` - AE grade recognition system documentation

### Database Schema

**Main Models:**
- `TestResult` - Medical test results with AI analysis
- `AeTrackerTask` - Adverse event tracking tasks
- `SubjectMedicalInfo` - Subject medical information with OCR status
- `ModelInvocationLog` - LLM API usage tracking

**Relationships:**
- Projects → Sites → Subjects → Visits → Medical Info → Test Results
- Foreign key relationships use `db_constraint=False` for flexibility

### Environment Configuration

**Settings Files:**
- `smo/settings/test.py` - Test environment configuration
- `smo/settings/uat.py` - UAT environment configuration
- `smo/settings/prod.py` - Production environment configuration

**Key Configuration:**
- NACOS for dynamic configuration management
- Multiple LLM service endpoints (DeepSeek, Qwen3)
- HIPAA de-identification service integration
- MinIO file storage configuration

### Development Patterns

**LLM Integration:**
- Use `common.clients.llm_client` for all LLM API calls
- Supports DeepSeek-R1-Distill-Qwen-32B and Qwen3-32B models
- Includes logging decorator for API usage tracking

**OCR Processing:**
- PocketFlow-based workflow for document processing
- HIPAA compliant de-identification
- Image correction and sensitive content masking

**Task Management:**
- Airflow DAGs for scheduled processing
- Cronicle for task execution and monitoring
- Django management commands for manual operations

### API Integration

**External Services:**
- DingTalk for authentication and notifications
- Dify for chat applications
- HIPAA de-identification service
- Multiple LLM providers

**Authentication:**
- Custom ERP system JWT authentication
- DingTalk integration for user authentication
- Project-based access control with whitelisting

### File Organization

**PocketFlow Projects:**
- `script/ae_grade/` - AE grade recognition system
- `script/test_result_format_ae_ocr/` - Test result OCR processing
- `zcw_test/PocketFlow/` - Medical OCR PocketFlow implementation

**Utility Scripts:**
- `script/` - Various processing scripts and utilities
- `dags/` - Airflow DAG definitions
- `etc/` - Configuration files for different environments