# Generated by Django 4.1.5 on 2025-03-26 22:00

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("patient", "0002_alter_patient_update_time"),
        (
            "subject",
            "0002_subjectepoch_alter_subject_update_time_subjectvisit_and_more",
        ),
        ("project", "0002_alter_project_update_time_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="SubjectMedicalFile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "delete_flag",
                    models.SmallIntegerField(
                        choices=[(0, "未删除"), (1, "已删除")],
                        db_index=True,
                        default=0,
                        verbose_name="删除标志（0：未删除；1：已删除）",
                    ),
                ),
                (
                    "data_version",
                    models.PositiveIntegerField(default=0, verbose_name="数据版本"),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True, db_index=True, verbose_name="创建时间"
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name="更新时间"
                    ),
                ),
                (
                    "create_user",
                    models.CharField(max_length=255, null=True, verbose_name="创建人工号"),
                ),
                (
                    "create_name",
                    models.CharField(max_length=255, null=True, verbose_name="创建人姓名"),
                ),
                (
                    "update_user",
                    models.CharField(max_length=255, null=True, verbose_name="更新人工号"),
                ),
                (
                    "update_name",
                    models.CharField(max_length=255, null=True, verbose_name="更新人姓名"),
                ),
                (
                    "original_filename",
                    models.CharField(max_length=255, verbose_name="原始文件名"),
                ),
                ("bucket_name", models.CharField(max_length=100, verbose_name="存储桶名称")),
                ("object_name", models.CharField(max_length=500, verbose_name="对象名称")),
                ("content_type", models.CharField(max_length=100, verbose_name="文件类型")),
                ("size", models.BigIntegerField(verbose_name="文件大小（字节）")),
                (
                    "hash",
                    models.CharField(
                        db_index=True, max_length=255, verbose_name="文件哈希（sha256）"
                    ),
                ),
            ],
            options={
                "verbose_name": "受试者医疗文件",
                "verbose_name_plural": "受试者医疗文件",
                "db_table": "subject_medical_file",
            },
        ),
        migrations.CreateModel(
            name="SubjectMedicalFileMasked",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "delete_flag",
                    models.SmallIntegerField(
                        choices=[(0, "未删除"), (1, "已删除")],
                        db_index=True,
                        default=0,
                        verbose_name="删除标志（0：未删除；1：已删除）",
                    ),
                ),
                (
                    "data_version",
                    models.PositiveIntegerField(default=0, verbose_name="数据版本"),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True, db_index=True, verbose_name="创建时间"
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name="更新时间"
                    ),
                ),
                (
                    "create_user",
                    models.CharField(max_length=255, null=True, verbose_name="创建人工号"),
                ),
                (
                    "create_name",
                    models.CharField(max_length=255, null=True, verbose_name="创建人姓名"),
                ),
                (
                    "update_user",
                    models.CharField(max_length=255, null=True, verbose_name="更新人工号"),
                ),
                (
                    "update_name",
                    models.CharField(max_length=255, null=True, verbose_name="更新人姓名"),
                ),
                (
                    "original_filename",
                    models.CharField(max_length=255, verbose_name="原始文件名"),
                ),
                ("bucket_name", models.CharField(max_length=100, verbose_name="存储桶名称")),
                ("object_name", models.CharField(max_length=500, verbose_name="对象名称")),
                ("content_type", models.CharField(max_length=100, verbose_name="文件类型")),
                ("size", models.BigIntegerField(verbose_name="文件大小（字节）")),
                (
                    "hash",
                    models.CharField(
                        db_index=True, max_length=255, verbose_name="文件哈希（sha256）"
                    ),
                ),
            ],
            options={
                "verbose_name": "受试者医疗脱敏文件",
                "verbose_name_plural": "受试者医疗脱敏文件",
                "db_table": "subject_medical_file_masked",
            },
        ),
        migrations.CreateModel(
            name="SubjectMedicalInfo",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "delete_flag",
                    models.SmallIntegerField(
                        choices=[(0, "未删除"), (1, "已删除")],
                        db_index=True,
                        default=0,
                        verbose_name="删除标志（0：未删除；1：已删除）",
                    ),
                ),
                (
                    "data_version",
                    models.PositiveIntegerField(default=0, verbose_name="数据版本"),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True, db_index=True, verbose_name="创建时间"
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name="更新时间"
                    ),
                ),
                (
                    "create_user",
                    models.CharField(max_length=255, null=True, verbose_name="创建人工号"),
                ),
                (
                    "create_name",
                    models.CharField(max_length=255, null=True, verbose_name="创建人姓名"),
                ),
                (
                    "update_user",
                    models.CharField(max_length=255, null=True, verbose_name="更新人工号"),
                ),
                (
                    "update_name",
                    models.CharField(max_length=255, null=True, verbose_name="更新人姓名"),
                ),
                ("row_ocr", models.JSONField(null=True, verbose_name="原始OCR识别结果")),
                ("ocr_time", models.DateTimeField(null=True, verbose_name="OCR时间")),
                ("test_time", models.DateTimeField(null=True, verbose_name="检查时间")),
                ("report_time", models.DateTimeField(null=True, verbose_name="报告时间")),
                (
                    "file",
                    models.OneToOneField(
                        db_column="file_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subject_medical_info",
                        to="subject_medical.subjectmedicalfile",
                        verbose_name="患者医疗文件ID",
                    ),
                ),
                (
                    "file_masked",
                    models.OneToOneField(
                        db_column="file_masked_id",
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subject_medical_info",
                        to="subject_medical.subjectmedicalfilemasked",
                        verbose_name="患者医疗脱敏文件ID",
                    ),
                ),
                (
                    "patient",
                    models.ForeignKey(
                        db_column="patient_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subject_medical_infos",
                        to="patient.patient",
                        to_field="patient_id",
                        verbose_name="患者ID",
                    ),
                ),
                (
                    "project",
                    models.ForeignKey(
                        db_column="project_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subject_medical_infos",
                        to="project.project",
                        to_field="project_id",
                        verbose_name="项目ID",
                    ),
                ),
                (
                    "project_site",
                    models.ForeignKey(
                        db_column="project_site_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subject_medical_infos",
                        to="project.projectsite",
                        to_field="project_site_id",
                        verbose_name="项目中心ID",
                    ),
                ),
                (
                    "subject",
                    models.ForeignKey(
                        db_column="subject_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subject_medical_infos",
                        to="subject.subject",
                        to_field="subject_id",
                        verbose_name="受试者ID",
                    ),
                ),
                (
                    "subject_epoch",
                    models.ForeignKey(
                        db_column="subject_epoch_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subject_medical_infos",
                        to="subject.subjectepoch",
                        to_field="subject_epoch_id",
                        verbose_name="受试者阶段ID）",
                    ),
                ),
                (
                    "subject_item",
                    models.ForeignKey(
                        db_column="subject_item_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subject_medical_infos",
                        to="subject.subjectitem",
                        to_field="subject_item_id",
                        verbose_name="受试者操作项ID）",
                    ),
                ),
                (
                    "subject_visit",
                    models.ForeignKey(
                        db_column="subject_visit_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subject_medical_infos",
                        to="subject.subjectvisit",
                        to_field="subject_visit_id",
                        verbose_name="受试者访视ID）",
                    ),
                ),
            ],
            options={
                "verbose_name": "受试者医疗信息",
                "verbose_name_plural": "受试者医疗信息",
                "db_table": "subject_medical_info",
            },
        ),
    ]
