#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Rerank功能全面测试
=================

测试目标：
1. 评估rerank系统的准确性和性能
2. 对比rerank结果与精确搜索结果
3. 输出清晰的评估报告，便于快速评估rerank质量

作者：Augment Agent
创建时间：2025-07-16
"""

import sys
import os
import time
from typing import List, Dict, Any, Tuple

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, '../../..'))
sys.path.insert(0, project_root)

def print_section_header(title: str, char: str = "="):
    """打印章节标题"""
    print(f"\n{char * 60}")
    print(f"🔍 {title}")
    print(f"{char * 60}")

def print_subsection_header(title: str):
    """打印子章节标题"""
    print(f"\n{'─' * 40}")
    print(f"📊 {title}")
    print(f"{'─' * 40}")

def exact_search_baseline(query: str, rules: List[Dict]) -> List[Dict]:
    """
    精确搜索基线方法
    使用字符串包含匹配作为baseline对比
    """
    matches = []
    query_lower = query.lower()
    
    for rule in rules:
        # 检查AE名称匹配
        if query_lower in rule['ae_name'].lower() or rule['ae_name'].lower() in query_lower:
            matches.append({
                'rule': rule,
                'match_type': 'ae_name',
                'match_score': 1.0
            })
            continue
            
        # 检查检查项匹配
        for check_item in rule['check_items']:
            if query_lower in check_item.lower() or check_item.lower() in query_lower:
                matches.append({
                    'rule': rule,
                    'match_type': 'check_item',
                    'match_score': 0.9
                })
                break
    
    # 按匹配分数排序
    matches.sort(key=lambda x: x['match_score'], reverse=True)
    return matches

def test_single_query_rerank(query: str, rules: List[Dict], search_texts: List[str]) -> Dict[str, Any]:
    """
    测试单个查询的rerank结果
    """
    try:
        from common.clients.rerank_client import call_rerank
        
        # 调用rerank服务
        start_time = time.time()
        rerank_results = call_rerank(
            query=query,
            texts=search_texts,
            raw_scores=True,
            return_text=False,
            truncate=True,
            truncation_direction="Right"
        )
        rerank_time = time.time() - start_time
        
        # 获取精确搜索结果
        start_time = time.time()
        exact_results = exact_search_baseline(query, rules)
        exact_time = time.time() - start_time
        
        # 处理rerank结果
        rerank_matches = []
        for result in rerank_results[:5]:  # 只取前5个
            rule_index = result['index']
            if 0 <= rule_index < len(rules):
                rerank_matches.append({
                    'rule': rules[rule_index],
                    'score': result['score'],
                    'rank': len(rerank_matches) + 1
                })
        
        return {
            'query': query,
            'rerank_results': rerank_matches,
            'exact_results': exact_results[:5],  # 只取前5个
            'rerank_time': rerank_time,
            'exact_time': exact_time,
            'success': True,
            'error': None
        }
        
    except Exception as e:
        return {
            'query': query,
            'rerank_results': [],
            'exact_results': [],
            'rerank_time': 0,
            'exact_time': 0,
            'success': False,
            'error': str(e)
        }

def display_comparison_results(result: Dict[str, Any]):
    """
    显示单个查询的对比结果
    """
    query = result['query']
    print(f"\n🎯 查询: '{query}'")
    
    if not result['success']:
        print(f"❌ 测试失败: {result['error']}")
        return
    
    print(f"⏱️  处理时间 - Rerank: {result['rerank_time']:.3f}s | 精确搜索: {result['exact_time']:.3f}s")
    
    # 显示Rerank结果
    print(f"\n🔄 Rerank结果 (Top 5):")
    if result['rerank_results']:
        for i, match in enumerate(result['rerank_results']):
            rule = match['rule']
            score = match['score']
            if score > 0 :
                print("【精度高】")
            check_items = ', '.join(rule['check_items'])
            ae_definition = rule.get('ae_definition')
            print(f"  {i+1}. {score:.4f} | {rule['ae_name']} | 检查项: [{check_items}] | 定义: {ae_definition}")
    else:
        print("  无结果")
    
    # 显示精确搜索结果
    print(f"\n🎯 精确搜索结果 (Top 5):")
    if result['exact_results']:
        for i, match in enumerate(result['exact_results']):
            rule = match['rule']
            match_type = match['match_type']
            match_score = match['match_score']
            check_items = ', '.join(rule['check_items'])
            print(f"  {i+1}. {match_score:.1f} | {rule['ae_name']} | 匹配类型: {match_type} | 检查项: [{check_items}]")
    else:
        print("  无结果")

def calculate_accuracy_metrics(rerank_results: List[Dict], exact_results: List[Dict]) -> Dict[str, float]:
    """
    计算准确性指标
    """
    if not exact_results:
        return {'precision': 0.0, 'recall': 0.0, 'overlap_ratio': 0.0}
    
    # 获取精确搜索的AE名称集合
    exact_ae_names = {match['rule']['ae_name'] for match in exact_results}
    
    # 获取rerank结果的AE名称集合
    rerank_ae_names = {match['rule']['ae_name'] for match in rerank_results}
    
    if not rerank_ae_names:
        return {'precision': 0.0, 'recall': 0.0, 'overlap_ratio': 0.0}
    
    # 计算重叠
    overlap = exact_ae_names.intersection(rerank_ae_names)
    
    # 计算指标
    precision = len(overlap) / len(rerank_ae_names) if rerank_ae_names else 0.0
    recall = len(overlap) / len(exact_ae_names) if exact_ae_names else 0.0
    overlap_ratio = len(overlap) / max(len(exact_ae_names), len(rerank_ae_names))
    
    return {
        'precision': precision,
        'recall': recall,
        'overlap_ratio': overlap_ratio
    }

def run_comprehensive_rerank_test():
    """
    运行全面的rerank测试
    """
    print_section_header("Rerank功能全面测试")
    
    try:
        # 导入必要模块
        from script.ae_grade.data.ctcae_rules import get_ctcae_rules, get_search_texts
        from script.ae_grade.test.test_rerank_data import list as test_data_list
        
        # 加载CTCAE规则和搜索文本
        print("📚 加载CTCAE规则和搜索文本...")
        rules = get_ctcae_rules()
        search_texts = get_search_texts()
        
        print(f"✅ 成功加载 {len(rules)} 条CTCAE规则")
        print(f"✅ 生成 {len(search_texts)} 个搜索文本")

        # 定义重点测试案例（已知在CTCAE中有对应的检验项目）
        priority_test_cases = [
            {"test_name": "白细胞计数", "description": "基础血液检验，应该能精确匹配到白细胞相关AE"},
            {"test_name": "血红蛋白", "description": "血红蛋白检验，应该能匹配到贫血相关AE"},
            {"test_name": "血小板计数", "description": "血小板检验，应该能匹配到血小板减少AE"},
            {"test_name": "丙氨酸氨基转移酶", "description": "肝功能检验，应该能匹配到ALT升高AE"},
            {"test_name": "肌酐", "description": "肾功能检验，应该能匹配到肌酐升高AE"},
            {"test_name": "凝血酶原时间", "description": "凝血功能检验，应该能匹配到凝血异常AE"}
        ]

        # 测试重点案例
        print_subsection_header("重点测试案例")
        priority_results = []

        for i, test_case in enumerate(priority_test_cases):
            print(f"\n[{i+1}/{len(priority_test_cases)}] 测试: {test_case['test_name']}")
            print(f"描述: {test_case['description']}")

            result = test_single_query_rerank(
                test_case['test_name'],
                rules,
                search_texts
            )
            priority_results.append(result)
            display_comparison_results(result)

        # 测试数据文件中的项目
        print_subsection_header("测试数据文件案例")
        data_file_results = []

        for i, test_item in enumerate(test_data_list):
            print(f"\n[{i+1}/{len(test_data_list)}] 测试: {test_item}")
            print(f"描述: 来自测试数据文件的检验项目")

            test_item = clean_special_symbols(test_item)

            result = test_single_query_rerank(test_item, rules, search_texts)
            data_file_results.append(result)
            display_comparison_results(result)

        # 生成综合报告
        print_section_header("综合评估报告")
        generate_summary_report(priority_results, data_file_results, [])
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


def clean_special_symbols(text: str) -> str:
    """
    清理test_code和test_name中的特殊符号
    移除 *、★、★、●、▲、▼、◆、■、□、+ 等特殊符号
    """
    # [FIX] 确保输入是字符串类型
    text = str(text) if text is not None else ''

    if not text:
        return text

    # 移除常见的特殊符号
    special_symbols = ['*', '★', '★', '●', '▲', '▼', '◆', '■', '□', '+', '△', '○', '◇','(干化学)','(尿流式)','(镜检)']
    cleaned_text = text
    for symbol in special_symbols:
        cleaned_text = cleaned_text.replace(symbol, '')

    return cleaned_text.strip()


def generate_summary_report(priority_results: List[Dict], edge_results: List[Dict], performance_results: List[Dict]):
    """
    生成综合评估报告
    """
    all_results = priority_results + edge_results + performance_results
    successful_results = [r for r in all_results if r['success']]

    if not successful_results:
        print("❌ 没有成功的测试结果，无法生成报告")
        return

    # 计算总体统计
    total_tests = len(all_results)
    success_rate = len(successful_results) / total_tests * 100
    avg_rerank_time = sum(r['rerank_time'] for r in successful_results) / len(successful_results)
    avg_exact_time = sum(r['exact_time'] for r in successful_results) / len(successful_results)

    print(f"📈 总体统计:")
    print(f"  测试总数: {total_tests}")
    print(f"  成功率: {success_rate:.1f}%")
    print(f"  平均Rerank时间: {avg_rerank_time:.3f}s")
    print(f"  平均精确搜索时间: {avg_exact_time:.3f}s")
    print(f"  性能比较: Rerank比精确搜索{'快' if avg_rerank_time < avg_exact_time else '慢'} {abs(avg_rerank_time - avg_exact_time):.3f}s")

    # 计算准确性指标
    accuracy_metrics = []
    for result in successful_results:
        metrics = calculate_accuracy_metrics(result['rerank_results'], result['exact_results'])
        accuracy_metrics.append(metrics)

    if accuracy_metrics:
        avg_precision = sum(m['precision'] for m in accuracy_metrics) / len(accuracy_metrics)
        avg_recall = sum(m['recall'] for m in accuracy_metrics) / len(accuracy_metrics)
        avg_overlap = sum(m['overlap_ratio'] for m in accuracy_metrics) / len(accuracy_metrics)

        print(f"\n🎯 准确性指标:")
        print(f"  平均精确率: {avg_precision:.3f}")
        print(f"  平均召回率: {avg_recall:.3f}")
        print(f"  平均重叠率: {avg_overlap:.3f}")

    # 重点案例分析
    print(f"\n🔍 重点案例分析:")
    for result in priority_results:
        if result['success']:
            query = result['query']
            rerank_count = len(result['rerank_results'])
            exact_count = len(result['exact_results'])
            metrics = calculate_accuracy_metrics(result['rerank_results'], result['exact_results'])

            print(f"  {query}: Rerank={rerank_count}个, 精确={exact_count}个, 重叠率={metrics['overlap_ratio']:.2f}")

    # 详细分析和建议
    print_section_header("详细分析和改进建议", "=")

    # 1. Rerank质量分析
    print("🔍 Rerank质量分析:")

    # 统计Top1准确率
    top1_correct = 0
    top1_total = 0
    for result in priority_results:
        if result['success'] and result['exact_results']:
            top1_total += 1
            if result['rerank_results']:
                rerank_top1 = result['rerank_results'][0]['rule']['ae_name']
                exact_top1 = result['exact_results'][0]['rule']['ae_name']
                if rerank_top1 == exact_top1:
                    top1_correct += 1

    if top1_total > 0:
        top1_accuracy = top1_correct / top1_total * 100
        print(f"  Top1准确率: {top1_accuracy:.1f}% ({top1_correct}/{top1_total})")

    # 分析高质量匹配案例
    print(f"\n✅ 高质量匹配案例:")
    for result in priority_results:
        if result['success']:
            metrics = calculate_accuracy_metrics(result['rerank_results'], result['exact_results'])
            if metrics['overlap_ratio'] >= 0.3:  # 重叠率>=30%认为是高质量
                query = result['query']
                if result['rerank_results']:
                    top_ae = result['rerank_results'][0]['rule']['ae_name']
                    top_score = result['rerank_results'][0]['score']
                    print(f"  {query} -> {top_ae} (分数: {top_score:.3f}, 重叠率: {metrics['overlap_ratio']:.2f})")

    # 分析问题案例
    print(f"\n⚠️  需要关注的案例:")
    for result in priority_results:
        if result['success']:
            metrics = calculate_accuracy_metrics(result['rerank_results'], result['exact_results'])
            if metrics['overlap_ratio'] < 0.3:  # 重叠率<30%需要关注
                query = result['query']
                if result['rerank_results']:
                    top_ae = result['rerank_results'][0]['rule']['ae_name']
                    top_score = result['rerank_results'][0]['score']
                    expected = [r['rule']['ae_name'] for r in result['exact_results'][:2]]
                    print(f"  {query} -> {top_ae} (分数: {top_score:.3f})")
                    print(f"    期望: {expected}")

    # 2. 性能分析
    print(f"\n⚡ 性能分析:")
    print(f"  Rerank服务平均响应时间: {avg_rerank_time:.3f}s")
    print(f"  单次查询成本: ~{avg_rerank_time * 1000:.0f}ms")

    # 分析响应时间分布
    rerank_times = [r['rerank_time'] for r in successful_results]
    min_time = min(rerank_times)
    max_time = max(rerank_times)
    print(f"  响应时间范围: {min_time:.3f}s - {max_time:.3f}s")

    # 3. 改进建议
    print(f"\n💡 改进建议:")

    if avg_precision < 0.5:
        print("  1. 精确率偏低，建议:")
        print("     - 优化搜索文本构建策略，增加关键词权重")
        print("     - 考虑引入领域特定的预训练模型")
        print("     - 调整rerank模型的阈值参数")

    if avg_recall < 0.7:
        print("  2. 召回率偏低，建议:")
        print("     - 扩展CTCAE规则的同义词和别名")
        print("     - 增加检查项的多种表达方式")
        print("     - 考虑使用模糊匹配作为补充")

    if avg_rerank_time > 0.2:
        print("  3. 性能优化建议:")
        print("     - 考虑批量处理多个查询")
        print("     - 实现结果缓存机制")
        print("     - 评估是否需要更快的rerank服务")

    print("  4. 通用改进建议:")
    print("     - 建立更全面的测试数据集")
    print("     - 定期评估和调优rerank参数")
    print("     - 考虑结合精确匹配和rerank的混合策略")
    print("     - 收集实际使用中的反馈数据进行持续优化")

if __name__ == "__main__":
    run_comprehensive_rerank_test()
