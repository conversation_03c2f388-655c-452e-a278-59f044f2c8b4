users/admin - {"username":"admin","password":"$2a$10$/WJUGk2xQEKjw0KNZgcbHeSFngu2MoNZoLIhfy8LZw.GIoJl.7X7a","full_name":"Administrator","email":"<EMAIL>","active":1,"modified":1683611526,"created":1683597570,"salt":"341a44a6bc13718afa2af41841503d7de395136d532292e1c2879b450e82d2c7","privileges":{"admin":1}}
global/users - {"page_size":100,"first_page":0,"last_page":0,"length":1,"type":"list"}
global/users/0 - {"type":"list_page","items":[{"username":"admin"}]}
global/schedule - {"page_size":50,"first_page":0,"last_page":0,"length":12,"type":"list"}
global/schedule/0 - {"type":"list_page","items":[{"enabled":1,"params":{"script":"#!/bin/sh\n\n# Enter your shell script code here\n\ncd /app\n\npython3.9 -m script.sync_ot_subject_item","annotate":0,"json":0},"timing":{"hours":[3],"minutes":[0]},"max_children":1,"timeout":21600,"catch_up":0,"queue_max":1000,"timezone":"Asia/Shanghai","plugin":"shellplug","title":"OT数据同步-受试者访视操作项","category":"general","target":"allgrp","algo":"random","multiplex":0,"stagger":0,"retries":0,"retry_delay":0,"detached":0,"queue":0,"chain":"","chain_error":"","notify_success":"","notify_fail":"","web_hook":"","cpu_limit":0,"cpu_sustain":0,"memory_limit":0,"memory_sustain":0,"log_max_size":0,"notes":"","id":"em8q03s550s","modified":1742998185,"created":1742998165,"username":"admin"},{"enabled":1,"params":{"script":"#!/bin/sh\n\n# Enter your shell script code here\n\ncd /app\n\npython3.9 -m script.sync_ot_subject_visit","annotate":0,"json":0},"timing":{"hours":[3],"minutes":[0]},"max_children":1,"timeout":21600,"catch_up":0,"queue_max":1000,"timezone":"Asia/Shanghai","plugin":"shellplug","title":"OT数据同步-受试者访视","category":"general","target":"allgrp","algo":"random","multiplex":0,"stagger":0,"retries":0,"retry_delay":0,"detached":0,"queue":0,"chain":"","chain_error":"","notify_success":"","notify_fail":"","web_hook":"","cpu_limit":0,"cpu_sustain":0,"memory_limit":0,"memory_sustain":0,"log_max_size":0,"notes":"","id":"em8q031q20m","modified":1742998131,"created":1742998131,"username":"admin"},{"enabled":1,"params":{"script":"#!/bin/sh\n\n# Enter your shell script code here\n\ncd /app\n\npython3.9 -m script.sync_ot_subject_epoch","annotate":0,"json":0},"timing":{"hours":[3],"minutes":[0]},"max_children":1,"timeout":21600,"catch_up":0,"queue_max":1000,"timezone":"Asia/Shanghai","plugin":"shellplug","title":"OT数据同步-受试者访视阶段","category":"general","target":"allgrp","algo":"random","multiplex":0,"stagger":0,"retries":0,"retry_delay":0,"detached":0,"queue":0,"chain":"","chain_error":"","notify_success":"","notify_fail":"","web_hook":"","cpu_limit":0,"cpu_sustain":0,"memory_limit":0,"memory_sustain":0,"log_max_size":0,"notes":"","id":"em8q002v00b","modified":1742998066,"created":1742997993,"username":"admin"},{"enabled":1,"params":{"script":"#!/bin/sh\n\n#Enter your shell script code here\n\ncd /app\n\n/usr/bin/python3.9 -m script.ae_grade_recognition","annotate":0,"json":0},"timing":{"minutes":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59]},"max_children":2,"timeout":36000,"catch_up":0,"queue_max":1000,"timezone":"Asia/Shanghai","plugin":"shellplug","title":"访视详情-AE等级识别","category":"general","target":"allgrp","algo":"random","multiplex":0,"stagger":0,"retries":0,"retry_delay":0,"detached":0,"queue":0,"chain":"","chain_error":"","notify_success":"","notify_fail":"","web_hook":"","cpu_limit":0,"cpu_sustain":0,"memory_limit":10737418240,"memory_sustain":0,"log_max_size":0,"notes":"","id":"em8inqgbb0u","modified":1742554125,"created":1742554125,"username":"admin"},{"enabled":1,"params":{"script":"#!/bin/sh\n\n# Enter your shell script code here\n\ncd /app\n\n/usr/bin/python3.9 -m script.test_result_format","annotate":0,"json":0},"timing":{"minutes":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59]},"max_children":1,"timeout":36000,"catch_up":0,"queue_max":1000,"timezone":"Asia/Shanghai","plugin":"shellplug","title":"访视详情-检验单数据提取","category":"general","target":"allgrp","algo":"random","multiplex":0,"stagger":0,"retries":0,"retry_delay":0,"detached":0,"queue":0,"chain":"","chain_error":"","notify_success":"","notify_fail":"","web_hook":"","cpu_limit":0,"cpu_sustain":0,"memory_limit":10737418240,"memory_sustain":0,"log_max_size":0,"notes":"","id":"em8inlw010j","modified":**********,"created":**********,"username":"admin"},{"enabled":1,"params":{"script":"#!/bin/sh\n\n# Enter your shell script code here\n\ncd /app\n\n/usr/bin/python3.9 -m script.subject_medical_file_mask","annotate":0,"json":0},"timing":{"minutes":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59]},"max_children":1,"timeout":36000,"catch_up":0,"queue_max":1000,"timezone":"Asia/Shanghai","plugin":"shellplug","title":"访视详情-病历文件打码","category":"general","target":"allgrp","algo":"random","multiplex":0,"stagger":0,"retries":0,"retry_delay":0,"detached":0,"queue":0,"chain":"","chain_error":"","notify_success":"","notify_fail":"","web_hook":"","cpu_limit":0,"cpu_sustain":0,"memory_limit":10737418240,"memory_sustain":0,"log_max_size":0,"notes":"","id":"em8ini7ed09","modified":**********,"created":**********,"username":"admin"},{"enabled":1,"params":{"script":"#!/bin/sh\n\n#Enter your shell script code here\n\ncd /app\n\n/usr/bin/python3.9 -m script.temp","annotate":0,"json":0},"timing":{"minutes":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59]},"max_children":2,"timeout":36000,"catch_up":0,"queue_max":1000,"timezone":"Asia/Shanghai","plugin":"shellplug","title":"病历归集-CRF生成","category":"general","target":"allgrp","algo":"random","multiplex":0,"stagger":0,"retries":0,"retry_delay":0,"detached":0,"queue":0,"chain":"","chain_error":"","notify_success":"","notify_fail":"","web_hook":"","cpu_limit":0,"cpu_sustain":0,"memory_limit":10737418240,"memory_sustain":0,"log_max_size":0,"notes":"","id":"em7tayha002","modified":**********,"created":**********,"username":"admin"},{"enabled":1,"params":{"script":"#!/bin/sh\n\n#Enter your shell script code here\n\n\ncd /app\n\n/usr/bin/python3.9 -m script.medical_file_mask","annotate":0,"json":0},"timing":{"minutes":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59]},"max_children":1,"timeout":36000,"catch_up":0,"queue_max":1000,"timezone":"Asia/Shanghai","plugin":"shellplug","title":"病历归集-病历文件打码","category":"general","target":"allgrp","algo":"random","multiplex":0,"stagger":0,"retries":0,"retry_delay":0,"detached":0,"queue":0,"chain":"","chain_error":"","notify_success":"","notify_fail":"","web_hook":"","cpu_limit":0,"cpu_sustain":0,"memory_limit":10737418240,"memory_sustain":0,"log_max_size":0,"notes":"","id":"em7snrf0402","modified":**********,"created":**********,"username":"admin"},{"enabled":1,"params":{"script":"#!/bin/sh\n\n# Enter your shell script code here\n\ncd /app\n\npython3.9 -m script.sync_ot_subject","annotate":0,"json":0},"timing":{"hours":[1],"minutes":[0]},"max_children":1,"timeout":3600,"catch_up":0,"queue_max":1000,"timezone":"Asia/Shanghai","plugin":"shellplug","title":"OT数据同步-受试者信息","category":"general","target":"allgrp","algo":"random","multiplex":0,"stagger":0,"retries":0,"retry_delay":0,"detached":0,"queue":0,"chain":"","chain_error":"","notify_success":"","notify_fail":"","web_hook":"","cpu_limit":0,"cpu_sustain":0,"memory_limit":0,"memory_sustain":0,"log_max_size":0,"notes":"","id":"em7sk15pl05","modified":1740975785,"created":1740975785,"username":"admin"},{"enabled":1,"params":{"script":"#!/bin/sh\n\n# Enter your shell script code here\n\ncd /app\n\npython3.9 -m script.sync_ot_project_site","annotate":0,"json":0},"timing":{"hours":[1],"minutes":[0]},"max_children":1,"timeout":3600,"catch_up":0,"queue_max":1000,"timezone":"Asia/Shanghai","plugin":"shellplug","title":"OT数据同步-项目中心信息","category":"general","target":"allgrp","algo":"random","multiplex":0,"stagger":0,"retries":0,"retry_delay":0,"detached":0,"queue":0,"chain":"","chain_error":"","notify_success":"","notify_fail":"","web_hook":"","cpu_limit":0,"cpu_sustain":0,"memory_limit":0,"memory_sustain":0,"log_max_size":0,"notes":"","id":"em7sk0arj04","modified":1741169746,"created":1740975745,"username":"admin"},{"enabled":1,"params":{"script":"#!/bin/sh\n\n# Enteryour shell script code here\n\ncd /app\n\npython3.9 -m script.sync_ot_project","annotate":0,"json":0},"timing":{"hours":[1],"minutes":[0]},"max_children":1,"timeout":3600,"catch_up":0,"queue_max":1000,"timezone":"Asia/Shanghai","plugin":"shellplug","title":"OT数据同步-项目信息","category":"general","target":"allgrp","algo":"random","multiplex":0,"stagger":0,"retries":0,"retry_delay":0,"detached":0,"queue":0,"chain":"","chain_error":"","notify_success":"","notify_fail":"","web_hook":"","cpu_limit":0,"cpu_sustain":0,"memory_limit":0,"memory_sustain":0,"log_max_size":0,"notes":"","id":"em7sjypem03","modified":1740976032,"created":1740975671,"username":"admin"},{"enabled":1,"params":{"script":"#!/bin/sh\n\n# Enter yourshell script code here\n\ncd /app\n\npython3.9 -m script.refresh_access_token","annotate":0,"json":0},"timing":{"minutes":[0]},"max_children":1,"timeout":3600,"catch_up":0,"queue_max":1000,"timezone":"Asia/Shanghai","plugin":"shellplug","title":"钉钉accessToken刷新/每小时","category":"general","target":"allgrp","algo":"random","multiplex":0,"stagger":0,"retries":0,"retry_delay":0,"detached":0,"queue":0,"chain":"","chain_error":"","notify_success":"","notify_fail":"","web_hook":"","cpu_limit":0,"cpu_sustain":0,"memory_limit":0,"memory_sustain":0,"log_max_size":0,"notes":"","id":"em7a5311m01","modified":1739862287,"created":1739862287,"username":"admin"}]}
