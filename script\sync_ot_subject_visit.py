import os
import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smo.settings.test')
django.setup()

from apps.subject.models import SubjectVisit, SubjectItem
from django.db import transaction
import psycopg2


# 'postgresql://postgres:Qr%4077.88@**************:5432/oceanus_trial_clinplus'
pg_conn_params = {
    'host': '**************',
    'database': 'oceanus_trial_clinplus',
    'user': 'postgres',
    'password': 'Qr@77.88',
    'port': 5432
}

PAGE_SIZE = 100  # 每次查询的记录数，可以根据实际情况调整

with open('./script/test_project_no.txt') as f:
    PROJECT_NO_LIST = f.readlines()

PROJECT_NO_LIST = list(set([i.strip() for i in PROJECT_NO_LIST if i.strip()]))
print(PROJECT_NO_LIST)


def migrate_subject():
    # Connect to PostgreSQL
    pg_conn = psycopg2.connect(**pg_conn_params)
    pg_cursor = pg_conn.cursor()

    try:
        # Get total record count
        count_query = f"SELECT COUNT(*) FROM subject_visit where project_id in (SELECT project_id FROM project where project_no in {tuple(PROJECT_NO_LIST)})"
        pg_cursor.execute(count_query)
        total_count = pg_cursor.fetchone()[0]

        # Calculate total pages
        total_pages = (total_count + PAGE_SIZE - 1) // PAGE_SIZE

        for page in range(total_pages):
            offset = page * PAGE_SIZE

            # Query to fetch subject_visit data from PostgreSQL
            pg_query = f"""
            SELECT
                t1.id as source_id,
                t1.visit->>'epochId' as subject_epoch_source_id,
                t1.visit->>'visitId' as visit_id,
                t1.visit_date,
                t1.status,
                dictconstantname('e888888', t1.status) as status_text,
                t1.visit->>'visitType' as type,
                dictconstantname('e888888', t1.visit->>'visitType') as type_text,
                t1.label,
                t1.skip_reason,
                t1.prepare_date,
                t1.project_id,
                t1.project_site_id,
                t1.sub_id as subject_id,
                t1.create_time as ot_create_time,
                t1.update_time as ot_update_time
            FROM
                subject_visit AS t1
            WHERE 
                project_id in (SELECT project_id FROM project where project_no in {tuple(PROJECT_NO_LIST)})
                and visit->>'visitType' != 'SUBJECT_VISIT_TYPE$UNPLAN'
                and status = 'SUBJECT_VISIT_STATUS$2'
            LIMIT {PAGE_SIZE} OFFSET {offset}
            """
            pg_cursor.execute(pg_query)
            subject_data = pg_cursor.fetchall()

            # Get column names
            column_names = [desc[0] for desc in pg_cursor.description]

            # Process each row and insert/update to MySQL
            for row in subject_data:
                with transaction.atomic():
                    # Create a dictionary of subject_visit data
                    subject_visit_dict = dict(zip(column_names, row))
                    subject_visit_dict['subject_visit_id'] = subject_visit_dict['source_id'] + '-' + subject_visit_dict['subject_id']
                    subject_visit_dict['subject_epoch_id'] = subject_visit_dict['subject_epoch_source_id'] + '-' + subject_visit_dict['subject_id']
                    subject_visit_dict.pop('subject_epoch_source_id')
                    
                    print(f"subject_visit: {subject_visit_dict['source_id'], subject_visit_dict['subject_id']}")

                    try:
                        # Try to get the existing subject
                        subject_visit = SubjectVisit.objects.get(subject_id=subject_visit_dict['subject_id'], source_id=subject_visit_dict['source_id'])

                        # Update existing subject
                        for key, value in subject_visit_dict.items():
                            setattr(subject_visit, key, value)
                        subject_visit.save()

                        print(f"Updated subject_visit: {subject_visit.subject_id}")

                    except SubjectVisit.DoesNotExist:
                        # Create new subject
                        subject_visit = SubjectVisit.objects.create(**subject_visit_dict)

                        print(f"Created new subject_visit: {subject_visit_dict['subject_id']}")

                    subject_item_dict = {
                        'subject_item_id': subject_visit_dict['subject_visit_id'] + '-' + subject_visit_dict['subject_id'],
                        'source_id': subject_visit_dict['subject_visit_id'],
                        'item_id': 'prs0000000000',
                        'label': '访视病历',
                        'status': 'SUBJECT_FORM_STATUS$2',
                        'status_text': '已完成',
                        'seq': '999',
                        'item_type': 3,
                        # 'ae_ai_current_step': '',
                        # 'ae_ai_task_id': '',
                        'project_id': subject_visit_dict['project_id'],
                        'project_site_id': subject_visit_dict['project_site_id'],
                        'subject_id': subject_visit_dict['subject_id'],
                        'subject_visit_id': subject_visit_dict['subject_visit_id']
                    }
                    
                    try:
                        # 尝试获取现有的 subject_item
                        subject_item = SubjectItem.objects.get(subject_id=subject_item_dict['subject_id'], source_id=subject_item_dict['source_id'])

                        # 更新现有的 subject_item
                        for key, value in subject_item_dict.items():
                            setattr(subject_item, key, value)
                        subject_item.save()

                        # print(f"更新了 subject_item: {subject_item.source_id}")

                    except SubjectItem.DoesNotExist:
                        # 创建新的 subject_item
                        subject_item = SubjectItem.objects.create(**subject_item_dict)

        print(f"Migration completed successfully. Processed {total_count} subject_visit records.")

    except Exception as e:
        print(f"Error during migration: {str(e)}")
    finally:
        pg_cursor.close()
        pg_conn.close()


if __name__ == "__main__":
    # python -m script.sync_ot_subject_visit
    migrate_subject()
