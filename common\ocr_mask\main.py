"""
OCR脱敏处理主入口模块 - 

提供与原始ocr_desensitive函数完全兼容的接口，
内部使用PocketFlow框架和HIPAA脱敏服务实现。

只支持HIPAA脱敏模式 - 使用专业的HIPAA脱敏服务
"""

import time
import os
from .flow import run_ocr_desensitive_flow

print("🔧 OCR脱敏模式: ")


def ocr_desensitive(input_img, project_no=None):
    """
    OCR脱敏处理主函数 - 

    简化返回值以适应架构，只返回核心处理结果。

    Args:
        input_img: 输入图片的字节数据
        project_no: 项目编号，将作为project_code传递给脱敏服务

    Returns:
        tuple: (处理后的图片对象, 输入文本, 脱敏文本, ocr_box)
    """
    # 如果用户尝试使用传统模式，给出警告

    print(f"🚀 启动OCR脱敏处理 - HIPAA模式")

    try:
        # 运行HIPAA PocketFlow工作流
        result = run_ocr_desensitive_flow(input_img, project_no=project_no)

        if not result["success"]:
            # 如果处理失败，返回默认值
            error_msg = result.get('error', '未知错误')
            print(f"❌ HIPAA脱敏处理失败: {error_msg}")
            return _create_fallback_response()

        # 提取结果
        image = result["image"]
        input_text = result["input_text"]
        masked_text = result["masked_text"]
        markdown_text = result["markdown_text"]
        ocr_box = result["ocr_box"]  # 新增：带坐标的OCR信息


        # 注释掉不再需要的AI相关变量，因为架构不需要这些
        final_keywords = result["final_keywords"]  # 不再返回
        # ai_response = None  # HIPAA模式为None，不再返回
        # think = f"使用HIPAA脱敏服务处理，识别到{len(result.get('hippa_entities', []))}个敏感实体"  # 不再返回
        # generated_tokens = {}  # 空字典，不再返回

        print(f"✅ OCR脱敏处理完成 - HIPAA模式")

        # 返回简化的4元组结果（适应架构）
        # 注释掉的返回值说明：
        # - ai_response: AI响应（HIPAA模式为None，已移除）
        # - think: 思考内容（HIPAA模式不需要，已移除）
        # - generated_tokens: token使用统计（HIPAA模式为空字典，已移除）
        # - 重复的input_text: 原始OCR文本（重复，已移除）
        # - final_keywords: 最终敏感词列表（HIPAA模式内部处理，已移除）
        return (
            image,                  # 处理后的图片对象
            markdown_text,          # 输入文本（换行的）
            masked_text,            # 脱敏后的文本
            ocr_box                 # 带坐标的OCR信息
        )

    except Exception as e:
        print(f"❌ OCR脱敏处理过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return _create_fallback_response()





def _create_fallback_response():
    """
    创建失败时的回退响应 - 

    Returns:
        tuple: 简化的4元组默认响应 (image, input_text, masked_text, ocr_box)
    """
    from PIL import Image

    # 创建一个空白图片作为默认返回
    default_image = Image.new('RGB', (100, 100), color='white')

    # 创建默认的ocr_box结构
    default_ocr_box = {
        "page": 1,
        "words_block_list": []
    }

    return (
        default_image,         # 默认图片
        "OCR处理失败",          # 输入文本
        "OCR处理失败",          # 脱敏文本
        default_ocr_box        # 默认ocr_box
    )


if __name__ == "__main__":
    """
    测试主函数
    """
    print("🧪 OCR脱敏处理模块测试")
    print("请注意：这是一个演示测试，需要实际的图片文件来进行完整测试")
    
    # 这里可以添加测试代码
    # 例如：
    # with open("test_image.jpg", "rb") as f:
    #     test_img = f.read()
    # result = ocr_desensitive(test_img, "output.jpg")
    # print("测试完成！")