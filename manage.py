#!/usr/bin/env python
"""Django's command-line utility for administrative tasks."""
import os
import sys
import re

from django.conf import settings
from django_comment_migrate import utils


def get_field_comment(field):
    value = getattr(field, settings.DCM_COMMENT_KEY)
    if value is not None:
        return str(value)


def get_table_comment(model):
    value = getattr(model._meta, settings.DCM_COMMENT_KEY)

    pattern = r'^[a-z0-9\s]+$'  # 未定义可读表名
    if re.match(pattern, value):
        value = model.__doc__

    if value is not None:
        return str(value).strip()


utils.get_field_comment = get_field_comment
utils.get_table_comment = get_table_comment


def main():
    """Run administrative tasks."""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smo.settings.test')
    # os.environ.setdefault('JAVA_HOME', 'C:\Program Files\Java\jdk-20')
    try:
        from django.core.management import execute_from_command_line
    except ImportError as exc:
        raise ImportError(
            "Couldn't import Django. Are you sure it's installed and "
            "available on your PYTHONPATH environment variable? Did you "
            "forget to activate a virtual environment?"
        ) from exc
    execute_from_command_line(sys.argv)


if __name__ == '__main__':
    main()
