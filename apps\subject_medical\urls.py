from django.urls import path, include
from rest_framework.routers import DefaultRout<PERSON>, SimpleRouter

from . import views

router = DefaultRouter(trailing_slash=False)
router.register(r'/subject-medical-info', views.SubjectMedicalInfoListViewSetDeprecated)
router.register(r'/subject-medical-info', views.SubjectMedicalInfoDetailViewSetDeprecated)

router.register(r'/subject-medical-infos', views.SubjectMedicalInfoListViewSet)
router.register(r'/subject-medical-infos', views.SubjectMedicalInfoDetailViewSet)
router.register(r'/subject-medical-info-masked', views.SubjectMedicalMaskedFileUpdateViewSet)
urlpatterns = []

urlpatterns = router.urls + urlpatterns
