import hashlib
from django.db import connection

def get_general_prompt_sql(prompt_type):
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT prompt_content 
            FROM general_prompt_info
            WHERE prompt_type = %s AND release_status = %s
            ORDER BY create_time DESC
            LIMIT 1
        """, [prompt_type, 1])
        row = cursor.fetchone()
    return row[0] if row else None

def get_project_prompt_info(project_id, epoch_name):
    """
    根据 project_id 获取 source_desc, edc_field, edc_field_desc 信息
    默认 delete_flag = 0
    :param project_id: 项目主键 id
    :return: 查询结果列表，每个元素是一个包含 source_desc, edc_field, edc_field_desc 的元组
    """
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT source_desc, edc_field, edc_field_desc
            FROM project_promopt_info
            WHERE project_id = %s AND delete_flag = %s AND epoch_name = %s
            ORDER BY sort_no ASC
        """, [project_id, 0, epoch_name])
        rows = cursor.fetchall()

    return rows if rows else None
def calculate_file_hash(file_obj, hash_algorithm='sha256', chunk_size=8192):
    """
    分块计算文件的哈希值
    :param file_obj: 文件对象
    :param hash_algorithm: 哈希算法，默认为 sha256
    :param chunk_size: 每次读取的块大小，默认为 8KB
    :return: 文件的哈希值
    """
    hash_func = hashlib.new(hash_algorithm)
    for chunk in file_obj.chunks(chunk_size):
        hash_func.update(chunk)
    file_obj.seek(0)
    return hash_func.hexdigest()
