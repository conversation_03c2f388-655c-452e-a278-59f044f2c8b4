from rest_framework import serializers
from common.minio_client import get_minio_client
from common.models import BaseFileModel
from common.serializers import FileUrlMixin
from .models import MaskingTask
from apps.subject_medical.models import SubjectMedicalFile, SubjectMedicalInfo


class MaskingFileObjectSerializer(FileUrlMixin, serializers.ModelSerializer):
    """文件对象"""
    url = serializers.SerializerMethodField(label='文件下载url，有效期2小时')

    class Meta:
        model = SubjectMedicalFile
        exclude = ['create_user', 'create_name', 'update_user', 'update_name']


class CreateMaskingTaskSerializer(serializers.ModelSerializer):
    file = serializers.FileField(write_only=True, help_text="上传的文件，支持图片，Word，PDF。")
    meta = serializers.CharField(required=False, help_text="附加的业务参数，JSON字符串")
    callback_url = serializers.CharField(required=False, help_text="任务执行成功或者失败回调的url")

    class Meta:
        model = MaskingTask
        fields = ['meta', 'file', 'callback_url']


class MaskingTaskSerializer(serializers.ModelSerializer):
    # original_file = MaskingFileObjectSerializer()
    masked_file = MaskingFileObjectSerializer()

    class Meta:
        model = MaskingTask
        exclude = ['create_user', 'create_name', 'update_user', 'update_name', 'extra', 'original_file']
