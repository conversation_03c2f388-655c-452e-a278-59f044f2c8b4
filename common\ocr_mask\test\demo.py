import sys
import os
import django
import glob
import time
import json
import ast
import tempfile
from datetime import datetime
from collections import defaultdict

# 尝试导入PyMuPDF (fitz)
try:
    import fitz
    HAS_FITZ = True
except ImportError:
    HAS_FITZ = False
    print("⚠️ 建议安装PyMuPDF库以支持PDF文件处理: pip install PyMuPDF")

def convert_pdf_to_images(pdf_path, output_folder):
    """
    将PDF文件的每一页转换为图片，并保存到指定文件夹。
    
    Args:
        pdf_path (str): PDF文件路径。
        output_folder (str): 图片输出文件夹路径。
        
    Returns:
        list: 生成的图片文件路径列表。
    """
    images_paths = []
    try:
        pdf_document = fitz.open(pdf_path)
        pdf_name = os.path.splitext(os.path.basename(pdf_path))[0]
        
        for page_num in range(len(pdf_document)):
            page = pdf_document.load_page(page_num)
            # 设置DPI，提高图片质量
            pix = page.get_pixmap(matrix=fitz.Matrix(200/72, 200/72)) # 200 DPI
            
            img_filename = f"{pdf_name}_page_{page_num + 1}.png"
            img_path = os.path.join(output_folder, img_filename)
            pix.save(img_path)
            images_paths.append(img_path)
            print(f"   ✅ PDF页面 {page_num + 1} 已转换为图片: {img_path}")
            
        pdf_document.close()
        print(f"✅ PDF文件 '{os.path.basename(pdf_path)}' 已成功转换为 {len(images_paths)} 张图片。")
    except Exception as e:
        print(f"❌ 转换PDF文件 '{os.path.basename(pdf_path)}' 时出错: {e}")
    return images_paths


# 尝试导入tabulate，如果没有则使用简单的表格格式
try:
    from tabulate import tabulate
    HAS_TABULATE = True
except ImportError:
    HAS_TABULATE = False
    print("⚠️ 建议安装tabulate库以获得更好的表格显示效果: pip install tabulate")

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# Django环境初始化（必须在导入Django模块之前）
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smo.settings.test')
os.environ["DJANGO_ALLOW_ASYNC_UNSAFE"] = "true"
django.setup()

# from common.ocr_mask.main import ocr_desensitive
from common.ocr_mask.main import ocr_desensitive

def load_correct_answers(json_path):
    """加载正确答案JSON文件"""
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 加载正确答案文件失败: {e}")
        return {}

def extract_sensitive_words_from_ai_response(output_text):
    """
    从AI响应中提取敏感词列表
    
    Args:
        output_text: AI响应的原始输出
    
    Returns:
        list: 提取的敏感词列表
    """
    sensitive_words = []
    
    try:
        if output_text and "choices" in output_text:
            content = output_text["choices"][0]["message"]["content"]
            
            # 提取JSON数组部分
            json_start = content.find("[")
            json_end = content.rfind("]") + 1
            
            if json_start != -1 and json_end > json_start:
                json_content = content[json_start:json_end]
                try:
                    sensitive_words = json.loads(json_content)
                    if isinstance(sensitive_words, list):
                        # 过滤无效词汇
                        sensitive_words = [word for word in sensitive_words 
                                         if word and word.strip() and word not in ['None', '无', '未提及', 'null']]
                except json.JSONDecodeError:
                    print("⚠️ AI响应JSON解析失败")
    except Exception as e:
        print(f"⚠️ 提取敏感词时出错: {e}")
    
    return sensitive_words

def process_single_image(image_path, output_dir, detailed_stats=None):
    """
    处理单个图片文件
    
    Args:
        image_path: 图片文件路径
        output_dir: 输出目录
        detailed_stats: 详细统计信息收集器
    
    Returns:
        dict: 处理结果信息
    """
    start_time = time.time()
    file_name = os.path.basename(image_path)
    
    result_info = {
        'file_name': file_name,
        'success': False,
        'sensitive_words': [],
        'sensitive_count': 0,
        'processing_time': 0,
        'ocr_time': 0,
        'ai_time': 0,
        'mask_time': 0,
        'error_message': None,
        'token_usage': {},
        'ocr_text_length': 0,
        'masked_text_length': 0,
        'ai_output_chars': 0,
        'ai_raw_keywords': []  # 新增：存储大模型识别的原始关键词
    }
    
    try:
        # 获取文件大小

        print(f"📁 正在处理: {file_name}")
        
        with open(image_path, 'rb') as f:
            input_img = f.read()
        
        # 调用的脱敏功能，返回3元组：(image, input_text, masked_text)
        # 注释掉的原8元组调用：
        # result_image, input_text, output_text, think, generated_tokens, ocr_text, ocr_text_mask, final_keywords = ocr_desensitive(input_img, None)
        result_image, input_text, masked_text,final_keywords = ocr_desensitive(input_img)

        # 为了保持后续代码兼容，将返回值映射到原有变量名
        ocr_text = input_text      # 原始OCR文本
        ocr_text_mask = masked_text  # 脱敏后的文本

        # 设置HIPAA模式下不再提供的变量为默认值
        output_text = None         # AI响应（HIPAA模式为None）
        think = f"HIPAA模式处理完成"  # 思考内容（简化）
        generated_tokens = {}      # token统计（空字典）
        # final_keywords = []        # 最终敏感词列表（HIPAA模式内部处理）



        # 提取敏感词 (现在使用最终过滤后的关键词)
        sensitive_words = final_keywords
        result_info['sensitive_words'] = sensitive_words
        result_info['ai_raw_keywords'] = extract_sensitive_words_from_ai_response(output_text)  # 存储大模型识别的原始关键词
        result_info['sensitive_count'] = len(sensitive_words)
        result_info['token_usage'] = generated_tokens if generated_tokens else {}
        result_info['ocr_text_length'] = len(ocr_text) if ocr_text else 0
        result_info['masked_text_length'] = len(ocr_text_mask) if ocr_text_mask else 0
        
        # 提取大模型输出字符统计
        ai_output_chars = 0
        if output_text and "choices" in output_text:
            try:
                ai_content = output_text["choices"][0]["message"]["content"]
                ai_output_chars = len(ai_content) if ai_content else 0
            except (KeyError, IndexError, TypeError):
                ai_output_chars = 0
        result_info['ai_output_chars'] = ai_output_chars

        print(f"🔍 敏感词({len(sensitive_words)}个): {sensitive_words if sensitive_words else '无'}")
        
        # 显示AI输出统计
        if ai_output_chars > 0 and generated_tokens:
            completion_tokens = generated_tokens.get('completion_tokens', 0)
            if completion_tokens > 0:
                chars_per_token = ai_output_chars / completion_tokens
                print(f"🤖 AI输出: {ai_output_chars}字符, {completion_tokens}Token, 比率{chars_per_token:.2f}")

        # 生成输出文件名
        name, ext = os.path.splitext(file_name)
        output_filename = f"{name}_脱敏{ext}"
        output_path = os.path.join(output_dir, output_filename)
        
        # 保存脱敏后的图片
        result_image.save(output_path)
        
        # 计算处理时间
        end_time = time.time()
        result_info['processing_time'] = end_time - start_time
        
        print(f"✅ 完成，耗时{result_info['processing_time']:.2f}秒")
        
        result_info['success'] = True
        print("-"*50)
        return result_info
            
    except FileNotFoundError:
        error_msg = f"找不到图片文件 {image_path}"
        print(f"❌ 错误: {error_msg}")
        result_info['error_message'] = error_msg
        return result_info
    except Exception as e:
        error_msg = f"处理图片 {file_name} 时出现错误: {str(e)}"
        print(f"❌ {error_msg}")
        result_info['error_message'] = error_msg
        print("-"*50)
        return result_info

def evaluate_single_image(image_path, correct_answers):
    """评估单个图片的检测结果"""
    # 创建临时输出目录
    temp_dir = tempfile.mkdtemp()
    
    # 获取文件名
    file_name = os.path.basename(image_path)
    
    # 获取正确答案
    correct_answer = correct_answers.get(file_name, [])
    if not correct_answer:
        print(f"⚠️ 没有找到 {file_name} 的正确答案")
        return None
    
    # 处理图片
    print(f"🔍 评估图片: {file_name}")
    result = process_single_image(image_path, temp_dir)
    
    # 获取检测结果 (现在使用最终过滤后的关键词)
    detected = result.get('sensitive_words', []) # sensitive_words 现在存储的是 final_keywords
    
    # 计算评估指标
    correct_detected = [word for word in detected if word in correct_answer]
    missed = [word for word in correct_answer if word not in detected]
    extra_detected = [word for word in detected if word not in correct_answer]
    
    precision = len(correct_detected) / len(detected) if detected else 0
    recall = len(correct_detected) / len(correct_answer) if correct_answer else 1
    f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
    
    evaluation_result = {
        'file_name': file_name,
        'correct_answer': correct_answer,
        'detected': detected,
        'correct_detected': correct_detected,
        'missed': missed,
        'extra_detected': extra_detected,
        'precision': precision,
        'recall': recall,
        'f1': f1,
        'processing_time': result.get('processing_time', 0),
        'ai_output_chars': result.get('ai_output_chars', 0),
        'ai_raw_keywords': result.get('ai_raw_keywords', [])  # 存储大模型识别的原始关键词
    }
    
    return evaluation_result



def generate_summary_table(results):
    """
    生成详细的统计表格
    
    Args:
        results: 处理结果列表
    """
    print("\n📊 处理结果统计表")
    print("="*50)
    
    # 准备表格数据
    table_data = []
    headers = ["序号", "文件名", "状态", "敏感词数量", "处理时间(s)", "AI输出字符", "敏感词列表"]
    
    for i, result in enumerate(results, 1):
        status = "✅成功" if result['success'] else "❌失败"
        file_name = result['file_name'][:20] + "..." if len(result['file_name']) > 23 else result['file_name']
        
        # 格式化敏感词列表
        if result['sensitive_words']:
            sensitive_words_str = ', '.join(result['sensitive_words'])
        else:
            sensitive_words_str = "无"
        
        table_data.append([
            i,
            file_name,
            status,
            result['sensitive_count'],
            f"{result['processing_time']:.2f}",
            result.get('ai_output_chars', 0),
            sensitive_words_str
        ])
    
    # 使用tabulate生成表格
    if HAS_TABULATE:
        print(tabulate(table_data, headers=headers, tablefmt="grid", stralign="left"))
    else:
        # 简单的表格格式
        print(f"{'序号':<4} {'文件名':<25} {'状态':<8} {'敏感词数量':<10} {'处理时间(s)':<12} {'AI输出字符':<10} {'敏感词列表':<40}")
        print("-" * 120)
        for row in table_data:
            print(f"{row[0]:<4} {row[1]:<25} {row[2]:<8} {row[3]:<10} {row[4]:<12} {row[5]:<10} {row[6]:<40}")
    
    print("="*50)

def print_evaluation_results(results, total_evaluation_time, processed_files_count):
    """打印评估结果"""
    print("\n" + "=" * 50)
    print("📊 评估结果")
    print("=" * 50)
    
    # 准备表格数据
    table_data = []
    headers = ["文件名", "精确率", "召回率", "F1分数", "处理时间(s)", "正确检测", "漏检", "误检"]
    
    # 计算总体指标
    total_correct = sum(len(r['correct_detected']) for r in results)
    total_detected = sum(len(r['detected']) for r in results)
    total_answers = sum(len(r['correct_answer']) for r in results)
    
    overall_precision = total_correct / total_detected if total_detected > 0 else 0
    overall_recall = total_correct / total_answers if total_answers > 0 else 0
    overall_f1 = 2 * overall_precision * overall_recall / (overall_precision + overall_recall) if (overall_precision + overall_recall) > 0 else 0
    
    # 填充表格数据
    for r in results:
        correct_str = ", ".join(r['correct_detected'])
        missed_str = ", ".join(r['missed'])
        extra_str = ", ".join(r['extra_detected'])
        
        table_data.append([
            r['file_name'],
            f"{r['precision']:.2%}",
            f"{r['recall']:.2%}",
            f"{r['f1']:.2%}",
            f"{r['processing_time']:.2f}",
            correct_str,
            missed_str,
            extra_str
        ])
    
    # 添加总体评估行
    # 使用传入的 total_evaluation_time 和 processed_files_count
    overall_avg_processing_time = total_evaluation_time / processed_files_count if processed_files_count > 0 else 0

    table_data.append([
        "总体评估",
        f"{overall_precision:.2%}",
        f"{overall_recall:.2%}",
        f"{overall_f1:.2%}",
        f"{total_evaluation_time:.2f}", # 使用整个评估过程的总时间
        f"{sum(r.get('ai_output_chars', 0) for r in results):,}", # 总体AI输出字符
        f"{total_correct}/{total_detected}",
        f"{total_answers-total_correct}",
        f"{total_detected-total_correct}"
    ])
    
    # 打印表格
    if HAS_TABULATE:
        print(tabulate(table_data, headers=headers, tablefmt="grid"))
    else:
        print(f"{'文件名':<18} {'精确率':<8} {'召回率':<8} {'F1分数':<8} {'处理时间(s)':<12} {'AI输出字符':<12} {'正确检测':<10} {'漏检':<10} {'误检':<10}")
        print("-" * 115)
        for row in table_data:
            print(f"{row[0]:<18} {row[1]:<8} {row[2]:<8} {row[3]:<8} {row[4]:<12} {row[5]:<12} {row[6]:<10} {row[7]:<10} {row[8]:<10}")

def generate_detailed_analysis(results, total_evaluation_time, processed_files_count):
    """
    生成详细的数据分析
    
    Args:
        results: 处理结果列表
        total_evaluation_time: 整个评估过程的总时间
        processed_files_count: 实际处理的文件数量
    """
    successful_results = [r for r in results if r['success']]
    failed_results = [r for r in results if not r['success']]
    
    print("\n" + "="*50)
    print("📈 详细分析")
    print("="*50)
    
    # 基础统计
    print("📋 基础统计:")
    print(f"   总文件数: {len(results)}")
    print(f"   成功处理: {len(successful_results)}")
    print(f"   处理失败: {len(failed_results)}")
    print(f"   成功率: {len(successful_results)/len(results)*100:.1f}%")
    
    if successful_results:
        # 敏感词统计
        total_sensitive_words = sum(r['sensitive_count'] for r in successful_results)
        avg_sensitive_words = total_sensitive_words / len(successful_results)
        max_sensitive = max(r['sensitive_count'] for r in successful_results)
        min_sensitive = min(r['sensitive_count'] for r in successful_results)
        
        print(f"\n🔍 敏感词检测统计:")
        print(f"   总敏感词数: {total_sensitive_words}")
        print(f"   平均每张: {avg_sensitive_words:.1f}个")
        print(f"   最多检测: {max_sensitive}个")
        print(f"   最少检测: {min_sensitive}个")
        
        # 性能统计 (使用传入的 total_evaluation_time 和 processed_files_count)
        total_time = total_evaluation_time
        avg_time = total_evaluation_time / processed_files_count if processed_files_count > 0 else 0
        # max_time 和 min_time 仍然基于单个文件的处理时间
        max_time = max(r['processing_time'] for r in successful_results) if successful_results else 0
        min_time = min(r['processing_time'] for r in successful_results) if successful_results else 0
        
        print(f"\n⏱️ 性能统计:")
        print(f"   总处理时间: {total_time:.2f}秒")
        print(f"   平均处理时间: {avg_time:.2f}秒/张")
        print(f"   最长处理时间: {max_time:.2f}秒")
        print(f"   最短处理时间: {min_time:.2f}秒")
        
        # Token和字符统计
        total_tokens = 0
        total_input_tokens = 0
        total_output_tokens = 0
        total_ai_chars = 0
        token_files = 0
        
        for r in successful_results:
            if r['token_usage']:
                if 'total_tokens' in r['token_usage']:
                    total_tokens += r['token_usage']['total_tokens']
                    token_files += 1
                if 'prompt_tokens' in r['token_usage']:
                    total_input_tokens += r['token_usage']['prompt_tokens']
                if 'completion_tokens' in r['token_usage']:
                    total_output_tokens += r['token_usage']['completion_tokens']
            
            total_ai_chars += r.get('ai_output_chars', 0)
        
        if token_files > 0 or total_ai_chars > 0:
            print(f"\n🤖 AI模型输出统计:")
            
            ai_total_time = sum(r['processing_time'] for r in successful_results)
            
            print(f"   📊 Token统计: {total_tokens:,}总Token, {total_output_tokens:,}输出Token, {total_tokens/token_files:.0f}平均/张")
            print(f"   📝 字符统计: {total_ai_chars:,}总字符, {total_ai_chars/len(successful_results):.0f}平均/张")
            
            if ai_total_time > 0 and total_output_tokens > 0:
                chars_per_token = total_ai_chars / total_output_tokens
                chars_per_second = total_ai_chars / ai_total_time
                tokens_per_second = total_output_tokens / ai_total_time
                print(f"   ⚡ 速度: {chars_per_second:.1f}字符/秒, {tokens_per_second:.1f}Token/秒, 比率{chars_per_token:.2f}")
        
        # 敏感词类型分析
        all_sensitive_words = []
        for r in successful_results:
            all_sensitive_words.extend(r['sensitive_words'])
        
        # if all_sensitive_words:
        #     word_frequency = defaultdict(int)
        #     for word in all_sensitive_words:
        #         word_frequency[word] += 1
        #
        #     print(f"\n🏷️ 高频敏感词统计 (Top 15):")
        #     sorted_words = sorted(word_frequency.items(), key=lambda x: x[1], reverse=True)
        #
        #     # 准备高频敏感词表格数据
        #     word_table_data = []
        #     word_headers = ["排名", "敏感词", "出现次数", "出现频率"]
        #
        #     for i, (word, count) in enumerate(sorted_words[:15], 1):
        #         frequency = f"{count/len(successful_results)*100:.1f}%"
        #         word_table_data.append([i, word, count, frequency])
        #
        #     # 使用tabulate生成敏感词表格
        #     if HAS_TABULATE:
        #         print(tabulate(word_table_data, headers=word_headers, tablefmt="grid", stralign="left"))
        #     else:
        #         print(f"{'排名':<4} {'敏感词':<20} {'出现次数':<8} {'出现频率':<8}")
        #         print("-" * 45)
        #         for row in word_table_data:
        #             print(f"{row[0]:<4} {row[1]:<20} {row[2]:<8} {row[3]:<8}")

    # 失败原因分析
    if failed_results:
        print(f"\n❌ 失败原因分析:")
        error_types = defaultdict(int)
        for r in failed_results:
            if r['error_message']:
                # 简化错误信息分类
                if "找不到" in r['error_message']:
                    error_types["文件不存在"] += 1
                elif "权限" in r['error_message']:
                    error_types["权限错误"] += 1
                elif "格式" in r['error_message']:
                    error_types["格式错误"] += 1
                else:
                    error_types["其他错误"] += 1
        
        for error_type, count in error_types.items():
            print(f"   {error_type}: {count}个文件")

def batch_process_images(folder_path, output_dir=None):
    """
    批量处理文件夹中的所有图片
    
    Args:
        folder_path: 图片文件夹路径
        output_dir: 输出目录，默认为输入文件夹下的'脱敏结果'子文件夹
    """
    print("🚀 开始批量医疗病历脱敏处理...")
    print("="*50)
    
    # 检查输入文件夹是否存在
    if not os.path.exists(folder_path):
        print(f"❌ 错误: 找不到文件夹 {folder_path}")
        return
    
    # 设置输出目录
    if output_dir is None:
        output_dir = os.path.join(folder_path, "脱敏结果")
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    print(f"📂 输出目录: {output_dir}")
    
    # 支持的图片和PDF格式
    supported_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff', '*.tif', '*.webp', '*.pdf']
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff', '*.tif', '*.webp']
    
    # 获取所有支持的文件
    all_files = set()
    for ext in supported_extensions:
        pattern = os.path.join(folder_path, ext)
        all_files.update(glob.glob(pattern, recursive=False))
        # 也搜索大写扩展名
        pattern_upper = os.path.join(folder_path, ext.upper())
        all_files.update(glob.glob(pattern_upper, recursive=False))
    
    # 存储最终要处理的图片文件路径
    files_to_process = []
    
    # 创建一个临时目录用于存放PDF转换的图片
    temp_pdf_images_dir = None
    
    if HAS_FITZ:
        temp_pdf_images_dir = tempfile.mkdtemp(prefix="pdf_images_")
        print(f"临时PDF图片目录: {temp_pdf_images_dir}")

    for file_path in sorted(list(all_files)): # 排序以确保处理顺序一致
        if file_path.lower().endswith('.pdf'):
            if HAS_FITZ:
                print(f"🔄 正在转换PDF文件: {os.path.basename(file_path)}")
                converted_images = convert_pdf_to_images(file_path, temp_pdf_images_dir)
                files_to_process.extend(converted_images)
            else:
                print(f"⚠️ 跳过PDF文件 '{os.path.basename(file_path)}'，因为未安装PyMuPDF库。")
        else:
            files_to_process.append(file_path)
            
    if not files_to_process:
        print(f"⚠️ 在文件夹 {folder_path} 中未找到支持的图片或PDF文件")
        print(f"支持的格式: {', '.join(supported_extensions)}")
        return
    
    print(f"📊 找到 {len(files_to_process)} 个图片文件 (包括PDF转换的图片)")
    print("-"*50)
    
    # 批量处理
    start_time = time.time()
    results = []
    
    for i, image_path in enumerate(files_to_process, 1):
        print(f"🔄 处理进度: {i}/{len(files_to_process)}")
        
        result = process_single_image(image_path, output_dir)
        results.append(result)
    
    # 处理完成统计
    end_time = time.time()
    total_time = end_time - start_time
    
    successful_results = [r for r in results if r['success']]
    failed_results = [r for r in results if not r['success']]
    
    print("🎉 处理完成!")
    print("="*50)
    
    # 确保 successful_results 不为空，避免除以零
    avg_time_per_image = total_time / len(successful_results) if successful_results else 0
    print(f"📊 统计: {len(successful_results)}/{len(files_to_process)}成功, 耗时{total_time:.2f}秒, 平均{avg_time_per_image:.2f}秒/张")
    
    # 快速AI输出摘要
    total_ai_chars_summary = sum(r.get('ai_output_chars', 0) for r in successful_results)
    total_tokens_summary = sum(r.get('token_usage', {}).get('total_tokens', 0) for r in successful_results)
    total_output_tokens = sum(r.get('token_usage', {}).get('completion_tokens', 0) for r in successful_results)
    
    if total_ai_chars_summary > 0 or total_tokens_summary > 0:
        chars_per_token = total_ai_chars_summary / total_output_tokens if total_output_tokens > 0 else 0
        chars_per_second = total_ai_chars_summary / total_time if total_time > 0 else 0
        print(f"🤖 AI输出: {total_ai_chars_summary:,}字符, {total_tokens_summary:,}Token, {chars_per_second:.1f}字符/秒, 比率{chars_per_token:.2f}")
    
    # 清理临时PDF图片目录
    if temp_pdf_images_dir and os.path.exists(temp_pdf_images_dir):
        try:
            import shutil
            shutil.rmtree(temp_pdf_images_dir)
            print(f"🗑️ 已清理临时PDF图片目录: {temp_pdf_images_dir}")
        except Exception as e:
            print(f"❌ 清理临时PDF图片目录失败: {e}")
    

    # 生成详细统计表格
    generate_summary_table(results)
    
    # 生成详细分析
    generate_detailed_analysis(results, total_time, len(files_to_process))

def run_evaluation(folder_path=None, correct_answers_path="correct_answers.json"):
    """运行评估流程"""
    # 加载正确答案
    correct_answers = load_correct_answers(correct_answers_path)
    if not correct_answers:
        print("❌ 无法继续评估，请检查正确答案文件")
        return
    
    print(f"✅ 已加载 {len(correct_answers)} 个正确答案")
    
    # 设置图片文件夹路径
    if not folder_path:
        folder_path = input("请输入图片文件夹路径: ").strip()
        if not folder_path:
            folder_path = r"D:\data\脱敏用\bug2"  # 默认路径
            print(f"使用默认路径: {folder_path}")
    
    if not os.path.exists(folder_path):
        print(f"❌ 文件夹不存在: {folder_path}")
        return
    
    # 支持的图片和PDF格式
    supported_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff', '*.tif', '*.webp', '*.pdf']
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff', '*.tif', '*.webp']
    
    # 获取所有支持的文件
    all_files = set()
    for ext in supported_extensions:
        pattern = os.path.join(folder_path, ext)
        all_files.update(glob.glob(pattern, recursive=False))
        pattern_upper = os.path.join(folder_path, ext.upper())
        all_files.update(glob.glob(pattern_upper, recursive=False))
    
    # 存储最终要处理的图片文件路径
    files_to_process = []
    
    # 创建一个临时目录用于存放PDF转换的图片
    temp_pdf_images_dir = None
    
    if HAS_FITZ:
        temp_pdf_images_dir = tempfile.mkdtemp(prefix="pdf_eval_images_")
        print(f"临时PDF评估图片目录: {temp_pdf_images_dir}")

    for file_path in sorted(list(all_files)): # 排序以确保处理顺序一致
        if file_path.lower().endswith('.pdf'):
            if HAS_FITZ:
                print(f"🔄 正在转换PDF文件进行评估: {os.path.basename(file_path)}")
                converted_images = convert_pdf_to_images(file_path, temp_pdf_images_dir)
                # 对于PDF转换的图片，其正确答案与原始PDF文件相同
                pdf_base_name = os.path.basename(file_path)
                if pdf_base_name in correct_answers:
                    for img_path in converted_images:
                        # 将转换后的图片文件名作为键，但其正确答案仍是原始PDF的答案
                        # 这样 evaluate_single_image 可以找到答案
                        correct_answers[os.path.basename(img_path)] = correct_answers[pdf_base_name]
                        files_to_process.append(img_path)
                else:
                    print(f"⚠️ 跳过PDF文件 '{os.path.basename(file_path)}' 的评估，因为没有对应的正确答案。")
            else:
                print(f"⚠️ 跳过PDF文件 '{os.path.basename(file_path)}' 的评估，因为未安装PyMuPDF库。")
        else:
            files_to_process.append(file_path)
            
    if not files_to_process:
        print(f"⚠️ 在文件夹 {folder_path} 中未找到支持的图片或PDF文件进行评估")
        print(f"支持的格式: {', '.join(supported_extensions)}")
        return
    
    print(f"📊 找到 {len(files_to_process)} 个图片文件 (包括PDF转换的图片) 用于评估")
    print("-" * 50)
    
    # 处理每个图片
    results = []
    evaluation_start_time = time.time() # 记录评估开始时间
    processed_files_count = 0 # 记录实际处理的文件数

    for i, image_path in enumerate(files_to_process, 1):
        file_name = os.path.basename(image_path)
        
        # 只处理有正确答案的图片
        if file_name in correct_answers:
            print(f"🔄 评估进度: {i}/{len(files_to_process)} - {file_name}")
            result = evaluate_single_image(image_path, correct_answers)
            if result:
                results.append(result)
                processed_files_count += 1 # 成功处理的文件数
        else:
            print(f"⚠️ 跳过 {file_name} - 没有对应的正确答案")
    
    evaluation_end_time = time.time() # 记录评估结束时间
    total_evaluation_time = evaluation_end_time - evaluation_start_time
    
    # 清理临时PDF评估图片目录
    if temp_pdf_images_dir and os.path.exists(temp_pdf_images_dir):
        try:
            import shutil
            shutil.rmtree(temp_pdf_images_dir)
            print(f"🗑️ 已清理临时PDF评估图片目录: {temp_pdf_images_dir}")
        except Exception as e:
            print(f"❌ 清理临时PDF评估图片目录失败: {e}")

    # 打印评估结果
    if results:
        print_evaluation_results(results, total_evaluation_time, processed_files_count)
        
        # 1. 生成大模型识别结果表格（所有识别到的关键词）
        print("\n🤖 大模型识别结果详细表:")
        ai_recognition_data = []
        ai_recognition_headers = ["文件名", "AI原始识别关键词"]
        
        for r in results:
            ai_raw_keywords_str = ', '.join(r['ai_raw_keywords']) if r['ai_raw_keywords'] else "无"
            ai_recognition_data.append([
                r['file_name'],
                ai_raw_keywords_str
            ])
        
        if HAS_TABULATE:
            print(tabulate(ai_recognition_data, headers=ai_recognition_headers, tablefmt="grid", stralign="left"))
        else:
            print(f"{'文件名':<20} {'识别关键词':<80}")
            print("-" * 105)
            for row in ai_recognition_data:
                print(f"{row[0]:<20} {row[1]:<80}")
        
        # 2. 生成匹配情况表格（只显示匹配统计和具体的漏检、误检）
        print("\n📊 识别准确性分析表:")
        accuracy_data = []
        accuracy_headers = ["文件名", "匹配数量", "漏检数量", "误检数量", "漏检关键词", "误检关键词"]
        
        for r in results:
            correct_matched_count = len(r['correct_detected'])
            missed_count = len(r['missed'])
            extra_detected_count = len(r['extra_detected'])
            
            missed_str = ', '.join(r['missed']) if r['missed'] else "无"
            extra_str = ', '.join(r['extra_detected']) if r['extra_detected'] else "无"
            
            accuracy_data.append([
                r['file_name'],
                correct_matched_count,
                missed_count,
                extra_detected_count,
                missed_str,
                extra_str
            ])
        
        if HAS_TABULATE:
            print(tabulate(accuracy_data, headers=accuracy_headers, tablefmt="grid", stralign="left"))
        else:
            print(f"{'文件名':<20} {'匹配数量':<10} {'漏检数量':<10} {'误检数量':<10} {'漏检关键词':<30} {'误检关键词':<30}")
            print("-" * 130)
            for row in accuracy_data:
                print(f"{row[0]:<20} {row[1]:<10} {row[2]:<10} {row[3]:<10} {row[4]:<30} {row[5]:<30}")
        
        # 3. 生成漏检误检汇总表格
        # print("\n⚠️ 漏检误检汇总表:")
        # error_summary_data = []
        # error_summary_headers = ["错误类型", "文件名", "关键词"]
        #
        # for r in results:
        #     # 添加漏检项
        #     for missed_word in r['missed']:
        #         error_summary_data.append(["漏检", r['file_name'], missed_word])
        #
        #     # 添加误检项
        #     for extra_word in r['extra_detected']:
        #         error_summary_data.append(["误检", r['file_name'], extra_word])
        #
        # if error_summary_data:
        #     if HAS_TABULATE:
        #         print(tabulate(error_summary_data, headers=error_summary_headers, tablefmt="grid", stralign="left"))
        #     else:
        #         print(f"{'错误类型':<8} {'文件名':<20} {'关键词':<40}")
        #         print("-" * 70)
        #         for row in error_summary_data:
        #             print(f"{row[0]:<8} {row[1]:<20} {row[2]:<40}")
        # else:
        #     print("🎉 恭喜！没有发现漏检或误检问题")
        # 4. 生成性能总结表格
        print("\n📈 性能总结报告:")
        
        # 计算总体指标
        total_correct = sum(len(r['correct_detected']) for r in results)
        total_detected = sum(len(r['detected']) for r in results)
        total_answers = sum(len(r['correct_answer']) for r in results)
        
        overall_precision = total_correct / total_detected if total_detected > 0 else 0
        overall_recall = total_correct / total_answers if total_answers > 0 else 0
        overall_f1 = 2 * overall_precision * overall_recall / (overall_precision + overall_recall) if (overall_precision + overall_recall) > 0 else 0
        
        # 计算性能指标 (使用传入的 total_evaluation_time 和 processed_files_count)
        total_processing_time = total_evaluation_time
        avg_processing_time = total_evaluation_time / processed_files_count if processed_files_count > 0 else 0
        
        # Token和字符统计
        total_tokens = 0
        total_input_tokens = 0
        total_output_tokens = 0
        total_ai_chars = 0
        token_files = 0
        
        for r in results:
            if r.get('token_usage'):
                if 'total_tokens' in r['token_usage']:
                    total_tokens += r['token_usage']['total_tokens']
                    token_files += 1
                if 'prompt_tokens' in r['token_usage']:
                    total_input_tokens += r['token_usage']['prompt_tokens']
                if 'completion_tokens' in r['token_usage']:
                    total_output_tokens += r['token_usage']['completion_tokens']
            total_ai_chars += r.get('ai_output_chars', 0)
        
        # 计算速度指标
        chars_per_token = total_ai_chars / total_output_tokens if total_output_tokens > 0 else 0
        # 简单计算：总字符数除以总处理时间
        chars_per_second = total_ai_chars / total_processing_time if total_processing_time > 0 else 0
        tokens_per_second = total_output_tokens / total_processing_time if total_processing_time > 0 else 0
        
        # 性能总结数据
        performance_data = [
            ["📊 准确性指标", "", ""],
            ["总体精确率", f"{overall_precision:.2%}", "正确识别/总识别数"],
            ["总体召回率", f"{overall_recall:.2%}", "正确识别/应识别数"],
            ["总体F1分数", f"{overall_f1:.2%}", "精确率和召回率的调和平均"],
            ["", "", ""],
            ["⏱️ 性能指标", "", ""],
            ["总处理时间", f"{total_processing_time:.2f}秒", f"处理{processed_files_count}个文件"], # 使用实际处理的文件数
            ["平均处理时间", f"{avg_processing_time:.2f}秒/文件", "单个文件平均耗时"],
            ["", "", ""],
            ["🤖 AI模型性能", "", ""],
            # ["总Token数", f"{total_tokens:,}", f"输入{total_input_tokens:,} + 输出{total_output_tokens:,}"],
            ["总字符数", f"{total_ai_chars:,}", "AI模型输出总字符"],
            # ["字符/Token比率", f"{chars_per_token:.2f}", "中文字符密度"],
            ["处理速度(字符)", f"{chars_per_second:.1f}字符/秒", "字符生成速度"],
            # ["处理速度(Token)", f"{tokens_per_second:.1f}Token/秒", "Token生成速度"],
            ["", "", ""],
            ["📋 数据统计", "", ""],
            ["测试文件数", f"{len(results)}", "参与评估的文件数量"],
            ["应识别总数", f"{total_answers}", "标准答案中的敏感词总数"],
            ["实际识别数", f"{total_detected}", "AI模型识别的敏感词总数"],
            ["正确识别数", f"{total_correct}", "正确识别的敏感词数量"],
            ["漏检数量", f"{total_answers - total_correct}", "应识别但未识别的数量"],
            ["误检数量", f"{total_detected - total_correct}", "错误识别的数量"]
        ]
        
        # 使用tabulate生成性能表格
        performance_headers = ["指标类别", "数值", "说明"]
        
        if HAS_TABULATE:
            print(tabulate(performance_data, headers=performance_headers, tablefmt="grid", stralign="left"))
        else:
            print(f"{'指标类别':<20} {'数值':<20} {'说明':<40}")
            print("-" * 85)
            for row in performance_data:
                print(f"{row[0]:<20} {row[1]:<20} {row[2]:<40}")
        
        


def test_single_image():
    """
    测试单个图片的脱敏功能
    """
    print("🧪 测试单个图片脱敏...")
    print("="*40)
    
    # 图片文件路径
    # image_path = r"D:\下载\11.png"
    image_path = r"D:\data\OCR-脱敏\脱敏验证用图片\原图片\C1D1_页面_03_图像_0001.jpg"

    # 创建输出目录
    output_dir = os.path.dirname(image_path)
    
    # 处理单个图片
    result = process_single_image(image_path, output_dir)
    
    # 显示大模型识别的关键词
    if result['success'] and result.get('ai_keywords'):
        print("\n🤖 大模型识别关键词:")
        for i, keyword in enumerate(result['ai_keywords'], 1):
            print(f"  {i}. {keyword}")

if __name__ == "__main__":
    print("🚀 医疗病历脱敏系统")
    print("="*40)
    print("请选择功能:")
    print("1. 批量处理图片脱敏")
    print("2. 单张图片测试")
    print("3. 评估检测准确率")
    print("="*40)
    
    choice = "1"
    
    if choice == "1":
        # 批量处理文件夹中的图片
        # folder_path = r"D:\data\脱敏用\13tu"
        folder_path = r"D:\data\OCR-脱敏\脱敏验证用图片\原图片"

        print(f"📂 处理文件夹: {folder_path}")
        batch_process_images(folder_path)
    elif choice == "2":
        test_single_image()
    elif choice == "3":
        # 运行评估
        try:
            print("🔍 启动评估程序...")
            run_evaluation(folder_path=r'D:\data\脱敏用\PDF_and_Images_Eval')
        except Exception as e:
            print(f"❌ 评估过程出错: {e}")
            import traceback
            traceback.print_exc()
    else:
        print("❌ 无效选择，默认执行批量处理")
        folder_path = r"D:\data\脱敏用\bug2"
        batch_process_images(folder_path)


