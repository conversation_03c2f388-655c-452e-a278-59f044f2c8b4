"""
java druid库数据库密码加解密
"""
import os
import subprocess


class ConfigTools(object):
    """
    com.alibaba.druid.filter.config.ConfigTools类的python实现
    """

    def decrypt(self, public_key, password):
        # 解密数据库密码
        code = f"""
        import os
        import sys
        import jpype
        from jpype import JClass
        # 启动JVM
        jpype.startJVM(classpath=['./druid-1.2.14.jar'])
        ConfigTools = JClass('com.alibaba.druid.filter.config.ConfigTools')
        public_key = '{public_key}'
        password = '{password}'
        result = ConfigTools.decrypt(public_key, password)
        print(result,  end='')
        """
        cwd = os.path.dirname(os.path.abspath(__file__))
        code = code.replace('        ', '')
        try:
            result = subprocess.run(["python3.9", "-c", code], capture_output=True, text=True, cwd=cwd)
        except:
            result = subprocess.run(["python", "-c", code], capture_output=True, text=True, cwd=cwd)
        assert result.stdout, result.stderr
        return result.stdout
