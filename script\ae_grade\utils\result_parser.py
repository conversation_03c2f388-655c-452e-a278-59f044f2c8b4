# -*- coding: utf-8 -*-
"""
结果解析器
解析和验证LLM返回的YAML/JSON结果
"""
import yaml
import re
from typing import Dict, List, Any, Optional
import logging

logger = logging.getLogger(__name__)

class ResultParser:
    """结果解析器"""
    
    def __init__(self):
        """初始化解析器"""
        # 大模型返回的标准字段格式
        self.required_fields = ["id", "ae_grade", "ae_name"]
        
    def parse_llm_response(self, response: str) -> Optional[Dict[str, Any]]:
        """
        解析LLM返回的响应，支持YAML和JSON格式
        
        Args:
            response: LLM返回的原始响应
            
        Returns:
            解析后的结果字典，失败时返回None
        """
        try:
            # 优先尝试YAML格式解析
            yaml_result = self._try_parse_yaml(response)
            if yaml_result:
                logger.info("成功解析YAML格式响应")
                return yaml_result
            

            logger.error("无法解析响应为YAML")
            return None
            
        except Exception as e:
            logger.error(f"解析LLM响应失败: {e}")
            return None
    
    def _try_parse_yaml(self, response: str) -> Optional[Dict[str, Any]]:
        """尝试解析YAML格式"""
        try:
            # 提取YAML部分
            yaml_str = self._extract_yaml_from_response(response)
            if not yaml_str:
                return None
            
            # 解析YAML
            result = yaml.safe_load(yaml_str)
            
            # 验证结果格式
            if not self._validate_result_format(result):
                logger.error("YAML结果格式验证失败")
                return None
            
            # 标准化结果
            standardized_result = self._standardize_result(result)
            return standardized_result
            
        except yaml.YAMLError as e:
            logger.debug(f"YAML解析失败: {e}")
            return None
        except Exception as e:
            logger.debug(f"YAML处理失败: {e}")
            return None

    def _extract_yaml_from_response(self, response: str) -> Optional[str]:
        """从响应中提取YAML字符串"""
        try:
            # 方法1: 查找```yaml代码块
            yaml_pattern = r'```yaml\s*(.*?)\s*```'
            match = re.search(yaml_pattern, response, re.DOTALL)
            if match:
                return match.group(1).strip()
            
            # 方法2: 查找```代码块，检查是否包含YAML格式
            code_pattern = r'```\s*(.*?)\s*```'
            match = re.search(code_pattern, response, re.DOTALL)
            if match:
                content = match.group(1).strip()
                # 检查是否是YAML格式（以results:开头）
                if content.startswith('results:'):
                    return content
            
            # 方法3: 直接查找results:开头的内容
            results_pattern = r'results:\s*\n(.*?)(?=\n\n|\n[^\s-]|\Z)'
            match = re.search(results_pattern, response, re.DOTALL)
            if match:
                # 重新构建完整的YAML
                yaml_content = f"results:\n{match.group(1)}"
                return yaml_content
            
            # 方法4: 查找以results:开头的行到文档结尾
            lines = response.split('\n')
            yaml_start = -1
            for i, line in enumerate(lines):
                if line.strip().startswith('results:'):
                    yaml_start = i
                    break
            
            if yaml_start >= 0:
                yaml_lines = lines[yaml_start:]
                # 找到YAML内容的结束位置（遇到非YAML格式的行）
                yaml_end = len(yaml_lines)
                for i, line in enumerate(yaml_lines[1:], 1):
                    # 如果遇到不符合YAML格式的行，结束
                    if line.strip() and not (line.startswith('- ') or line.startswith('  ') or line.strip().endswith(':')):
                        yaml_end = i
                        break
                
                yaml_content = '\n'.join(yaml_lines[:yaml_end])
                return yaml_content
            
            return None
            
        except Exception as e:
            logger.error(f"提取YAML失败: {e}")
            return None

    
    def _validate_result_format(self, result: Dict[str, Any]) -> bool:
        """验证结果格式"""
        try:
            # 检查是否有results字段
            if "results" in result:
                results_list = result["results"]
            elif isinstance(result, list):
                results_list = result
            else:
                logger.error("结果格式错误：缺少results字段或不是列表")
                return False
            
            if not isinstance(results_list, list):
                logger.error("results字段不是列表")
                return False
            
            if len(results_list) == 0:
                logger.warning("results列表为空")
                return True
            
            # 检查每个结果项
            for i, item in enumerate(results_list):
                if not isinstance(item, dict):
                    logger.error(f"结果项{i}不是字典")
                    return False
                
                # 检查必需字段 - 大模型标准格式
                missing_fields = [f for f in self.required_fields if f not in item]
                if missing_fields:
                    logger.error(f"结果项{i}缺少必需字段: {missing_fields}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"验证结果格式失败: {e}")
            return False
    
    def _standardize_result(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """标准化结果格式"""
        try:
            # 获取结果列表
            if "results" in result:
                results_list = result["results"]
            else:
                results_list = result
            
            standardized_results = []
            
            for item in results_list:
                # 大模型返回的标准格式：id, ae_grade, ae_name, ae_desc
                standardized_item = {}

                # 标准化ID
                standardized_item["id"] = item.get("id", 0)

                # 标准化CTCAE分级
                grade = item.get("ae_grade", "")
                if grade in [None, "null", "NULL"]:
                    grade = ""
                elif isinstance(grade, (int, float)):
                    grade = str(int(grade))
                elif isinstance(grade, str):
                    grade = grade.strip()
                standardized_item["ae_grade"] = grade

                # 标准化不良事件名称
                ae_name = item.get("ae_name", "")
                if ae_name in [None, "null", "NULL"]:
                    ae_name = ""
                elif isinstance(ae_name, str):
                    ae_name = ae_name.strip()
                standardized_item["ae_name"] = ae_name

                # 标准化不良事件定义
                ae_desc = item.get("ae_desc", "")
                if ae_desc in [None, "null", "NULL"]:
                    ae_desc = ""
                elif isinstance(ae_desc, str):
                    ae_desc = ae_desc.strip()
                standardized_item["ae_desc"] = ae_desc
                
                standardized_results.append(standardized_item)
            
            return {"results": standardized_results}
            
        except Exception as e:
            logger.error(f"标准化结果失败: {e}")
            return result
    
    def validate_against_input(self, parsed_result: Dict[str, Any], 
                             input_items: List[Dict[str, Any]], 
                             auto_complete: bool = True) -> tuple[bool, Optional[Dict[str, Any]]]:
        """
        验证解析结果与输入数据的一致性，并自动补全缺失项
        
        Args:
            parsed_result: 解析后的结果
            input_items: 输入的检验项列表
            auto_complete: 是否启用自动补全功能
            
        Returns:
            tuple[bool, Optional[Dict[str, Any]]]: 
            - 第一个值: 验证是否通过（True表示通过或已补全）
            - 第二个值: 补全后的完整结果（如果有补全）或None
        """
        try:
            results_list = parsed_result.get("results", [])
            
            # 检查数量是否一致
            if len(results_list) < len(input_items):
                if auto_complete:
                    logger.warning(f"大模型输出数量不足 - 输入: {len(input_items)}项, 输出: {len(results_list)}项, 缺失: {len(input_items) - len(results_list)}项")
                    
                    # 检测缺失项
                    missing_items = self._detect_missing_items(parsed_result, input_items)
                    
                    if missing_items:
                        logger.info("开始自动补全缺失的检验项")
                        # 自动补全缺失项
                        completed_result = self._auto_complete_missing_items(missing_items, parsed_result)
                        
                        # 重新验证补全后的结果
                        final_validation = self._validate_completed_result(completed_result, input_items)
                        return final_validation, completed_result if final_validation else None
                    else:
                        logger.error("无法检测到具体的缺失项")
                        return False, None
                else:
                    logger.error(f"结果数量({len(results_list)})少于输入数量({len(input_items)})")
                    return False, None
                    
            elif len(results_list) > len(input_items):
                logger.warning(f"结果数量({len(results_list)})大于输入数量({len(input_items)})")
                # 数量过多时继续验证，但记录警告
            
            # 检查序号是否匹配
            validation_result = self._validate_completed_result(parsed_result, input_items)
            return validation_result, None
            
        except Exception as e:
            logger.error(f"验证结果一致性失败: {e}")
            return False, None
    
    def _validate_completed_result(self, result: Dict[str, Any], 
                                 input_items: List[Dict[str, Any]]) -> bool:
        """
        验证完整结果的序号匹配性
        
        Args:
            result: 要验证的结果
            input_items: 输入的检验项列表
            
        Returns:
            验证是否通过
        """
        try:
            results_list = result.get("results", [])
            
            # 检查序号是否匹配
            input_seq_nums = set()
            for item in input_items:
                seq_num = item.get("序号", item.get("id", 0))
                input_seq_nums.add(seq_num)
            
            result_seq_nums = set()
            for item in results_list:
                seq_num = item.get("序号", 0)
                result_seq_nums.add(seq_num)
            
            if input_seq_nums != result_seq_nums:
                logger.error(f"序号不匹配: 输入{input_seq_nums}, 结果{result_seq_nums}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"验证完整结果失败: {e}")
            return False
    
    def _detect_missing_items(self, parsed_result: Dict[str, Any], 
                            input_items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        检测缺失的输入项
        
        Args:
            parsed_result: 解析后的结果
            input_items: 输入的检验项列表
            
        Returns:
            缺失的输入项列表
        """
        try:
            # 获取解析结果中的序号集合
            results_list = parsed_result.get("results", [])
            result_seq_nums = set()
            for item in results_list:
                seq_num = item.get("序号", 0)
                result_seq_nums.add(seq_num)
            
            # 获取输入项的序号集合，支持"序号"和"id"字段
            input_seq_nums = set()
            input_items_map = {}
            for item in input_items:
                seq_num = item.get("序号", item.get("id", 0))
                input_seq_nums.add(seq_num)
                input_items_map[seq_num] = item
            
            # 找出缺失的序号
            missing_seq_nums = input_seq_nums - result_seq_nums
            
            # 根据缺失序号找到对应的原始输入项
            missing_items = []
            for seq_num in missing_seq_nums:
                if seq_num in input_items_map:
                    missing_items.append(input_items_map[seq_num])
            
            logger.info(f"检测到缺失项 - 输入总数: {len(input_items)}, 输出总数: {len(results_list)}, 缺失数: {len(missing_items)}")
            return missing_items
            
        except Exception as e:
            logger.error(f"检测缺失项失败: {e}")
            return []
    
    def _create_default_item(self, input_item: Dict[str, Any]) -> Dict[str, Any]:
        """
        为缺失项创建默认结果
        
        Args:
            input_item: 原始输入项
            
        Returns:
            默认结果项
        """
        seq_num = input_item.get("序号", input_item.get("id", 0))
        
        default_item = {
            "序号": seq_num,
            "状态": "",  # 空字符串，区别于正常的"-"状态
            "CTCAE分级": "",
            "不良事件名称": "CTCAE未涉及，请研究者判定",
            "不良事件定义": ""
        }
        
        return default_item
    
    def _auto_complete_missing_items(self, missing_items: List[Dict[str, Any]], 
                                   parsed_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        自动补全缺失的项目
        
        Args:
            missing_items: 缺失的输入项列表
            parsed_result: 当前解析结果
            
        Returns:
            补全后的完整结果
        """
        try:
            if not missing_items:
                return parsed_result
            
            # 获取当前结果列表
            current_results = parsed_result.get("results", [])
            
            # 为每个缺失项创建默认项
            default_items = []
            for missing_item in missing_items:
                default_item = self._create_default_item(missing_item)
                default_items.append(default_item)
                seq_num = default_item["序号"]
                logger.info(f"补全缺失项 - 序号: {seq_num}, 状态: 空, 名称: CTCAE未涉及，请研究者判定")
            
            # 合并结果并按序号排序
            all_results = current_results + default_items
            all_results.sort(key=lambda x: x.get("序号", 0))
            
            completed_result = {"results": all_results}
            
            logger.info(f"自动补全完成 - 补全项数: {len(default_items)}, 最终总数: {len(all_results)}")
            return completed_result
            
        except Exception as e:
            logger.error(f"自动补全失败: {e}")
            return parsed_result

    def create_fallback_result(self, input_items: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        创建降级结果（当解析失败时使用）
        
        Args:
            input_items: 输入的检验项列表
            
        Returns:
            降级结果
        """
        fallback_results = []
        
        for item in input_items:
            seq_num = item.get("序号", item.get("id", 0))
            fallback_item = {
                "序号": seq_num,
                "状态": "-",
                "CTCAE分级": "",
                "不良事件名称": "解析失败，请人工判定",
                "不良事件定义": ""
            }
            fallback_results.append(fallback_item)
        
        return {"results": fallback_results}
