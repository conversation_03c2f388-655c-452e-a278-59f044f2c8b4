from datetime import timedelta
from django.conf import settings
from rest_framework import serializers

from common.minio_client import get_minio_client
from common.models import BaseFileModel
from common.serializers import FileUrlMixin

from . import models


class PrivacyRuleConfigAddRequestSerializer(serializers.Serializer):
    field_name = serializers.CharField(label="隐私字段名称", required=True)
    status_ind = serializers.CharField(label="字段状态", required=False)

class PrivacyRuleConfigResponseSerializer(serializers.ModelSerializer):

    class Meta:
        model = models.PrivacyRuleConfig
        fields = '__all__'  # ['test_type', 'test_code', 'test_name', 'test_unit', 'test_value', 'reference_value', 'reference_range_min', 'reference_range_max']
        # exclude = ['project', 'project_site', 'subject', 'subject_epoch', 'patient', 'subject_visit', 'subject_item']


class PrivacyRuleImportExcelRequestSerializer(serializers.Serializer):
    file = serializers.FileField(required=True)  # 文件上传字段