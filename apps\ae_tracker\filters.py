
from django.db.models import Q
from django_filters import rest_framework as filters


class OcrResultListFilter(filters.FilterSet):
    subject_id = filters.CharFilter(field_name='subject_id', label='受试者id', required=False)
    # subject_epoch_id = filters.CharFilter(field_name='subject_epoch_id', label='阶段ID')
    # subject_visit_id = filters.CharFilter(field_name='subject_visit_id', label='访视ID')
    test_type = filters.CharFilter(field_name='test_type', label='检查结果值类型：数值/定性')
    subject_item_id = filters.CharFilter(field_name='subject_item_id', label='访视项目ID')
    subject_item_label = filters.CharFilter(field_name='subject_item__label', label='操作项名称')
    test_name = filters.CharFilter(field_name='test_name', label='检查名称')
    abnormal_flag = filters.CharFilter(field_name='abnormal_flag', label='是否为异常值；0：正常， 1：异常')
    ae_tracker_flag = filters.CharFilter(field_name='ae_tracker_flag', label='是否为tracker；0：不是， 1：是')
    # query = filters.CharFilter(method='filter_by_query', label='综合查询（模糊匹配）')

    # def filter_by_query(self, queryset, name, value):
    #     """
    #     综合查询，AND 关系
    #     """
    #     return queryset.filter(
    #         Q(subject_id=value) & Q(epoch_id=value) & Q(visit_id=value) & Q(item_id=value)
    #     )


class OcrResultTextFilter(filters.FilterSet):
    subject_id = filters.CharFilter(field_name='subject_id', label='受试者id', required=False)
    subject_item_id = filters.CharFilter(field_name='subject_item_id', label='访视项目ID', required=False)


class ProjectMaterialInfoFilter(filters.FilterSet):
    category = filters.CharFilter(field_name='category', lookup_expr='exact', label='素材分类')


class SubjectItemFilter(filters.FilterSet):
    subject_id = filters.CharFilter(field_name='subject.subject_id', label='受试者ID', required=True)
    subject_item_id = filters.CharFilter(field_name='subject_item_id', label='受试者检查项ID', required=True)

from apps.system.models import OperationLog
class OperationLogFilter(filters.FilterSet):

    class Meta:
        model = OperationLog
        fields = ['target_type', 'target_id']