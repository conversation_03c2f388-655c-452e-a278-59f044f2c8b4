# -*- coding: utf-8 -*-
"""
页面级并行处理器实现

本模块实现了AE结构化处理工作流的页面级并行处理架构，包括：
- PageData: 页面数据封装
- PageProcessor: 页面处理器
- ResultAggregator: 结果聚合器
- ParallelPageProcessor: 并行处理管理器
- ParallelIntegrationAdapter: 集成适配器
"""

import os
import sys
import time
import uuid
import logging
import threading
import psutil
from dataclasses import dataclass
from typing import List, Dict, Optional, Any, TYPE_CHECKING
from concurrent.futures import ThreadPoolExecutor, as_completed

if TYPE_CHECKING:
    # TestItem将在需要时动态导入
    pass
from collections import defaultdict

# 获取项目根目录并添加到路径
current_script_path = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_script_path)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# TestItem将在需要时动态导入

logger = logging.getLogger(__name__)


@dataclass
class PageData:
    """页面数据封装类，包含页面处理所需的所有信息"""
    
    # 基础信息
    page_id: str                                    # 页面唯一标识
    page_content: str                               # 页面文本内容
    page_num: int                                   # 页码
    file_id: str                                    # 源文件ID (subject_medical_info_id)
    
    # OCR信息
    ocr_blocks: List[Dict]                          # 该页OCR块列表
    
    # 元数据（保持完整的上下文信息）
    metadata: Dict[str, Any]                        # 完整元数据字典
    
    # 任务信息
    task_info: Dict[str, Any]                       # 任务上下文信息
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.page_id:
            self.page_id = f"{self.file_id}_page_{self.page_num}_{uuid.uuid4().hex[:8]}"
    
    @classmethod
    def from_file_data(cls, page_content: str, page_num: int, 
                      file_metadata: Dict, ocr_blocks: List[Dict] = None) -> 'PageData':
        """从文件数据创建页面数据"""
        return cls(
            page_id="",  # 将在__post_init__中生成
            page_content=page_content,
            page_num=page_num,
            file_id=str(file_metadata.get('subject_medical_info_id', '')),
            ocr_blocks=ocr_blocks or [],
            metadata=file_metadata.copy(),
            task_info=file_metadata.get('task_info', {})
        )


@dataclass
class PageResult:
    """页面处理结果"""
    page_id: str
    page_num: int
    file_id: str
    test_items: List[Any]  # TestItem对象列表
    processing_time: float
    success: bool
    error_message: Optional[str]
    metadata: Dict[str, Any]


@dataclass
class FileResult:
    """文件处理结果"""
    file_id: str
    test_items: List[Any]  # TestItem对象列表
    total_pages: int
    successful_pages: int
    failed_pages: List[int]
    total_processing_time: float
    metadata: Dict[str, Any]
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        return self.successful_pages / self.total_pages if self.total_pages > 0 else 0
    
    @property
    def has_failures(self) -> bool:
        """是否有失败的页面"""
        return len(self.failed_pages) > 0


class PageProcessor:
    """页面处理器，负责处理单个页面的业务逻辑"""
    
    def __init__(self, enable_coordinate_extraction: bool = True):
        self.enable_coordinate_extraction = enable_coordinate_extraction
        self.logger = logger
    
    def process_page(self, page_data: PageData) -> PageResult:
        """
        处理单个页面
        
        Args:
            page_data: 页面数据
            
        Returns:
            PageResult: 页面处理结果
        """
        try:
            start_time = time.time()
            
            # 调用现有的单页处理逻辑
            from script.test_result_format_ae_ocr.main import process_single_page_ocr
            
            test_items = process_single_page_ocr(
                page_content=page_data.page_content,
                page_num=page_data.page_num,
                task_info=page_data.task_info,
                words_block_list=page_data.ocr_blocks
            )
            
            processing_time = time.time() - start_time
            
            return PageResult(
                page_id=page_data.page_id,
                page_num=page_data.page_num,
                file_id=page_data.file_id,
                test_items=test_items,
                processing_time=processing_time,
                success=True,
                error_message=None,
                metadata=page_data.metadata
            )
            
        except Exception as e:
            self.logger.error(f"页面 {page_data.page_id} 处理失败: {e}")
            return PageResult(
                page_id=page_data.page_id,
                page_num=page_data.page_num,
                file_id=page_data.file_id,
                test_items=[],
                processing_time=0,
                success=False,
                error_message=str(e),
                metadata=page_data.metadata
            )


class ResultAggregator:
    """结果聚合器，负责将页面结果聚合为文件结果"""
    
    def __init__(self):
        self.logger = logger
    
    def aggregate_results(self, page_results: List[PageResult]) -> FileResult:
        """
        聚合页面结果为文件结果
        
        Args:
            page_results: 页面结果列表
            
        Returns:
            FileResult: 聚合后的文件结果
        """
        if not page_results:
            raise ValueError("页面结果列表不能为空")
        
        # 按文件ID分组
        file_groups = defaultdict(list)
        for result in page_results:
            file_groups[result.file_id].append(result)
        
        file_results = []
        
        for file_id, results in file_groups.items():
            # 排序页面结果
            results.sort(key=lambda x: x.page_num)
            
            # 聚合测试项目
            all_test_items = []
            failed_pages = []
            total_processing_time = 0
            
            for result in results:
                if result.success:
                    all_test_items.extend(result.test_items)
                    total_processing_time += result.processing_time
                else:
                    failed_pages.append(result.page_num)
            
            # 处理seq字段连续性
            all_test_items = self._ensure_seq_continuity(all_test_items, results[0].metadata)
            
            # 处理collect_time
            all_test_items = self._process_collect_times(all_test_items, results[0].metadata)
            
            file_result = FileResult(
                file_id=file_id,
                test_items=all_test_items,
                total_pages=len(results),
                successful_pages=len(results) - len(failed_pages),
                failed_pages=failed_pages,
                total_processing_time=total_processing_time,
                metadata=results[0].metadata
            )
            
            file_results.append(file_result)
        
        return file_results[0] if len(file_results) == 1 else file_results
    
    def _ensure_seq_continuity(self, test_items: List[Any], metadata: Dict) -> List[Any]:
        """确保seq字段的连续性"""
        try:
            from apps.ae_tracker.models import TestResult
            
            subject_id = metadata.get('subject_id')
            subject_item_id = metadata.get('subject_item_id')
            
            max_seq_value = TestResult.objects.filter(
                subject_id=subject_id,
                subject_item_id=subject_item_id,
                delete_flag=0
            ).values_list('seq', flat=True).order_by('-seq').first() or 0
            
            # 为每个测试项目分配连续的seq
            for i, item in enumerate(test_items):
                if hasattr(item, 'seq'):
                    item.seq = max_seq_value + i + 1
        except Exception as e:
            self.logger.warning(f"seq字段处理失败: {e}")
        
        return test_items
    
    def _process_collect_times(self, test_items: List[Any], metadata: Dict) -> List[Any]:
        """处理collect_time逻辑"""
        try:
            from common.tools import collect_time_choice
            
            # 构建data参数（从metadata中提取）
            data = metadata.get('visit_data', [])
            
            # 按页码分组处理collect_time
            page_collect_times = {}
            for item in test_items:
                page_num = getattr(item, 'page_num', 0)
                if page_num not in page_collect_times:
                    page_collect_times[page_num] = collect_time_choice(
                        getattr(item, 'collect_time', None), data
                    )
            
            # 应用collect_time
            for item in test_items:
                page_num = getattr(item, 'page_num', 0)
                if hasattr(item, 'collect_time'):
                    item.collect_time = page_collect_times.get(page_num)
        except Exception as e:
            self.logger.warning(f"collect_time处理失败: {e}")
        
        return test_items


class ResourceManager:
    """资源管理器，动态调整并发参数"""
    
    def __init__(self):
        self.logger = logger
        self._lock = threading.Lock()
        self.current_config = self._get_default_config()
        
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        cpu_count = psutil.cpu_count()
        memory_gb = psutil.virtual_memory().total / (1024**3)
        
        # 基于系统资源动态计算并发度
        if memory_gb >= 16 and cpu_count >= 8:
            max_workers = min(5, cpu_count - 2)
        elif memory_gb >= 8 and cpu_count >= 4:
            max_workers = min(3, cpu_count - 1)
        else:
            max_workers = 2
            
        return {
            'max_workers': max_workers,
            'timeout': 300,  # 5分钟
            'page_timeout': 60,  # 单页1分钟
            'memory_threshold': 0.8,  # 内存使用率阈值
            'cpu_threshold': 0.9,  # CPU使用率阈值
        }
    
    def get_optimal_config(self) -> Dict[str, Any]:
        """获取当前最优配置"""
        with self._lock:
            # 检查系统资源使用情况
            memory_percent = psutil.virtual_memory().percent / 100
            cpu_percent = psutil.cpu_percent(interval=1) / 100
            
            config = self.current_config.copy()
            
            # 根据资源使用情况调整并发度
            if memory_percent > config['memory_threshold']:
                config['max_workers'] = max(1, config['max_workers'] - 1)
                self.logger.warning(f"内存使用率过高 ({memory_percent:.1%})，降低并发度至 {config['max_workers']}")
            
            if cpu_percent > config['cpu_threshold']:
                config['max_workers'] = max(1, config['max_workers'] - 1)
                self.logger.warning(f"CPU使用率过高 ({cpu_percent:.1%})，降低并发度至 {config['max_workers']}")
            
            return config


class ParallelPageProcessor:
    """并行页面处理管理器"""

    def __init__(self, max_workers: int = 3, timeout: int = 300):
        self.max_workers = max_workers
        self.timeout = timeout
        self.processor = PageProcessor()
        self.aggregator = ResultAggregator()
        self.resource_manager = ResourceManager()
        self.logger = logger
        self._stats_lock = threading.Lock()  # 线程安全的统计信息锁

    def process_file_parallel(self, ocr_text: str, task_info: dict = None,
                             ocr_blocks: Optional[List[Dict]] = None,
                             file_metadata: Dict = None) -> FileResult:
        """
        并行处理单个文件的所有页面

        Args:
            ocr_text: OCR识别的文本，可能包含多页
            task_info: 任务信息字典
            ocr_blocks: OCR文本块列表
            file_metadata: 文件元数据

        Returns:
            FileResult: 文件处理结果
        """
        start_time = time.time()

        # 获取最优配置
        config = self.resource_manager.get_optimal_config()
        actual_max_workers = min(self.max_workers, config['max_workers'])

        # 1. 页面拆分
        from script.test_result_format_ae_ocr.main import split_ocr_text_by_pages
        pages, page_blocks_map = split_ocr_text_by_pages(ocr_text, ocr_blocks)

        if not pages:
            self.logger.warning("没有有效的页面内容")
            return FileResult(
                file_id=file_metadata.get('subject_medical_info_id', '') if file_metadata else '',
                test_items=[],
                total_pages=0,
                successful_pages=0,
                failed_pages=[],
                total_processing_time=0,
                metadata=file_metadata or {}
            )

        # 2. 创建页面数据对象
        page_data_list = []
        for page_content, page_num in pages:
            current_page_blocks = page_blocks_map.get(page_num, [])

            # 构建完整的文件元数据
            complete_metadata = {
                **(file_metadata or {}),
                'task_info': task_info or {},
                'visit_data': file_metadata.get('visit_data', []) if file_metadata else []
            }

            page_data = PageData.from_file_data(
                page_content=page_content,
                page_num=page_num,
                file_metadata=complete_metadata,
                ocr_blocks=current_page_blocks
            )
            page_data_list.append(page_data)

        # 3. 并行处理页面
        page_results = self._process_pages_parallel(page_data_list, actual_max_workers)

        # 4. 聚合结果
        file_result = self.aggregator.aggregate_results(page_results)

        # 5. 更新处理时间
        total_time = time.time() - start_time
        file_result.total_processing_time = total_time

        # 6. 输出统计信息
        self._log_processing_stats(file_result, len(pages), actual_max_workers)

        return file_result

    def _process_pages_parallel(self, page_data_list: List[PageData], max_workers: int) -> List[PageResult]:
        """并行处理页面列表"""
        page_results = []

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有页面处理任务
            future_to_page = {
                executor.submit(self.processor.process_page, page_data): page_data
                for page_data in page_data_list
            }

            # 收集结果
            for future in as_completed(future_to_page, timeout=self.timeout):
                page_data = future_to_page[future]
                try:
                    result = future.result(timeout=30)  # 单页面30秒超时
                    page_results.append(result)

                    if result.success:
                        self.logger.info(f"页面 {result.page_num} 处理成功，耗时 {result.processing_time:.2f}秒")
                    else:
                        self.logger.error(f"页面 {result.page_num} 处理失败: {result.error_message}")

                except Exception as e:
                    self.logger.error(f"页面 {page_data.page_num} 处理异常: {e}")
                    # 创建失败结果
                    error_result = PageResult(
                        page_id=page_data.page_id,
                        page_num=page_data.page_num,
                        file_id=page_data.file_id,
                        test_items=[],
                        processing_time=0,
                        success=False,
                        error_message=str(e),
                        metadata=page_data.metadata
                    )
                    page_results.append(error_result)

        # 按页码排序
        page_results.sort(key=lambda x: x.page_num)
        return page_results

    def _log_processing_stats(self, file_result: FileResult, total_pages: int, max_workers: int):
        """记录处理统计信息"""
        with self._stats_lock:
            self.logger.info("=" * 50)
            self.logger.info("📊 并行处理统计信息")
            self.logger.info(f"📄 文件ID: {file_result.file_id}")
            self.logger.info(f"📄 总页数: {total_pages}")
            self.logger.info(f"🔧 并发度: {max_workers}")
            self.logger.info(f"✅ 成功页数: {file_result.successful_pages}")
            self.logger.info(f"❌ 失败页数: {len(file_result.failed_pages)}")
            self.logger.info(f"📊 成功率: {file_result.success_rate:.1%}")
            self.logger.info(f"🔬 总检验项目数: {len(file_result.test_items)}")
            self.logger.info(f"⏱️ 总处理时间: {file_result.total_processing_time:.2f}秒")
            if file_result.failed_pages:
                self.logger.warning(f"⚠️ 失败页面: {file_result.failed_pages}")
            self.logger.info("=" * 50)


class ParallelIntegrationAdapter:
    """并行处理集成适配器，用于与现有代码无缝集成"""

    def __init__(self, enable_parallel: bool = True, max_workers: int = 3):
        self.enable_parallel = enable_parallel
        self.parallel_processor = ParallelPageProcessor(max_workers=max_workers) if enable_parallel else None
        self.logger = logger

    def process_medical_ocr_enhanced(self, ocr_text: str, task_info: dict = None,
                                   ocr_blocks: Optional[List[Dict]] = None,
                                   file_metadata: Dict = None) -> Dict:
        """
        增强版医疗OCR处理函数，兼容现有接口

        Args:
            ocr_text: OCR识别的文本
            task_info: 任务信息字典
            ocr_blocks: OCR文本块列表
            file_metadata: 文件元数据

        Returns:
            Dict: 兼容原有格式的处理结果
        """
        try:
            if self.enable_parallel and self.parallel_processor:
                # 使用并行处理
                self.logger.info("🚀 启用页面级并行处理")
                file_result = self.parallel_processor.process_file_parallel(
                    ocr_text=ocr_text,
                    task_info=task_info,
                    ocr_blocks=ocr_blocks,
                    file_metadata=file_metadata
                )

            else:
                # 使用原有串行处理
                self.logger.info("📝 使用串行处理模式")
                from script.test_result_format_ae_ocr.main import process_medical_ocr
                test_items = process_medical_ocr(ocr_text, task_info, ocr_blocks)

                # 构建兼容的FileResult
                file_result = FileResult(
                    file_id=file_metadata.get('subject_medical_info_id', '') if file_metadata else '',
                    test_items=test_items,
                    total_pages=1,  # 串行模式假设为单页
                    successful_pages=1,
                    failed_pages=[],
                    total_processing_time=0,
                    metadata=file_metadata or {}
                )

            # 转换为原有格式
            return self._convert_to_legacy_format(file_result)

        except Exception as e:
            self.logger.error(f"医疗OCR处理失败: {e}")
            return {"test_results": []}

    def _convert_to_legacy_format(self, file_result: FileResult) -> Dict:
        """转换为原有的JSON格式"""
        test_results = []
        for item in file_result.test_items:
            item_dict = item.to_dict()
            # 过滤掉不兼容的字段
            filtered_dict = {k: v for k, v in item_dict.items() if k != 'test_text'}
            test_results.append(filtered_dict)

        return {"test_results": test_results}
