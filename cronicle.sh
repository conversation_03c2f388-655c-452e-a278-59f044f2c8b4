#!/bin/bash

# 获取参数指定的文件的路径
given_path="$1"

# 判断是否是相对路径并处理
if [[ -n $1 && $given_path != /* ]]; then
    given_path=$(readlink -f "$1")
fi

# 获取环境变量 DJANGO_SETTINGS_MODULE 的值并提取文件名
settings_module=${DJANGO_SETTINGS_MODULE:-"smo.settings.test"}
settings_suffix=${settings_module#smo.settings.}

# 设置 CONFIG_FILE
CONFIG_FILE="${given_path:-/app/etc/cronicle/${settings_suffix}.txt}"

# 打印 CONFIG_FILE
echo "CONFIG_FILE is set to: $CONFIG_FILE"

# migrate database
echo "Migrating database..."
/usr/bin/python3.9 manage.py migrate
if [[ $? -ne 0 ]]; then
    echo "Database migration failed."
    exit 1
fi

# setup Cronicle
echo "Setting up Cronicle..."
/opt/cronicle/bin/control.sh setup
if [[ $? -ne 0 ]]; then
    echo "Cronicle setup failed."
    exit 2
fi

# import data
echo "Importing data from $CONFIG_FILE..."
/opt/cronicle/bin/control.sh import "$CONFIG_FILE"
if [[ $? -ne 0 ]]; then
    echo "Data import failed."
    exit 3
fi

# start Cronicle
echo "Starting Cronicle..."
/opt/cronicle/bin/control.sh start
if [[ $? -ne 0 ]]; then
    echo "Cronicle start failed."
    exit 4
fi

echo "All tasks completed successfully."
exit 0
