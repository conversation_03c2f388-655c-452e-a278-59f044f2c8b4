worker_processes 4;

user nobody nogroup;
error_log  /app/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
  worker_connections 1024;
  accept_mutex on;
}

http {
  include mime.types;
  default_type application/octet-stream;
  
  # 定义自定义日志格式，包含Origin和Host
  log_format custom_format '$remote_addr - $remote_user [$time_local] "$request" '
                           '$status $body_bytes_sent "$http_referer" '
                           '"$http_user_agent" "$http_origin" "$http_host"';
  
  # 使用自定义日志格式
  access_log /app/log/nginx/access.log custom_format;
  
  sendfile on;

  # 定义允许的源
  map $http_origin $cors_allow_origin {
    default "";
    "https://ddapp-test.smo-clinplus.com:18082" "https://ddapp-test.smo-clinplus.com:18082";
    "http://**************:30377/" "http://**************:30377/";
  }

  upstream app_server {
    server unix:/tmp/gunicorn.sock fail_timeout=0;
  }

  server {
    listen 80 default_server;
    return 444;
  }

  server {
    listen 8000;
    client_max_body_size 100M;
    keepalive_timeout 5;
    root /app/www;

    # /ai 路径 - 不添加CORS
    location /ai {
      rewrite ^/ai/(.*) /$1 break;
      try_files $uri @proxy_to_app;
    }

    # 根路径 - 添加CORS
    location / {      
      try_files $uri @proxy_to_app_with_cors;
    }

    # 标准代理配置（不添加CORS，用于/ai路径）
    location @proxy_to_app {
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $scheme;
      proxy_set_header Host $http_host;
      proxy_redirect off;
      proxy_pass http://app_server;
    }

    # 带CORS的代理配置（用于根路径）
    location @proxy_to_app_with_cors {
      # 处理 OPTIONS 预检请求
      if ($request_method = 'OPTIONS') {
        # 使用具体源而非通配符
        # add_header 'Access-Control-Allow-Origin' $cors_allow_origin always;
        add_header "Access-Control-Allow-Origin" $http_origin always;
        add_header 'Access-Control-Allow-Credentials' 'true' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE, PATCH' always;
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,X-CSRFToken,X-Access-Token,X-Chat-Name' always;
        add_header 'Access-Control-Max-Age' 1728000 always;
        add_header 'Content-Type' 'text/plain charset=UTF-8' always;
        add_header 'Content-Length' 0 always;
        add_header 'Vary' 'Origin' always;
        add_header 'Vary' 'Access-Control-Request-Method' always;
        add_header 'Vary' 'Access-Control-Request-Headers' always;
        return 204;
      }
      
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $scheme;
      proxy_set_header Host $http_host;
      proxy_redirect off;
      proxy_pass http://app_server;
      
      # 为非OPTIONS请求添加CORS头
      # add_header 'Access-Control-Allow-Origin' $cors_allow_origin always;
      add_header "Access-Control-Allow-Origin" $http_origin always;
      add_header 'Access-Control-Allow-Credentials' 'true' always;
      add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE, PATCH' always;
      add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,X-CSRFToken,X-Access-Token,X-Chat-Name' always;
      add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range' always;
      add_header 'Vary' 'Origin' always;
    }
  }
}