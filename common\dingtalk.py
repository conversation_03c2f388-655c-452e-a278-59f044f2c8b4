# -*- coding: utf-8 -*-
import os
import requests
import django
from django.conf import settings


def get_access_token(app_key: str, app_secret: str) -> str:
    """
    获取钉钉访问令牌
    :param app_key: 钉钉应用的AppKey
    :param app_secret: 钉钉应用的AppSecret
    :return: 访问令牌
    """
    url = f"https://api.dingtalk.com/v1.0/oauth2/accessToken"
    try:
        # 发送请求并获取响应
        response = requests.post(url, json={'appKey': app_key, 'appSecret': app_secret})
        return response.json()
    except Exception as err:
        # 处理异常
        print(f"Error message: {err}")
        raise err


def get_user_info(access_token: str, code: str) -> str:
    """
    通过免登码获取用户 userid
    :param access_token: 钉钉访问令牌
    :param code: 免登授权码
    :return: 用户 userid
    """
    url = f"https://oapi.dingtalk.com/topapi/v2/user/getuserinfo?access_token={access_token}"

    try:
        # 发送请求并获取响应
        response = requests.post(url, data={'code': code})
        return response.json()
    except Exception as err:
        # 处理异常
        print(f"Error message: {err}")
        raise err


def airflow_notify_success_dingtalk(context):
    # webhook_url = 'https://oapi.dingtalk.com/robot/send?access_token=03c676f94d70dc487e1d88be1b4cd361dda971c5e7cdcdcd1035e383dcdec1ef'

    # dag_id = context['dag'].dag_id
    # task_id = context['task_instance'].task_id
    # execution_time = context['execution_date'].in_timezone("Asia/Shanghai")

    # message = {
    #     "msgtype": "text",
    #     "text": {
    #         "content": f"✅ DAG {dag_id} 的任务 {task_id} 执行成功！\n"
    #                    f"执行时间: {execution_time}"
    #     },
    #     "at": {
    #         "isAtAll": False
    #     }
    # }

    # response = requests.post(webhook_url, json=message)
    # if response.status_code != 200:
    #     print(f"钉钉成功通知失败，响应代码: {response.status_code}")
    pass


def airflow_notify_failure_dingtalk(context):
    dag_id = context['dag'].dag_id
    task_id = context['task_instance'].task_id
    dag_run_id = context['dag_run'].run_id
    execution_time = context['execution_date'].in_timezone("Asia/Shanghai")
    error_message = context.get('exception')
    webhook_url = 'https://oapi.dingtalk.com/robot/send?access_token=03c676f94d70dc487e1d88be1b4cd361dda971c5e7cdcdcd1035e383dcdec1ef'
    url = f"http://{settings.AIRFLOW_ENDPOINT}/dags/{dag_id}/grid?tab=graph&dag_run_id={dag_run_id}"

    env = os.environ.get('DJANGO_SETTINGS_MODULE', 'smo.settings.test')
    env = str.upper(env.split('.')[-1])

    message = {
        "msgtype": "markdown",
        "markdown": {
            "title": f"DAG {dag_id} 任务失败",
            "text": f"#### ❌ 任务执行失败通知\n"
                    f"- **ENV**: {env}\n"
                    f"- **DAG**: {dag_id}\n"
                    f"- **任务**: {task_id}\n"
                    f"- **执行时间**: {execution_time}\n"
                    f"- **错误信息**: {error_message}\n"
                    f"- [🔗 查看任务详情]({url})"
        },
        "at": {
            "isAtAll": True
        }
    }

    response = requests.post(webhook_url, json=message)
    if response.status_code != 200:
        print(f"钉钉失败通知失败，响应代码: {response.status_code}")


if __name__ == '__main__':
    # 示例使用
    # python -m common.dingtalk
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smo.settings.test')
    django.setup()

    # app_key = 'dinggblpwxtne5eey9kz'
    # app_secret = 'QMYqZojVPxNMUA_W2ciSrwhrI5WLshhFlaBcKPKjfxOSUt0kbqhwu__fmgpVzNYF'
    # code = '238a739bbfde33fdbe2c7865391473df'
    # access_token = get_access_token(app_key, app_secret)

    # print(f"Access Token: {access_token}")
    # access_token = access_token['accessToken']

    # user_info = get_user_info(access_token, code)
    # print(f"User Info: {user_info}")

    # from types import SimpleNamespace
    # from datetime import datetime
    # import pytz

    # # 模拟 context 数据
    # from types import SimpleNamespace
    # import pendulum

    # # 用 pendulum 创建 Asia/Shanghai 时区的时间
    # execution_date = pendulum.datetime(2025, 4, 14, 14, 59, 8, tz="Asia/Shanghai")

    # context = {
    #     'dag': SimpleNamespace(dag_id='crf_generation'),
    #     'task_instance': SimpleNamespace(task_id='process_task'),
    #     'dag_run': SimpleNamespace(run_id='manual__2025-04-14T14:59:08+08:00__cef2adcc'),
    #     'execution_date': execution_date,
    #     'exception': 'FileNotFoundError: [Errno 2] No such file or directory: /data/file.csv'
    # }

    # airflow_notify_failure_dingtalk(context)
