import io
import json
import cv2
import time
import ast
import numpy as np
from PIL import Image, ImageDraw
from common.ocr_tools import getBase64, getOcrResult, useLlmApi, sponsor_list, rotate_and_resize_image


def calculate_keyword_precise_coordinates(text_block, keyword, location):
    """
    计算关键词在文本块中的精确坐标

    Args:
        text_block: OCR识别的完整文本
        keyword: 需要遮挡的敏感词
        location: 文本块的四个角坐标 [[x1,y1], [x2,y2], [x3,y3], [x4,y4]]

    Returns:
        list: 敏感词的坐标列表，每个匹配返回 [x1, y1, x2, y2]
    """
    if keyword not in text_block:
        return []

    # 获取文本块的边界坐标
    x1, y1 = location[0]  # 左上角
    x2, y2 = location[1]  # 右上角
    x3, y3 = location[2]  # 右下角
    x4, y4 = location[3]  # 左下角

    # 计算文本块的实际宽度和高度
    block_width = max(x2, x3) - min(x1, x4)
    block_height = max(y3, y4) - min(y1, y2)

    # 计算每个字符的平均宽度（考虑中英文混合）
    char_count = len(text_block)
    if char_count == 0:
        return []

    # 中文字符一般比英文字符宽，进行加权计算
    chinese_chars = sum(1 for char in text_block if '\u4e00' <= char <= '\u9fff')
    english_chars = char_count - chinese_chars

    effective_char_count = chinese_chars * 1.8 + english_chars
    avg_char_width = block_width / effective_char_count if effective_char_count > 0 else block_width / char_count

    keyword_coords = []
    start_index = 0

    # 查找所有匹配的关键词位置
    while True:
        index = text_block.find(keyword, start_index)
        if index == -1:
            break

        # 计算关键词前面字符的有效宽度
        prefix_text = text_block[:index]
        prefix_chinese = sum(1 for char in prefix_text if '\u4e00' <= char <= '\u9fff')
        prefix_english = len(prefix_text) - prefix_chinese
        prefix_width = (prefix_chinese * 1.8 + prefix_english) * avg_char_width

        # 计算关键词本身的宽度
        keyword_chinese = sum(1 for char in keyword if '\u4e00' <= char <= '\u9fff')
        keyword_english = len(keyword) - keyword_chinese
        keyword_width = (keyword_chinese * 1.8 + keyword_english) * avg_char_width

        # 计算关键词的实际坐标
        keyword_x1 = min(x1, x4) + prefix_width
        keyword_y1 = min(y1, y2)
        keyword_x2 = keyword_x1 + keyword_width
        keyword_y2 = max(y3, y4)

        # 添加一些边距确保完全覆盖
        margin_x = avg_char_width * 0.1  # 10%的边距
        margin_y = block_height * 0.05  # 5%的边距

        keyword_coords.append([
            max(0, int(keyword_x1 - margin_x)),
            max(0, int(keyword_y1 - margin_y)),
            int(keyword_x2 + margin_x),
            int(keyword_y2 + margin_y)
        ])

        start_index = index + 1

    return keyword_coords


def mask_keywords_precisely(draw, ocr_result, keywords_list, fill_color=(0, 0, 0)):
    """
    精确遮挡关键词，避免过度遮挡

    Args:
        draw: PIL.ImageDraw.Draw对象
        ocr_result: OCR识别结果
        keywords_list: 需要遮挡的关键词列表
        fill_color: 遮挡颜色，默认黑色
    """
    words_blocks = ast.literal_eval(ocr_result)["result"]["words_block_list"]

    # print("words_blocks")
    # print(words_blocks)

    # 统计遮挡信息
    total_masks = 0
    precise_masks = 0
    fallback_masks = 0

    for keyword in keywords_list:
        if not keyword or keyword in ['None', '无', '未提及']:
            continue

        # print(f"正在处理敏感词: '{keyword}'")

        for block in words_blocks:
            text_block = block["words"]
            location = block["location"]

            if keyword in text_block:
                # 如果关键词等于整个文本块，直接遮挡整块
                if keyword.strip() == text_block.strip():
                    a, b, c, d = location[0][0], location[0][1], location[2][0], location[2][1]
                    draw.rectangle([min(a, c), min(b, d), max(a, c), max(b, d)], fill=fill_color)
                    # print(f"  完全匹配遮挡: '{text_block}'")
                    fallback_masks += 1
                else:
                    # 精确计算关键词坐标
                    keyword_coords = calculate_keyword_precise_coordinates(text_block, keyword, location)

                    if keyword_coords:
                        for coord in keyword_coords:
                            draw.rectangle(coord, fill=fill_color)
                            # print(f"  精确遮挡: 在'{text_block}'中遮挡'{keyword}' -> 坐标{coord}")
                            precise_masks += 1
                    else:
                        # fallback: 如果计算失败，遮挡整个文本块
                        a, b, c, d = location[0][0], location[0][1], location[2][0], location[2][1]
                        draw.rectangle([min(a, c), min(b, d), max(a, c), max(b, d)], fill=fill_color)
                        # print(f"  降级遮挡: '{text_block}' (计算失败)")
                        fallback_masks += 1

                total_masks += 1

    return total_masks, precise_masks, fallback_masks


def desensitize_text(ocr_markdown_text, keywords_list):
    """
    对OCR识别的markdown文本进行脱敏处理
    
    Args:
        ocr_markdown_text: OCR识别的markdown原文本
        keywords_list: 需要脱敏的关键词列表
    
    Returns:
        str: 脱敏后的文本，敏感词被替换为相应数量的*
    """
    if not ocr_markdown_text or not isinstance(ocr_markdown_text, str):
        return ocr_markdown_text
    
    if not keywords_list or not isinstance(keywords_list, list):
        return ocr_markdown_text
    
    # 复制原文本进行处理
    masked_text = ocr_markdown_text
    
    # 过滤有效的关键词
    valid_keywords = [
        keyword for keyword in keywords_list 
        if keyword and isinstance(keyword, str) and keyword.strip() 
        and keyword not in ['None', '无', '未提及', 'null']
    ]
    
    # 按长度降序排序，确保长关键词优先匹配（避免短词覆盖长词的问题）
    valid_keywords.sort(key=len, reverse=True)
    
    # 记录脱敏统计
    desensitized_count = 0
    
    for keyword in valid_keywords:
        keyword = keyword.strip()
        if not keyword:
            continue
            
        # 计算需要替换的*号数量
        star_count = len(keyword)
        replacement = '*' * star_count
        
        # 统计替换前的出现次数
        before_count = masked_text.count(keyword)
        
        # 进行替换
        masked_text = masked_text.replace(keyword, replacement)
        
        # 统计实际替换次数
        after_count = masked_text.count(replacement) - masked_text.count(replacement.replace(keyword, ''))
        
        if before_count > 0:
            desensitized_count += before_count
            print(f"  脱敏处理: '{keyword}' -> '{replacement}' (共{before_count}处)")
    

    return masked_text


def ocr_desensitive(input_img):
    start_time = time.time()
    imgpath = input_img
    encode_start = time.time()
    encoded_str = getBase64(imgpath)  # base64编码
    encode_end = time.time()

    # url = "https://ocr.cn-north-4.myhuaweicloud.com/v2/0c038707a20026892f51c001ae871efa/ocr/general-text"
    url = "http://192.168.230.3:8011/ocr/single"
    # logInfo = "{\r\n    \"image\": \"base64\",\r\n    \"detect_direction\": false,\r\n    \"quick_mode\": false,\r\n    \"language\": \"zh\"\r\n}"
    # payload = logInfo.replace("base64", encoded_str)

    payload = {
        "image": encoded_str,
        "detect_direction": True,
        "quick_mode": False,
        "character_mode": False,
        "language": "zh",
        "single_orientation_mode": True,
        "pdf_page_number": 1
    }

    headers = {
        'Content-Type': 'application/json',
        'Auth-Username': 'mauser',
        'Auth-Password': 'Prs@123456',
        'Authorization': 'Basic Og=='
    }

    # response = requests.request("POST", url, headers=headers, data=payload,verify=False)
    # print(response.text)
    import ast
    try:
        ocrResult = getOcrResult(url, payload, headers)
        print("📋 OCR原始结果:")
        print("=" * 60)
        print(ocrResult)
        print("=" * 60)

        # 尝试解析并显示简化信息
        # try:
        #     import ast
        #     ocr_data = ast.literal_eval(ocrResult)
        #     if "result" in ocr_data and "words_block_list" in ocr_data["result"]:
        #         words_blocks = ocr_data["result"]["words_block_list"]
        #         print(f"📊 OCR解析摘要: 识别到 {len(words_blocks)} 个文本块")
        #         for i, block in enumerate(words_blocks[:5]):  # 只显示前5个
        #             words = block.get("words", "")
        #             print(f"  块{i+1}: '{words}'")
        #         if len(words_blocks) > 5:
        #             print(f"  ... 还有 {len(words_blocks) - 5} 个文本块")
        # except Exception as e:
        #     print(f"📊 OCR解析摘要: 解析失败 - {e}")
        # print("-" * 60)

        if not ocrResult:
            print("❌ OCR服务返回空结果")
            ocrResult = '{"result": {"words_block_list": [], "markdown_result": ""}}'
    except Exception as e:
        print(f"❌ OCR服务调用失败: {e}")
        ocrResult = '{"result": {"words_block_list": [], "markdown_result": ""}}'
    try:
        angle = ast.literal_eval(ocrResult)["result"]["direction"]

        angle_float = float(angle)
        if angle_float > 5:
            print(angle_float)
            print('需要旋转！！！')
            image_bytess = io.BytesIO(input_img)
            images = Image.open(image_bytess)
            image_np = np.array(images)
            # OpenCV默认使用BGR颜色通道顺序，而PIL使用RGB，因此需要进行转换
            images = cv2.cvtColor(image_np, cv2.COLOR_RGB2BGR)
            rotated_images = rotate_and_resize_image(images, angle)
            imagesss = Image.fromarray(rotated_images)

            byte_stream = io.BytesIO()
            # 将图像保存到字节流中
            imagesss.save(byte_stream, format='JPEG')
            # 获取字节流数据
            input_img = byte_stream.getvalue()
            encoded_str = getBase64(input_img)
            payload["image"] = encoded_str
            try:
                ocrResult = getOcrResult(url, payload, headers)
                print("📋 旋转后OCR原始结果:")
                print("=" * 60)
                print(ocrResult)
                print("=" * 60)
                
                # 尝试解析并显示简化信息
                try:
                    import ast
                    ocr_data = ast.literal_eval(ocrResult)
                    if "result" in ocr_data and "words_block_list" in ocr_data["result"]:
                        words_blocks = ocr_data["result"]["words_block_list"]
                        print(f"📊 旋转后OCR解析摘要: 识别到 {len(words_blocks)} 个文本块")
                        for i, block in enumerate(words_blocks[:5]):  # 只显示前5个
                            words = block.get("words", "")
                            print(f"  块{i+1}: '{words}'")
                        if len(words_blocks) > 5:
                            print(f"  ... 还有 {len(words_blocks) - 5} 个文本块")
                    else:
                        print("📊 旋转后OCR解析摘要: 未找到有效的文本块")
                except Exception as e:
                    print(f"📊 旋转后OCR解析摘要: 解析失败 - {e}")
                print("-" * 60)
                
                if not ocrResult:
                    print("❌ 旋转后OCR服务返回空结果")
                    ocrResult = '{"result": {"words_block_list": [], "markdown_result": ""}}'
            except Exception as e:
                print(f"❌ 旋转后OCR服务调用失败: {e}")
                ocrResult = '{"result": {"words_block_list": [], "markdown_result": ""}}'
    except Exception as e:
        pass

    ocr_end = time.time()

    # 提取原始文本
    inputText = ""
    try:
        ocr_data = ast.literal_eval(ocrResult)
        if "result" in ocr_data and "words_block_list" in ocr_data["result"]:
            for result in ocr_data["result"]["words_block_list"]:
                words = result.get("words", "")
                inputText += words + " "
            inputText = inputText.strip()
        else:
            inputText = "OCR结果格式异常"
            print("⚠️ OCR结果缺少必要字段")
    except Exception as e:
        inputText = "OCR文本解析失败"
        print(f"⚠️ OCR文本提取失败: {e}")

    # 提取结构化文本信息（包含markdown文本）
    structured_text = extract_structured_text(ocrResult)
    ocr_text = structured_text.get("markdown_text", inputText)  # 获取markdown文本，如果没有则使用普通文本

    # AI敏感词识别
    ai_start = time.time()
    ai_extracted_keywords, keywords_to_mask = extract_keywords_with_ai(ocrResult)
    print("keywords_to_mask")
    print(keywords_to_mask)
    ai_end = time.time()

    # 文本脱敏处理
    print("\n📝 开始文本脱敏处理...")
    ocr_text_mask = desensitize_text(ocr_text, ai_extracted_keywords)

    # 使用原始AI响应
    outputText = keywords_to_mask

    # 初始化默认值，避免变量未定义错误
    think = ""
    generated_tokens = {}

    # 提取think内容（保持原有格式）
    if keywords_to_mask and "choices" in keywords_to_mask:
        try:
            content = keywords_to_mask["choices"][0]["message"]["content"]
            think_start = content.find("<think>")
            think_end = content.find("</think>")
            if think_start != -1 and think_end != -1:
                think = content[think_start:think_end]
            else:
                think = "AI响应中未找到思考内容"
            generated_tokens = keywords_to_mask.get('usage', {})
        except (KeyError, IndexError, TypeError) as e:
            print(f"⚠️ 解析AI响应时出错: {e}")
            think = "AI响应解析失败"
            generated_tokens = {}
    else:
        print("⚠️ AI响应为空或格式异常，使用默认值")
        think = "AI响应为空"
        generated_tokens = {}

    try:
        # 图片处理和遮挡
        mask_start = time.time()
        A = io.BytesIO(input_img)
        image = Image.open(A)
        draw = ImageDraw.Draw(image)

        # 使用AI提取的敏感词列表
        tag = ai_extracted_keywords.copy() if ai_extracted_keywords else []

        # 合并赞助商列表
        tag = tag + sponsor_list
        all_keywords = list(set([s for s in tag if s and s != 'None' and s != '无' and s != '未提及']))
        # print(f"最终需要遮挡的关键词: {all_keywords}")

        # 使用精确遮挡方法
        aa = (0, 0, 0)  # 黑色遮挡
        total_masks, precise_masks, fallback_masks = mask_keywords_precisely(draw, ocrResult, all_keywords,
                                                                             fill_color=aa)


    except Exception as e:
        print(f"图片处理过程出错: {e}")
        A = io.BytesIO(input_img)
        image = Image.open(A)

    mask_end = time.time()

    # 计算各阶段耗时
    ocr_time = ocr_end - encode_end
    ai_time = ai_end - ai_start
    mask_time = mask_end - mask_start
    total_time = mask_end - start_time

    print("OCR处理时间统计:")
    print(f"├── OCR识别: {ocr_time:.2f}s")
    print(f"├── 敏感词识别: {ai_time:.2f}s")
    print(f"├── 图片遮挡: {mask_time:.2f}s")
    print(f"└── 总耗时: {total_time:.2f}s")



    return image, inputText, outputText, think, generated_tokens, ocr_text, ocr_text_mask




def extract_structured_text(ocr_result):
    """
    从OCR结果中提取结构化文本信息

    Args:
        ocr_result: OCR识别结果的JSON字符串

    Returns:
        dict: 包含文本内容和位置信息的结构化数据
    """
    # 初始化默认返回值
    default_result = {
        "full_text": "", 
        "markdown_text": "",
        "text_blocks": [], 
        "total_blocks": 0
    }
    
    try:
        # 检查输入是否为空
        if not ocr_result or not isinstance(ocr_result, str):
            print(f"⚠️ OCR结果为空或格式错误: {type(ocr_result)}")
            return default_result
            
        # 尝试解析JSON
        ocr_data = ast.literal_eval(ocr_result)
        
        # 检查基本结构
        if not isinstance(ocr_data, dict) or "result" not in ocr_data:
            print(f"⚠️ OCR结果缺少必要的'result'字段")
            return default_result
            
        result = ocr_data["result"]
        if "words_block_list" not in result:
            print(f"⚠️ OCR结果缺少'words_block_list'字段")
            return default_result
            
        words_block_list = result["words_block_list"]
        if not isinstance(words_block_list, list):
            print(f"⚠️ 'words_block_list'不是列表格式")
            return default_result

        text_blocks = []
        full_text = ""

        for i, block in enumerate(words_block_list):
            try:
                if not isinstance(block, dict):
                    print(f"⚠️ 文本块 {i} 不是字典格式，跳过")
                    continue
                    
                words = block.get("words", "")
                location = block.get("location", [])

                text_blocks.append({
                    "text": words,
                    "location": location,
                    "confidence": block.get("confidence", 1.0)
                })
                full_text += words + " "
            except Exception as e:
                print(f"⚠️ 处理文本块 {i} 时出错: {e}")
                continue

        markdown_text = result.get("markdown_result", "")

        result_data = {
            "full_text": full_text.strip(),
            "markdown_text": markdown_text,
            "text_blocks": text_blocks,
            "total_blocks": len(text_blocks)
        }
        
        return result_data
        
    except (ValueError, SyntaxError) as e:
        print(f"❌ OCR结果JSON解析失败: {e}")
        print(f"原始OCR结果前200字符: {ocr_result[:200] if ocr_result else 'None'}")
        return default_result
    except Exception as e:
        print(f"❌ OCR结果解析时发生未知错误: {e}")
        return default_result


def build_prompt(structured_text):
    """
    构建敏感词提取提示词

    Args:
        structured_text: 结构化的文本数据

    Returns:
        str: 优化后的提示词
    """

    # 定义预设的敏感信息类型（严格按照要求）
    sensitive_categories = {
        "个人基本信息": ["个人姓名", "民族", "国籍", "家庭关系", "住址", "新生儿姓名", "新生儿出生地址"],
        "联系通信信息": ["电话号码", "联系电话", "手机号码", "传真号码", "电子邮箱地址", "微信", "QQ", "联系方式",
                         "IP地址", "URL"],
        "职业教育信息": ["职业", "职位", "工作单位", "学历", "学位", "教育经历", "工作经历", "培训记录", "成绩单"],
        "通信数据": ["通信记录和内容", "短信", "彩信", "电子邮件", "通讯录", "好友列表", "群列表", "群组列表",
                     "电子邮件地址列表"],
        "生物特征": ["个人基因", "指纹", "声纹", "掌纹", "耳廓", "虹膜", "面部识别特征", "DNA"],
        "财务信息": ["银行账户", "存款信息", "支付收款记录", "房产信息", "信贷信息", "征信信息", "交易和消费信息",
                     "流水记录", "银行卡号"],
        "医疗证件": ["病案号", "门诊号", "住院号", "床号", "商业保险单号", "医保卡号", "社保卡号"],
        "身份证件": ["身份证", "军官证", "护照", "驾驶证", "工作证", "居住证"],
        "其他编号": ["车辆编号", "保险编号"],
        "隐私信息": ["性取向", "婚史", "宗教信仰", "未公开的违法犯罪记录"],
        "位置行为": ["行踪轨迹", "网页浏览记录", "住宿信息", "精准定位信息"],
        "机构名称": ["申办方名称", "CRO名称", "医院名称", "中心名称"]
    }

    # 构建分类说明
    category_desc = []
    for category, items in sensitive_categories.items():
        category_desc.append(f"- {category}: {', '.join(items)}")

        prompt1 = f"""
# 角色设定
你是一名顶级的敏感信息审计专家，拥有零容忍的精确度。你的任务是精准、无遗漏地识别并提取文本中的所有指定类型的敏感信息。

## 核心任务
你的唯一任务是，分析下方【输入数据】中的文本，严格遵循【处理规则】，将所有识别出的敏感词，完全遵照【任务要求】进行输出。

【处理规则】
1.  **敏感信息类别定义**: 你必须严格且只识别以下预定义类别中的信息。任何不在此清单中的内容都必须被忽略。
    {category_desc}
2.  **核心提取原则**:
    * **精确范围**: 严格只识别并提取属于上述【敏感信息类别定义】中的词语。
    * **只提取值，不提取标签**: 只提取敏感信息本身（如 “张三”、“13800138000”），绝不包含其字段名或标签（如 “姓名：”、“电话：”）。
    * **内容保真**: 提取的敏感词必须与原文中的字符一模一样，不得进行任何形式的修改、补全或缩略。
    * **忽略非敏感内容**: 必须忽略所有非敏感的业务数据（如药品名称、检验项目、结果数值、普通描述性文本）和任何形式的占位符（如 "___" 或 "未填写"）。

【示例】
通过以下正反案例，精确理解处理规则。

**示例1: 标准多项提取**
输入文本: `患者姓名：李晓明，联系电话13912345678，就诊于北京协C医院。审核医生：王伟。`
✅ 正确输出参考:
["李晓明", "13912345678", "北京协C医院", "王伟"]

**示例2: 忽略字段标签和非敏感词**
输入文本: `姓名：赵四。药物：阿司匹林。过敏史：无。`
✅ 正确输出参考:
["赵四"]
❌ 错误输出参考 (包含了标签和非敏感词):
["姓名：赵四", "阿司匹林", "无"]

**示例3: 处理不存在或占位符的值**
输入文本: `送检医生： 刘医生。 患者电话：___。`
✅ 正确输出参考:
["刘医生"]
❌ 错误输出参考 (包含了占位符):
["刘医生", "___"]

**示例4: 文本中无任何敏感词**
输入文本: `该样本血常规检查结果显示一切正常。`
✅ 正确输出参考:
[]

【输入数据】
{structured_text['markdown_text']}

/no_think
"""

    prompt = f"""你是一个专业的敏感词识别专家。请严格按照指定的敏感信息类型，提取敏感词。

**待分析文本：**
{structured_text['markdown_text']}

<think>
分析步骤：
1. 逐行扫描文本，寻找指定类型的敏感信息
2. 严格按照预定义类别进行识别，不识别类别外的内容
3. 区分字段名和具体值，只识别具体的敏感内容
4. 忽略空值、占位符和纯字段标签
5. 确保识别的内容确实属于敏感信息范围
6. 识别所有人员姓名（患者、医生、护士、检验者、审核者等）
7. 识别所有的个人基本信息
</think>

**严格识别以下类型的敏感信息：**
{chr(10).join(category_desc)}

**识别要求：**
1. **严格范围**：只识别上述预定义类别中的敏感信息，其他内容一律忽略
2. **具体内容**：只识别具体的敏感值，不识别字段名、标签或空值
3. **实际存在**：确保识别的敏感词在原文中真实存在且完整
4. **精确匹配**：敏感词必须与原文完全一致

**识别示例：**
- 应识别：任何姓名（如"张三"）、电话号码（如"13800138000"）、医院名（如"人民医院"）、性别年龄（如"男，30岁"）
- 不识别：字段标签（如"姓名："、"电话："）、空值（如"姓名：___"）、性别年龄（如"男，30岁"）的字段名
- 不识别：非敏感内容（如检查项目、药品名称、检验结果数值）
- 不识别：受试者筛选号（此编号不在要求识别的敏感信息中）

**输出格式：**
严格按照JSON数组格式输出：
```json
["敏感词1", "敏感词2", "敏感词3"]
```

**注意事项：**
- 只输出JSON数组，无其他内容
- 只包含预定义类别中的敏感信息
- 如无敏感词，输出：[]
- 保持原文格式和字符
"""
    # print(prompt)
    # print(structured_text['markdown_text'])

    return prompt


def extract_keywords_with_ai(ocr_result):
    """
    使用混合策略提取敏感词：正则表达式 + AI识别

    Args:
        ocr_result: OCR识别结果

    Returns:
        tuple: (敏感词列表, AI原始响应)
    """
    try:
        # 提取结构化文本
        structured_text = extract_structured_text(ocr_result)
        
        # 详细诊断OCR结果
        # print(f"📊 OCR结果诊断:")
        # print(f"  - 原始OCR结果长度: {len(ocr_result) if ocr_result else 0}")
        # print(f"  - 结构化文本块数量: {structured_text.get('total_blocks', 0)}")
        # print(f"  - 提取的完整文本长度: {len(structured_text.get('full_text', ''))}")
        # print(f"  - Markdown文本长度: {len(structured_text.get('markdown_text', ''))}")

        if not structured_text["full_text"].strip():
            print("❌ 未检测到有效文本内容")
            print("🔍 可能原因:")
            print("  1. 图片质量过低或模糊")
            print("  2. 图片中没有文字内容")
            print("  3. OCR服务识别失败")
            print("  4. 图片格式不支持")
            return [], None

        full_text = structured_text["full_text"]
        print(f"✅ 成功提取文本: {full_text[:100]}..." if len(full_text) > 100 else f"✅ 成功提取文本: {full_text}")

        # 第一步：使用正则表达式快速识别常见格式的敏感信息
        print("🔍 第一步：正则表达式识别...")
        regex_keywords = extract_keywords_with_regex(full_text)

        # 第二步：使用AI识别更复杂的敏感信息
        print("\n🤖 第二步：AI模型识别...")

        # 构建提示词
        smart_prompt = build_prompt(structured_text)

        # 调用AI分析
        try:
            ai_response = useLlmApi(smart_prompt)
        except Exception as e:
            print(f"❌ AI服务调用失败: {e}")
            ai_response = None

        ai_keywords = []
        if ai_response and "choices" in ai_response:
            # 解析AI返回的内容
            content = ai_response["choices"][0]["message"]["content"]
            print(content)

            # 提取思考过程
            think_start = content.find("<think>")
            think_end = content.find("</think>")
            if think_start != -1 and think_end != -1:
                content = content[think_end + 8:].strip()

            # 提取JSON数组
            json_start = content.find("[")
            json_end = content.rfind("]") + 1

            if json_start != -1 and json_end > json_start:
                json_content = content[json_start:json_end]

                try:
                    sensitive_words = json.loads(json_content)
                    if isinstance(sensitive_words, list):
                        ai_keywords = [word for word in sensitive_words
                                       if word and word.strip() and word not in ['None', '无', '未提及', 'null']]

                except json.JSONDecodeError:
                    print(f"AI结果JSON解析失败")
            else:
                print("AI返回结果中未找到有效的JSON格式")
        else:
            print("AI分析服务返回异常")

        # 第三步：合并两种方法的结果
        print("\n第三步：合并识别结果...")
        final_keywords, merge_stats = merge_keywords(regex_keywords, ai_keywords)

        # 输出详细统计
        print(f"📊 识别统计:")
        print(f"  正则表达式识别: {merge_stats['regex_found']} 个")
        print(f"  AI模型识别: {merge_stats['ai_found']} 个")
        print(f"  去重后总计: {merge_stats['total_unique']} 个")
        print(f"  共同识别: {merge_stats['common']}")
        print(f"\n✅ 最终敏感词列表: {final_keywords}")
        print("=" * 50)
        print("✅正则识别")
        # 统计正则识别结果
        for category, words in regex_keywords.items():
            if words:
                print(f"  {category}: {words}")

        print("✅大模型识别")
        print(ai_keywords)

        return final_keywords, ai_response


    except Exception as e:
        print(f"敏感词提取过程出错: {e}")
        import traceback
        traceback.print_exc()
        return [], None


def extract_keywords_with_regex(text):
    """
    使用正则表达式识别容易格式化识别的敏感信息
    专注于高准确率的模式，避免误识别字段名

    Args:
        text: 要分析的文本

    Returns:
        dict: {category: [keywords]} 按类别组织的敏感词
    """
    import re

    # 精简的正则表达式模式 - 只识别高置信度的格式化信息
    patterns = {
        # 1. 电话号码 - 格式清晰，误识别率低
        "电话号码": [
            r'1[3-9]\d{9}',  # 11位手机号
            r'0\d{2,3}-\d{7,8}',  # 带区号的固定电话
        ],

        # 2. 身份证件 - 格式固定
        "身份证件": [
            r'\b\d{17}[\dxX]\b',  # 18位身份证
        ],

        # 3. 邮箱地址 - 格式明确
        "邮箱": [
            r'\b[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\b',
        ],

        # 4. 医疗证件的具体数值（不包含字段名）
        "医疗证件编号": [
            r'病案号[：:]\s*(\d{4,})',  # 病案号：数字
            r'床号[：:]\s*(\d{1,4})',  # 床号：数字
            r'门诊号[：:]\s*(\d{4,})',  # 门诊号：数字
            r'住院号[：:]\s*(\d{4,})',  # 住院号：数字
            r'床号[：:]\s*(\d{1,4})',  # 床号：数字
            r'医保卡号[：:]\s*(\d{1,10})',  # 医保卡号：数字
            r'社保卡号[：:]\s*(\d{1,10})',  # 社保卡号：数字
            r'卡号[：:]\s*(\d{1,10})',  # 卡号：数字
        ],

        # 5. 人员姓名 - 只识别明确标注的姓名
        "人员姓名": [
            r'姓名[：:]\s*([\u4e00-\u9fff]{2,4})',  # 姓名：XX
            r'申请医生[：:]\s*([\u4e00-\u9fff]{2,4})',  # 申请医生：XX
            r'检验者[：:]\s*([\u4e00-\u9fff]{2,4})',  # 检验者：XX
            r'审核者[：:]\s*([\u4e00-\u9fff]{2,4})',  # 审核者：XX
        ],
        # 生物特征识别
        "生物特征": [
            r'身高[：:]\s*(\d{2,3}(?:\.\d{1,2})?)\s*(?:cm|厘米)?',  # 身高
            r'体重[：:]\s*(\d{2,3}(?:\.\d{1,2})?)\s*(?:kg|公斤)?',  # 体重
            r'血型[：:]\s*([ABO]{1,2}[+-](?:型?|RH[+-])?)',  # 血型
            r'指纹编号[：:]\s*([A-Z0-9-]{4,})',  # 指纹编号
            r'视力[：:]\s*([0-9.]{1,4}\/[0-9.]{1,4})',  # 视力
            r'DNA[编号]*[：:]\s*([A-Z0-9-]{4,})',  # DNA编号
            r'虹膜特征[：:]\s*([A-Z0-9-]{4,})',  # 虹膜特征码
            r'面部特征[：:]\s*([A-Z0-9-]{4,})',  # 面部特征码
        ],

        # 医疗卫生信息
        "医疗证件编号": [
            r'病案号[：:]\s*(\d{4,})',  # 病案号：数字
            r'门诊号[：:]\s*(\d{4,})',  # 门诊号：数字
            r'住院号[：:]\s*(\d{4,})',  # 住院号：数字
            r'床号[：:]\s*(\d{1,4}[A-Z]?)',  # 床号：数字+可选字母
            r'医保卡号[：:]\s*([A-Z0-9-]{4,})',  # 医保卡号：字母数字组合
            r'社保卡号[：:]\s*([A-Z0-9-]{4,})',  # 社保卡号：字母数字组合
            r'体检号[：:]\s*([A-Z0-9-]{4,})',  # 体检编号
            r'处方编号[：:]\s*([A-Z0-9-]{4,})',  # 处方编号
            r'报告编号[：:]\s*([A-Z0-9-]{4,})',  # 报告编号
            r'检验编号[：:]\s*([A-Z0-9-]{4,})',  # 检验编号
            r'医疗单号[：:]\s*([A-Z0-9-]{4,})',  # 医疗单号
        ],

        # 民族识别
        "民族": [
            r'民族[：:]\s*([\u4e00-\u9fff]{1,8}族)',  # 匹配"XX族"
            r'族别[：:]\s*([\u4e00-\u9fff]{1,8}族)',
            r'(汉族|壮族|满族|回族|苗族|维吾尔族|土家族|彝族|蒙古族|藏族|布依族|侗族|瑶族|朝鲜族|白族|哈尼族)',
        ],
        # 国籍识别
        "国籍": [
            r'国籍[：:]\s*([\u4e00-\u9fff]{2,})',  # 中文国籍
            r'nationality[：:]\s*([a-zA-Z\s]{2,})',  # 英文国籍
            r'(中国|美国|英国|法国|德国|日本|韩国|俄罗斯|加拿大|澳大利亚)(籍|公民|国籍)',
        ],

        # 地址信息识别
        "地址信息": [
            r'地址[：:]\s*([\u4e00-\u9fff0-9\s#-]{5,})',  # 一般地址
            r'住址[：:]\s*([\u4e00-\u9fff0-9\s#-]{5,})',  # 居住地址
            r'居住地[：:]\s*([\u4e00-\u9fff0-9\s#-]{5,})',
            r'住所[：:]\s*([\u4e00-\u9fff0-9\s#-]{5,})',
            r'通讯地址[：:]\s*([\u4e00-\u9fff0-9\s#-]{5,})',
        ],

        # 行政区划
        "行政区划": [
            r'([\u4e00-\u9fff]{2,}?(省|自治区|市|县|镇|乡|村|街道))',
            r'([东南西北中]关村|什刹海|西单|王府井|三里屯)',  # 北京知名地区
            r'([0-9]{3}(?:省|市|区|县))',  # 行政区划代码
        ],

        # 宗教信仰
        "宗教信仰": [
            r'宗教[：:]\s*([\u4e00-\u9fff]{2,}教)',  # XX教
            r'信仰[：:]\s*([\u4e00-\u9fff]{2,})',
            r'(佛教|基督教|天主教|伊斯兰教|道教|犹太教)',
        ],
        # 所有可以用正则识别的信息。比如民族

        # 6. 完整医院名称 - 只识别完整的医院名
        "医院名称": [
            r'[\u4e00-\u9fff]{2,}大学[\u4e00-\u9fff]{2,}医院',  # XX大学XX医院
            r'[\u4e00-\u9fff]{2,}大学',  # XX大学
            r'[\u4e00-\u9fff]{2,}医院',  # XX医院
        ],

        # 位置行为信息
        "位置行为": [
            r'出行记录[：:]\s*([\u4e00-\u9fff0-9\s-]{5,})',  # 出行记录
            r'住宿[记录信息]*[：:]\s*([\u4e00-\u9fff0-9\s-]{5,})',  # 住宿信息
            r'行程轨迹[：:]\s*([\u4e00-\u9fff0-9\s-]{5,})',  # 行程轨迹
            r'定位信息[：:]\s*([\u4e00-\u9fff0-9\s,.°′″]{5,})',  # GPS定位信息
            r'([北南]纬[0-9]{1,2}度[0-9]{1,2}分[0-9]{1,2}秒[，,][东西]经[0-9]{1,3}度[0-9]{1,2}分[0-9]{1,2}秒)',  # 经纬度
            r'活动范围[：:]\s*([\u4e00-\u9fff，。、]{2,})',  # 活动范围描述
        ],

        # 机构组织名称
        "机构名称": [
            r'申办方[：:]\s*([\u4e00-\u9fff]{2,}?(?:公司|企业|集团|机构))',  # 申办方名称
            r'CRO[：:]\s*([\u4e00-\u9fff]{2,}?(?:公司|企业|集团|机构))',  # CRO机构
            r'医疗机构[：:]\s*([\u4e00-\u9fff]{2,}?(?:医院|诊所|中心|研究所))',  # 医疗机构
            r'研究中心[：:]\s*([\u4e00-\u9fff]{2,}?(?:中心|基地|实验室|研究所))',  # 研究中心
            r'实验室[：:]\s*([\u4e00-\u9fff]{2,}?(?:实验室|研究室|中心))',  # 实验室
            r'监管机构[：:]\s*([\u4e00-\u9fff]{2,}?(?:局|处|会|部|办|委员会))',  # 监管机构
        ],

        # 职业教育详细信息
        "职业教育信息": [
            r'教育程度[：:]\s*([\u4e00-\u9fff]{2,})',  # 教育程度
            r'专业类别[：:]\s*([\u4e00-\u9fff]{2,})',  # 专业类别
            r'教育经历[：:]\s*([\u4e00-\u9fff]{2,}(?:大学|学院|学校)[\u4e00-\u9fff\s\d-]*)',  # 教育经历
            # 培训相关信息，限制在2-50个字符之间
            r'培训[记录经历]*[：:]\s*([\u4e00-\u9fff\s\d-]{2,50})',  # 培训记录/经历
            r'培训[项目内容]*[：:]\s*([\u4e00-\u9fff\s\d-]{2,50})',  # 培训项目/内容
            r'进修[记录内容]*[：:]\s*([\u4e00-\u9fff\s\d-]{2,50})',  # 进修记录/内容
            r'培训证书[：:]\s*([A-Z0-9-]{4,})',  # 培训证书编号
            r'职称证书[：:]\s*([A-Z0-9-]{4,})',  # 职称证书编号
            r'资格证书[：:]\s*([A-Z0-9-]{4,})',  # 资格证书编号
            r'工作年限[：:]\s*(\d{1,2}年)',  # 工作年限
            r'从业时间[：:]\s*(\d{4}年\d{1,2}月)',  # 从业时间
            # 工作经历
            r'工作经历[：:]\s*([\u4e00-\u9fff\s\d-]{2,50})',  # 工作经历
            r'工作单位[：:]\s*([\u4e00-\u9fff]{2,}?(?:公司|企业|集团|机构|医院|学校))',  # 工作单位
            r'职位[：:]\s*([\u4e00-\u9fff]{2,})',  # 职位
            r'职务[：:]\s*([\u4e00-\u9fff]{2,})',  # 职务
            r'工作岗位[：:]\s*([\u4e00-\u9fff]{2,})',  # 工作岗位
            r'工作职责[：:]\s*([\u4e00-\u9fff]{2,})',  # 工作职责
            # 成绩单
            r'成绩单[：:]\s*([\u4e00-\u9fff]{2,})',  # 成绩单

        ],

        # 隐私信息
        "隐私信息": [
            r'婚姻状况[：:]\s*(未婚|已婚|离异|丧偶)',  # 婚姻状况
            r'婚史[：:]\s*([\u4e00-\u9fff，。、]{2,})',  # 婚史描述
            r'生育史[：:]\s*([\u4e00-\u9fff，。、]{2,})',  # 生育史
            r'宗教信仰[：:]\s*([\u4e00-\u9fff]{2,})',  # 宗教信仰
            r'违法记录[：:]\s*([\u4e00-\u9fff，。、]{2,})',  # 违法记录
            r'犯罪记录[：:]\s*([\u4e00-\u9fff，。、]{2,})',  # 犯罪记录
        ],

        # 生物特征描述
        "生物特征": [
            r'(左|右)手(食|中|无名|小)指纹[：:]\s*([A-Z0-9-]{4,})',  # 具体指纹信息
            r'瞳距[：:]\s*(\d{2,3}(?:\.\d{1,2})?)\s*(?:mm|毫米)',  # 瞳距
            r'足长[：:]\s*(\d{2,3}(?:\.\d{1,2})?)\s*(?:cm|厘米)',  # 足长
            r'头围[：:]\s*(\d{2,3}(?:\.\d{1,2})?)\s*(?:cm|厘米)',  # 头围
            r'胸围[：:]\s*(\d{2,3}(?:\.\d{1,2})?)\s*(?:cm|厘米)',  # 胸围
            r'肩宽[：:]\s*(\d{2,3}(?:\.\d{1,2})?)\s*(?:cm|厘米)',  # 肩宽
            r'腰围[：:]\s*(\d{2,3}(?:\.\d{1,2})?)\s*(?:cm|厘米)',  # 腰围
            r'特殊标识[：:]\s*([\u4e00-\u9fff，。、]{2,})',  # 特殊体征标识
            r'残疾情况[：:]\s*([\u4e00-\u9fff，。、]{2,})',  # 残疾情况
        ],

        # 医疗补充信息
        "医疗号码": [
            r'入院记录[：:]\s*([A-Z0-9-]{4,})',  # 入院记录编号
            r'出院记录[：:]\s*([A-Z0-9-]{4,})',  # 出院记录编号
            r'手术编号[：:]\s*([A-Z0-9-]{4,})',  # 手术编号
            r'麻醉记录[：:]\s*([A-Z0-9-]{4,})',  # 麻醉记录编号
            r'病理号[：:]\s*([A-Z0-9-]{4,})',  # 病理号
            r'检查号[：:]\s*([A-Z0-9-]{4,})',  # 检查号
            r'医嘱号[：:]\s*([A-Z0-9-]{4,})',  # 医嘱号
        ],

        # 教育信息
        "教育信息": [
            r'学历[：:]\s*([\u4e00-\u9fff]{2,})',  # 最高学历
            r'学位[：:]\s*([\u4e00-\u9fff]{2,}?[学士|硕士|博士])',  # 学位
            r'(小学|初中|高中|中专|大专|本科|研究生|博士)',  # 学历层次
            r'毕业院校[：:]\s*([\u4e00-\u9fff]{2,}?(?:大学|学院|学校))',  # 毕业院校
            r'专业[：:]\s*([\u4e00-\u9fff]{2,})',  # 专业
        ],

        # 职业信息
        "职业信息": [
            r'职业[：:]\s*([\u4e00-\u9fff]{2,})',  # 职业
            r'职务[：:]\s*([\u4e00-\u9fff]{2,})',  # 职务
            r'职位[：:]\s*([\u4e00-\u9fff]{2,})',  # 职位
            r'工作单位[：:]\s*([\u4e00-\u9fff]{2,})',  # 工作单位
            r'部门[：:]\s*([\u4e00-\u9fff]{2,})',  # 部门
        ],

        # 网络账号
        "网络账号": [
            r'IP[：:]\s*(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})',  # IP地址
            r'(https?://(?:[-\w.]|(?:%[\da-fA-F]{2}))+[^\s]*)',  # URL
        ],

        # 银行卡号
        "银行卡号": [
            r'银行卡[号码]*[：:]\s*(\d{16,19})',  # 银行卡号16-19位数字
            r'储蓄卡[：:]\s*(\d{16,19})',
            r'信用卡[：:]\s*(\d{16,19})',
            r'\b(\d{4}[\s-]\d{4}[\s-]\d{4}[\s-]\d{4})\b',  # 格式化的银行卡号
        ],

        # 车辆信息
        "车辆信息": [
            r'车牌[号码]*[：:]\s*([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼]{1}[A-Z]{1}[A-Z0-9]{5,6})',
            # 车牌号
            r'车架号[：:]\s*([A-HJ-NPR-Z0-9]{17})',  # 车架号
            r'发动机号[：:]\s*([A-Z0-9]{6,})',  # 发动机号
        ],

        # 社交账号
        "社交账号": [
            r'微信[：:]\s*([\w\-]{6,20})',  # 微信号
            r'QQ[：:]\s*(\d{5,11})',  # QQ号
            r'微博[：:]\s*(@[\u4e00-\u9fff\w\-]{2,30})',  # 微博账号
        ],

        # 家庭关系
        "家庭关系": [
            r'配偶[：:]\s*([\u4e00-\u9fff]{2,4})',  # 配偶姓名
            r'父亲[：:]\s*([\u4e00-\u9fff]{2,4})',  # 父亲姓名
            r'母亲[：:]\s*([\u4e00-\u9fff]{2,4})',  # 母亲姓名
            r'子女[：:]\s*([\u4e00-\u9fff]{2,4})',  # 子女姓名
            r'监护人[：:]\s*([\u4e00-\u9fff]{2,4})',  # 监护人姓名
        ],
    }

    # 动态构建结果字典，避免 KeyError 风险
    regex_keywords = {}
    
    # 执行正则匹配
    for category, pattern_list in patterns.items():
        # 确保每个类别都有对应的列表
        if category not in regex_keywords:
            regex_keywords[category] = []
            
        for pattern in pattern_list:
            try:
                matches = re.findall(pattern, text, re.IGNORECASE)
                for match in matches:
                    # 处理分组匹配
                    if isinstance(match, tuple):
                        for item in match:
                            if item and len(item.strip()) > 1:
                                regex_keywords[category].append(item.strip())
                    else:
                        if match and len(match.strip()) > 1:
                            regex_keywords[category].append(match.strip())
            except re.error as e:
                print(f"正则表达式错误 - 类别: {category}, 模式: {pattern}, 错误: {e}")
                continue
            except Exception as e:
                print(f"匹配过程出错 - 类别: {category}, 错误: {e}")
                continue

    # 去重并清理空结果
    cleaned_keywords = {}
    for category, keywords in regex_keywords.items():
        if keywords:  # 只保留有结果的类别
            cleaned_keywords[category] = list(set(keywords))
    
    return cleaned_keywords


def merge_keywords(regex_keywords, ai_keywords):
    """
    合并正则表达式和AI识别的结果

    Args:
        regex_keywords: 正则表达式识别的关键词字典
        ai_keywords: AI识别的关键词列表

    Returns:
        tuple: (合并后的关键词列表, 详细统计信息)
    """
    # 安全处理输入参数
    if not isinstance(regex_keywords, dict):
        regex_keywords = {}
    if not isinstance(ai_keywords, list):
        ai_keywords = []

    # 将正则结果扁平化
    regex_flat = []
    regex_stats = {}

    for category, keywords in regex_keywords.items():
        if keywords and isinstance(keywords, list):
            regex_flat.extend(keywords)
            regex_stats[category] = len(keywords)

    # 过滤无效的AI关键词
    valid_ai_keywords = [
        word for word in ai_keywords 
        if word and isinstance(word, str) and word.strip() and word not in ['None', '无', '未提及', 'null']
    ]

    # 合并去重
    all_keywords = list(set(regex_flat + valid_ai_keywords))

    # 生成统计信息
    stats = {
        "regex_found": len(regex_flat),
        "ai_found": len(valid_ai_keywords),
        "total_unique": len(all_keywords),
        "regex_categories": regex_stats,
        "regex_only": list(set(regex_flat) - set(valid_ai_keywords)),
        "ai_only": list(set(valid_ai_keywords) - set(regex_flat)),
        "common": list(set(regex_flat) & set(valid_ai_keywords))
    }

    return all_keywords, stats