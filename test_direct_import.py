# -*- coding: utf-8 -*-
"""
直接导入测试脚本

绕过__init__.py直接导入模块进行测试
"""

import os
import sys
import importlib.util

def import_module_from_path(module_name, file_path):
    """从文件路径直接导入模块"""
    spec = importlib.util.spec_from_file_location(module_name, file_path)
    module = importlib.util.module_from_spec(spec)
    sys.modules[module_name] = module
    spec.loader.exec_module(module)
    return module

def test_config_direct():
    """直接测试配置模块"""
    try:
        config_path = "script/test_result_format_ae_ocr/simple_config.py"
        config_module = import_module_from_path("simple_config", config_path)
        
        print("✅ 配置模块直接导入成功")
        
        # 测试配置类
        config_class = config_module.SimpleParallelConfig
        config = config_class()
        print(f"  - 默认并行模式: {config.enable_parallel}")
        print(f"  - 默认并发数: {config.max_workers}")
        
        # 测试配置管理器
        manager_class = config_module.SimpleConfigManager
        manager = manager_class()
        current_config = manager.get_config()
        print(f"  - 管理器获取配置成功: {type(current_config)}")
        
        # 测试全局函数
        global_config = config_module.get_simple_config()
        print(f"  - 全局配置函数成功: {type(global_config)}")
        
        return True
    except Exception as e:
        print(f"❌ 配置模块直接导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_parallel_processor_direct():
    """直接测试并行处理器模块"""
    try:
        processor_path = "script/test_result_format_ae_ocr/parallel_processor.py"
        processor_module = import_module_from_path("parallel_processor", processor_path)
        
        print("✅ 并行处理器模块直接导入成功")
        
        # 测试基础类
        PageData = processor_module.PageData
        page_data = PageData(
            page_id="test_001",
            page_content="测试内容",
            page_num=1,
            file_id="file_001",
            ocr_blocks=[],
            metadata={},
            task_info={}
        )
        print(f"  - PageData创建成功: {page_data.page_id}")
        
        # 测试适配器（不启用并行，避免Django依赖）
        ParallelIntegrationAdapter = processor_module.ParallelIntegrationAdapter
        adapter = ParallelIntegrationAdapter(enable_parallel=False, max_workers=1)
        print(f"  - 适配器创建成功: {type(adapter)}")
        
        return True
    except Exception as e:
        print(f"❌ 并行处理器模块直接导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_transaction_manager_direct():
    """直接测试事务管理器模块"""
    try:
        transaction_path = "script/test_result_format_ae_ocr/simple_transaction.py"
        transaction_module = import_module_from_path("simple_transaction", transaction_path)
        
        print("✅ 事务管理器模块直接导入成功")
        
        # 测试事务管理器
        SimpleTransactionManager = transaction_module.SimpleTransactionManager
        manager = SimpleTransactionManager()
        print(f"  - 事务管理器创建成功: {type(manager)}")
        
        # 测试全局函数
        global_manager = transaction_module.get_simple_transaction_manager()
        print(f"  - 全局事务管理器函数成功: {type(global_manager)}")
        
        return True
    except Exception as e:
        print(f"❌ 事务管理器模块直接导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_functionality():
    """测试配置功能"""
    try:
        config_path = "script/test_result_format_ae_ocr/simple_config.py"
        config_module = import_module_from_path("simple_config_test", config_path)
        
        print("🔧 测试配置功能...")
        
        # 获取默认配置
        config = config_module.get_simple_config()
        print(f"  - 默认配置: 并行={config.enable_parallel}, 并发数={config.max_workers}")
        
        # 更新配置
        updated_config = config_module.update_simple_config(max_workers=5, debug_mode=True)
        print(f"  - 更新配置: 并发数={updated_config.max_workers}, 调试={updated_config.debug_mode}")
        
        # 应用预设配置
        preset_config = config_module.apply_preset_config("conservative")
        print(f"  - 保守配置: 并发数={preset_config.max_workers}, 成功率阈值={preset_config.success_rate_threshold}")
        
        return True
    except Exception as e:
        print(f"❌ 配置功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integration_without_django():
    """测试不依赖Django的集成功能"""
    try:
        # 测试配置和并行处理器的协同工作
        config_path = "script/test_result_format_ae_ocr/simple_config.py"
        config_module = import_module_from_path("config_for_integration", config_path)
        
        processor_path = "script/test_result_format_ae_ocr/parallel_processor.py"
        processor_module = import_module_from_path("processor_for_integration", processor_path)
        
        print("🔗 测试集成功能（无Django依赖）...")
        
        # 获取配置
        config = config_module.get_simple_config()
        print(f"  - 配置获取成功: 并行={config.enable_parallel}")
        
        # 创建适配器（禁用并行以避免Django依赖）
        ParallelIntegrationAdapter = processor_module.ParallelIntegrationAdapter
        adapter = ParallelIntegrationAdapter(enable_parallel=False, max_workers=1)
        print(f"  - 适配器创建成功")
        
        # 测试基础数据结构
        PageData = processor_module.PageData
        test_page = PageData(
            page_id="integration_test",
            page_content="集成测试内容",
            page_num=1,
            file_id="integration_file",
            ocr_blocks=[],
            metadata={'test': True},
            task_info={'task_id': 'integration'}
        )
        print(f"  - 测试数据创建成功: {test_page.page_id}")
        
        return True
    except Exception as e:
        print(f"❌ 集成功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 开始直接导入测试")
    print("=" * 60)
    
    tests = [
        ("配置模块直接导入", test_config_direct),
        ("并行处理器直接导入", test_parallel_processor_direct),
        ("事务管理器直接导入", test_transaction_manager_direct),
        ("配置功能测试", test_config_functionality),
        ("集成功能测试（无Django）", test_integration_without_django),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 测试: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - 通过")
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"💥 {test_name} - 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有直接导入测试通过！")
        print("📝 模块可以独立工作，不依赖Django环境")
        print("🚀 下一步可以在Django环境中进行完整测试")
    else:
        print("⚠️ 部分测试失败，请检查相关模块")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
