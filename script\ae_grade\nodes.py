# -*- coding: utf-8 -*-
"""
PocketFlow节点定义
实现AE等级识别的各个处理节点
"""
import asyncio
import threading
import time
import json
import requests
from typing import List, Dict, Any, Tuple, Optional, Optional
import logging

from tabulate import tabulate
# PocketFlow imports
from pocketflow import Node, AsyncNode, AsyncParallelBatchNode

# 本地imports
from script.ae_grade.config import (
    PRIMARY_MODEL, SECONDARY_MODEL,
     TIMEOUT_SECONDS,
    BATCH_SIZE, MAX_CONCURRENT_BATCHES
)
from script.ae_grade.utils import CTCAERuleMatcher, SmartPromptBuilder, ResultParser
from script.ae_grade.data.ctcae_rules import get_ctcae_rules

logger = logging.getLogger(__name__)

class AbnormalItemFilterAndPreprocessNode(Node):
    """异常项筛选和预处理节点：一次循环完成筛选和数据清洗"""

    def prep(self, shared: Dict[str, Any]) -> List[Dict[str, Any]]:
        """准备阶段：获取原始数据库数据"""
        raw_data = shared.get("raw_input_data", [])
        logger.info(f"开始异常项筛选和预处理，原始数据项数: {len(raw_data)}")
        return raw_data

    def exec(self, raw_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """执行阶段：一次循环完成异常项筛选和数据预处理"""
        if not raw_data:
            return []

        # test_flag到符号的转换映射
        flag_to_status = {
            0: '-',
            1: '↑',
            2: '↓'
        }

        abnormal_items = []
        normal_items = []

        # 统计各种异常情况
        abnormal_by_flag = 0
        abnormal_by_symbol = 0
        abnormal_by_both = 0

        for item in raw_data:
            # 获取原始字段用于异常判断
            test_flag = item.get('test_flag', 0)
            original_abnormal_symbol = item.get('abnormal_symbol', '')

            # 基于原始字段进行异常判断
            is_abnormal_by_flag = test_flag in [1, 2]
            is_abnormal_by_symbol = original_abnormal_symbol and original_abnormal_symbol != '-'
            is_abnormal = is_abnormal_by_flag or is_abnormal_by_symbol

            if is_abnormal:
                # 只对异常项进行数据清洗和转换
                processed_item = item.copy()
                processed_item['test_name'] = self._clean_special_symbols(processed_item['test_name'])
                processed_item['test_code'] = self._clean_special_symbols(processed_item['test_code'])

                # 转换test_flag为符号格式（原始abnormal_symbol丢弃，因为ocr识别、大模型结构化结果可能导致异常符号不对）
                try:
                    flag_int = int(test_flag)
                    if flag_int in flag_to_status:
                        processed_item['abnormal_symbol'] = flag_to_status[flag_int]
                except (ValueError, TypeError):
                    logger.warning(f"无法转换test_flag值: {test_flag}")

                # 丢弃test_flag字段
                del processed_item['test_flag']

                abnormal_items.append(processed_item)

                # 统计异常类型（基于原始字段）
                if is_abnormal_by_flag and is_abnormal_by_symbol:
                    abnormal_by_both += 1
                elif is_abnormal_by_flag:
                    abnormal_by_flag += 1
                elif is_abnormal_by_symbol:
                    abnormal_by_symbol += 1
            else:
                # 正常项保持原样，不进行额外处理
                normal_items.append(item)

        # 使用表格格式输出详细的筛选统计
        filter_stats = [
            ["总输入项目", len(raw_data)],
            ["筛选出异常项", len(abnormal_items)],
            ["正常项目", len(normal_items)],
            ["仅test_flag异常", abnormal_by_flag],
            ["仅abnormal_symbol异常", abnormal_by_symbol],
            ["两个字段都异常", abnormal_by_both],
            ["异常项占比", f"{len(abnormal_items)/len(raw_data)*100:.1f}%" if len(raw_data) > 0 else "0%"]
        ]

        logger.info("异常项筛选和预处理统计:")
        logger.info("\n" + tabulate(filter_stats, headers=["项目", "数量"], tablefmt="grid", stralign="center"))

        # 将正常项目保存到shared中
        self._normal_items = normal_items

        return abnormal_items

    def _clean_special_symbols(self, text: str) -> str:
        """
        清理test_code和test_name中的特殊符号
        移除 *、★、★、●、▲、▼、◆、■、□、+ 等特殊符号
        """
        # [FIX] 确保输入是字符串类型
        text = str(text) if text is not None else ''

        if not text:
            return text

        # 移除常见的特殊符号
        special_symbols = ['*', '★', '★', '●', '▲', '▼', '◆', '■', '□', '+', '△', '○', '◇','(干化学)','(尿流式)','(镜检)']
        cleaned_text = text
        for symbol in special_symbols:
            cleaned_text = cleaned_text.replace(symbol, '')

        return cleaned_text.strip()

    def post(self, shared: Dict[str, Any], prep_res: List[Dict[str, Any]],
             exec_res: List[Dict[str, Any]]) -> str:
        """后处理阶段：保存筛选结果和正常项目"""
        abnormal_items = exec_res  # 异常项目（已预处理）
        normal_items = getattr(self, '_normal_items', [])  # 正常项目

        # 保存分类结果
        shared["abnormal_items"] = abnormal_items
        shared["normal_items"] = normal_items
        shared["test_results"] = prep_res  # 保存原始数据用于后续处理
        shared["original_raw_data"] = prep_res  # 保存原始数据用于后续处理

        logger.info(f"异常项筛选和预处理完成 - 异常项: {len(abnormal_items)}, 正常项: {len(normal_items)}")

        # for a in abnormal_items:
        #     print(a)


        if not abnormal_items:
            logger.warning("没有筛选出异常项")
            return "no_abnormal_items"

        return "has_abnormal_items"



class CTCAERuleMatcherNode(Node):
    """CTCAE规则匹配节点：专门负责CTCAE规则匹配和异常项分类"""

    def __init__(self, max_retries=2, wait=1, max_concurrent_rerank=20):
        super().__init__(max_retries=max_retries, wait=wait)
        # 在构造函数中初始化CTCAE规则匹配器，支持并发配置
        self.rule_matcher = CTCAERuleMatcher(max_concurrent_rerank=max_concurrent_rerank)
        
    def prep(self, shared: Dict[str, Any]) -> List[Dict[str, Any]]:
        """准备阶段：获取异常项"""
        abnormal_items = shared.get("abnormal_items", [])
        logger.info(f"开始CTCAE规则匹配，异常项数: {len(abnormal_items)}")
        return abnormal_items
    
    def exec(self, abnormal_items: List[Dict[str, Any]]) -> Tuple[Dict[int, List[Dict[str, Any]]], List[Dict[str, Any]], List[Dict[str, Any]]]:
        """执行阶段：匹配CTCAE规则并分离有匹配和无匹配的项目"""
        # 记录开始时间
        start_time = time.time()
        
        # 1. 使用并发版本匹配相关的 CTCAE 规则
        matched_rules = self.rule_matcher.match_relevant_rules_concurrent(test_items=abnormal_items)
        
        # 记录结束时间并计算耗时
        end_time = time.time()
        rerank_total_time = end_time - start_time

        # 2. 分离有匹配和无匹配的项目
        items_with_matches = []
        items_without_matches = []

        for item in abnormal_items:
            item_id = item.get('id', 0)
            item_rules = matched_rules.get(item_id, [])

            if item_rules:  # 有CTCAE规则匹配
                items_with_matches.append(item)
            else:  # 无CTCAE规则匹配
                items_without_matches.append(item)

        # 获取rerank详细统计信息
        rerank_call_count = getattr(self.rule_matcher, 'rerank_call_count', 0)
        rerank_service_time = getattr(self.rule_matcher, 'total_rerank_time', 0.0)
        max_concurrent = getattr(self.rule_matcher, 'max_concurrent_rerank', 1)

        # 使用表格格式输出CTCAE匹配统计
       

        print(f"CTCAE规则匹配完成，总耗时: {rerank_total_time:.2f}秒，Rerank调用次数: {rerank_call_count}次，最大并发数: {max_concurrent}")
        
        return (matched_rules, items_with_matches, items_without_matches)
    
    def exec_fallback(self, abnormal_items: List[Dict[str, Any]], exc: Exception) -> Tuple[Dict[int, List[Dict[str, Any]]], List[Dict[str, Any]], List[Dict[str, Any]]]:
        """降级处理：返回空规则匹配结果"""
        logger.warning(f"CTCAE规则匹配失败，降级处理: {exc}")
        # 降级时将所有项目都视为无匹配
        return ({}, [], abnormal_items)


    def post(self, shared: Dict[str, Any], prep_res: List[Dict[str, Any]],
             exec_res: Tuple[Dict[int, List[Dict[str, Any]]], List[Dict[str, Any]], List[Dict[str, Any]]]) -> str:
        """后处理阶段：保存CTCAE规则匹配结果和分离的项目数据"""
        matched_rules, items_with_matches, items_without_matches = exec_res

        # print("CTCAE匹配=================================================================================")
        # for i in items_with_matches:
        #     print(i)
        # print("CTCAE未匹配=================================================================================")
        # for i in items_without_matches:
        #     print(i)

        # 保存CTCAE规则匹配结果
        shared["matched_rules_by_item"] = matched_rules

        # 保存分离的项目数据
        shared["items_with_ctcae_matches"] = items_with_matches
        shared["items_without_ctcae_matches"] = items_without_matches

        return "default"


class ConcurrencyControlledBatchNode(AsyncNode):
    """支持并发数量控制的批处理双模型推理节点，支持动态超时配置"""

    def __init__(self, max_retries=2, wait=5,timeout=None):
        """初始化，设置重试参数和超时时间

        Args:
            max_retries: 最大重试次数
            wait: 重试间隔时间（秒）
            timeout: 超时时间（秒），默认使用配置文件中的TIMEOUT_SECONDS
        """
        super().__init__(max_retries=max_retries, wait=wait)
        self.model_functions = {}
        self.semaphore = None
        # 支持动态配置超时时间
        self.timeout = TIMEOUT_SECONDS
        # 简化性能统计
        self.perf_stats = {}
        
    async def prep_async(self, shared: Dict[str, Any]) -> List[Dict[str, Any]]:
        """准备阶段：将有CTCAE匹配的异常项分批处理"""
        # 初始化性能统计
        self.perf_stats = {
            'total_start_time': time.time(),
            'batch_times': []
        }
        
        # 初始化信号量控制并发数量
        if self.semaphore is None:
            self.semaphore = asyncio.Semaphore(MAX_CONCURRENT_BATCHES)

        # 获取CTCAE规则匹配结果
        self.matched_rules_by_item = shared.get("matched_rules_by_item", {})

        # 从shared中获取task_info，直接保存用于模型调用，确保始终是字典
        self._task_info = shared.get("task_info", {})
        
        # 从shared中获取预加载的提示词模板
        self._prompt_templates = shared.get("prompt_templates")

        # 获取有CTCAE匹配的异常项（只处理这些项目）
        items_with_matches = shared.get("items_with_ctcae_matches", [])
        if not items_with_matches:
            logger.warning("没有有CTCAE匹配的异常项需要处理")
            return []
        
        # 分批处理
        batch_size = BATCH_SIZE
        batches = []

        for i in range(0, len(items_with_matches), batch_size):
            batch_items = items_with_matches[i:i + batch_size]
            batch_data = {
                "batch_id": i // batch_size + 1,
                "items": batch_items,
                "total_batches": (len(items_with_matches) + batch_size - 1) // batch_size
            }
            batches.append(batch_data)

        logger.info(f"分批处理完成: {len(items_with_matches)}个有CTCAE匹配的异常项 → {len(batches)}个批次")
        return batches
    
    async def exec_async(self, batches: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """执行阶段：并发处理多个批次"""
        # 确保模型函数已加载
        if not self.model_functions:
            self._load_model_functions()
        
        # 创建信号量任务列表
        tasks = []
        for batch in batches:
            task = self._process_batch_with_semaphore(batch)
            tasks.append(task)
        
        # 等待所有批次完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果和异常
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"批次 {i+1} 处理失败: {result}")
                processed_results.append({
                    "batch_id": i + 1,
                    "success": False,
                    "error": str(result),
                    "model_results": []
                })
            else:
                processed_results.append(result)
        
        return processed_results
    
    async def _process_batch_with_semaphore(self, batch: Dict[str, Any]) -> Dict[str, Any]:
        """使用信号量控制并发的批次处理"""
        async with self.semaphore:
            batch_id = batch["batch_id"]
            items = batch["items"]
            
            logger.info(f"开始处理批次 {batch_id}，包含 {len(items)} 个异常项")
            
            try:
                # 构建批次提示词，使用CTCAE规则匹配结果
                prompt,prompt_qwen = self._build_batch_prompt(items, getattr(self, 'matched_rules_by_item', {}))
                
                # 执行双模型推理
                model_results, model_times = await self._dual_model_inference(prompt,prompt_qwen)
                
                # 记录批次耗时
                self.perf_stats['batch_times'].append({
                    'batch_id': batch_id,
                    PRIMARY_MODEL: model_times[0],
                    SECONDARY_MODEL: model_times[1]
                })

                logger.info(f"批次 {batch_id} 处理完成")
                
                return {
                    "batch_id": batch_id,
                    "success": True,
                    "model_results": model_results,
                    "processed_items": len(items)
                }
                
            except Exception as e:
                error_msg = f"批次 {batch_id} 处理失败: {e}"
                logger.error(error_msg)
                raise Exception(error_msg)
    
    def _build_batch_prompt(self, items: List[Dict[str, Any]], matched_rules_by_item: Dict[int, List[Dict[str, Any]]]) -> str:
        """构建批次提示词，基于当前批次的异常项和对应的CTCAE规则"""
        from script.ae_grade.utils import SmartPromptBuilder

        # 使用SmartPromptBuilder构建提示词
        prompt_builder = SmartPromptBuilder()

        # 为当前批次的项目提取对应的CTCAE规则
        batch_matched_rules = {}
        for item in items:
            item_id = item.get('id', 0)
            if item_id in matched_rules_by_item:
                batch_matched_rules[item_id] = matched_rules_by_item[item_id]

        # 从shared中获取预加载的提示词模板
        prompt_templates = getattr(self, '_prompt_templates', None)

        # 构建针对当前批次的优化提示词
        batch_prompt,optimized_template_qwen = prompt_builder.build_optimized_prompt(
            test_items=items,
            matched_rules=batch_matched_rules,
            prompt_templates=prompt_templates
        )

        return batch_prompt,optimized_template_qwen
    
    async def _dual_model_inference(self, prompt: str,prompt_qwen:str):
        """双模型推理"""
        # 获取模型函数
        primary_model_func = self.model_functions.get(PRIMARY_MODEL)
        secondary_model_func = self.model_functions.get(SECONDARY_MODEL)
        
        if not primary_model_func:
            logger.error(f"模型函数 {PRIMARY_MODEL} 加载失败")
            raise Exception(f"Primary model {PRIMARY_MODEL} not available")
        
        if not secondary_model_func:
            logger.error(f"模型函数 {SECONDARY_MODEL} 加载失败")
            raise Exception(f"Primary model {SECONDARY_MODEL} not available")
        
        # 获取task_info（从shared中获取任务信息）
        task_info = getattr(self, '_task_info', {})
        
        # 并发调用两个模型，使用动态超时配置和task_info
        task1 = asyncio.to_thread(self._call_model_safely, primary_model_func, prompt, self.timeout, PRIMARY_MODEL, task_info)
        task2 = asyncio.to_thread(self._call_model_safely, secondary_model_func, prompt_qwen, self.timeout, SECONDARY_MODEL, task_info)
        
        # 等待两个模型完成
        results = await asyncio.gather(task1, task2, return_exceptions=True)

        print('=' * 50)
        print(prompt)
        
        # 解包结果和时间
        res1, time1 = (results[0][0], results[0][1]) if not isinstance(results[0], Exception) else (None, 0)
        res2, time2 = (results[1][0], results[1][1]) if not isinstance(results[1], Exception) else (None, 0)

        print('=' * 50)
        print(res1)
        print('=' * 50)
        print(res2)
        
        # 处理结果
        primary_result = {
            "model_name": PRIMARY_MODEL,
            "response": res1,
            "success": not isinstance(results[0], Exception),
            "error": str(results[0]) if isinstance(results[0], Exception) else None
        }
        
        secondary_result = {
            "model_name": SECONDARY_MODEL,
            "response": res2,
            "success": not isinstance(results[1], Exception),
            "error": str(results[1]) if isinstance(results[1], Exception) else None
        }
        
        # 如果两个模型都失败，抛出异常
        if not primary_result["success"] and not secondary_result["success"]:
            error_msg = f"双模型推理均失败: {results[0]}, {results[1]}"
            logger.error(error_msg)
            raise Exception(error_msg)
        
        return [primary_result, secondary_result], (time1, time2)
    
    def _load_model_functions(self):
        """加载模型函数"""
        try:
            # 动态导入模型函数
            from common.clients.llm_client import deepseek_r1_distill_qwen_32b
            from common.clients.llm_client import qwen3_32b

            self.model_functions = {
                "deepseek_r1_qwen_32b": deepseek_r1_distill_qwen_32b,
                "qwen3_32b": qwen3_32b
            }
            logger.info("模型函数加载成功")
            
        except ImportError as e:
            logger.error(f"模型函数导入失败: {e}")
            self.model_functions = {}
    
    def _call_model_safely(self, model_func, prompt: str, timeout: int, model_name: str, task_info=None):
        """安全调用模型函数，返回(结果, 时间)元组，支持新的日志装饰器"""
        start_time = time.time()
        logger.info(f"开始调用模型 {model_name}，超时时间: {timeout}秒...")

        try:
            # 直接调用模型函数，装饰器会自动处理日志参数的检查
            result = model_func(
                prompt,
                timeout=timeout,
                category='AE_TEST_REPORT',  # 固定为AE测试报告类别
                **(task_info or {})
            )
            
            # 记录结束时间和统计信息
            processing_time = time.time() - start_time
            
            response_length = len(result) if result else 0
            logger.info(f"模型 {model_name} 调用成功，响应长度: {response_length}，耗时: {processing_time:.2f}秒")
            return result, processing_time

        except requests.exceptions.Timeout as e:
            logger.warning(f"模型 {model_name} 调用超时 (超过{timeout}秒)，将触发重试...")
            # 重新抛出异常，以便Node的重试机制能够捕获并处理
            raise e
        except Exception as e:
            logger.error(f"模型 {model_name} 调用失败: {e}")
            raise e
    
    async def post_async(self, shared: Dict[str, Any], prep_res: List[Dict[str, Any]], exec_res: List[Dict[str, Any]]) -> str:
        """后处理阶段：合并批次结果并进行AE描述丰富化"""
        # 合并所有批次的模型结果
        all_model_results = []
        successful_batches = 0
        total_processed_items = 0
        
        for batch_result in exec_res:
            if batch_result["success"]:
                successful_batches += 1
                total_processed_items += batch_result.get("processed_items", 0)
                all_model_results.extend(batch_result["model_results"])
            else:
                logger.warning(f"批次 {batch_result['batch_id']} 处理失败: {batch_result.get('error', '未知错误')}")
        
        # 对模型结果进行AE描述丰富化处理
        enriched_model_results = self._enrich_ae_descriptions(all_model_results)
        
        # 保存丰富化后的结果到shared
        shared["model_results"] = enriched_model_results

        # 输出简化性能统计
        self._print_simple_stats(successful_batches)

        # 使用表格格式输出批处理统计
        batch_stats = [
            ["总批次数", len(exec_res)],
            ["成功批次", successful_batches],
            ["失败批次", len(exec_res) - successful_batches],
            ["成功率", f"{successful_batches/len(exec_res)*100:.1f}%" if len(exec_res) > 0 else "0%"],
            ["处理异常项", total_processed_items],
            ["平均每批次", f"{total_processed_items/successful_batches:.1f}项" if successful_batches > 0 else "0项"]
        ]
 
        logger.info("批处理完成统计:")
        logger.info("\n" + tabulate(batch_stats, headers=["指标", "数值"], tablefmt="grid", stralign="center"))
        return "default"

    def _print_simple_stats(self, successful_batches: int):
        """输出简化的性能统计"""
        logger.info("=" * 50)
        logger.info("性能统计")
        logger.info("=" * 50)
        
        total_time = time.time() - self.perf_stats.get('total_start_time', time.time())
        batch_times = self.perf_stats.get('batch_times', [])
        
        # 1. 按批次显示两个模型的时间
        if batch_times:
            batch_data = []
            model1_total = 0
            model2_total = 0
            
            for batch in batch_times:
                model1_time = batch.get(PRIMARY_MODEL, 0)
                model2_time = batch.get(SECONDARY_MODEL, 0)
                model1_total += model1_time
                model2_total += model2_time
                batch_data.append([
                    f"批次 {batch['batch_id']}",
                    f"{model1_time:.2f}秒",
                    f"{model2_time:.2f}秒"
                ])
            
            logger.info("各批次模型时间:")
            headers = ["批次", PRIMARY_MODEL, SECONDARY_MODEL]
            logger.info("\n" + tabulate(batch_data, headers=headers, tablefmt="grid", stralign="center"))

            # 2. 模型平均时间和总时间
            model1_avg = model1_total / successful_batches if successful_batches > 0 else 0
            model2_avg = model2_total / successful_batches if successful_batches > 0 else 0
            
            model_summary = [
                [PRIMARY_MODEL, f"{model1_total:.2f}秒", f"{model1_avg:.2f}秒"],
                [SECONDARY_MODEL, f"{model2_total:.2f}秒", f"{model2_avg:.2f}秒"]
            ]
            
            logger.info("\n模型总结:")
            headers = ["模型名称", "总时间", "平均时间"]
            logger.info("\n" + tabulate(model_summary, headers=headers, tablefmt="grid", stralign="center"))
        
        # 3. 总体时间
        logger.info(f"\n总处理时间: {total_time:.2f}秒")
        logger.info("=" * 50)
    
    def _enrich_ae_descriptions(self, model_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """对模型结果进行AE描述丰富化处理"""
        logger.info(f"开始AE描述丰富化处理，模型结果数: {len(model_results)}")
        
        # 加载CTCAE规则（如果还未加载）
        if not hasattr(self, '_ctcae_rules'):
            self._load_ctcae_rules()
        
        enriched_results = []
        for model_result in model_results:
            enriched_result = model_result.copy()
            
            # 处理模型响应中的结果
            if 'response' in enriched_result and enriched_result['response']:
                try:
                    # 解析模型响应
                    from script.ae_grade.utils import ResultParser
                    parser = ResultParser()
                    parsed_results = parser.parse_llm_response(enriched_result['response'])
                    
                    # 对解析后的每个结果项进行AE描述丰富化
                    if parsed_results and 'results' in parsed_results:
                        for result_item in parsed_results['results']:
                            self._enrich_single_result_ae_desc(result_item)
                    
                    # 更新模型结果中的响应
                    enriched_result['parsed_response'] = parsed_results
                    
                except Exception as e:
                    logger.warning(f"解析模型响应时出错: {e}，跳过AE描述丰富化")
            
            enriched_results.append(enriched_result)
        
        logger.info(f"AE描述丰富化处理完成")
        return enriched_results
    
    def _load_ctcae_rules(self):
        """加载CTCAE规则数据"""
        try:
            self._ctcae_rules = get_ctcae_rules()
            logger.info(f"CTCAE规则加载成功，共 {len(self._ctcae_rules)} 条规则")
        except Exception as e:
            logger.error(f"CTCAE规则加载失败: {e}")
            self._ctcae_rules = []
    
    def _enrich_single_result_ae_desc(self, result_item: Dict[str, Any]):
        """为单个结果项丰富化AE描述"""
        ae_name = result_item.get('ae_name', '').strip()
        ae_grade = result_item.get('ae_grade', 0)
        
        # ae_grade可能是整数类型，也可能是字符串
        try:
            ae_grade = int(ae_grade) if ae_grade else 0
        except (ValueError, TypeError):
            ae_grade = 0
        
        # 只处理有效的AE结果
        if (ae_name and 
            ae_grade > 0 and 
            ae_name != "CTCAE未涉及，请研究者判定"):
            
            grade_desc = self._get_grade_description(ae_name, ae_grade)
            if grade_desc:
                result_item['ae_desc'] = grade_desc
                logger.debug(f"为 {ae_name} 等级 {ae_grade} 设置描述: {grade_desc[:50]}...")
    
    def _get_grade_description(self, ae_name: str, ae_grade: int) -> str:
        """根据AE名称获取所有等级的描述，拼接成字符串"""
        for rule in self._ctcae_rules:
            if rule.get('ae_name') == ae_name:
                grade_rules = rule.get('grade_rules', {})
                # 将所有等级的定义拼接成字符串，不同等级之间换行
                descriptions = []
                for grade in sorted(grade_rules.keys(), key=int):
                    descriptions.append(f"{grade}: {grade_rules[grade]}")
                return '\n'.join(descriptions)
        return ''






class OutputNode(Node):
    """简化输出节点 - 直接从模型结果提取标准化输出"""

    def __init__(self):
        super().__init__()
        from script.ae_grade.utils import ResultParser
        self.parser = ResultParser()

    def prep(self, shared: Dict[str, Any]) -> Dict[str, Any]:
        """准备阶段：获取模型结果和其他必要数据"""
        model_results = shared.get("model_results", [])
        normal_items = shared.get("normal_items", [])
        items_without_ctcae_matches = shared.get("items_without_ctcae_matches", [])
        items_with_ctcae_matches = shared.get("items_with_ctcae_matches", [])
        original_raw_data = shared.get("original_raw_data", [])

        logger.info(f"开始简化输出处理 - 模型结果: {len(model_results)}, 正常项: {len(normal_items)}, 无CTCAE匹配: {len(items_without_ctcae_matches)}, 有CTCAE匹配: {len(items_with_ctcae_matches)}")

        return {
            "model_results": model_results,
            "normal_items": normal_items,
            "items_without_ctcae_matches": items_without_ctcae_matches,
            "items_with_ctcae_matches": items_with_ctcae_matches,
            "original_raw_data": original_raw_data
        }

    def exec(self, prep_data: Dict[str, Any]) -> Dict[str, Any]:
        """执行阶段：直接提取和标准化输出"""
        model_results = prep_data["model_results"]
        normal_items = prep_data["normal_items"]
        items_without_ctcae_matches = prep_data["items_without_ctcae_matches"]
        original_raw_data = prep_data["original_raw_data"]

        all_results = []

        # 1. 处理正常项目
        for item in normal_items:
            result = {
                "id": item.get("id", 0),
                "ae_name": "",
                "ae_grade": 0,
                "ae_desc": ""
            }
            all_results.append(result)

        # 2. 处理无CTCAE匹配的异常项
        for item in items_without_ctcae_matches:
            result = {
                "id": item.get("id", 0),
                "ae_name": "CTCAE未涉及，请研究者判定",
                "ae_grade": "",
                "ae_desc": ""
            }
            all_results.append(result)

        # 3. 处理模型推理结果 - 保存双模型的原始结果
        dual_model_data = {"llm1_results": [], "llm2_results": []}

        if model_results:
            # 分离主模型和副模型的结果
            primary_model_results = []
            secondary_model_results = []

            for i, result in enumerate(model_results):
                if result.get("model_name") == "deepseek_r1_qwen_32b":
                    primary_model_results.append(result)
                elif result.get("model_name") == "qwen3_32b":
                    secondary_model_results.append(result)

            # 解析所有主模型结果
            for result in primary_model_results:
                if result.get("success"):
                    try:
                        # 优先使用丰富化后的parsed_response，如果没有则使用原始response
                        if "parsed_response" in result and result["parsed_response"]:
                            parsed_data = result["parsed_response"]
                            logger.debug("使用丰富化后的parsed_response")
                        elif result.get("response"):
                            parsed_data = self.parser.parse_llm_response(result["response"])
                            logger.debug("使用原始response进行解析")
                        else:
                            continue
                            
                        if parsed_data and isinstance(parsed_data, dict) and "results" in parsed_data:
                            dual_model_data["llm1_results"].extend(parsed_data["results"])
                        elif parsed_data and isinstance(parsed_data, list):
                            dual_model_data["llm1_results"].extend(parsed_data)
                        elif parsed_data:
                            dual_model_data["llm1_results"].append(parsed_data)
                    except Exception as e:
                        logger.error(f"主模型批次解析失败: {e}")

            # 解析所有副模型结果
            for result in secondary_model_results:
                if result.get("success"):
                    try:
                        # 优先使用丰富化后的parsed_response，如果没有则使用原始response
                        if "parsed_response" in result and result["parsed_response"]:
                            parsed_data = result["parsed_response"]
                            logger.debug("使用丰富化后的parsed_response")
                        elif result.get("response"):
                            parsed_data = self.parser.parse_llm_response(result["response"])
                            logger.debug("使用原始response进行解析")
                        else:
                            continue
                            
                        if parsed_data and isinstance(parsed_data, dict) and "results" in parsed_data:
                            dual_model_data["llm2_results"].extend(parsed_data["results"])
                        elif parsed_data and isinstance(parsed_data, list):
                            dual_model_data["llm2_results"].extend(parsed_data)
                        elif parsed_data:
                            dual_model_data["llm2_results"].append(parsed_data)
                    except Exception as e:
                        logger.error(f"副模型批次解析失败: {e}")

            logger.info(f"主模型(DeepSeek-R1)解析成功，提取了 {len(dual_model_data['llm1_results'])} 项CTCAE匹配异常项")
            logger.info(f"副模型(Qwen3-32B)解析成功，提取了 {len(dual_model_data['llm2_results'])} 项CTCAE匹配异常项")

            # 选择主要结果用于最终输出（优先主模型）
            # primary_model_items = dual_model_data["llm1_results"] if dual_model_data["llm1_results"] else dual_model_data["llm2_results"]

            # 标准化每个项目
            # for item in primary_model_items:
            #     if isinstance(item, dict):
            #         result = {
            #             "id": item.get("id", item.get("序号", 0)),
            #             "ae_name": item.get("ae_name", item.get("不良事件名称", "")),
            #             "ae_grade": self._safe_int_conversion(item.get("ae_grade", item.get("CTCAE分级", 0))),
            #             "ae_desc": item.get("ae_desc", item.get("不良事件定义", ""))
            #         }
            #         all_results.append(result)
            #
            # logger.info(f"最终输出使用了 {len(primary_model_items)} 项模型结果")

        # 4. 数据合并：将正常项和CTCAE未匹配项合并到dual_model_data中
        logger.info("开始数据合并：将正常项和CTCAE未匹配项合并到dual_model_data中")

        try:
            # 为正常项和CTCAE未匹配项创建标准化格式
            normal_and_unmatched_items = []

            # 添加正常项
            for item in normal_items:
                item_id = item.get("id")
                if item_id is None or item_id == "":
                    logger.warning(f"跳过缺少id的正常项: {item}")
                    continue

                standardized_item = {
                    "id": item_id,  # 确保id字段存在且有效
                    "ae_name": "",
                    "ae_grade": 0,
                    "ae_desc": ""
                }
                normal_and_unmatched_items.append(standardized_item)

            # 添加CTCAE未匹配的异常项
            for item in items_without_ctcae_matches:
                item_id = item.get("id")
                if item_id is None or item_id == "":
                    logger.warning(f"跳过缺少id的CTCAE未匹配项: {item}")
                    continue

                standardized_item = {
                    "id": item_id,  # 确保id字段存在且有效
                    "ae_name": "CTCAE未涉及，请研究者判定",
                    "ae_grade": "",
                    "ae_desc": ""
                }
                normal_and_unmatched_items.append(standardized_item)

            # 合并到两个模型结果中，使用深拷贝确保数据独立
            import copy
            dual_model_data["llm1_results"].extend(copy.deepcopy(normal_and_unmatched_items))
            dual_model_data["llm2_results"].extend(copy.deepcopy(normal_and_unmatched_items))

            logger.info(f"数据合并完成 - 主模型结果总数: {len(dual_model_data['llm1_results'])}, 次模型结果总数: {len(dual_model_data['llm2_results'])}")
            logger.info(f"合并的正常项: {len(normal_items)}, 合并的CTCAE未匹配项: {len(items_without_ctcae_matches)}")

            # 验证合并后的数据完整性
            for i, item in enumerate(dual_model_data["llm1_results"]):
                if 'id' not in item:
                    logger.error(f"LLM1结果第{i}项缺少id字段: {item}")
            for i, item in enumerate(dual_model_data["llm2_results"]):
                if 'id' not in item:
                    logger.error(f"LLM2结果第{i}项缺少id字段: {item}")

        except Exception as e:
            logger.error(f"数据合并过程中发生错误: {e}")
            logger.warning("数据合并失败，dual_model_data将只包含CTCAE匹配的异常项")

        # 按ID排序确保输出顺序一致，处理None值
        all_results.sort(key=lambda x: x.get("id") or 0)

        # 构建最终输出，包含双模型的原始结果
        final_output = {
            "results": all_results,
            "summary": {
                "total_processed": len(original_raw_data),
                "abnormal_detected": len([r for r in all_results if r.get("ae_grade") and r.get("ae_grade") != 0]),
                "processing_time": 0,  # 将在主系统中更新
                "status": "completed"
            },
            "dual_model_data": dual_model_data  # 保存双模型的原始解析结果
        }

        # 使用表格格式输出简化输出统计
        ctcae_matched_count = len(all_results) - len(normal_items) - len(items_without_ctcae_matches)
        abnormal_detected_count = len([r for r in all_results if r.get("ae_grade") and r.get("ae_grade") != 0])

        output_stats = [
            ["总输出项目", len(all_results)],
            ["正常项目", len(normal_items)],
            ["异常无CTCAE匹配", len(items_without_ctcae_matches)],
            ["异常有CTCAE匹配", ctcae_matched_count],
            ["异常检出项目", abnormal_detected_count],
            ["", ""],  # 分隔线
            ["LLM1结果总数(合并后)", len(dual_model_data['llm1_results'])],
            ["LLM2结果总数(合并后)", len(dual_model_data['llm2_results'])]
        ]

        logger.info("简化输出完成统计:")
        logger.info("\n" + tabulate(output_stats, headers=["项目", "数量"], tablefmt="grid", stralign="center"))


        # 输出结果
        # print("大模型1结果-===========================================")
        # for o in dual_model_data['llm1_results']:
        #     print(o)
        # print("大模型2结果-===========================================")
        # for o in dual_model_data['llm2_results']:
        #     print(o)

        return final_output

    def _safe_int_conversion(self, value: Any) -> int:
        """安全的整数转换"""
        if value is None or value == "":
            return 0
        try:
            return int(value)
        except (ValueError, TypeError):
            return 0

    def post(self, shared: Dict[str, Any], prep_res: Dict[str, Any], exec_res: Dict[str, Any]) -> str:
        """后处理阶段：保存最终输出结果"""
        shared["output_result"] = exec_res

        logger.info("简化输出结果已保存")
        return "default"



class NoAbnormalItemsNode(Node):
    """无异常项处理节点"""

    def prep(self, shared: Dict[str, Any]) -> List[Dict[str, Any]]:
        """准备阶段：获取检验项目数据"""
        # 优先使用预处理后的数据
        test_results = shared.get("test_results", [])
        if not test_results:
            test_results = shared.get("original_raw_data", [])
        
        logger.info(f"处理无异常项情况，项目数: {len(test_results)}")
        return test_results

    def exec(self, test_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """执行阶段：创建正常结果"""
        results = []
        for i, item in enumerate(test_results):
            result = {
                "id": item.get("id", i + 1),
                "ae_name": "",  # 无AE
                "ae_grade": 0,     # 0级表示正常
                "ae_desc": ""
                # abnormal_flag将在后处理阶段基于ae_grade计算
            }
            results.append(result)

        logger.info(f"创建正常结果完成，项目数: {len(results)}")
        return results
    
    def post(self, shared: Dict[str, Any], prep_res: List[Dict[str, Any]],
             exec_res: List[Dict[str, Any]]) -> str:
        """后处理阶段：设置无异常项标志，为OutputNode准备数据"""
        # 设置空的模型结果，表示没有进行模型推理
        shared["model_results"] = []

        # 确保normal_items包含所有项目
        shared["normal_items"] = prep_res

        # 确保无CTCAE匹配项为空
        shared["items_without_ctcae_matches"] = []

        logger.info(f"无异常项处理完成，设置了 {len(prep_res)} 项为正常项目")
        return "default"


