from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SimpleRouter

from . import views

urlpatterns = [
    #发送对话消息
    path('/chat-messages', views.ChatMessagesView.as_view()),

    #上传文件
    path('/files/upload', views.FilesUploadView.as_view()),

    # 停止响应
    path('/chat-messages/<str:task_id>/stop', views.StopChatMessagesView.as_view()),

    # 消息反馈（点赞）
    path('/messages/<str:message_id>/feedbacks', views.FeedbacksView.as_view()),

    # # 获取下一轮建议问题列表
    # path('/messages/<str:message_id>/suggested', views.SuggestedView.as_view()),

    # 获取会话历史消息
    path('/messages', views.MessagesView.as_view()),

    # 获取会话列表
    path('/conversations', views.ConversationsView.as_view()),

    # 删除会话
    path('/conversations/<str:conversation_id>', views.DeleteConversationsView.as_view()),

    # 会话重命名
    path('/conversations/<str:conversation_id>/name', views.RenameConversationsView.as_view()),

    # # 语音转文字
    # path('/audio-to-text', views.AudioToText.as_view()),
    #
    # # 文字转语音
    # path('/text-to-audio', views.TextToAudio.as_view()),

    # 获取应用基本信息
    path('/info', views.InfoView.as_view()),

    # 获取应用参数
    path('/parameters', views.ParametersView.as_view()),

    # 获取应用Meta信息
    path('/meta', views.MetaView.as_view()),
    #
    # # 获取标注列表
    # path('/apps/annotations', views.ChatMessagesView.as_view()),
    #
    # # 创建标注
    # path('/apps/annotations', views.ChatMessagesView.as_view()),
    #
    # # 更新标注
    # path('/apps/annotations/{annotation_id}', views.ChatMessagesView.as_view()),
    #
    # # 删除标注
    # path('/apps/annotations/{annotation_id}', views.ChatMessagesView.as_view()),
    #
    # # 标注回复初始设置
    # path('/apps/annotation-reply/{action}', views.ChatMessagesView.as_view()),
    #
    # # 查询标注回复初始设置任务状态
    # path('/apps/annotation-reply/{action}/status/{job_id}', views.ChatMessagesView.as_view()),
]


router = DefaultRouter(trailing_slash=False)
router.register(r'/conversation-feedback', views.ConversationFeedbackListViewSet)

urlpatterns = router.urls + urlpatterns

