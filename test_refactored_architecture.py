# -*- coding: utf-8 -*-
"""
重构后架构测试脚本

测试重构后的统一处理架构是否正确工作
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smo.settings.test')
os.environ["DJANGO_ALLOW_ASYNC_UNSAFE"] = "true"

try:
    django.setup()
    print("✅ Django环境初始化成功")
except Exception as e:
    print(f"❌ Django环境初始化失败: {e}")
    sys.exit(1)

def test_unified_processor():
    """测试统一处理器"""
    try:
        from script.test_result_format_ae_ocr.unified_processor import (
            process_medical_ocr_unified, 
            process_medical_file,
            process_medical_file_complete
        )
        
        print("\n🏥 测试统一处理器...")
        
        # 测试数据
        test_ocr_text = """
        血常规检查报告
        
        检查项目    结果    单位    参考范围
        白细胞计数  6.5     10^9/L  3.5-9.5
        红细胞计数  4.2     10^12/L 4.3-5.8
        血红蛋白    120     g/L     130-175
        """
        
        test_metadata = {
            'subject_medical_info_id': 'unified_test_001',
            'subject_id': 'subject_001',
            'subject_item_id': 'item_001',
            'task_info': {'task_id': 'unified_test'},
            'visit_data': []
        }
        
        # 1. 测试简化接口
        print("  测试简化接口...")
        result1 = process_medical_ocr_unified(
            ocr_text=test_ocr_text,
            task_info={'task_id': 'unified_test'},
            ocr_blocks=[],
            file_metadata=test_metadata
        )
        print(f"    - 简化接口结果: {len(result1.get('test_results', []))} 个项目")
        
        # 2. 测试完整接口（不含事务）
        print("  测试完整接口（不含事务）...")
        result2 = process_medical_file(
            ocr_text=test_ocr_text,
            task_info={'task_id': 'unified_test'},
            ocr_blocks=[],
            file_metadata=test_metadata,
            subject_id='subject_001',
            subject_item_id='item_001',
            visit_data=[]
        )
        print(f"    - 完整接口结果: {len(result2.get('test_results', []))} 个项目")
        
        # 3. 测试完整接口（含事务，但不提供事务参数）
        print("  测试完整接口（含事务参数，但禁用事务）...")
        result3 = process_medical_file_complete(
            ocr_text=test_ocr_text,
            task_info={'task_id': 'unified_test'},
            ocr_blocks=[],
            file_metadata=test_metadata,
            subject_id='subject_001',
            subject_item_id='item_001',
            visit_data=[],
            enable_transaction=False  # 禁用事务以避免数据库操作
        )
        print(f"    - 完整接口（禁用事务）结果: {len(result3.get('test_results', []))} 个项目")
        print(f"    - 事务状态: {result3.get('transaction_success')}")
        print(f"    - 处理模式: {result3.get('processing_mode')}")
        
        print("✅ 统一处理器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 统一处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_script_integration():
    """测试主脚本集成"""
    try:
        from script.test_result_format import request_llm_api1
        
        print("\n🔗 测试主脚本集成...")
        
        # 测试数据
        test_ocr_text = """
        生化检查报告
        
        检查项目    结果    单位
        总胆固醇    5.2     mmol/L
        甘油三酯    1.8     mmol/L
        """
        
        test_metadata = {
            'subject_medical_info_id': 'main_script_test_001',
            'subject_id': 'subject_001',
            'subject_item_id': 'item_001',
            'task_info': {'task_id': 'main_script_test'},
            'visit_data': []
        }
        
        # 调用重构后的主脚本函数
        result = request_llm_api1(
            ocr_text=test_ocr_text,
            task_info={'task_id': 'main_script_test'},
            ocr_blocks=[],
            file_metadata=test_metadata
        )
        
        print(f"  - 主脚本集成结果: {len(result.get('test_results', []))} 个项目")
        print(f"  - 结果类型: {type(result)}")
        
        print("✅ 主脚本集成测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 主脚本集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_configuration_modes():
    """测试配置模式"""
    try:
        from script.test_result_format_ae_ocr.simple_config import (
            get_simple_config, 
            apply_preset_config,
            update_simple_config
        )
        from script.test_result_format_ae_ocr.unified_processor import process_medical_ocr_unified
        
        print("\n🔧 测试配置模式...")
        
        test_ocr_text = "血常规检查报告\n白细胞计数  6.5  10^9/L"
        test_metadata = {'subject_medical_info_id': 'config_test_001'}
        
        # 1. 测试串行模式
        print("  测试串行模式...")
        apply_preset_config("serial_only")
        config = get_simple_config()
        print(f"    - 当前配置: 并行={config.enable_parallel}, 强制串行={config.force_serial_processing}")
        
        result1 = process_medical_ocr_unified(
            ocr_text=test_ocr_text,
            file_metadata=test_metadata
        )
        print(f"    - 串行模式结果: {len(result1.get('test_results', []))} 个项目")
        
        # 2. 测试并行模式
        print("  测试并行模式...")
        apply_preset_config("balanced")
        config = get_simple_config()
        print(f"    - 当前配置: 并行={config.enable_parallel}, 并发数={config.max_workers}")
        
        result2 = process_medical_ocr_unified(
            ocr_text=test_ocr_text,
            file_metadata=test_metadata
        )
        print(f"    - 并行模式结果: {len(result2.get('test_results', []))} 个项目")
        
        print("✅ 配置模式测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置模式测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_error_handling():
    """测试错误处理"""
    try:
        from script.test_result_format_ae_ocr.unified_processor import process_medical_ocr_unified
        
        print("\n🛠️ 测试错误处理...")
        
        # 1. 测试空输入
        print("  测试空输入...")
        result1 = process_medical_ocr_unified(
            ocr_text="",
            file_metadata={}
        )
        print(f"    - 空输入结果: {len(result1.get('test_results', []))} 个项目")
        
        # 2. 测试无效输入
        print("  测试无效输入...")
        result2 = process_medical_ocr_unified(
            ocr_text="这是一段无效的文本，不包含任何医疗信息",
            file_metadata={'subject_medical_info_id': 'error_test'}
        )
        print(f"    - 无效输入结果: {len(result2.get('test_results', []))} 个项目")
        
        print("✅ 错误处理测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_architecture_separation():
    """测试架构分离"""
    try:
        print("\n🏗️ 测试架构分离...")
        
        # 1. 检查统一处理器模块的独立性
        print("  检查统一处理器模块...")
        from script.test_result_format_ae_ocr import unified_processor
        print(f"    - 统一处理器模块: {type(unified_processor)}")
        
        # 2. 检查配置管理的独立性
        print("  检查配置管理模块...")
        from script.test_result_format_ae_ocr import simple_config
        print(f"    - 配置管理模块: {type(simple_config)}")
        
        # 3. 检查并行处理器的独立性
        print("  检查并行处理器模块...")
        from script.test_result_format_ae_ocr import parallel_processor
        print(f"    - 并行处理器模块: {type(parallel_processor)}")
        
        # 4. 检查事务管理器的独立性
        print("  检查事务管理器模块...")
        from script.test_result_format_ae_ocr import simple_transaction
        print(f"    - 事务管理器模块: {type(simple_transaction)}")
        
        print("✅ 架构分离测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 架构分离测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 开始重构后架构测试")
    print("=" * 60)
    
    tests = [
        ("统一处理器", test_unified_processor),
        ("主脚本集成", test_main_script_integration),
        ("配置模式", test_configuration_modes),
        ("错误处理", test_error_handling),
        ("架构分离", test_architecture_separation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"⚠️ {test_name}测试未通过")
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 重构后架构测试全部通过！")
        print("📝 架构重构成功:")
        print("  ✅ 代码分离清晰：主脚本专注任务管理，OCR模块专注处理逻辑")
        print("  ✅ 统一入口：所有并行/串行逻辑统一管理")
        print("  ✅ 配置灵活：支持多种处理模式和动态切换")
        print("  ✅ 错误处理：完善的回退和容错机制")
        print("  ✅ 向后兼容：保持与现有代码的完全兼容")
    else:
        print("⚠️ 部分测试失败，请检查相关模块")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
