"""
Nacos服务发现心跳脚本
"""
import sys
import os
import socket
import time
import json
import logging
import http.client
from http import HTTPStatus
from urllib.parse import urlencode
from urllib.error import HTTPError, URLError

import retry
import nacos
import django
from django.conf import settings
from nacos.client import DEFAULT_GROUP_NAME
from nacos.exception import NacosException, NacosRequestException

from common.pynacos import NacosClientPlus


os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smo.settings.test')
django.setup()

metadata = {'DJANGO_SETTINGS_MODULE': os.environ.get('DJANGO_SETTINGS_MODULE')}


# @retry.retry(tries=3, delay=1)
def check_http_service(ip, port):
    try:
        conn = http.client.HTTPConnection(ip, port, timeout=5)
        conn.request("GET", "/api/status/")
        response = conn.getresponse()
        if response.status == 200:
            return response.read()
        else:
            raise Exception('http service error')
    except Exception as e:
        logging.exception("[check_http_service] exception %s occur" % str(e))
        raise


def main():
    client = NacosClientPlus(
        server_addresses=settings.NACOS['SERVER_ADDR'],
        namespace=settings.NACOS['NAMESPACE'],
        username=settings.NACOS['USERNAME'],
        password=settings.NACOS['PASSWORD'],
    )
    logging.info(client)

    instance = dict(
        service_name=settings.NACOS['APP_SERVICE_NAME'],
        ip=settings.NACOS['APP_DEPLOY_HOST'],
        port=settings.NACOS['APP_DEPLOY_PORT'],
        group_name=settings.NACOS['APP_SERVER_GROUP'],
        metadata=json.dumps(metadata)
    )
    logging.info(instance)

    first = True
    while True:
        try:
            http_res = check_http_service(settings.NACOS['APP_DEPLOY_HOST'], settings.NACOS['APP_DEPLOY_PORT'])
            logging.debug(f'http_res: {http_res}')

            nacos_res = client.send_heartbeat(**instance)
            logging.debug(f'nacos_res: {nacos_res}')

            if first:  # 首次登录时，确保instance metadata数据是最新的
                nacos_res = client.modify_naming_instance(**instance)
                logging.debug(f'nacos_res: {nacos_res}')
                first = False
        except Exception as e:
            logging.error(f'{e}')

        time.sleep(5)  # 每5秒检测一次服务状态发送一次nacos心跳


if __name__ == "__main__":
    main()
