
from django.db.models import Q
from django_filters import rest_framework as filters
from . import models


class MedicalInfoLiteFilter(filters.FilterSet):
    subject_visit_id = filters.CharFilter(field_name='subject_visit__subject_visit_id', label='受试者访视ID', required=True)
    project_id = filters.CharFilter(field_name='project__project_id', label='项目ID', required=False)
    project_site_id = filters.CharFilter(field_name='project_site__project_site_id', label='项目中心ID', required=False)
    subject_id = filters.CharFilter(field_name='subject__subject_id', label='受试者ID', required=True)
    subject_item_id = filters.CharFilter(field_name='subject_item__subject_item_id', label='受试者操作项ID', required=True)
    

    class Meta:
        model = models.MedicalInfoLite
        fields = ['subject_visit_id','project_id', 'project_site_id', 'subject_id']
