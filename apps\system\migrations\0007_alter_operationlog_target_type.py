# Generated by Django 4.1.5 on 2025-06-21 19:36

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("system", "0006_alter_operationlog_target_type"),
    ]

    operations = [
        migrations.AlterField(
            model_name="operationlog",
            name="target_type",
            field=models.CharField(
                choices=[
                    ("CONFIRM_OCR_LOG", "ocr确认操作痕迹"),
                    ("CONFIRM_AE_LOG", "ae等级确认操作痕迹"),
                    ("CREATE_AE_TRACKER_LOG", "创建ae-tracker确认操作痕迹"),
                    ("EDIT_AE_LOG", "编辑单条ae等级操作痕迹"),
                    ("CONFIRM_AE_MEDICATION_MEASURES", "确认AE用药操作痕迹"),
                    ("EDIT_AE_MEDICATION_MEASURES", "单条编辑用药操作痕迹"),
                ],
                db_index=True,
                default="CONFIRM_OCR_LOG",
                max_length=50,
                verbose_name="目标类型",
            ),
        ),
    ]
