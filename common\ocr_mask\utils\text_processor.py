"""
文本处理相关工具函数
"""
import json


def _is_coordinate_reasonable(location, image_width=None, image_height=None):
    """
    检查OCR坐标是否合理

    Args:
        location: OCR文本块的四个角坐标 [[x1,y1], [x2,y2], [x3,y3], [x4,y4]]
        image_width: 图片宽度（可选）
        image_height: 图片高度（可选）

    Returns:
        bool: 坐标是否合理
    """
    if not location or len(location) != 4:
        return False

    try:
        # 提取所有坐标点
        x_coords = [point[0] for point in location]
        y_coords = [point[1] for point in location]

        # 计算文本块的宽度和高度
        width = max(x_coords) - min(x_coords)
        height = max(y_coords) - min(y_coords)

        # 检查基本合理性
        if width <= 0 or height <= 0:
            return False

        # 检查是否有负坐标
        if any(x < 0 for x in x_coords) or any(y < 0 for y in y_coords):
            return False

        # 如果提供了图片尺寸，检查是否超出边界
        if image_width and any(x > image_width for x in x_coords):
            return False
        if image_height and any(y > image_height for y in y_coords):
            return False

        # 检查文本块是否过大（可能是错误坐标）
        # 如果文本块覆盖超过图片50%的面积，可能有问题
        if image_width and image_height:
            block_area = width * height
            image_area = image_width * image_height
            if block_area > image_area * 0.5:
                print(f"⚠️ 检测到异常大的文本块: 宽度={width}, 高度={height}, 覆盖面积={block_area/image_area:.1%}")
                return False
        else:
            # 即使没有图片尺寸，也检查明显异常的大小
            # 如果文本块宽度或高度超过1500像素，很可能是错误坐标
            if width > 1500 or height > 2000:
                print(f"⚠️ 检测到异常大的文本块: 宽度={width}, 高度={height}")
                return False

        return True

    except Exception as e:
        print(f"⚠️ 坐标合理性检查出错: {e}")
        return False


def build_char_to_block_mapping(ocr_result):
    """
    构建字符位置到OCR文本块的映射关系

    这是适配新HIPAA脱敏服务的核心算法。新服务返回的敏感词包含在完整文本中的
    字符位置(start/end)，需要映射到具体的OCR文本块以便进行图像遮挡。

    Args:
        ocr_result: OCR识别结果的JSON字符串或已解析的字典

    Returns:
        list: char_to_block_mapping - 字符位置映射列表

    映射表结构:
    [
        {
            "char_start": 0,      # 在完整文本中的起始字符位置
            "char_end": 10,       # 在完整文本中的结束字符位置
            "block_index": 0,     # 对应的文本块索引
            "block_text": "文本内容",  # 文本块内容
            "block_location": [[x1,y1], [x2,y2], [x3,y3], [x4,y4]]  # 图像坐标
        }
    ]
    """
    try:
        # 处理OCR结果 - 可以是JSON字符串或已解析的字典
        if not ocr_result:
            return []

        # 如果是字符串，解析为字典
        if isinstance(ocr_result, str):
            ocr_data = json.loads(ocr_result)
        # 如果已经是字典，直接使用
        elif isinstance(ocr_result, dict):
            ocr_data = ocr_result
        else:
            return []

        if not isinstance(ocr_data, dict) or "result" not in ocr_data:
            return []

        result = ocr_data.get("result", {})
        if "words_block_list" not in result:
            return []

        words_block_list = result.get("words_block_list", [])
        char_to_block_mapping = []
        full_text = ""
        current_char_pos = 0

        # 遍历每个文本块，构建映射关系
        for block_index, block in enumerate(words_block_list):
            if not isinstance(block, dict):
                continue

            block_text = block.get("words", "")
            block_location = block.get("location", [])

            if not block_text:
                continue

            # 🔧 检查坐标合理性
            if not _is_coordinate_reasonable(block_location):
                print(f"⚠️ 跳过坐标异常的文本块 {block_index}: '{block_text}' - 坐标: {block_location}")
                # 仍然添加文本到full_text中，但不添加到映射表（这样敏感词检测仍然有效，但不会进行图像遮挡）
                full_text += block_text
                current_char_pos += len(block_text)

                # 添加空格分隔符（如果不是最后一个块）
                if block_index < len(words_block_list) - 1:
                    full_text += " "
                    current_char_pos += 1
                continue

            # 计算当前文本块在完整文本中的字符位置范围
            char_start = current_char_pos
            char_end = current_char_pos + len(block_text)

            # 添加映射记录
            char_to_block_mapping.append({
                "char_start": char_start,
                "char_end": char_end,
                "block_index": block_index,
                "block_text": block_text,
                "block_location": block_location,
                "confidence": block.get("confidence", 1.0)
            })

            # 更新完整文本和字符位置
            full_text += block_text
            current_char_pos = char_end

            # 添加空格分隔符（如果不是最后一个块）
            if block_index < len(words_block_list) - 1:
                full_text += " "
                current_char_pos += 1

        return char_to_block_mapping

    except Exception as e:
        print(f"❌ 构建字符位置映射时出错: {e}")
        return []


def map_entity_to_blocks(entity, char_to_block_mapping):
    """
    将敏感词实体映射到对应的OCR文本块

    Args:
        entity: 敏感词实体，格式如：
        {
            "text": "张三",
            "entity_type": "PERSON",
            "start": 2,
            "end": 4,
            "confidence": 0.95
        }
        char_to_block_mapping: 字符位置映射表

    Returns:
        list: 匹配的文本块信息列表，每个元素包含：
        {
            "entity": 原始实体信息,
            "block_info": 文本块信息,
            "relative_start": 在文本块内的相对起始位置,
            "relative_end": 在文本块内的相对结束位置
        }
    """
    if not entity or not char_to_block_mapping:
        return []

    entity_start = entity.get("start", 0)
    entity_end = entity.get("end", 0)
    entity_text = entity.get("text", "")

    matched_blocks = []

    # 查找与敏感词位置重叠的文本块
    for mapping in char_to_block_mapping:
        block_start = mapping["char_start"]
        block_end = mapping["char_end"]

        # 检查是否有重叠
        if entity_end <= block_start or entity_start >= block_end:
            continue  # 没有重叠

        # 计算在文本块内的相对位置
        relative_start = max(0, entity_start - block_start)
        relative_end = min(len(mapping["block_text"]), entity_end - block_start)

        # 验证文本匹配（可选的安全检查）
        if relative_start < relative_end:
            block_text_segment = mapping["block_text"][relative_start:relative_end]

            matched_blocks.append({
                "entity": entity,
                "block_info": mapping,
                "relative_start": relative_start,
                "relative_end": relative_end,
                "matched_text": block_text_segment
            })

    return matched_blocks

