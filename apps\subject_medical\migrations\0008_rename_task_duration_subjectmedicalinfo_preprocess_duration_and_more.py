# Generated by Django 4.1.5 on 2025-08-20 11:00

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("subject_medical", "0007_subjectmedicalinfo_task_duration_and_more"),
    ]

    operations = [
        migrations.RenameField(
            model_name="subjectmedicalinfo",
            old_name="task_duration",
            new_name="preprocess_duration",
        ),
        migrations.RenameField(
            model_name="subjectmedicalinfo",
            old_name="task_end_time",
            new_name="preprocess_end_time",
        ),
        migrations.RenameField(
            model_name="subjectmedicalinfo",
            old_name="task_start_time",
            new_name="preprocess_start_time",
        ),
        migrations.AddField(
            model_name="subjectmedicalinfo",
            name="ocr_box",
            field=models.JSONField(
                blank=True, null=True, verbose_name="OCR带坐标识别结果"
            ),
        ),
    ]
