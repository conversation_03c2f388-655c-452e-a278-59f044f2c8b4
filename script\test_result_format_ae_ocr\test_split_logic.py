#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试OCR块拆分逻辑
"""

import sys
import os

# 获取当前脚本的绝对路径
current_script_path = os.path.abspath(__file__)
# 获取项目根目录
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_script_path)))

# 将项目根目录添加到 sys.path
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 直接导入main模块
sys.path.insert(0, os.path.dirname(current_script_path))
from main import split_ocr_text_by_pages

def test_split_ocr_blocks():
    """测试OCR块拆分功能"""
    
    # 模拟多页OCR文本
    ocr_text = """[Page 1]
第一页内容
葡萄糖 4.90 3.90-6.10 mmol/L

[Page 2]
第二页内容
脂肪酶 58.2 13.0-60.0 U/L

[Page 3]
第三页内容
其他检测项目"""
    
    # 模拟OCR块数据
    ocr_blocks = [
        {"page": 1, "words": "葡萄糖", "location": [[100, 200], [200, 200], [200, 250], [100, 250]]},
        {"page": 1, "words": "4.90", "location": [[300, 200], [350, 200], [350, 250], [300, 250]]},
        {"page": 2, "words": "脂肪酶", "location": [[150, 300], [250, 300], [250, 350], [150, 350]]},
        {"page": 2, "words": "58.2", "location": [[350, 300], [400, 300], [400, 350], [350, 350]]},
        {"page": 3, "words": "其他检测项目", "location": [[200, 400], [350, 400], [350, 450], [200, 450]]},
        {"page": 4, "words": "第四页内容", "location": [[100, 500], [250, 500], [250, 550], [100, 550]]},  # 这个页码没有对应的文本
    ]
    
    print("=== 测试OCR块拆分功能 ===")
    print(f"原始OCR文本页数: 3页")
    print(f"原始OCR块数量: {len(ocr_blocks)}个")
    
    # 调用拆分函数
    pages, page_blocks_map = split_ocr_text_by_pages(ocr_text, ocr_blocks)
    
    print(f"\n=== 拆分结果 ===")
    print(f"文本页面数: {len(pages)}")
    print(f"OCR块分组数: {len(page_blocks_map)}")
    
    print(f"\n=== 页面详情 ===")
    for page_content, page_num in pages:
        page_blocks = page_blocks_map.get(page_num, [])
        print(f"第{page_num}页:")
        print(f"  - 文本长度: {len(page_content)}")
        print(f"  - OCR块数: {len(page_blocks)}")
        if page_blocks:
            print(f"  - OCR块内容: {[block['words'] for block in page_blocks]}")
        print()
    
    print("=== 验证结果 ===")
    # 验证第1页
    page_1_blocks = page_blocks_map.get(1, [])
    assert len(page_1_blocks) == 2, f"第1页应该有2个OCR块，实际有{len(page_1_blocks)}个"
    assert page_1_blocks[0]['words'] == "葡萄糖", f"第1页第一个块应该是'葡萄糖'，实际是'{page_1_blocks[0]['words']}'"
    
    # 验证第2页
    page_2_blocks = page_blocks_map.get(2, [])
    assert len(page_2_blocks) == 2, f"第2页应该有2个OCR块，实际有{len(page_2_blocks)}个"
    assert page_2_blocks[0]['words'] == "脂肪酶", f"第2页第一个块应该是'脂肪酶'，实际是'{page_2_blocks[0]['words']}'"
    
    # 验证第3页
    page_3_blocks = page_blocks_map.get(3, [])
    assert len(page_3_blocks) == 1, f"第3页应该有1个OCR块，实际有{len(page_3_blocks)}个"
    
    # 验证第4页（没有对应文本，应该不在结果中）
    assert 4 not in page_blocks_map, "第4页不应该在结果中（因为没有对应文本）"
    
    print("✅ 所有测试通过！")
    
    # 测试无OCR块的情况
    print(f"\n=== 测试无OCR块情况 ===")
    pages_no_blocks, page_blocks_map_no_blocks = split_ocr_text_by_pages(ocr_text, None)
    assert len(pages_no_blocks) == 3, "无OCR块时应该仍能正常拆分文本"
    assert len(page_blocks_map_no_blocks) == 0, "无OCR块时应该返回空的分组字典"
    print("✅ 无OCR块测试通过！")
    
    # 测试单页情况
    print(f"\n=== 测试单页情况 ===")
    single_page_text = "单页内容\n葡萄糖 4.90 3.90-6.10 mmol/L"
    pages_single, page_blocks_map_single = split_ocr_text_by_pages(single_page_text, ocr_blocks[:2])
    assert len(pages_single) == 1, "单页文本应该返回1页"
    assert len(page_blocks_map_single) == 1, "单页OCR块应该返回1个分组"
    assert page_blocks_map_single[1][0]['words'] == "葡萄糖", "单页OCR块应该正确分组"
    print("✅ 单页测试通过！")

if __name__ == "__main__":
    test_split_ocr_blocks()