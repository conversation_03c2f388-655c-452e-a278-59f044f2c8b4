import os
import django
from django.utils import timezone
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smo.settings.test')
django.setup()

import psycopg2
from django.db import transaction
from apps.project.models import ProjectSite

pg_conn_params = {
    'host': '**************',
    'database': 'oceanus_trial_clinplus',
    'user': 'postgres',
    'password': 'Qr@77.88',
    'port': 5432
}

PAGE_SIZE = 1000


def migrate_project_site():
    pg_conn = psycopg2.connect(**pg_conn_params)
    pg_cursor = pg_conn.cursor()
    total_processed = 0
    offset = 0
    processed_project_site_ids = set()
    try:
        while True:
            print(f"\n📄 Fetching offset: {offset}")

            pg_query = f"""
            SELECT
                t1.id as project_site_id,
                t1.project_id,
                t1.hosp_id,
                t1.hosp_name,
                t1.hosp_department_no,
                t1.status,
                dictconstantname('e888888', t1.status) as status_text,
                projectsiteroleusersandno(t1.id, '20107') as accredit_crc_text,
                projectsiteroleusersandno(t1.id, '212886296')  as backup_crc_text
            FROM project_site AS t1
            ORDER BY t1.id
            LIMIT {PAGE_SIZE} OFFSET {offset}
            """
            pg_cursor.execute(pg_query)
            data = pg_cursor.fetchall()
            column_names = [desc[0] for desc in pg_cursor.description]

            if not data:
                break  # 没有更多数据了

            project_site_dicts = [dict(zip(column_names, row)) for row in data]
            project_site_ids = [d["project_site_id"] for d in project_site_dicts]
            processed_project_site_ids.update(project_site_ids)
            existing_qs = ProjectSite.objects.filter(project_site_id__in=project_site_ids)
            existing_map = {obj.project_site_id: obj for obj in existing_qs}

            to_create = []
            to_update = []

            for d in project_site_dicts:
                pk = d["project_site_id"]
                if pk in existing_map:
                    obj = existing_map[pk]
                    changed = False
                    for key, value in d.items():
                        if getattr(obj, key) != value:
                            setattr(obj, key, value)
                            changed = True
                    if changed:
                        to_update.append(obj)
                else:
                    to_create.append(ProjectSite(**d))

            with transaction.atomic():
                if to_create:
                    ProjectSite.objects.bulk_create(to_create, batch_size=PAGE_SIZE)
                    print(f"✅ Created {len(to_create)} records")

                if to_update:
                    update_fields = list(project_site_dicts[0].keys())
                    ProjectSite.objects.bulk_update(to_update, update_fields, batch_size=PAGE_SIZE)
                    print(f"🔄 Updated {len(to_update)} records")

            total_processed += len(data)
            offset += PAGE_SIZE

        # 软删除源系统中已删除的数据
        print("🔍 Checking for records to delete...")
        # 获取当前时间用于更新 update_time 字段
        current_time = timezone.now()

        # 分批处理删除操作，避免一次性加载大量数据到内存
        batch_size = 10000
        deleted_count = 0
        offset = 0

        while True:
            # 获取一批本地未删除的记录ID
            local_site_ids_batch = list(ProjectSite.objects.filter(
                delete_flag=0
            ).values_list('project_site_id', flat=True)[offset:offset + batch_size])

            if not local_site_ids_batch:
                break

            # 找出这批记录中需要删除的（即在源系统中不存在的）
            to_delete_batch = [sid for sid in local_site_ids_batch if sid not in processed_project_site_ids]

            if to_delete_batch:
                # 将这些记录标记为已删除 (软删除) 并更新 update_time
                count = ProjectSite.objects.filter(project_site_id__in=to_delete_batch).update(
                    delete_flag=1,
                    update_time=current_time
                )
                deleted_count += count
                print(f"🗑️  Soft deleted {count} records in current batch")

            offset += batch_size

        print(f"🗑️  Total soft deleted {deleted_count} records that no longer exist in source system")

        print(f"\n🎉 Migration completed. Total processed: {total_processed}")

    except Exception as e:
        print(f"\n❌ Error during migration: {str(e)}")

    finally:
        pg_cursor.close()
        pg_conn.close()
        print("🔚 PostgreSQL connection closed.")


if __name__ == "__main__":
    # python -m script.sync_ot_project_site_all
    migrate_project_site()
