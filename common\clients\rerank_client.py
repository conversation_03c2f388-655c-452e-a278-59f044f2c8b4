"""
极简Rerank客户端
提供通用的文本重排序API调用功能
"""
import time
import logging
import requests
from typing import List, Dict, Any
from urllib3.util.retry import Retry
from requests.adapters import HTTPAdapter

logger = logging.getLogger(__name__)

# 默认配置
DEFAULT_CONFIG = {
    'base_url': "http://192.168.230.3:8081",
    'token': "cHKajrZMzxw5tgQAeOjSEtgZUWr4AB4P",
    'timeout': 30,
    'max_retries': 3
}

def call_rerank(query: str,
                texts: List[str],
                base_url: str = None,
                token: str = None,
                timeout: int = None,
                raw_scores: bool = True,
                return_text: bool = False,
                truncate: bool = True,
                truncation_direction: str = "Right"):
    """
    调用rerank服务对文本进行重排序

    Args:
        query: 查询文本
        texts: 待排序的文本列表
        base_url: 服务基础URL
        token: 认证token
        timeout: 请求超时时间（秒）
        raw_scores: 是否返回原始分数
        return_text: 是否返回文本内容
        truncate: 是否截断长文本
        truncation_direction: 截断方向 ("Left" 或 "Right")

    Returns:
        List[Dict]: 按相关性分数从高到低排序的结果列表
        每个字典包含以下字段：
        - text (str): 原始文本内容
        - score (float): 相关性分数
        - index (int): 该文本在原始输入列表中的位置索引

        示例：[
            {"text": "血小板计数降低...", "score": 0.9872, "index": 2},
            {"text": "白细胞计数异常...", "score": 0.0099, "index": 0},
            {"text": "血小板聚集异常...", "score": 0.0097, "index": 3},
            {"text": "血红蛋白降低...", "score": 0.0081, "index": 1}
        ]

    Raises:
        ValueError: 输入参数错误
        Exception: API调用失败
    """
    if not texts:
        return []
    
    # 使用默认配置
    config = DEFAULT_CONFIG.copy()
    if base_url:
        config['base_url'] = base_url
    if token:
        config['token'] = token
    if timeout:
        config['timeout'] = timeout
    
    # 构建请求URL和头部
    rerank_url = f"{config['base_url'].rstrip('/')}/rerank"
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {config["token"]}' if config['token'] else None
    }
    
    # 构建请求数据
    request_data = {
        "query": query,
        "texts": texts,
        "raw_scores": raw_scores,
        "return_text": return_text,
        "truncate": truncate,
        "truncation_direction": truncation_direction
    }
    
    try:
        start_time = time.time()
        
        # 发送请求
        response = requests.post(
            rerank_url,
            json=request_data,
            headers=headers,
            timeout=config['timeout'],
            verify=False
        )
        
        request_time = time.time() - start_time
        response.raise_for_status()
        
        # 解析响应
        result = response.json()
        
        # 提取分数
        if isinstance(result, dict) and 'scores' in result:
            scores = result['scores']
        elif isinstance(result, list):
            scores = result
        else:
            raise ValueError(f"意外的响应格式: {result}")
        
        # 处理分数格式 - 统一返回排序后的结果
        if scores and isinstance(scores[0], dict):
            logger.debug(f"Rerank返回字典格式，包含{len(scores)}个结果")

            # 构建排序后的结果（服务已排序，直接添加文本信息）
            sorted_results = []
            for item in scores:
                if isinstance(item, dict) and 'index' in item and 'score' in item:
                    index = item['index']
                    score = item['score']
                    if 0 <= index < len(texts):
                        sorted_results.append({
                            'text': texts[index],
                            'score': score,
                            'index': index
                        })
                    else:
                        logger.warning(f"Rerank返回的index {index} 超出范围 [0, {len(texts)})")
                else:
                    logger.warning(f"Rerank返回项格式错误: {item}")

            return sorted_results
        else:
            # 处理直接返回分数列表的情况（不太可能，但保持兼容性）
            logger.warning("Rerank返回非字典格式，可能是旧版本API")
            # 假设返回的是按原始顺序的分数，需要手动排序
            indexed_scores = [(score, i) for i, score in enumerate(scores)]
            indexed_scores.sort(key=lambda x: x[0], reverse=True)

            sorted_results = []
            for score, index in indexed_scores:
                if 0 <= index < len(texts):
                    sorted_results.append({
                        'text': texts[index],
                        'score': score,
                        'index': index
                    })

            return sorted_results
        
    except requests.exceptions.Timeout:
        logger.error(f"Rerank请求超时 (>{config['timeout']}秒)")
        raise Exception(f"Rerank请求超时")
    except requests.exceptions.ConnectionError:
        logger.error(f"无法连接到Rerank服务: {rerank_url}")
        raise Exception(f"无法连接到Rerank服务")
    except requests.exceptions.HTTPError as e:
        error_detail = ""
        try:
            error_detail = e.response.text
        except:
            pass
        logger.error(f"Rerank服务返回HTTP错误: {e.response.status_code}, 详情: {error_detail}")
        raise Exception(f"Rerank服务HTTP错误: {e.response.status_code}, 详情: {error_detail}")
    except Exception as e:
        logger.error(f"Rerank请求失败: {e}")
        raise Exception(f"Rerank请求失败: {e}")


def batch_rerank(queries: List[str],
                texts_lists: List[List[str]],
                **kwargs) -> List[List[Dict]]:
    """
    批量处理多个查询

    Args:
        queries: 查询文本列表
        texts_lists: 对应的文本列表的列表
        **kwargs: 其他参数传递给call_rerank

    Returns:
        List[List[Dict]]: 每个查询对应的排序结果列表
        每个结果字典包含 text, score, index 字段

    Raises:
        ValueError: 查询数量与文本列表数量不匹配
    """
    if len(queries) != len(texts_lists):
        raise ValueError("查询数量与文本列表数量不匹配")

    results = []

    for i, (query, texts) in enumerate(zip(queries, texts_lists)):
        try:
            sorted_results = call_rerank(query, texts, **kwargs)
            results.append(sorted_results)
            logger.debug(f"批量rerank进度: {i+1}/{len(queries)}")
        except Exception as e:
            logger.error(f"批量rerank第{i+1}个查询失败: {e}")
            # 返回空结果作为降级
            results.append([])

    return results


def main():
    """
    测试rerank服务是否正常工作
    """
    # 测试数据
    query = "血小板计数异常"
    texts = [
        "白细胞计数异常，可能提示感染或血液疾病",
        "血红蛋白降低，提示可能存在贫血", 
        "血小板计数降低，需要注意出血风险",
        "血小板聚集异常，影响凝血功能"
    ]
    
    print("开始测试rerank服务...")
    print(f"查询: {query}")
    print(f"待排序文本数量: {len(texts)}")
    
    try:
        # 调用rerank服务
        results = call_rerank(query, texts)
        
        print("\n排序结果:")
        for i, result in enumerate(results):
            print(f"{i+1}. [分数: {result['score']:.4f}] {result['text']}")
            
        print(f"\n测试成功！共返回{len(results)}个结果")
        
    except Exception as e:
        print(f"测试失败: {e}")
        

if __name__ == "__main__":
    main()

