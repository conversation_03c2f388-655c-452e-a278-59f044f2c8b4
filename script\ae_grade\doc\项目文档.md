# AE Grade Recognition System Overview

## Purpose
This system optimizes Adverse Event (AE) grade recognition for medical laboratory test results using the PocketFlow framework. The goal is to reduce processing time from 60 seconds to 8 seconds (87% improvement) while maintaining accuracy.

## Core Architecture
- **Framework**: PocketFlow-based LLM workflow system
- **Primary Model**: DeepSeek-R1-Distill-Qwen-32B (primary analysis)
- **Secondary Model**: qwen3-32b (comparison/validation)
- **Processing Pattern**: Dual model comparison (NOT aggregation)

## Key Components
- @script/ae_grade/main.py - System entry point and orchestration
- @script/ae_grade/flow.py - PocketFlow workflow definition
- @script/ae_grade/nodes.py - Processing node implementations
- @script/ae_grade/ae_grade_recognition.py - Main system class (database operations only)
- @script/ae_grade/optimization_config.py - Configuration management

## Data Flow
1. **Input**: Raw database query results (id, test_name, test_value, test_flag)
2. **Processing**: PocketFlow handles all business logic
3. **Output**: Compatible with existing database update format

## Critical Design Principle
ALL business logic processing occurs within PocketFlow nodes. The main system class only handles:
- Database operations (read → call → update)
- Error handling and performance monitoring
- System coordination


# PocketFlow Usage Patterns

## Framework Compliance Requirements

### Node Implementation
- Use standard PocketFlow Node/AsyncNode classes from `pocketflow` package
- Implement proper lifecycle methods: `prep()`, `exec()`, `post()`
- For async nodes: `prep_async()`, `exec_async()`, `post_async()`

### Flow Definition
- Use `>>` operator for sequential connections: `node1 >> node2`
- Use `-` operator for conditional routing: `node - "condition" - target_node`
- **NEVER** use `flow.add_node()` or `flow.add_edge()` (deprecated APIs)

### Data Management
- All inter-node communication through shared state dictionaries
- Nodes read from shared state in `prep()`, write back in `post()`
- Example: `shared["processed_data"] = exec_res`

## Project-Specific Patterns

### Configuration Access
```python
from .optimization_config import get_config
config = get_config()
```

### Error Handling
- Implement `exec_fallback()` methods for critical nodes
- Use proper logging with context: `logger.error(f"Node failed: {e}")`
- Fail fast principle: avoid try-catch unless specific recovery needed

### Performance Monitoring
- Integration with @script/ae_grade/utils/performance_monitor.py
- Track processing stages in `shared["processing_metadata"]`

## Current Flow Architecture
1. **DataPreprocessNode** - Database format conversion
2. **AbnormalItemFilterNode** - Filter abnormal test results
3. **SmartPromptBuilderNode** - CTCAE rule matching + prompt building
4. **DualModelInferenceNode** - Async dual model processing
5. **ResultParserNode** - Parse model outputs
6. **ResultComparisonNode** - Compare model results
7. **DataPostprocessNode** - Final result formatting
