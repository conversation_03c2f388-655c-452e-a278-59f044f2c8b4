from datetime import timedelta
from django.conf import settings
from rest_framework import serializers

from common.minio_client import get_minio_client
from common.models import BaseFileModel
from common.serializers import FileUrlMixin

from . import models

class SubjectMedicalInfoMaskedUpdateRequestSerializer(serializers.ModelSerializer):
    project_id = serializers.CharField(source='project.project_id', label="项目ID")
    project_site_id = serializers.CharField(source='project_site.project_site_id', label="项目中心ID")
    file_masked_id = serializers.Char<PERSON>ield(required=True, label="脱敏文件ID") 
    # 新增的三个字段
    key_id = serializers.CharField(required=True, label="主键id")

    subject_id = serializers.CharField(source='subject.subject_id', label="受试者ID")
    file = serializers.FileField(required=True)  # 文件上传字段
    # file_masked_id = serializers.Char<PERSON><PERSON>(required=False, label="患者医疗脱敏文件ID") 

    class Meta:
        model = models.SubjectMedicalInfo
        fields = [
            'project_id',
            'project_site_id',
            'file_masked_id',
            'key_id',
            'subject_id',
            'file'
        ]

class SubjectMedicalInfoFileSerializer(FileUrlMixin, serializers.ModelSerializer):
    url = serializers.SerializerMethodField(label='文件下载url')

    class Meta:
        model = models.SubjectMedicalFile
        exclude = ['create_user', 'create_name', 'update_user', 'update_name']


class SubjectMedicalInfoSerializer(serializers.ModelSerializer):
    project_id = serializers.CharField(source='project.project_id', label="项目ID")
    project_site_id = serializers.CharField(source='project_site.project_site_id', label="项目中心ID")
    subject_id = serializers.CharField(source='subject.subject_id', label="受试者ID")
    subject_item_id = serializers.CharField(source='subject_item.subject_item_id', label="受试者操作项ID")
    file = SubjectMedicalInfoFileSerializer()
    file_masked = SubjectMedicalInfoFileSerializer()

    class Meta:
        model = models.SubjectMedicalInfo
        exclude = ['project', 'project_site', 'subject', 'patient', 'subject_epoch', 'subject_visit', 'subject_item',
                   'create_user', 'create_name', 'update_user', 'update_name']


class SubjectMedicalInfoCreateRequestSerializer(serializers.ModelSerializer):
    # project_id = serializers.CharField(source='project.project_id', label="项目ID")
    # project_site_id = serializers.CharField(source='project_site.project_site_id', label="项目中心ID")
    subject_id = serializers.CharField(source='subject.subject_id', label="受试者ID")
    subject_item_id = serializers.CharField(source='subject_item.subject_item_id', label="受试者操作项ID")
    file = serializers.FileField(required=True)  # 文件上传字段
    # item_type = serializers.CharField(label="操作项类型:1检验项目,2检查项目,3病史,0NA", required=False, help_text="操作项类型:1检验项目,2检查项目,3病史,0NA" )

    class Meta:
        model = models.SubjectMedicalInfo
        # fields = ['project_id', 'project_site_id', 'subject_id', 'subject_item_id', 'file']
        # fields = ['subject_id', 'subject_item_id', 'file', 'item_type']
        fields = ['subject_id', 'subject_item_id', 'file']


class MedicalFileUpdateSerializer(FileUrlMixin, serializers.ModelSerializer):

    class Meta:
        model = models.SubjectMedicalFile
        fields = ['original_filename']


class MedicalFileMaskedUpdateSerializer(FileUrlMixin, serializers.ModelSerializer):

    class Meta:
        model = models.SubjectMedicalFileMasked
        fields = ['original_filename']
