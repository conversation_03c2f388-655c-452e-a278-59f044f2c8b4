"""
每日OT项目同步DAG
------------------------------------
这个DAG在每天凌晨1:00运行，用于同步OT项目数据。

特点:
- 使用BashOperator运行外部脚本
- 任务超时时间设为2小时
- 限制同时只能运行1个实例

使用方法:
将此文件放入Airflow的DAGs文件夹，并确保引用的脚本位于正确的位置且具有适当的权限。
"""

from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.bash import BashOperator
from airflow.utils.dates import days_ago

# 为DAG中的所有任务定义默认参数
default_args = {
    'owner': 'ai',                   # DAG所有者(便于组织管理)
    'depends_on_past': False,               # 是否依赖上一次执行结果
    'start_date': datetime(2025, 4, 5),     # DAG开始时间
    'email': ['<EMAIL>'],        # 失败通知邮箱(请更新为您的邮箱)
    'email_on_failure': False,               # 失败时发送邮件
    'email_on_retry': False,                # 重试时不发送邮件
    'retries': 0,                           # 失败后重试次数
    'retry_delay': timedelta(minutes=5),    # 重试间隔时间
    'execution_timeout': timedelta(hours=8),  # 任务执行超时时间(2小时)
    'queue': 'default',                     # 任务队列
    'pool': 'default_pool',                 # 资源池
    'priority_weight': 10,                  # 任务优先级权重
    'end_date': None,                       # DAG停止调度时间
    'wait_for_downstream': False,           # 等待下游任务完成
    'dag_timeout': timedelta(hours=3),      # 整个DAG的超时时间
}

# 创建DAG
dag = DAG(
    'sync_ot_subject',                # DAG ID (唯一标识符)
    default_args=default_args,              # 应用默认参数
    description='每天凌晨1:00同步OT受试者数据的DAG',
    schedule_interval='0 1 * * *',          # Cron表达式，每天凌晨1:00运行
    catchup=False,                          # 不执行历史任务
    max_active_runs=1,                      # 只允许一个活跃的DAG运行实例
    max_active_tasks=1,                          # 所有DAG实例中同时只能运行一个任务
    tags=['sync', 'daily', 'ot'],   # UI中组织DAG的标签
    doc_md=__doc__,                         # 使用模块文档字符串作为文档
)

# 定义BashOperator任务
sync_task = BashOperator(
    task_id='sync_task',         # 任务ID
    cwd='/app',
    bash_command=f'python3.9 -m script.sync_ot_subject_all',     # 要执行的命令
    dag=dag,
)

# 任务特定设置，覆盖默认值
# sync_task.executor_config = {
#     'KubernetesExecutor': {
#         'request_memory': '2Gi',            # 内存请求(使用Kubernetes时)
#         'request_cpu': '1',                 # CPU请求(使用Kubernetes时)
#         'limit_memory': '4Gi',              # 内存限制(使用Kubernetes时)
#         'limit_cpu': '2',                   # CPU限制(使用Kubernetes时)
#     }
# }

# 您可以添加更多任务并使用 >> 定义依赖关系
# 例如:
# prep_task = BashOperator(
#     task_id='prepare_data',
#     bash_command='bash /path/to/prep_script.sh',
#     dag=dag,
# )
#
# cleanup_task = BashOperator(
#     task_id='cleanup',
#     bash_command='bash /path/to/cleanup_script.sh',
#     dag=dag,
# )
#
# prep_task >> sync_task >> cleanup_task

# 完成后监控或通知任务
# notify_task = BashOperator(
#     task_id='send_notification',
#     bash_command='bash /path/to/notification_script.sh',
#     dag=dag,
# )
#
# sync_task >> notify_task

# 用于命令行测试和调试
if __name__ == "__main__":
    dag.test()  # dag.cli()的现代替代方案
