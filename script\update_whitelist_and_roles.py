import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'smo.settings.test')
django.setup()

from apps.users.models import AuthProjectRoleACL, User

# 插入/更新角色权限配置
role_acl_data = [
    ('20101', 'PM', 'PM', 1, 1),
    ('20105', 'CoPM', 'CoPM', 1, 1),
    ('20107', '授权CRC', '授权CRC', 1, 2),
    ('20111', 'PL', 'PL', 1, 1),
    ('20116', 'BU Lead', 'BU Lead', 1, 1),
    ('20117', 'BU 总', 'BU 总', 1, 1),
    ('20119', 'BU Head', 'BU Head', 1, 1),
    ('20121', 'PMD', 'PMD', 1, 1),
    ('212886296', 'backup-CRC(参与项目执行CRC)', 'backup-CRC', 1, 2),
    ('23349789', 'BU PA', 'BU PA', 1, 1),
    ('10306', 'BU PA', 'BU PA', 1, 1),
]

for role_code, role_name, role_name_en, is_allowed, access_scope in role_acl_data:
    obj, created = AuthProjectRoleACL.objects.update_or_create(
        role_code=role_code,
        defaults={
            'role_name': role_name,
            'role_name_en': role_name_en,
            'is_allowed': bool(is_allowed),
            'access_scope': access_scope
        }
    )
    print(f"{'创建' if created else '更新'}角色权限: {role_code} - {role_name}")

# 插入/更新白名单用户配置
PROJECT_WHITE_LIST = [
    'smo990001', 'smo990112', 'smo990129', 'smo990128', 'smo990138',
    'smo990328', 'smo9911547', 'smo9915014', 'smo9910407', 'smo9913938',
    'smo9911147', 'smo9913054', 'smo9914198', 'smo9916937', 'smo9916728',
    'smo991795',
]

if os.environ.get('DJANGO_SETTINGS_MODULE') == 'smo.settings.test':
    PROJECT_WHITE_LIST.append('smo9912098')
    PROJECT_WHITE_LIST.append('smo9910013')
    PROJECT_WHITE_LIST.append('EA01')

for username in PROJECT_WHITE_LIST:
    user, created = User.objects.update_or_create(
        username=username,
        defaults={'project_whitelist_flag': 1}
    )
    print(f"{'创建' if created else '更新'}用户白名单标识: {username}")

print("✅ 所有数据插入或更新完成！")
