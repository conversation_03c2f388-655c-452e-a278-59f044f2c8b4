# Generated by Django 4.1.5 on 2025-05-14 16:24

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("project", "0002_alter_project_update_time_and_more"),
        ("subject", "0003_subjectitem_item_type"),
        ("patient", "0002_alter_patient_update_time"),
        ("system", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="ModelInvocationLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "delete_flag",
                    models.SmallIntegerField(
                        choices=[(0, "未删除"), (1, "已删除")],
                        db_index=True,
                        default=0,
                        verbose_name="删除标志（0：未删除；1：已删除）",
                    ),
                ),
                ("task_id", models.Char<PERSON>ield(max_length=50, verbose_name="任务ID")),
                (
                    "create_user",
                    models.CharField(max_length=255, null=True, verbose_name="创建人工号"),
                ),
                (
                    "create_name",
                    models.CharField(max_length=255, null=True, verbose_name="创建人姓名"),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True, db_index=True, verbose_name="创建时间"
                    ),
                ),
                (
                    "category",
                    models.CharField(
                        blank=True, max_length=50, null=True, verbose_name="任务名称"
                    ),
                ),
                (
                    "model_type",
                    models.CharField(
                        blank=True, max_length=100, null=True, verbose_name="模型名称"
                    ),
                ),
                (
                    "start_time",
                    models.DateTimeField(blank=True, null=True, verbose_name="模型开始时间"),
                ),
                (
                    "end_time",
                    models.DateTimeField(blank=True, null=True, verbose_name="模型结束时间"),
                ),
                (
                    "input_text",
                    models.TextField(blank=True, null=True, verbose_name="输入文本"),
                ),
                (
                    "think_text",
                    models.TextField(blank=True, null=True, verbose_name="think文本"),
                ),
                (
                    "out_text",
                    models.TextField(blank=True, null=True, verbose_name="输出文本"),
                ),
                (
                    "prompt_tokens",
                    models.CharField(max_length=50, verbose_name="输入token长度"),
                ),
                (
                    "completion_tokens",
                    models.CharField(max_length=50, verbose_name="输出token长度"),
                ),
                (
                    "patient",
                    models.ForeignKey(
                        db_column="patient_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="model_invocation_log",
                        to="patient.patient",
                        to_field="patient_id",
                        verbose_name="患者ID",
                    ),
                ),
                (
                    "project",
                    models.ForeignKey(
                        db_column="project_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="model_invocation_log",
                        to="project.project",
                        to_field="project_id",
                        verbose_name="项目ID",
                    ),
                ),
                (
                    "project_site",
                    models.ForeignKey(
                        db_column="project_site_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="model_invocation_log",
                        to="project.projectsite",
                        to_field="project_site_id",
                        verbose_name="项目中心ID",
                    ),
                ),
                (
                    "subject",
                    models.ForeignKey(
                        db_column="subject_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="model_invocation_log",
                        to="subject.subject",
                        to_field="subject_id",
                        verbose_name="受试者ID",
                    ),
                ),
                (
                    "subject_epoch",
                    models.ForeignKey(
                        db_column="subject_epoch_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="model_invocation_log",
                        to="subject.subjectepoch",
                        to_field="subject_epoch_id",
                        verbose_name="受试者阶段ID）",
                    ),
                ),
                (
                    "subject_item",
                    models.ForeignKey(
                        db_column="subject_item_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="model_invocation_log",
                        to="subject.subjectitem",
                        to_field="subject_item_id",
                        verbose_name="受试者操作项ID）",
                    ),
                ),
                (
                    "subject_visit",
                    models.ForeignKey(
                        db_column="subject_visit_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="model_invocation_log",
                        to="subject.subjectvisit",
                        to_field="subject_visit_id",
                        verbose_name="受试者访视ID）",
                    ),
                ),
            ],
            options={
                "verbose_name": "模型调用记录表",
                "verbose_name_plural": "模型调用记录表",
                "db_table": "model_invocation_log",
            },
        ),
    ]
