# Generated by Django 4.1.5 on 2025-03-26 22:00

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="OperationLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "delete_flag",
                    models.SmallIntegerField(
                        choices=[(0, "未删除"), (1, "已删除")],
                        db_index=True,
                        default=0,
                        verbose_name="删除标志（0：未删除；1：已删除）",
                    ),
                ),
                (
                    "data_version",
                    models.PositiveIntegerField(default=0, verbose_name="数据版本"),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True, db_index=True, verbose_name="创建时间"
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name="更新时间"
                    ),
                ),
                (
                    "create_user",
                    models.CharField(max_length=255, null=True, verbose_name="创建人工号"),
                ),
                (
                    "create_name",
                    models.Char<PERSON>ield(max_length=255, null=True, verbose_name="创建人姓名"),
                ),
                (
                    "update_user",
                    models.CharField(max_length=255, null=True, verbose_name="更新人工号"),
                ),
                (
                    "update_name",
                    models.CharField(max_length=255, null=True, verbose_name="更新人姓名"),
                ),
                (
                    "target_type",
                    models.CharField(
                        choices=[
                            ("CONFIRM_OCR_LOG", "ocr确认操作痕迹"),
                            ("CONFIRM_AE_LOG", "ae等级确认操作痕迹"),
                            ("CREATE_AE_TRACKER_LOG", "创建ae-tracker确认操作痕迹"),
                            ("EDIT_AE_LOG", "编辑单条ae等级操作痕迹"),
                        ],
                        db_index=True,
                        default="CONFIRM_OCR_LOG",
                        max_length=50,
                        verbose_name="目标类型",
                    ),
                ),
                (
                    "target_id",
                    models.CharField(
                        db_index=True, default=0, max_length=100, verbose_name="操作目标id"
                    ),
                ),
                (
                    "operate_content",
                    models.CharField(default="", max_length=255, verbose_name="操作内容"),
                ),
            ],
            options={
                "verbose_name": "操作日志",
                "verbose_name_plural": "操作日志",
                "db_table": "operation_log",
            },
        ),
        migrations.CreateModel(
            name="SysDict",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "delete_flag",
                    models.SmallIntegerField(
                        choices=[(0, "未删除"), (1, "已删除")],
                        db_index=True,
                        default=0,
                        verbose_name="删除标志（0：未删除；1：已删除）",
                    ),
                ),
                (
                    "data_version",
                    models.PositiveIntegerField(default=0, verbose_name="数据版本"),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True, db_index=True, verbose_name="创建时间"
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name="更新时间"
                    ),
                ),
                (
                    "create_user",
                    models.CharField(max_length=255, null=True, verbose_name="创建人工号"),
                ),
                (
                    "create_name",
                    models.CharField(max_length=255, null=True, verbose_name="创建人姓名"),
                ),
                (
                    "update_user",
                    models.CharField(max_length=255, null=True, verbose_name="更新人工号"),
                ),
                (
                    "update_name",
                    models.CharField(max_length=255, null=True, verbose_name="更新人姓名"),
                ),
                (
                    "dict_code",
                    models.CharField(
                        default="", max_length=50, unique=True, verbose_name="字典编码"
                    ),
                ),
                (
                    "dict_name",
                    models.CharField(default="", max_length=50, verbose_name="字典名称"),
                ),
                (
                    "dict_desc",
                    models.CharField(default="", max_length=255, verbose_name="字典描述"),
                ),
            ],
            options={
                "verbose_name": "数据字典",
                "verbose_name_plural": "数据字典",
                "db_table": "sys_dict",
            },
        ),
        migrations.CreateModel(
            name="SysDictItem",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "delete_flag",
                    models.SmallIntegerField(
                        choices=[(0, "未删除"), (1, "已删除")],
                        db_index=True,
                        default=0,
                        verbose_name="删除标志（0：未删除；1：已删除）",
                    ),
                ),
                (
                    "data_version",
                    models.PositiveIntegerField(default=0, verbose_name="数据版本"),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True, db_index=True, verbose_name="创建时间"
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name="更新时间"
                    ),
                ),
                (
                    "create_user",
                    models.CharField(max_length=255, null=True, verbose_name="创建人工号"),
                ),
                (
                    "create_name",
                    models.CharField(max_length=255, null=True, verbose_name="创建人姓名"),
                ),
                (
                    "update_user",
                    models.CharField(max_length=255, null=True, verbose_name="更新人工号"),
                ),
                (
                    "update_name",
                    models.CharField(max_length=255, null=True, verbose_name="更新人姓名"),
                ),
                (
                    "dict_code",
                    models.CharField(default="", max_length=50, verbose_name="字典编码"),
                ),
                (
                    "item_code",
                    models.CharField(default="", max_length=50, verbose_name="字典明细编码"),
                ),
                (
                    "item_name",
                    models.CharField(default="", max_length=50, verbose_name="字典明细名称"),
                ),
                (
                    "item_desc",
                    models.CharField(default="", max_length=255, verbose_name="字典明细描述"),
                ),
            ],
            options={
                "verbose_name": "数据字典明细",
                "verbose_name_plural": "数据字典明细",
                "db_table": "sys_dict_item",
            },
        ),
        migrations.AddConstraint(
            model_name="sysdictitem",
            constraint=models.UniqueConstraint(
                fields=("dict_code", "item_code"), name="unique_dict_item"
            ),
        ),
    ]
