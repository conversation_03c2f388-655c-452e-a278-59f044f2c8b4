# Generated by Django 4.1.5 on 2025-05-16 13:09

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="MaskingMaskedFile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "delete_flag",
                    models.SmallIntegerField(
                        choices=[(0, "未删除"), (1, "已删除")],
                        db_index=True,
                        default=0,
                        verbose_name="删除标志（0：未删除；1：已删除）",
                    ),
                ),
                (
                    "data_version",
                    models.PositiveIntegerField(default=0, verbose_name="数据版本"),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True, db_index=True, verbose_name="创建时间"
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name="更新时间"
                    ),
                ),
                (
                    "create_user",
                    models.CharField(max_length=255, null=True, verbose_name="创建人工号"),
                ),
                (
                    "create_name",
                    models.CharField(max_length=255, null=True, verbose_name="创建人姓名"),
                ),
                (
                    "update_user",
                    models.CharField(max_length=255, null=True, verbose_name="更新人工号"),
                ),
                (
                    "update_name",
                    models.CharField(max_length=255, null=True, verbose_name="更新人姓名"),
                ),
                (
                    "original_filename",
                    models.CharField(max_length=255, verbose_name="原始文件名"),
                ),
                ("bucket_name", models.CharField(max_length=100, verbose_name="存储桶名称")),
                ("object_name", models.CharField(max_length=500, verbose_name="对象名称")),
                ("content_type", models.CharField(max_length=100, verbose_name="文件类型")),
                ("size", models.BigIntegerField(verbose_name="文件大小（字节）")),
                (
                    "hash",
                    models.CharField(
                        db_index=True, max_length=255, verbose_name="文件哈希（sha256）"
                    ),
                ),
            ],
            options={
                "verbose_name": "外部系统脱敏文件",
                "verbose_name_plural": "外部系统脱敏文件",
                "db_table": "external_masking_masked_file",
            },
        ),
        migrations.CreateModel(
            name="MaskingOriginalFile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "delete_flag",
                    models.SmallIntegerField(
                        choices=[(0, "未删除"), (1, "已删除")],
                        db_index=True,
                        default=0,
                        verbose_name="删除标志（0：未删除；1：已删除）",
                    ),
                ),
                (
                    "data_version",
                    models.PositiveIntegerField(default=0, verbose_name="数据版本"),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True, db_index=True, verbose_name="创建时间"
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name="更新时间"
                    ),
                ),
                (
                    "create_user",
                    models.CharField(max_length=255, null=True, verbose_name="创建人工号"),
                ),
                (
                    "create_name",
                    models.CharField(max_length=255, null=True, verbose_name="创建人姓名"),
                ),
                (
                    "update_user",
                    models.CharField(max_length=255, null=True, verbose_name="更新人工号"),
                ),
                (
                    "update_name",
                    models.CharField(max_length=255, null=True, verbose_name="更新人姓名"),
                ),
                (
                    "original_filename",
                    models.CharField(max_length=255, verbose_name="原始文件名"),
                ),
                ("bucket_name", models.CharField(max_length=100, verbose_name="存储桶名称")),
                ("object_name", models.CharField(max_length=500, verbose_name="对象名称")),
                ("content_type", models.CharField(max_length=100, verbose_name="文件类型")),
                ("size", models.BigIntegerField(verbose_name="文件大小（字节）")),
                (
                    "hash",
                    models.CharField(
                        db_index=True, max_length=255, verbose_name="文件哈希（sha256）"
                    ),
                ),
            ],
            options={
                "verbose_name": "外部系统文件",
                "verbose_name_plural": "外部系统文件",
                "db_table": "external_masking_original_file",
            },
        ),
        migrations.CreateModel(
            name="MaskingTask",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "delete_flag",
                    models.SmallIntegerField(
                        choices=[(0, "未删除"), (1, "已删除")],
                        db_index=True,
                        default=0,
                        verbose_name="删除标志（0：未删除；1：已删除）",
                    ),
                ),
                (
                    "data_version",
                    models.PositiveIntegerField(default=0, verbose_name="数据版本"),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True, db_index=True, verbose_name="创建时间"
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name="更新时间"
                    ),
                ),
                (
                    "create_user",
                    models.CharField(max_length=255, null=True, verbose_name="创建人工号"),
                ),
                (
                    "create_name",
                    models.CharField(max_length=255, null=True, verbose_name="创建人姓名"),
                ),
                (
                    "update_user",
                    models.CharField(max_length=255, null=True, verbose_name="更新人工号"),
                ),
                (
                    "update_name",
                    models.CharField(max_length=255, null=True, verbose_name="更新人姓名"),
                ),
                (
                    "task_id",
                    models.CharField(max_length=255, unique=True, verbose_name="任务ID"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("TODO", "待办"),
                            ("IN_PROGRESS", "进行中"),
                            ("COMPLETED", "已完成"),
                            ("CANCELLED", "已取消"),
                            ("ERROR", "执行错误"),
                        ],
                        default="TODO",
                        max_length=20,
                        verbose_name="任务状态",
                    ),
                ),
                (
                    "note",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="备注"
                    ),
                ),
                (
                    "start_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="开始时间"),
                ),
                ("end_time", models.DateTimeField(null=True, verbose_name="结束时间")),
                ("meta", models.JSONField(null=True, verbose_name="附加的业务参数")),
                ("extra", models.JSONField(null=True, verbose_name="内部业务参数")),
                (
                    "callback_url",
                    models.CharField(max_length=512, null=True, verbose_name="回调URL"),
                ),
                (
                    "callback_time",
                    models.CharField(max_length=512, null=True, verbose_name="回调时间"),
                ),
                (
                    "callback_status",
                    models.CharField(max_length=20, null=True, verbose_name="回调状态"),
                ),
                (
                    "masked_file",
                    models.OneToOneField(
                        db_column="file_masked_id",
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subject_medical_info",
                        to="external.maskingmaskedfile",
                        verbose_name="外部系统脱敏文件",
                    ),
                ),
                (
                    "original_file",
                    models.OneToOneField(
                        db_column="file_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subject_medical_info",
                        to="external.maskingoriginalfile",
                        verbose_name="外部系统文件ID",
                    ),
                ),
            ],
            options={
                "verbose_name": "外部文件脱敏任务",
                "verbose_name_plural": "外部文件脱敏任务",
                "db_table": "external_masking_task",
            },
        ),
    ]
