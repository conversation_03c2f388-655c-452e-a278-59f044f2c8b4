FROM nexus.smo-clinplus.com:8184/ubuntu:20.04

RUN sed -i 's/archive.ubuntu.com/mirrors.aliyun.com/g' /etc/apt/sources.list
# RUN sed -i 's/archive.ubuntu.com/mirrors.tuna.tsinghua.edu.cn/g' /etc/apt/sources.list

RUN apt-get update && \
    apt-get install -y default-jdk

RUN apt-get update && apt-get install -y locales fonts-wqy-zenhei fonts-wqy-microhei && \
    locale-gen zh_CN.UTF-8 && \
    echo "export LANG=zh_CN.UTF-8" >> /etc/profile && \
    echo "export LANGUAGE=zh_CN:zh" >> /etc/profile && \
    echo "export LC_ALL=zh_CN.UTF-8" >> /etc/profile

RUN apt-get update && \
    apt-get install -y curl && \
    apt-get install -y gnupg && \
    curl https://packages.microsoft.com/keys/microsoft.asc | apt-key add - && \
    curl https://packages.microsoft.com/config/ubuntu/20.04/prod.list -o /etc/apt/sources.list.d/mssql-release.list && \
    apt-get update && \
    ACCEPT_EULA=Y apt-get install -y msodbcsql17  && \
    ACCEPT_EULA=Y apt-get install -y mssql-tools  && \
    sed -i '1i\openssl_conf = default_conf' /etc/ssl/openssl.cnf  && \
    echo '[ default_conf ]'  >> /etc/ssl/openssl.cnf  && \
    echo 'ssl_conf = ssl_sect' >> /etc/ssl/openssl.cnf  && \
    echo '[ssl_sect]' >> /etc/ssl/openssl.cnf  && \
    echo 'system_default = system_default_sect'  >> /etc/ssl/openssl.cnf  && \
    echo '[system_default_sect]'  >> /etc/ssl/openssl.cnf  && \
    echo 'MinProtocol = TLSv1.2'  >> /etc/ssl/openssl.cnf  && \
    echo 'CipherString = DEFAULT:@SECLEVEL=1'  >> /etc/ssl/openssl.cnf  && \
    echo 'export PATH="$PATH:/opt/mssql-tools/bin"' >> ~/.bashrc && \
    . ~/.bashrc

RUN apt-get update && \
    apt-get install -y python3.9 && \
    apt-get install -y python3-pip && \
    apt-get install -y nginx && \
    apt-get install -y supervisor && \
    apt-get install -y vim && \
    apt-get install -y git && \
    apt-get install -y wget && \
    apt-get install -y sudo && \
    wget http://***************:8081/python-package/google-chrome-stable_current_amd64.deb && \
    sudo apt install -y ./google-chrome-stable_current_amd64.deb && \
    sudo apt-get install -y xvfb && \
    sudo apt-get install -y unzip && \
    wget http://***************:8081/python-package/chromedriver_linux64.zip && \
    unzip ./chromedriver_linux64.zip && \
    sudo mv -f chromedriver /usr/local/share/chromedriver && \
    sudo ln -s /usr/local/share/chromedriver /usr/local/bin/chromedriver && \
    sudo ln -s /usr/local/share/chromedriver /usr/bin/chromedriver

RUN wget http://***************:8081/python-package/node-v20.9.0-linux-x64.tar.xz && \
    tar -xJf node-v20.9.0-linux-x64.tar.xz && \
    cd node-v20.9.0-linux-x64 && \
    sudo cp -r * /usr/local/

# RUN npm config set registry https://registry.npmmirror.com && \
#     curl -s https://raw.githubusercontent.com/jhuckaby/Cronicle/master/bin/install.js | node

RUN npm config set registry https://registry.npmmirror.com && \
    npm install -g npm@10.1.0 && \
    mkdir -p /opt/cronicle && \
    cd /opt/cronicle && \
    curl -L http://***************:8081/python-package/Cronicle-0.9.76.tar.gz | tar zxvf - --strip-components 1 && \
    npm install && \
    node bin/build.js dist

RUN apt-get update && \
    apt-get install -y libreoffice

# RUN sudo apt install -y tesseract-ocr && \
#     sudo apt install -y libtesseract-dev && \
#     cd /usr/share/tesseract-ocr/4.00/tessdata && \
#     wget http://***************:8081/python-package/chi_sim_vert.traineddata && \
#     wget http://***************:8081/python-package/chi_sim.traineddata

RUN echo 'ulimit -c 1024' >> ~/.bashrc && \
    echo 'ulimit -n 100000' >> ~/.bashrc && \
    . ~/.bashrc

RUN apt-get update && \
    apt-get install -y python3.9-dev libpq-dev

# https://pypi.tuna.tsinghua.edu.cn/simple
# https://mirrors.aliyun.com/pypi/simple/

RUN python3.9 -m pip --no-cache-dir install -i https://pypi.tuna.tsinghua.edu.cn/simple \
    Django==4.1.5 \
    djangorestframework==3.14.0 \
    gunicorn==20.1.0 \
    pymssql==2.2.7 \
    django-cors-headers==3.13.0 \
    rest-pandas==1.1.0 \
    openpyxl==3.1.0 \
    nacos-sdk-python==0.1.12 \
    retry==0.9.2 \
    mssql-django==1.4.2 \
    JPype1==1.4.1 \
    PyYAML==6.0 \
    sqlacodegen==2.3.0 \
    django-filter==22.1 \
    pymysql==1.0.2 \
    django_extensions==3.2.1 \
    drf-spectacular==0.28.0 \
    drf-spectacular-sidecar==2025.2.1 \
    nbformat==5.7.3 \
    nbconvert==7.2.9 \
    IPython==8.9.0 \
    SQLAlchemy==1.4.54 \
    PyJWT==2.10.1 \
    selenium==4.16.0 \
    requests==2.31.0 \
    lxml==5.2.1 \
    psycopg2==2.9.9 \
    django_comment_migrate==0.1.7 \
    scikit-learn==1.4.0 \
    cryptography==41.0.2 \
    pdfplumber==0.11.4 \
    opencv-python==********* \
    Pillow==10.4.0 \
    # pytesseract==0.3.13 \
    # paddlepaddle-gpu==2.6.1 \
    # paddleocr==2.8.1 \
    pypdf==4.3.1 \
    playwright==1.47.0 \
    ddddocr==1.5.6 \
    fake-useragent==2.0.3  \
    XlsxWriter==3.2.2  \
    tqdm==4.67.1 \
    minio==7.2.15 \
    PyMuPDF==1.25.1 \
    xlrd==2.0.1 \
    pillow_heif==0.22.0 \
    python-docx==0.8.11 \
    json-repair==0.44.1 \
    pocketflow==0.0.2 \
    timeout-decorator==0.5.0 \
    docx2pdf==0.1.8 \
    pdf2image==1.17.0 \
    pandoc==2.4 \
    plumbum==1.9.0 \
    ply==3.11 \
    wkhtmltopdf==0.2

RUN python3.9 -m pip install \
    --only-binary=google-re2 google-re2 \
    apache-airflow==2.10.5 \
    pendulum==2.1.2 \
    apache-airflow-providers-fab==1.1.0 \
    -i https://pypi.tuna.tsinghua.edu.cn/simple

# 强制降级并忽略依赖检查
RUN python3.9 -m pip install --force-reinstall --no-deps marshmallow==3.26.1 marshmallow-oneofschema==3.1.1 -i https://pypi.tuna.tsinghua.edu.cn/simple

RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    libwoff1 \
    libevent-2.1-7 \
    libopus0 \
    libwebpdemux2 \
    libharfbuzz-icu0 \
    libenchant-2-2 \
    libsecret-1-0 \
    libhyphen0 \
    libflite1 \
    libegl1 \
    libgudev-1.0-0 \
    libevdev2 \
    libgles2 \
    gstreamer1.0-libav

RUN python3.9 -m playwright install

WORKDIR /app

COPY . /app

# RUN cd /app && \
#     wget http://***************:8081/python-package/ch_PP-OCRv4_det_server_infer.tar && \
#     tar -xvf ch_PP-OCRv4_det_server_infer.tar && \
#     wget http://***************:8081/python-package/ch_PP-OCRv4_rec_server_infer.tar && \
#     tar -xvf ch_PP-OCRv4_rec_server_infer.tar

RUN cp /app/etc/nginx.conf /etc/nginx/nginx.conf && \
    cp -r /app/etc/cronicle /etc/cronicle

EXPOSE 8000 3012

# CMD ["supervisord", "-c", "/app/etc/supervisord.conf"]

# CMD ["/bin/bash", "-c", "tail -f /dev/null"]

CMD ["/bin/bash", "start.sh"]
