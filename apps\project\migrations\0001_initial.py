# Generated by Django 4.1.5 on 2025-03-03 12:32

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Project",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "delete_flag",
                    models.SmallIntegerField(
                        choices=[(0, "未删除"), (1, "已删除")],
                        db_index=True,
                        default=0,
                        verbose_name="删除标志（0：未删除；1：已删除）",
                    ),
                ),
                (
                    "data_version",
                    models.PositiveIntegerField(default=0, verbose_name="数据版本"),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True, db_index=True, verbose_name="创建时间"
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "create_user",
                    models.CharField(max_length=255, null=True, verbose_name="创建人工号"),
                ),
                (
                    "create_name",
                    models.CharField(max_length=255, null=True, verbose_name="创建人姓名"),
                ),
                (
                    "update_user",
                    models.CharField(max_length=255, null=True, verbose_name="更新人工号"),
                ),
                (
                    "update_name",
                    models.CharField(max_length=255, null=True, verbose_name="更新人姓名"),
                ),
                (
                    "project_id",
                    models.CharField(max_length=255, unique=True, verbose_name="项目ID"),
                ),
                (
                    "project_no",
                    models.CharField(
                        db_index=True, max_length=255, verbose_name="项目编号"
                    ),
                ),
                (
                    "project_name",
                    models.CharField(
                        db_index=True, max_length=512, verbose_name="项目名称"
                    ),
                ),
                (
                    "indication",
                    models.CharField(max_length=255, null=True, verbose_name="适应症"),
                ),
                (
                    "indication_text",
                    models.CharField(max_length=255, null=True, verbose_name="适应症描述"),
                ),
                (
                    "stage",
                    models.CharField(
                        db_index=True, max_length=255, null=True, verbose_name="项目分期"
                    ),
                ),
                (
                    "stage_text",
                    models.CharField(max_length=255, null=True, verbose_name="项目分期描述"),
                ),
                (
                    "status",
                    models.CharField(
                        db_index=True, max_length=255, null=True, verbose_name="项目阶段"
                    ),
                ),
                (
                    "status_text",
                    models.CharField(max_length=255, null=True, verbose_name="项目阶段描述"),
                ),
                (
                    "pm_text",
                    models.CharField(max_length=255, null=True, verbose_name="PM描述"),
                ),
                (
                    "pm_group_text",
                    models.CharField(max_length=255, null=True, verbose_name="PM分组描述"),
                ),
                (
                    "bu_text",
                    models.CharField(max_length=255, null=True, verbose_name="BU描述"),
                ),
                ("site_count", models.IntegerField(default=0, verbose_name="中心数量")),
                ("subject_count", models.IntegerField(default=0, verbose_name="受试者数量")),
            ],
            options={
                "verbose_name": "项目信息",
                "verbose_name_plural": "项目信息",
                "db_table": "project_info",
            },
        ),
        migrations.CreateModel(
            name="ProjectSite",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "delete_flag",
                    models.SmallIntegerField(
                        choices=[(0, "未删除"), (1, "已删除")],
                        db_index=True,
                        default=0,
                        verbose_name="删除标志（0：未删除；1：已删除）",
                    ),
                ),
                (
                    "data_version",
                    models.PositiveIntegerField(default=0, verbose_name="数据版本"),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True, db_index=True, verbose_name="创建时间"
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "create_user",
                    models.CharField(max_length=255, null=True, verbose_name="创建人工号"),
                ),
                (
                    "create_name",
                    models.CharField(max_length=255, null=True, verbose_name="创建人姓名"),
                ),
                (
                    "update_user",
                    models.CharField(max_length=255, null=True, verbose_name="更新人工号"),
                ),
                (
                    "update_name",
                    models.CharField(max_length=255, null=True, verbose_name="更新人姓名"),
                ),
                (
                    "project_site_id",
                    models.CharField(
                        max_length=255, unique=True, verbose_name="项目研究中心ID"
                    ),
                ),
                (
                    "hosp_id",
                    models.CharField(
                        db_index=True, max_length=255, verbose_name="医院ID"
                    ),
                ),
                (
                    "hosp_name",
                    models.CharField(
                        db_index=True, max_length=255, verbose_name="中心名称"
                    ),
                ),
                (
                    "hosp_department_no",
                    models.CharField(
                        db_index=True, max_length=255, verbose_name="中心编号"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        db_index=True, max_length=255, null=True, verbose_name="中心状态"
                    ),
                ),
                (
                    "status_text",
                    models.CharField(max_length=255, null=True, verbose_name="中心状态描述"),
                ),
                (
                    "accredit_crc_text",
                    models.TextField(null=True, verbose_name="授权CRC描述"),
                ),
                (
                    "backup_crc_text",
                    models.TextField(null=True, verbose_name="Backup CRC描述"),
                ),
                (
                    "project",
                    models.ForeignKey(
                        db_column="project_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sites",
                        to="project.project",
                        to_field="project_id",
                        verbose_name="项目ID",
                    ),
                ),
            ],
            options={
                "verbose_name": "项目研究中心信息",
                "verbose_name_plural": "项目研究中心信息",
                "db_table": "project_site_info",
            },
        ),
        migrations.CreateModel(
            name="ProjectMaterialInfo",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "delete_flag",
                    models.SmallIntegerField(
                        choices=[(0, "未删除"), (1, "已删除")],
                        db_index=True,
                        default=0,
                        verbose_name="删除标志（0：未删除；1：已删除）",
                    ),
                ),
                (
                    "data_version",
                    models.PositiveIntegerField(default=0, verbose_name="数据版本"),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True, db_index=True, verbose_name="创建时间"
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "create_user",
                    models.CharField(max_length=255, null=True, verbose_name="创建人工号"),
                ),
                (
                    "create_name",
                    models.CharField(max_length=255, null=True, verbose_name="创建人姓名"),
                ),
                (
                    "update_user",
                    models.CharField(max_length=255, null=True, verbose_name="更新人工号"),
                ),
                (
                    "update_name",
                    models.CharField(max_length=255, null=True, verbose_name="更新人姓名"),
                ),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("CRF_TEMPLATE", "CRF表头"),
                            ("MEDICAL_RECORD_TEMPLATE", "病历模版"),
                        ],
                        db_index=True,
                        default="CRF_TEMPLATE",
                        max_length=50,
                        verbose_name="素材分类",
                    ),
                ),
                (
                    "project",
                    models.ForeignKey(
                        db_column="project_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="material_infos",
                        to="project.project",
                        to_field="project_id",
                        verbose_name="项目ID",
                    ),
                ),
            ],
            options={
                "verbose_name": "项目素材信息",
                "verbose_name_plural": "项目素材信息",
                "db_table": "project_material_info",
                "unique_together": {("project", "category")},
            },
        ),
        migrations.CreateModel(
            name="ProjectMaterialFile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "delete_flag",
                    models.SmallIntegerField(
                        choices=[(0, "未删除"), (1, "已删除")],
                        db_index=True,
                        default=0,
                        verbose_name="删除标志（0：未删除；1：已删除）",
                    ),
                ),
                (
                    "data_version",
                    models.PositiveIntegerField(default=0, verbose_name="数据版本"),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True, db_index=True, verbose_name="创建时间"
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "create_user",
                    models.CharField(max_length=255, null=True, verbose_name="创建人工号"),
                ),
                (
                    "create_name",
                    models.CharField(max_length=255, null=True, verbose_name="创建人姓名"),
                ),
                (
                    "update_user",
                    models.CharField(max_length=255, null=True, verbose_name="更新人工号"),
                ),
                (
                    "update_name",
                    models.CharField(max_length=255, null=True, verbose_name="更新人姓名"),
                ),
                (
                    "original_filename",
                    models.CharField(max_length=255, verbose_name="原始文件名"),
                ),
                ("bucket_name", models.CharField(max_length=100, verbose_name="存储桶名称")),
                ("object_name", models.CharField(max_length=500, verbose_name="对象名称")),
                ("content_type", models.CharField(max_length=100, verbose_name="文件类型")),
                ("size", models.BigIntegerField(verbose_name="文件大小（字节）")),
                (
                    "hash",
                    models.CharField(
                        db_index=True, max_length=255, verbose_name="文件哈希（sha256）"
                    ),
                ),
                (
                    "material_info",
                    models.ForeignKey(
                        db_column="material_info_id",
                        db_constraint=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="files",
                        to="project.projectmaterialinfo",
                        verbose_name="项目素材信息ID",
                    ),
                ),
            ],
            options={
                "verbose_name": "项目素材文件",
                "verbose_name_plural": "项目素材文件",
                "db_table": "project_material_file",
            },
        ),
    ]
