#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
最简化的三个核心节点测试脚本
测试DataPreprocessNode、AbnormalItemFilterNode、SmartPromptBuilderNode
"""

import sys
import os
import time

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, '../../..'))
sys.path.insert(0, project_root)

from script.ae_grade.nodes import DataPreprocessNode, AbnormalItemFilterNode, SmartPromptBuilderNode

# 测试数据
raw_test_data = [
    {
        "id": 1,
        "test_name": "白细胞计数",
        "test_value": "12.5",
        "test_flag": 1,  # 偏高
        "test_unit": "×10⁹/L",
        "reference_value": "3.5-9.5",
        "test_code": "WBC"
    },
    {
        "id": 2,
        "test_name": "血红蛋白",
        "test_value": "85",
        "test_flag": 2,  # 偏低
        "test_unit": "g/L",
        "reference_value": "120-160",
        "test_code": "HGB"
    },
    {
        "id": 3,
        "test_name": "血小板计数",
        "test_value": "150",
        "test_flag": 0,  # 正常
        "test_unit": "×10⁹/L",
        "reference_value": "125-350",
        "test_code": "PLT"
    },
    {
        "id": 4,
        "test_name": "丙氨酸氨基转移酶",
        "test_value": "120",
        "test_flag": 1,  # 偏高
        "test_unit": "U/L",
        "reference_value": "9-50",
        "test_code": "ALT"
    }
]

def test_data_preprocess_node():
    """测试数据预处理节点"""
    print("=" * 60)
    print("🧪 测试 DataPreprocessNode")
    print("=" * 60)
    
    # 创建节点
    node = DataPreprocessNode()
    
    # 准备共享数据
    shared = {"raw_input_data": raw_test_data}
    
    # 执行节点
    start_time = time.time()
    
    # prep阶段
    prep_result = node.prep(shared)
    print(f"📋 prep结果: {len(prep_result)} 项原始数据")
    
    # exec阶段
    exec_result = node.exec(prep_result)
    print(f"📋 exec结果: {len(exec_result)} 项转换数据")
    print("   转换示例:")
    for i, item in enumerate(exec_result[:2]):
        print(f"     {i+1}. {item}")
    
    # post阶段
    post_result = node.post(shared, prep_result, exec_result)
    print(f"📋 post结果: {post_result}")
    print(f"📋 共享数据更新: test_results={len(shared.get('test_results', []))} 项")
    
    duration = time.time() - start_time
    print(f"⏱️  耗时: {duration:.3f}秒")
    
    return shared

def test_abnormal_item_filter_node(shared):
    """测试异常项筛选节点"""
    print("\n" + "=" * 60)
    print("🧪 测试 AbnormalItemFilterNode")
    print("=" * 60)
    
    # 创建节点
    node = AbnormalItemFilterNode()
    
    # 执行节点
    start_time = time.time()
    
    # prep阶段
    prep_result = node.prep(shared)
    print(f"📋 prep结果: {len(prep_result)} 项输入数据")
    
    # exec阶段
    exec_result = node.exec(prep_result)
    print(f"📋 exec结果: {len(exec_result)} 项异常数据")
    print("   异常项详情:")
    for i, item in enumerate(exec_result):
        test_name = item.get('检查项目', '未知')
        status = item.get('状态', '-')
        value = item.get('结果', '')
        print(f"     {i+1}. {test_name}: {value} ({status})")
    
    # post阶段
    post_result = node.post(shared, prep_result, exec_result)
    print(f"📋 post结果: {post_result}")
    print(f"📋 共享数据更新: abnormal_items={len(shared.get('abnormal_items', []))} 项")
    
    duration = time.time() - start_time
    print(f"⏱️  耗时: {duration:.3f}秒")
    
    return shared

def test_smart_prompt_builder_node(shared):
    """测试智能提示词构建节点"""
    print("\n" + "=" * 60)
    print("🧪 测试 SmartPromptBuilderNode")
    print("=" * 60)
    
    # 创建节点
    try:
        node = SmartPromptBuilderNode()
        print("✅ 节点创建成功")
    except Exception as e:
        print(f"❌ 节点创建失败: {e}")
        return shared
    
    # 执行节点
    start_time = time.time()
    
    try:
        # prep阶段
        prep_result = node.prep(shared)
        print(f"📋 prep结果: {len(prep_result)} 项异常数据")
        
        # exec阶段
        exec_result = node.exec(prep_result)
        optimized_prompt, matched_rules = exec_result
        
        print(f"📋 exec结果:")
        print(f"   提示词长度: {len(optimized_prompt)} 字符")
        print(f"   匹配规则数: {len(matched_rules)} 项")
        
        # 显示提示词片段
        # prompt_preview = optimized_prompt[:200] + "..." if len(optimized_prompt) > 200 else optimized_prompt
        print(f"   提示词预览: {optimized_prompt}")
        
        # 显示匹配规则详情
        if matched_rules:
            print("   匹配规则详情:")
            for item_id, rules in matched_rules.items():
                print(f"     项目{item_id}: {len(rules)} 条规则")
                for rule in rules[:2]:  # 只显示前2条
                    rule_name = rule.get('ae_name', '未知')
                    print(f"       - {rule_name}")
        
        # post阶段
        post_result = node.post(shared, prep_result, exec_result)
        print(f"📋 post结果: {post_result}")
        
        duration = time.time() - start_time
        print(f"⏱️  耗时: {duration:.3f}秒")
        
        # 测试rerank功能
        print("\n🔧 Rerank功能测试:")
        if hasattr(node, 'rule_matcher'):
            try:
                # 测试rerank客户端连接
                rerank_client = node.rule_matcher._rerank_client
                if rerank_client:
                    connection_ok = rerank_client.test_connection()
                    print(f"   Rerank服务连接: {'✅ 正常' if connection_ok else '❌ 失败'}")
                else:
                    print("   Rerank客户端: ❌ 未初始化")
            except Exception as e:
                print(f"   Rerank测试失败: {e}")
        
        print("✅ SmartPromptBuilderNode测试完成")
        
    except Exception as e:
        duration = time.time() - start_time
        print(f"❌ 测试失败: {e}")
        print(f"⏱️  耗时: {duration:.3f}秒")
        
        # 测试降级处理
        print("\n🔧 测试降级处理:")
        try:
            fallback_result = node.exec_fallback(prep_result, e)
            fallback_prompt, fallback_rules = fallback_result
            print(f"   降级提示词长度: {len(fallback_prompt)} 字符")
            print(f"   降级提示词: {fallback_prompt}")
            print("✅ 降级处理正常")
        except Exception as fallback_e:
            print(f"❌ 降级处理也失败: {fallback_e}")
    
    return shared

def main():
    """主测试函数"""
    print("🧪 AE Grade Recognition System - 核心节点测试")
    print("=" * 80)
    print(f"📅 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📊 测试数据: {len(raw_test_data)} 项")
    print("=" * 80)
    
    # 依次测试三个节点
    shared = test_data_preprocess_node()
    shared = test_abnormal_item_filter_node(shared)
    shared = test_smart_prompt_builder_node(shared)
    
    # 总结
    print("\n" + "=" * 80)
    print("📋 测试总结")
    print("=" * 80)
    print("✅ DataPreprocessNode: 数据格式转换正常")
    print("✅ AbnormalItemFilterNode: 异常项筛选正常")
    print("✅ SmartPromptBuilderNode: 智能提示词构建测试完成")
    print("\n🎉 所有节点测试完成!")

if __name__ == "__main__":
    main()
