# -*- coding: utf-8 -*-
"""
简化的并行处理配置

用于阶段一的最小侵入式改造，提供基础的配置管理功能
"""

import logging
from dataclasses import dataclass
from typing import Dict, Any

logger = logging.getLogger(__name__)


@dataclass
class SimpleParallelConfig:
    """简化的并行处理配置类"""
    
    # 基础配置
    enable_parallel: bool = True                    # 是否启用并行处理
    max_workers: int = 3                           # 最大并发工作线程数
    timeout: int = 300                             # 总超时时间（秒）
    page_timeout: int = 60                         # 单页处理超时时间（秒）
    
    # 错误处理配置
    success_rate_threshold: float = 0.8            # 成功率阈值
    continue_on_partial_failure: bool = True       # 部分失败时是否继续处理
    
    # 监控配置
    enable_performance_monitoring: bool = True     # 是否启用性能监控
    enable_detailed_logging: bool = True          # 是否启用详细日志
    
    # 调试配置
    debug_mode: bool = False                      # 调试模式
    force_serial_processing: bool = False         # 强制串行处理（用于对比测试）


class SimpleConfigManager:
    """简化的配置管理器"""
    
    def __init__(self):
        self.logger = logger
        self._config = None
    
    def get_config(self) -> SimpleParallelConfig:
        """获取当前配置"""
        if self._config is None:
            self._config = self._create_default_config()
        return self._config
    
    def update_config(self, **kwargs) -> SimpleParallelConfig:
        """更新配置"""
        config = self.get_config()
        
        for key, value in kwargs.items():
            if hasattr(config, key):
                setattr(config, key, value)
                self.logger.info(f"配置更新: {key} = {value}")
            else:
                self.logger.warning(f"未知配置项: {key}")
        
        return config
    
    def _create_default_config(self) -> SimpleParallelConfig:
        """创建默认配置"""
        config = SimpleParallelConfig()
        
        # 根据实际需求调整默认配置
        # 这里可以根据系统资源动态调整
        try:
            import psutil
            cpu_count = psutil.cpu_count()
            memory_gb = psutil.virtual_memory().total / (1024**3)
            
            # 基于系统资源调整并发度
            if memory_gb >= 16 and cpu_count >= 8:
                config.max_workers = min(5, cpu_count - 2)
            elif memory_gb >= 8 and cpu_count >= 4:
                config.max_workers = min(3, cpu_count - 1)
            else:
                config.max_workers = 2
                
            self.logger.info(f"根据系统资源调整并发度: {config.max_workers} (CPU: {cpu_count}, 内存: {memory_gb:.1f}GB)")
            
        except ImportError:
            self.logger.warning("psutil未安装，使用默认并发度配置")
        
        self._log_config(config)
        return config
    
    def _log_config(self, config: SimpleParallelConfig):
        """记录配置信息"""
        self.logger.info("🔧 并行处理配置:")
        self.logger.info(f"  - 启用并行处理: {config.enable_parallel}")
        self.logger.info(f"  - 最大并发数: {config.max_workers}")
        self.logger.info(f"  - 总超时时间: {config.timeout}秒")
        self.logger.info(f"  - 成功率阈值: {config.success_rate_threshold:.1%}")
        self.logger.info(f"  - 性能监控: {config.enable_performance_monitoring}")
        if config.debug_mode:
            self.logger.info("  - 调试模式: 已启用")
        if config.force_serial_processing:
            self.logger.info("  - 强制串行处理: 已启用")


# 全局配置管理器实例
_config_manager = SimpleConfigManager()


def get_simple_config() -> SimpleParallelConfig:
    """获取简化配置"""
    return _config_manager.get_config()


def update_simple_config(**kwargs) -> SimpleParallelConfig:
    """更新简化配置"""
    return _config_manager.update_config(**kwargs)


# 预设配置方案
PRESET_CONFIGS = {
    "conservative": {
        "enable_parallel": True,
        "max_workers": 2,
        "success_rate_threshold": 0.9,
        "continue_on_partial_failure": False
    },
    "balanced": {
        "enable_parallel": True,
        "max_workers": 3,
        "success_rate_threshold": 0.8,
        "continue_on_partial_failure": True
    },
    "aggressive": {
        "enable_parallel": True,
        "max_workers": 5,
        "success_rate_threshold": 0.7,
        "continue_on_partial_failure": True
    },
    "serial_only": {
        "enable_parallel": False,
        "force_serial_processing": True
    }
}


def apply_preset_config(preset_name: str) -> SimpleParallelConfig:
    """应用预设配置"""
    if preset_name not in PRESET_CONFIGS:
        logger.warning(f"未知的预设配置: {preset_name}，可用配置: {list(PRESET_CONFIGS.keys())}")
        return get_simple_config()
    
    preset = PRESET_CONFIGS[preset_name]
    logger.info(f"应用预设配置: {preset_name}")
    return update_simple_config(**preset)


# 配置使用示例
if __name__ == "__main__":
    # 获取默认配置
    config = get_simple_config()
    print(f"默认配置 - 并行: {config.enable_parallel}, 并发数: {config.max_workers}")
    
    # 应用保守配置
    config = apply_preset_config("conservative")
    print(f"保守配置 - 并行: {config.enable_parallel}, 并发数: {config.max_workers}")
    
    # 应用平衡配置
    config = apply_preset_config("balanced")
    print(f"平衡配置 - 并行: {config.enable_parallel}, 并发数: {config.max_workers}")
    
    # 自定义配置
    config = update_simple_config(max_workers=4, debug_mode=True)
    print(f"自定义配置 - 并发数: {config.max_workers}, 调试模式: {config.debug_mode}")
