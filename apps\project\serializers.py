from datetime import timedelta
from django.conf import settings
from rest_framework import serializers

from common.minio_client import get_minio_client
from common.models import BaseFileModel
from common.serializers import FileUrlMixin

from . import models


class ProjectSerializer(serializers.ModelSerializer):
    has_file_flag = serializers.SerializerMethodField(label='是否有文件标志')
    role_codes = serializers.SerializerMethodField(label='角色代码列表')
    # site_count = serializers.IntegerField(read_only=True, label='中心数量')
    # subject_count = serializers.IntegerField(read_only=True, label='受试者数量')

    class Meta:
        model = models.Project
        exclude = ['create_user', 'create_name', 'update_user', 'update_name']

    def get_has_file_flag(self, obj):
        # 直接从模型实例获取注解的字段
        return getattr(obj, 'has_file_flag', 0)

    def get_role_codes(self, obj):
        # 从request中获取角色代码信息
        request = self.context.get('request')
        if request and hasattr(request, 'project_roles'):
            project_roles = request.project_roles
            return project_roles.get(obj.project_id, [])
        # 对于白名单用户，默认返回空列表
        return []
    # def get_has_file_flag(self, obj):
    #     # 如果对象上已经设置了has_file_flag属性，则返回它
    #     if hasattr(obj, 'has_file_flag'):
    #         return getattr(obj, 'has_file_flag', 0)
    #     # 否则默认返回0
    #     return 0

class ProjectSiteSerializer(serializers.ModelSerializer):
    project_id = serializers.CharField(source='project.project_id', read_only=True, label="项目ID")
    project_no = serializers.CharField(source='project.project_no', read_only=True, label="项目编号")

    class Meta:
        model = models.ProjectSite
        exclude = ['project', 'create_user', 'create_name', 'update_user', 'update_name']


class ProjectMaterialFileUpdateSerializer(FileUrlMixin, serializers.ModelSerializer):

    class Meta:
        model = models.ProjectMaterialFile
        fields = ['original_filename']


class ProjectMaterialFileSerializer(FileUrlMixin, serializers.ModelSerializer):
    project_id = serializers.CharField(source='material_info.project_id', label="项目ID")
    category = serializers.CharField(source='material_info.category', label="素材分类")
    url = serializers.SerializerMethodField(label='文件下载url')

    class Meta:
        model = models.ProjectMaterialFile
        exclude = ['create_user', 'create_name', 'update_user', 'update_name']


class ProjectMaterialInfoSerializer(serializers.ModelSerializer):
    project_id = serializers.CharField(source='project.project_id', label="项目ID")
    file = ProjectMaterialFileSerializer()

    class Meta:
        model = models.ProjectMaterialInfo
        exclude = ['project', 'create_user', 'create_name', 'update_user', 'update_name']


class ProjectMaterialLatestRequestSerializer(serializers.ModelSerializer):
    project_id = serializers.CharField(source='project.project_id', label="项目ID")
    epoch_id = serializers.CharField(required=False, allow_null=True, label="阶段ID") 

    class Meta:
        model = models.ProjectMaterialInfo
        fields = ['project_id','epoch_id', 'category']


class ProjectMaterialCreateRequestSerializer(serializers.ModelSerializer):
    project_id = serializers.CharField(source='project.project_id', label="项目ID")
    epoch_id = serializers.CharField(required=False, allow_null=True, label="阶段ID") 
    file = serializers.FileField(required=True)  # 文件上传字段

    class Meta:
        model = models.ProjectMaterialInfo
        fields = ['project_id','epoch_id', 'category', 'file']
