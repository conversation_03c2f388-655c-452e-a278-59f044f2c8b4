from django.urls import path, include
from rest_framework.routers import DefaultRout<PERSON>, SimpleRouter

from . import views

router = DefaultRouter(trailing_slash=False)
router.register(r'/projects', views.ProjectListViewSet)
router.register(r'/projects/(?P<project_id>\w+)/project-sites', views.ProjectSiteListViewSetDeprecated)
router.register(r'/project-sites', views.ProjectSiteListViewSet)

# router.register(r'/projects/(?P<project_id>\w+)/material-files', views.ProjectMaterialInfoListViewSetDeprecated)
router.register(r'/project-material-files', views.ProjectMaterialInfoListViewSet)
router.register(r'/project-material-files', views.ProjectMaterialFileDetailViewSet)

urlpatterns = []

urlpatterns = router.urls + urlpatterns
